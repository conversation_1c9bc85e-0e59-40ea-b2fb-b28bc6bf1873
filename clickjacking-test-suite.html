<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>ROVA Clickjacking Protection Test Suite</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .test-container {
                background: white;
                margin: 20px 0;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .test-title {
                color: #d32f2f;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .test-description {
                color: #666;
                margin-bottom: 15px;
            }
            .iframe-container {
                border: 2px solid #ddd;
                padding: 10px;
                background: #fafafa;
            }
            iframe {
                width: 100%;
                height: 400px;
                border: 1px solid #ccc;
            }
            .expected-result {
                background: #e8f5e8;
                padding: 10px;
                margin-top: 10px;
                border-left: 4px solid #4caf50;
            }
            .warning {
                background: #fff3cd;
                padding: 10px;
                margin: 20px 0;
                border-left: 4px solid #ffc107;
                color: #856404;
            }
            .instructions {
                background: #e3f2fd;
                padding: 15px;
                margin: 20px 0;
                border-left: 4px solid #2196f3;
            }
        </style>
    </head>
    <body>
        <h1>🛡️ ROVA Clickjacking Protection Test Suite</h1>

        <div class="warning">
            <strong>⚠️ Important:</strong> Make sure your ROVA application is running on
            <code>http://localhost:3000</code> before testing.
        </div>

        <div class="instructions">
            <h3>📋 How to Test:</h3>
            <ol>
                <li>Open your browser's Developer Tools (F12)</li>
                <li>Go to the <strong>Console</strong> tab</li>
                <li>Look for error messages about frame blocking</li>
                <li>Check the <strong>Network</strong> tab for security headers</li>
            </ol>
        </div>

        <!-- Test 1: Basic Iframe Embedding -->
        <div class="test-container">
            <div class="test-title">Test 1: Basic Iframe Embedding</div>
            <div class="test-description">This test attempts to load your ROVA application in a standard iframe.</div>
            <div class="iframe-container">
                <iframe src="https://client-ui.cibng-dev-rova.com" title="ROVA App Test 1"></iframe>
            </div>
            <div class="expected-result">
                <strong>✅ Expected Result:</strong> The iframe should be blank or show an error. Console should show:
                "Refused to display in a frame because it set 'X-Frame-Options' to 'deny'."
            </div>
        </div>

        <!-- Test 2: Invisible Iframe (Clickjacking Simulation) -->
        <div class="test-container">
            <div class="test-title">Test 2: Invisible Iframe (Clickjacking Simulation)</div>
            <div class="test-description">
                This simulates a real clickjacking attack with an invisible iframe overlaid with fake content.
            </div>
            <div style="position: relative; height: 400px; background: #f0f0f0; border: 1px solid #ccc">
                <!-- Fake content that attackers might use -->
                <div
                    style="
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        text-align: center;
                        z-index: 2;
                    "
                >
                    <h3>🎁 Click here to claim your FREE $100!</h3>
                    <button
                        style="
                            padding: 15px 30px;
                            font-size: 16px;
                            background: #4caf50;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        "
                    >
                        CLAIM NOW
                    </button>
                </div>
                <!-- Hidden iframe that would contain the real app -->
                <iframe
                    src="https://client-ui.cibng-dev-rova.com/"
                    style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.1; z-index: 1"
                    title="Hidden ROVA App"
                >
                </iframe>
            </div>
            <div class="expected-result">
                <strong>✅ Expected Result:</strong> Even though there's a fake button overlay, the hidden iframe should
                be blocked. The ROVA app should not load in the transparent iframe.
            </div>
        </div>

        <!-- Test 3: Different Domain Simulation -->
        <div class="test-container">
            <div class="test-title">Test 3: Cross-Origin Iframe Test</div>
            <div class="test-description">
                This test simulates loading your app from a different domain (like an attacker's site).
            </div>
            <div class="iframe-container">
                <iframe src="https://client-ui.cibng-dev-rova.com/" title="ROVA App Cross-Origin Test"></iframe>
            </div>
            <div class="expected-result">
                <strong>✅ Expected Result:</strong> Should also be blocked. Our protection works regardless of how the
                iframe is loaded.
            </div>
        </div>

        <!-- Test 4: Nested Iframe Test -->
        <div class="test-container">
            <div class="test-title">Test 4: Nested Iframe Test</div>
            <div class="test-description">This tests protection against nested iframe attacks.</div>
            <div class="iframe-container">
                <iframe
                    srcdoc="<iframe src='https://client-ui.cibng-dev-rova.com/' width='100%' height='300'></iframe>"
                    style="height: 350px"
                    title="Nested Iframe Test"
                ></iframe>
            </div>
            <div class="expected-result">
                <strong>✅ Expected Result:</strong> The nested iframe should also be blocked by our protection.
            </div>
        </div>

        <!-- JavaScript Test -->
        <div class="test-container">
            <div class="test-title">Test 5: JavaScript Frame Detection</div>
            <div class="test-description">This tests our client-side FrameBuster component.</div>
            <div id="js-test-result" style="padding: 10px; background: #f5f5f5; border: 1px solid #ddd; margin: 10px 0">
                Testing JavaScript frame detection...
            </div>
            <div class="expected-result">
                <strong>✅ Expected Result:</strong> Should show "Not in frame - Protection active" or similar message.
            </div>
        </div>

        <script>
            // Test JavaScript frame detection
            function testFrameDetection() {
                const resultDiv = document.getElementById("js-test-result");

                try {
                    if (window.self !== window.top) {
                        resultDiv.innerHTML =
                            "🚨 <strong>WARNING:</strong> This page is loaded in a frame! Our FrameBuster should have prevented this.";
                        resultDiv.style.background = "#ffebee";
                        resultDiv.style.color = "#c62828";
                    } else {
                        resultDiv.innerHTML = "✅ <strong>GOOD:</strong> Not in frame - Protection is active.";
                        resultDiv.style.background = "#e8f5e8";
                        resultDiv.style.color = "#2e7d32";
                    }
                } catch (e) {
                    resultDiv.innerHTML = "🔒 <strong>PROTECTED:</strong> Cross-origin frame detected - this is good!";
                    resultDiv.style.background = "#e3f2fd";
                    resultDiv.style.color = "#1565c0";
                }
            }

            // Run the test when page loads
            window.addEventListener("load", testFrameDetection);
        </script>

        <div class="instructions">
            <h3>🔍 What to Look For:</h3>
            <ul>
                <li><strong>Blank iframes:</strong> All iframe areas should be empty or show errors</li>
                <li><strong>Console errors:</strong> Look for "X-Frame-Options" or "frame-ancestors" errors</li>
                <li><strong>Network headers:</strong> Check that security headers are present in responses</li>
                <li><strong>No app content:</strong> Your ROVA application should never appear inside any iframe</li>
            </ul>

            <h3>🚨 If Protection Failed:</h3>
            <p>
                If you can see your ROVA application inside any of the iframes above, the protection is not working
                correctly. Check:
            </p>
            <ul>
                <li>Make sure you've restarted your development server after making changes</li>
                <li>Clear your browser cache and cookies</li>
                <li>Check browser console for any JavaScript errors</li>
                <li>Verify the security headers are being sent in the Network tab</li>
            </ul>
        </div>
    </body>
</html>
