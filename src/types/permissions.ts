/**
 * Permission Type Definitions
 *
 * This file contains type definitions for the permissions system
 * to avoid circular dependencies between modules.
 */

// Backward compatibility types
export interface DynamicPermissions {
    // Overview Permissions
    OVERVIEW_PERMISSIONS: Record<string, string>;
    // Settings Permissions
    SETTINGS_PERMISSIONS: Record<string, string>;
    // Outgoing Payments Permissions
    OUTGOING_PAYMENTS_PERMISSIONS: Record<string, string>;
    // Send Money Permissions
    SEND_MONEY_PERMISSIONS: Record<string, string>;
    // Accounts Permissions
    ACCOUNTS_PERMISSIONS: Record<string, string>;
    // Transactions Permissions
    TRANSACTIONS_PERMISSIONS: Record<string, string>;
    // Bill Payments Permissions
    BILL_PAYMENTS_PERMISSIONS: Record<string, string>;
    // Beneficiaries Permissions
    BENEFICIARIES_PERMISSIONS: Record<string, string>;
    // Team Management Permissions
    TEAM_MANAGEMENT_PERMISSIONS: Record<string, string>;
    // Support Permissions
    SUPPORT_PERMISSIONS: Record<string, string>;
    // Onboarding Permissions
    ONBOARDING_PERMISSIONS: Record<string, string>;
    // All permissions combined
    ALL_PERMISSIONS: Record<string, string>;
}
