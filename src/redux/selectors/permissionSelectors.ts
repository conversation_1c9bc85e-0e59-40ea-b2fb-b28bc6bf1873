/**
 * Redux Permission Selectors
 *
 * Memoized selectors for efficient permission checking and data access.
 * These selectors provide the core permission checking logic used throughout the app.
 */

import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "../index";
import type { Permission } from "../types/permissions";
import { transformToBackwardCompatibleFormat } from "@/utils/permissionTransforms";
import type { DynamicPermissions } from "@/types/permissions";

// Base selectors
export const selectPermissionsState = (state: RootState) => state.permissions;

export const selectSystemPermissions = (state: RootState) => state.permissions.systemPermissions;
export const selectUserPermissions = (state: RootState) => state.permissions.userPermissions;

export const selectSystemPermissionsLoading = (state: RootState) => state.permissions.systemPermissionsLoading;
export const selectUserPermissionsLoading = (state: RootState) => state.permissions.userPermissionsLoading;

export const selectSystemPermissionsError = (state: RootState) => state.permissions.systemPermissionsError;
export const selectUserPermissionsError = (state: RootState) => state.permissions.userPermissionsError;

export const selectIsPermissionsReady = (state: RootState) => state.permissions.isReady;
export const selectIsPermissionsInitialized = (state: RootState) => state.permissions.isInitialized;
export const selectIsUsingCache = (state: RootState) => state.permissions.isUsingCache;
export const selectSessionId = (state: RootState) => state.permissions.sessionId;

// Computed selectors
export const selectIsLoading = createSelector(
    [selectSystemPermissionsLoading, selectUserPermissionsLoading],
    (systemLoading, userLoading) => systemLoading || userLoading
);

export const selectHasErrors = createSelector(
    [selectSystemPermissionsError, selectUserPermissionsError],
    (systemError, userError) => Boolean(systemError || userError)
);

export const selectPermissionErrors = createSelector(
    [selectSystemPermissionsError, selectUserPermissionsError],
    (systemError, userError) => ({
        systemError,
        userError,
        hasErrors: Boolean(systemError || userError),
    })
);

// Permission checking selectors
export const selectHasPermission = createSelector(
    [selectUserPermissions],
    (userPermissions) => (permission: string): boolean => userPermissions.includes(permission)
);

export const selectHasAnyPermission = createSelector(
    [selectUserPermissions],
    (userPermissions) => (permissions: string[]): boolean => {
        if (permissions.length === 0) return true;
        return permissions.some(permission => userPermissions.includes(permission));
    }
);

export const selectHasAllPermissions = createSelector(
    [selectUserPermissions],
    (userPermissions) => (permissions: string[]): boolean => {
        if (permissions.length === 0) return true;
        return permissions.every(permission => userPermissions.includes(permission));
    }
);

// Permission existence checker
export const selectPermissionExists = createSelector(
    [selectSystemPermissions],
    (systemPermissions) => (permissionName: string): boolean => systemPermissions.some(p => p.name === permissionName)
);

// Get permissions by module
export const selectPermissionsByModule = createSelector(
    [selectSystemPermissions],
    (systemPermissions) => (moduleName: string): Permission[] => systemPermissions.filter(p => p.appModule === moduleName)
);

// Get all modules
export const selectAllModules = createSelector(
    [selectSystemPermissions],
    (systemPermissions): string[] => {
        const modules = new Set(systemPermissions.map(p => p.appModule));
        return Array.from(modules).sort();
    }
);

// Backward compatibility - transform to old format
export const selectDynamicPermissions = createSelector(
    [selectSystemPermissions],
    (systemPermissions): DynamicPermissions => {
        // Group permissions by module
        const moduleGroups = systemPermissions.reduce((acc, permission) => {
            const moduleName = permission.appModule || "General";
            if (!acc[moduleName]) {
                acc[moduleName] = [];
            }
            acc[moduleName].push(permission);
            return acc;
        }, {} as Record<string, Permission[]>);

        // Convert to the expected format
        const allPermissions: Record<string, Record<string, string>> = {};
        
        Object.entries(moduleGroups).forEach(([moduleName, permissions]) => {
            const normalizedModuleName = moduleName
                .toUpperCase()
                .replace(/[^A-Z0-9]/g, "_")
                .replace(/_+/g, "_")
                .replace(/(^_|_$)/g, "") + "_PERMISSIONS";
            
            allPermissions[normalizedModuleName] = {};
            
            permissions.forEach((permission) => {
                const constantKey = permission.name
                    .toUpperCase()
                    .replace(/[^A-Z0-9]/g, "_")
                    .replace(/_+/g, "_")
                    .replace(/(^_|_$)/g, "");
                allPermissions[normalizedModuleName][constantKey] = permission.name;
            });
        });

        return transformToBackwardCompatibleFormat(allPermissions);
    }
);

// Route access checker
export const selectCanAccessRoute = createSelector(
    [selectUserPermissions],
    (userPermissions) => (requiredPermissions: string[], requireAll: boolean = false): boolean => {
        if (requiredPermissions.length === 0) return true;
        
        if (requireAll) {
            return requiredPermissions.every(permission => userPermissions.includes(permission));
        } else {
            return requiredPermissions.some(permission => userPermissions.includes(permission));
        }
    }
);

// Action permission checker
export const selectCanPerformAction = createSelector(
    [selectUserPermissions],
    (userPermissions) => (requiredPermissions: string | string[], requireAll: boolean = false): boolean => {
        if (typeof requiredPermissions === "string") {
            return userPermissions.includes(requiredPermissions);
        }
        
        if (requiredPermissions.length === 0) return true;
        
        if (requireAll) {
            return requiredPermissions.every(permission => userPermissions.includes(permission));
        } else {
            return requiredPermissions.some(permission => userPermissions.includes(permission));
        }
    }
);

// System status selector
export const selectPermissionSystemStatus = createSelector(
    [
        selectIsPermissionsReady,
        selectIsPermissionsInitialized,
        selectIsLoading,
        selectHasErrors,
        selectIsUsingCache,
        selectPermissionErrors,
    ],
    (isReady, isInitialized, isLoading, hasErrors, isUsingCache, errors) => ({
        isReady,
        isInitialized,
        isLoading,
        hasErrors,
        isUsingCache,
        errors,
        canProceed: isReady && !hasErrors,
    })
);

// User permission summary
export const selectUserPermissionSummary = createSelector(
    [selectUserPermissions, selectSystemPermissions],
    (userPermissions, systemPermissions) => ({
        totalUserPermissions: userPermissions.length,
        totalSystemPermissions: systemPermissions.length,
        permissionCoverage: systemPermissions.length > 0 
            ? (userPermissions.length / systemPermissions.length) * 100 
            : 0,
        userPermissions,
    })
);
