/**
 * @file accounts.ts
 *
 * Purpose: Provides Redux state management and utility functions for user bank accounts.
 *
 * Functionality: This file manages the accounts data lifecycle in the Redux store. It defines
 * the account data structures, implements account fetching and detail retrieval thunks, and
 * provides utility functions for formatting account information for display. The file handles
 * loading states, error conditions, and caches account data to optimize API requests. It serves
 * as the central source of truth for account data throughout the application.
 *
 * Dependencies:
 * - ReduxToolkit: For state management and async thunks
 * - acctsAxios: Configured axios instance for account API calls
 * - Account and AccountDetails interfaces: For type definitions
 *
 * Usage: Import the reducer, actions, and utility functions in components that need to
 * display or manage account data. Use the formatAccountName and formatAccountNumber utilities
 * to ensure consistent account display across the application.
 */

import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";

interface ApiErrorResponse {
    response?: {
        data?: {
            message?: string;
        };
    };
    message: string;
}

export interface Account {
    id: number;
    accountNumber: string;
    accountName: string;
    preferredName: string;
    currencyCode: string;
    primaryFlag: boolean;
    hiddenFlag: boolean;
    status: string;
    schemeType: string;
    schemeCode: string;
}

// Account details interface based on the account details API response
export interface AccountDetails {
    accountName: string;
    accountNumber: string;
    customerId: string;
    balance: number;
    currencyCode: string;
    schemeCode: string;
    status: string;
}

interface AccountsState {
    accounts: Account[];
    selectedAccount: string;
    loading: boolean;
    error: string | null;
}

/**
 * Formats the account number for display in the UI by masking all but the last 4 digits
 * for security purposes.
 *
 * @param accountNumber - The full account number to be masked
 * @returns A formatted string with masked account number (e.g., "****1234")
 */
export const formatAccountNumber = (accountNumber: string): string => {
    const lastFourDigits = accountNumber.slice(-4);
    return `****${lastFourDigits}`;
};

/**
 * Formats the account name for display in the UI.
 * Uses the actual account holder name from the API response when available,
 * falling back to the account type (e.g., "Savings account") when the name is not provided.
 *
 * @param account - The account object containing accountName and schemeType
 * @returns A formatted string representing the account name
 */
export const formatAccountName = (account: Account): string => {
    // Use accountName if available, otherwise fallback to scheme type format
    if (account.accountName) {
        return account.accountName;
    }

    // Fallback to previous implementation for backward compatibility
    const schemeType = account.schemeType.charAt(0).toUpperCase() + account.schemeType.slice(1).toLowerCase();
    return `${schemeType} account`;
};

export const getAccountBalance = (balance?: number): string =>
    // Return formatted balance; empty or undefined balances are surfaced to UI
    balance ? balance.toString() : "";

const initialState: AccountsState = {
    accounts: [],
    selectedAccount: "",
    loading: false,
    error: null,
};

export const fetchAccounts = createAsyncThunk("accounts/fetchAccounts", async (_, { rejectWithValue }) => {
    try {
        const response = await acctsAxios.get("/api/v1/accounts");

        if (!response.data || !Array.isArray(response.data)) {
            return rejectWithValue("Invalid response format from accounts API");
        }

        // API returns array directly, not wrapped in content
        return response.data;
    } catch (error: unknown) {
        const typedError = error as ApiErrorResponse;
        sendCatchFeedback(error);
        return rejectWithValue(typedError.response?.data?.message ?? typedError.message ?? "Failed to fetch accounts");
    }
});

export const accountsSlice = createSlice({
    name: "accounts",
    initialState,
    reducers: {
        setSelectedAccount: (state, action: PayloadAction<string>) => {
            state.selectedAccount = action.payload;
        },
        resetAccounts: (state) => {
            state.selectedAccount = initialState.selectedAccount;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAccounts.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAccounts.fulfilled, (state, action: PayloadAction<Account[]>) => {
                state.accounts = action.payload;
                state.loading = false;
                if (action.payload.length > 0) {
                    const firstAccount = action.payload[0];
                    const selectedAccount = `${formatAccountName(firstAccount)} ${formatAccountNumber(firstAccount.accountNumber)}`;
                    state.selectedAccount = selectedAccount;
                }
            })
            .addCase(fetchAccounts.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            });
    },
});

export const { setSelectedAccount, resetAccounts } = accountsSlice.actions;
export default accountsSlice.reducer;
