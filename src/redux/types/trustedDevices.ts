export type TrustedDevices = {
    getTrustedDevices: {
        data: TrustedDevicesData[] | null;
        loading: boolean;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: any;
        message: string | null;
        success: boolean;
    };
    removeDevice: {
        data: removeDeviceResponse | null;
        loading: boolean;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: any;
        message: string | null;
        success: boolean;
    };
    removeAllDevices: {
        data: removeDeviceResponse | null;
        loading: boolean;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: any;
        message: string | null;
        success: boolean;
    };
};

export type removeDeviceResponse = {
    message: string;
    timestamp: string;
};

export type TrustedDevicesData = {
    id: number;
    name: string;
    os: string;
    browser: string;
    city: string;
    country: string;
    logo: string | null;
    ip: string;
    trustedUntil: string | null;
    userId: number;
};

// {
//     "id": 35,
//     "name": "<PERSON> <PERSON>",
//     "os": "Unknown OS",
//     "browser": "Unknown Browser",
//     "ip": "**************",
//     "trustedUntil": "2025-04-24",
//     "userId": 1
// }
