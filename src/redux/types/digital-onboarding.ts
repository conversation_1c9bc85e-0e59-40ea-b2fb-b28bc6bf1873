export interface FileMetadata {
    name: string;
    size: number;
    type?: string;
}
export type ProofOfAddressField = File | FileMetadata | null;
export type DocumentType = {
    documentType: string;
    file: File | FileMetadata | undefined;
};
export type BusinessInformationAccountForm = {
    proofOfAddress: File | ProofOfAddressField | null;
    legalBusinessName: string;
    isTradingNameSameAsBusinessName: boolean;
    tradingName: string;
    countryOfRegistration: string;
    businessIndustry: string;
    businessCategory: string;
    businessDescription: string;
    yearOfRegistration: string | Date;
    registrationType: string;
    businessEmail: string;
    businessWebsite: string;
    businessAddress: string;
    corporateId?: string;
    city: string;
    state: string;
    isAddressDifferentFromOperational: boolean;
    businessRegistrationNumber: string;
    businessEmailAddress: string;
    businessRegistrationType: string;
    isBusinessNameTradingName: boolean;
    taxIdentificationNumber: string;
    operationalAddress: string;
};

export type BusinessValidationForm = {
    taxIdentificationNumber: string;
    businessRegistrationNumber: string;
};

export type BusinessDocumentType =
    | "CERTIFICATE_OF_INCORPORATION"
    | "MEMORANDUM_AND_ARTICLES_OF_ASSOCIATION"
    | "CAC_FORM_11"
    | "TAX_IDENTIFICATION_NUMBER_CERTIFICATE"
    | "AUDITED_FINANCIAL_STATEMENT"
    | "BOARD_RESOLUTION_DOCUMENT"
    | "UTILITY_BILL"
    | "SCUML_CERTIFICATE"
    | "MANUAL_ACCOUNT_OPENING_FORM";
