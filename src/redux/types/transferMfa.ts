export type TeamMembersData = {
    id: number;
    corporateId: number;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    roleId: number;
    roleName: string | null;
    preferredMfaMethod: string | null;
    mfaStatus: boolean;
    lastLogin: string;
};

export type TransferMfaState = {
    teamMember: TeamMembersData | null;
    getTeamMemberDetails: {
        loading: boolean;
        error: string | null;
        success: boolean;
    };
};
