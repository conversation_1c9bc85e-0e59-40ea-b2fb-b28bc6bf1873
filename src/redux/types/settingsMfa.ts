export type MFAMethod = "SECURITY_QUESTION" | "AUTHENTICATOR" | "SMS";

// MFA validation request types
export interface ValidateSecurityQuestionRequest {
    userEmail: string;
    question: string;
    answer: string;
}

export interface ValidateAuthenticatorRequest {
    username: string;
    otp: string;
}

export interface ValidateSmsOtpRequest {
    receiver: string;
    otp: string;
}

// MFA validation response types (with tokens)
export interface MfaValidationResponse {
    token: string;
    message?: string;
    status?: string;
}

// Settings MFA state types
export interface SettingsMfaState {
    validateSecurityQuestion: {
        loading: boolean;
        success: boolean;
        error: string | null;
        token: string | null;
    };
    validateAuthenticator: {
        loading: boolean;
        success: boolean;
        error: string | null;
        token: string | null;
    };
    validateSmsOtp: {
        loading: boolean;
        success: boolean;
        error: string | null;
        token: string | null;
    };
    // Current MFA verification state
    currentVerification: {
        mfaType: MFAMethod | null;
        isVerifying: boolean;
        token: string | null;
    };
}

// Action types for settings operations with tokens
export interface SettingsActionWithToken {
    token: string;
}

export interface ChangePasswordWithTokenRequest extends SettingsActionWithToken {
    email: string;
    newPassword: string;
    oldPassword: string;
}

export interface ChangeTransactionPinWithTokenRequest extends SettingsActionWithToken {
    oldPin: string;
    newPin: string;
}

export interface UpdateTransactionLimitWithTokenRequest extends SettingsActionWithToken {
    type: string;
    dailyLimit: number;
    perTransactionLimit: number;
    maximumLimit: number;
}
