export type ITransactionType = "Debit" | "Credit";

interface TransactionUser {
    firstName: string;
    lastName: string;
    role: string;
    approvalDate: string;
}

export interface ITransactionResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: ITransaction[];
}

export interface ITransaction {
    createdDate: string;
    counterpartyType: string;
    beneficiaryAccountNumber: string;
    beneficiaryBank: string;
    narration: string;
    sourceAccount: string;
    amount: number;
    status: string;
    transactionId: string;
    transactionType: ITransactionType;
    reference: string;
    users?: TransactionUser[];
}

export interface IAccount {
    id: number;
    customerId: string;
    accountNumber: string;
    accountName: string;
    preferredName: string;
    currencyCode: string;
    primaryFlag: boolean;
    hiddenFlag: boolean;
    status: "ACTIVE" | "INACTIVE";
    schemeType: "SAVINGS" | "CHECKING" | "CREDIT";
    schemeCode: string;
}

export interface IAccountResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: IAccount[];
}

interface IList {
    isAvailable: boolean;
    previous: boolean;
    next: boolean;
    count: number;
    loading: boolean;
    error: string | null;
    size: number;
    pages: number;
    currentPage: number;
}

interface IGet {
    loading: boolean;
    message: string | null;
    error: string | null;
}

interface IAccountGet {
    accounts: IAccount[];
    account: IAccount | null;
    loading: boolean;
    message: string | null;
    error: string | null;
}

export interface IInitialState {
    transactions: ITransaction[];
    transaction: ITransaction | null;
    list: IList;
    get: IGet;
    accountGet: IAccountGet;
}
