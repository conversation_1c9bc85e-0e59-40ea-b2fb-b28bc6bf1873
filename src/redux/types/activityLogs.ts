export type ActivityLogsState = {
    getActivityLogs: {
        data: ActivityLogsResponse | null;
        loading: boolean;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: any;
        message: string | null;
        success: boolean;
    };
    exportActivityLogs: {
        data: ExportResponse | null;
        loading: boolean;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: any;
        message: string | null;
        success: boolean;
    };
};

export type ActivityLogsData = {
    id: string;
    performedBy: PerformedByString | PerformedByObject;
    action: string;
    actionType: string;
    timestamp: string;
    location: string;
    ipAddress: string;
    details?: string;
    role: string; // Added role property to ActivityLogsData
};

export type ActivityLogsResponse = {
    content: ActivityLogsData[];
    totalElements: number;
    totalPages: number;
    currentPage?: number;
    size: number;
    first: boolean;
    last: boolean;
};

export type ActivityLogsFilters = {
    startDate?: string;
    endDate?: string;
    roleId?: number;
    actionTypes?: string;
    searchKeyword?: string;
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: "ASC" | "DESC";
};

export type DateRangeValues = "THIS_MONTH" | "LAST_30_DAYS" | "LAST_3_MONTHS" | "LAST_6_MONTHS" | "CUSTOM";

export type ExportActivityLogsPayload = {
    startDate?: string;
    endDate?: string;
    roleId?: number;
    actionTypes?: string;
    searchKeyword?: string;
    format: "csv" | "pdf";
    selectedRows?: ActivityLogsData[];
    dateRange: DateRangeValues;
};

export type ExportResponse = {
    success: boolean;
    message: string;
    downloadUrl?: string;
};

export type PerformedByString = string;
export type PerformedByObject = {
    id: number;
    name: string;
    role: string;
};
