/**
 * Redux Permission Type Definitions
 *
 * This file contains type definitions for the Redux-based permissions system.
 */

// Core permission data structure from API
export interface Permission {
    id: number;
    name: string;
    appModule: string;
    endpoint: string;
    method: string;
    createdDate: string | null;
    createdBy: string | null;
    lastModifiedDate: string | null;
    lastModifiedBy: string | null;
}

// Redux permission state structure
export interface PermissionState {
    // System permissions (all available permissions)
    systemPermissions: Permission[];
    systemPermissionsLoading: boolean;
    systemPermissionsError: string | null;
    systemPermissionsLastFetched: number | null;
    
    // User permissions (permissions assigned to current user)
    userPermissions: string[];
    userPermissionsLoading: boolean;
    userPermissionsError: string | null;
    userPermissionsLastFetched: number | null;
    
    // Cache and session management
    isUsingCache: boolean;
    sessionId: string | null;
    
    // System readiness
    isInitialized: boolean;
    isReady: boolean;
}

// Cache structure for localStorage
export interface PermissionCache {
    systemPermissions: Permission[];
    userPermissions: string[];
    sessionId: string;
    timestamp: number;
    userId: string;
}

// Action payload types
export interface FetchSystemPermissionsPayload {
    permissions: Permission[];
    timestamp: number;
}

export interface FetchUserPermissionsPayload {
    permissions: string[];
    timestamp: number;
    sessionId: string;
    userId: string;
}

export interface InitializePermissionsPayload {
    systemPermissions: Permission[];
    userPermissions: string[];
    sessionId: string;
    userId: string;
    fromCache: boolean;
}

// Error response type
export interface PermissionErrorResponse {
    message: string;
    details?: string;
    timestamp?: string;
}
