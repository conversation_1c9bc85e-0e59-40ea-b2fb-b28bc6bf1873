import { ITransactionType } from "./transactions";

export type GetAccountStatementBody = {
    accountNumber: string;
    startDate: string;
    endDate: string;
};

export type DashboardTransactionType = {
    createdDate: string;
    counterpartyType: string;
    sourceAccount: string;
    amount: number;
    transactionId: string;
    transactionType: ITransactionType;
    beneficiaryAccountNumber: string;
    beneficiaryBank: string;
    narration: string;
    status: string;
    reference: string;
};
export interface IDashboardInitialState {
    getAccountStatement: {
        error: string | null;
        loading: boolean;
        success: boolean;
    };
    getTransactions: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: DashboardTransactionType[];
    };
    getRecentTransactions: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: DashboardTransactionType[];
    };
    getAccountStatistics: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: DashboardStatisticsType[];
    };
    getAccountDetails: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: AccountDetailsType | undefined;
    };
}

export type GetTransactionsBody = {
    startDate: string;
    endDate: string;
    accountNumber?: string;
};

export type DashboardStatisticsType = {
    accountNumber: string;
    accountName: string;
    balance: number;
    totalInflow: number;
    totalOutflow: number;
    noOfTransactions: number;
    currencyCode: string;
};

export type AccountDetailsType = {
    accountName: string;
    accountNumber: string;
    customerId: string;
    balance: number;
    currencyCode: string;
    schemeCode: string;
    status: string;
};
