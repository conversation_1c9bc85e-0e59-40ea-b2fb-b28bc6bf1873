import { Middleware } from "@reduxjs/toolkit";
import { resetBillCategories } from "../slices/billPaymentSlice";

/**
 * Middleware that listens for user logout actions and clears bill payment categories
 * This ensures categories are refetched on next login.
 */
const billPaymentClearMiddleware: Middleware = (store) => (next) => (action) => {
    const result = next(action);

    // Listen for logout action from user slice
    if (action.type === "user/signOut") {
        // Clear bill payment categories when user logs out
        store.dispatch(resetBillCategories());
    }

    return result;
};

export default billPaymentClearMiddleware;
