import { Middleware } from "@reduxjs/toolkit";
import { clearPermissions } from "../actions/permissionsActions";
import type { AppDispatch } from "../index";

/**
 * Middleware that listens for user logout actions and clears permissions
 * This ensures permissions are cleared on logout and refetched on next login.
 */
const permissionsClearMiddleware: Middleware = (store) => (next) => (action) => {
    const result = next(action);

    // Listen for logout action from user slice
    if (action.type === "user/signOut") {
        // Clear permissions when user logs out
        // Type assertion to AppDispatch to handle async thunk dispatch
        (store.dispatch as AppDispatch)(clearPermissions());
    }

    return result;
};

export default permissionsClearMiddleware;

