/**
 * Transfer Slice - Redux State Management for Outgoing Payments
 *
 * Purpose: This file manages the Redux state for all outgoing payment transactions in the CIB application,
 * including instant, scheduled, and recurring transfers. It handles data fetching, processing, and state
 * management for the outgoing payments module.
 *
 * Functionality:
 * - Manages three types of outgoing payments: instant, scheduled, and recurring transfers
 * - Processes nested API responses and transforms them into normalized transfer objects
 * - Handles both single and bulk transfer scenarios with proper data aggregation
 * - Implements comprehensive error handling and null safety checks to prevent runtime errors
 * - Manages loading states, pagination data, and error states for each transfer type
 * - Supports payment approval workflows and transfer status updates
 * - Provides download receipt functionality for completed transfers
 * - Includes robust data validation to handle malformed API responses gracefully
 *
 * Dependencies:
 * - Redux Toolkit for state management and async thunks
 * - Transfer actions from transferActions.ts for API calls
 * - ITransfer and OutgoingState types from outgoing module types
 * - OutgoingResponse interface for API response structure
 *
 * Usage: This slice is consumed by the outgoing payments components to display transfer data,
 * handle user interactions, and manage the overall state of the outgoing payments module.
 * The processOutgoingResponse helper function includes extensive null checks and data validation
 * to prevent "Cannot convert undefined or null to object" errors when processing API responses.
 *
 * Error Prevention: Added comprehensive null/undefined checks throughout the data processing
 * pipeline to handle edge cases where API responses may be malformed or missing expected data.
 * This ensures the application remains stable even when receiving unexpected data structures.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ITransfer, OutgoingState } from "@/components/page-components/dashboard/outgoing/types";
import {
    fetchOutgoing,
    fetchScheduledOutgoing,
    fetchRecurringOutgoing,
    OutgoingResponse,
    mapApiResponseToTransfer,
    fetchPaymentApprovals,
    PaymentApprovalResponse,
    updateTransferStatus,
    downloadReceipt,
} from "../actions/transferActions";

// Initial state
const initialState: OutgoingState = {
    // Regular outgoing payments
    transfers: [],
    transfersLoading: false,
    transfersError: null,
    transfersTotalElements: 0,
    transfersTotalPages: 0,
    transfersCurrentPage: 0,
    transfersHasNext: false,
    transfersHasPrevious: false,

    // Scheduled outgoing payments
    scheduledTransfers: [],
    scheduledTransfersLoading: false,
    scheduledTransfersError: null,
    scheduledTransfersTotalElements: 0,
    scheduledTransfersTotalPages: 0,
    scheduledTransfersCurrentPage: 0,
    scheduledTransfersHasNext: false,
    scheduledTransfersHasPrevious: false,

    // Recurring outgoing payments
    recurringTransfers: [],
    recurringTransfersLoading: false,
    recurringTransfersError: null,
    recurringTransfersTotalElements: 0,
    recurringTransfersTotalPages: 0,
    recurringTransfersCurrentPage: 0,
    recurringTransfersHasNext: false,
    recurringTransfersHasPrevious: false,

    // Payment approvals
    paymentApprovals: [],
    paymentApprovalsLoading: false,
    paymentApprovalsError: null,
    paymentApprovalsTotalElements: 0,
    paymentApprovalsTotalPages: 0,

    // Update transfer status
    updateTransferStatusLoading: false,
    updateTransferStatusError: null,

    // Download receipt
    downloadReceiptLoading: false,
    downloadReceiptError: null,
};

// Helper function to process the nested API response
const processOutgoingResponse = (response: OutgoingResponse): ITransfer[] => {
    const result: ITransfer[] = [];

    // Check if response and content exist before processing
    if (!response?.content) {
        return result;
    }

    // Iterate through each counterparty in the content object
    Object.entries(response.content).forEach(([counterpartyName, transfers]) => {
        // Ensure transfers is an array and has valid data
        if (!Array.isArray(transfers) || transfers.length === 0) {
            return;
        }

        if (transfers.length > 1) {
            // This is a bulk transfer (multiple recipients with the same counterparty name)
            // Create one transfer object representing the bulk transfer

            // Use the first transfer as a base, but set totalTransfers to the array length
            const bulkTransfer = mapApiResponseToTransfer(transfers[0], counterpartyName);
            bulkTransfer.totalTransfers = transfers.length;

            // For bulk transfers, the total amount should be the sum of all individual transfers
            bulkTransfer.amount = transfers.reduce((sum, item) => sum + item.totalAmount, 0);

            // Store the individual transfers data which will be used in the Recipients tab
            // Map each recipient individually to ensure their status values are preserved
            bulkTransfer.recipients = transfers.map((t) => {
                const recipient = mapApiResponseToTransfer(t, t.destinationAccountName || counterpartyName);

                // Explicitly preserve original status field from the API to ensure it's available in the Recipients tab
                recipient.transferStatus = t.transferStatus;

                return recipient;
            });

            result.push(bulkTransfer);
        } else if (transfers.length === 1) {
            // Single transfer
            const singleTransfer = mapApiResponseToTransfer(transfers[0], counterpartyName);
            singleTransfer.totalTransfers = 1; // Explicitly set to 1 for single transfers

            // Preserve original status field here too for consistency
            singleTransfer.transferStatus = transfers[0].transferStatus;

            result.push(singleTransfer);
        }
        // Ignore empty arrays if they exist
    });

    return result;
};

// Create the slice
export const outgoingSlice = createSlice({
    name: "outgoing",
    initialState,
    reducers: {
        // Add any additional reducers if needed
        clearOutgoingErrors: (state) => {
            state.transfersError = null;
            state.scheduledTransfersError = null;
            state.recurringTransfersError = null;
            state.paymentApprovalsError = null;
            state.downloadReceiptError = null;
        },
    },
    extraReducers: (builder) => {
        // Regular outgoing payments
        builder
            .addCase(fetchOutgoing.pending, (state) => {
                state.transfersLoading = true;
                state.transfersError = null;
            })
            .addCase(fetchOutgoing.fulfilled, (state, action) => {
                state.transfersLoading = false;
                // Process the nested API response with tab type "instant"
                state.transfers = processOutgoingResponse(action.payload);

                state.transfersTotalElements = action.payload.totalElements;
                state.transfersTotalPages = action.payload.totalPages;
                state.transfersCurrentPage = action.payload.number || 0;
                state.transfersHasNext = action.payload.hasNext || false;
                state.transfersHasPrevious = action.payload.hasPrevious || false;
            })
            .addCase(fetchOutgoing.rejected, (state, action) => {
                state.transfersLoading = false;
                state.transfersError = action.error.message || "Failed to fetch outgoing payments";
            })

            // Scheduled outgoing payments
            .addCase(fetchScheduledOutgoing.pending, (state) => {
                state.scheduledTransfersLoading = true;
                state.scheduledTransfersError = null;
            })
            .addCase(fetchScheduledOutgoing.fulfilled, (state, action) => {
                state.scheduledTransfersLoading = false;
                // Process the nested API response with tab type "scheduled"
                state.scheduledTransfers = processOutgoingResponse(action.payload);

                state.scheduledTransfersTotalElements = action.payload.totalElements;
                state.scheduledTransfersTotalPages = action.payload.totalPages;
                state.scheduledTransfersCurrentPage = action.payload.number || 0;
                state.scheduledTransfersHasNext = action.payload.hasNext || false;
                state.scheduledTransfersHasPrevious = action.payload.hasPrevious || false;
            })
            .addCase(fetchScheduledOutgoing.rejected, (state, action) => {
                state.scheduledTransfersLoading = false;
                state.scheduledTransfersError = action.error.message || "Failed to fetch scheduled outgoing payments";
            })

            // Recurring outgoing payments
            .addCase(fetchRecurringOutgoing.pending, (state) => {
                state.recurringTransfersLoading = true;
                state.recurringTransfersError = null;
            })
            .addCase(fetchRecurringOutgoing.fulfilled, (state, action) => {
                state.recurringTransfersLoading = false;
                // Process the nested API response with tab type "recurring"
                state.recurringTransfers = processOutgoingResponse(action.payload);

                state.recurringTransfersTotalElements = action.payload.totalElements;
                state.recurringTransfersTotalPages = action.payload.totalPages;
                state.recurringTransfersCurrentPage = action.payload.number || 0;
                state.recurringTransfersHasNext = action.payload.hasNext || false;
                state.recurringTransfersHasPrevious = action.payload.hasPrevious || false;
            })
            .addCase(fetchRecurringOutgoing.rejected, (state, action) => {
                state.recurringTransfersLoading = false;
                state.recurringTransfersError = action.error.message || "Failed to fetch recurring outgoing payments";
            })

            // Payment approvals
            .addCase(fetchPaymentApprovals.pending, (state) => {
                state.paymentApprovalsLoading = true;
                state.paymentApprovalsError = null;
            })
            .addCase(fetchPaymentApprovals.fulfilled, (state, action: PayloadAction<PaymentApprovalResponse>) => {
                state.paymentApprovalsLoading = false;
                state.paymentApprovals = action.payload.content;
                state.paymentApprovalsTotalElements = action.payload.totalElements;
                state.paymentApprovalsTotalPages = action.payload.totalPages;
            })
            .addCase(fetchPaymentApprovals.rejected, (state, action) => {
                state.paymentApprovalsLoading = false;
                state.paymentApprovalsError = action.error.message || "Failed to fetch payment approvals";
            })

            // Update transfer status (cancel, pause, resume)
            .addCase(updateTransferStatus.pending, (state) => {
                state.updateTransferStatusLoading = true;
                state.updateTransferStatusError = null;
            })
            .addCase(updateTransferStatus.fulfilled, (state) => {
                state.updateTransferStatusLoading = false;
            })
            .addCase(updateTransferStatus.rejected, (state, action) => {
                state.updateTransferStatusLoading = false;
                state.updateTransferStatusError = action.error.message || "Failed to update transfer status";
            })

            // Download receipt
            .addCase(downloadReceipt.pending, (state) => {
                state.downloadReceiptLoading = true;
                state.downloadReceiptError = null;
            })
            .addCase(downloadReceipt.fulfilled, (state) => {
                state.downloadReceiptLoading = false;
            })
            .addCase(downloadReceipt.rejected, (state, action) => {
                state.downloadReceiptLoading = false;
                state.downloadReceiptError = action.error.message || "Failed to download receipt";
            });
    },
});

// Export actions and reducer
export const { clearOutgoingErrors } = outgoingSlice.actions;

// For backward compatibility
export const { clearOutgoingErrors: clearTransferErrors } = outgoingSlice.actions;

export default outgoingSlice.reducer;
