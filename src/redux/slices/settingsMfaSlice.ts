import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { SettingsMfaState, MFAMethod } from "../types/settingsMfa";
import {
    validateSettingsSecurityQuestion,
    validateSettingsAuthenticator,
    validateSettingsSmsOtp,
    clearSettingsMfaState,
} from "../actions/settingsMfaActions";

const initialState: SettingsMfaState = {
    validateSecurityQuestion: {
        loading: false,
        success: false,
        error: null,
        token: null,
    },
    validateAuthenticator: {
        loading: false,
        success: false,
        error: null,
        token: null,
    },
    validateSmsOtp: {
        loading: false,
        success: false,
        error: null,
        token: null,
    },
    currentVerification: {
        mfaType: null,
        isVerifying: false,
        token: null,
    },
};

export const settingsMfaSlice = createSlice({
    name: "settingsMfa",
    initialState,
    reducers: {
        // Set current MFA verification type
        setCurrentMfaType: (state, action: PayloadAction<MFAMethod | null>) => {
            state.currentVerification.mfaType = action.payload;
            state.currentVerification.isVerifying = action.payload !== null;
            // Clear token when starting new verification
            if (action.payload) {
                state.currentVerification.token = null;
            }
        },

        // Set the current verification token
        setCurrentToken: (state, action: PayloadAction<string | null>) => {
            state.currentVerification.token = action.payload;
        },

        // Clear current verification state
        clearCurrentVerification: (state) => {
            state.currentVerification = {
                mfaType: null,
                isVerifying: false,
                token: null,
            };
        },

        // Reset all MFA states
        resetAllStates: (state) => initialState,
    },
    extraReducers: (builder) => {
        builder
            // Security Question validation
            .addCase(validateSettingsSecurityQuestion.pending, (state) => {
                state.validateSecurityQuestion.loading = true;
                state.validateSecurityQuestion.error = null;
                state.validateSecurityQuestion.success = false;
            })
            .addCase(validateSettingsSecurityQuestion.fulfilled, (state, action) => {
                state.validateSecurityQuestion.loading = false;
                state.validateSecurityQuestion.success = true;
                state.validateSecurityQuestion.error = null;
                state.validateSecurityQuestion.token = action.payload.token;
                // Set current verification token
                state.currentVerification.token = action.payload.token;
            })
            .addCase(validateSettingsSecurityQuestion.rejected, (state, action) => {
                state.validateSecurityQuestion.loading = false;
                state.validateSecurityQuestion.success = false;
                state.validateSecurityQuestion.error = action.payload || "Failed to validate security question";
                state.validateSecurityQuestion.token = null;
            })

            // Authenticator validation
            .addCase(validateSettingsAuthenticator.pending, (state) => {
                state.validateAuthenticator.loading = true;
                state.validateAuthenticator.error = null;
                state.validateAuthenticator.success = false;
            })
            .addCase(validateSettingsAuthenticator.fulfilled, (state, action) => {
                state.validateAuthenticator.loading = false;
                state.validateAuthenticator.success = true;
                state.validateAuthenticator.error = null;
                state.validateAuthenticator.token = action.payload.token;
                // Set current verification token
                state.currentVerification.token = action.payload.token;
            })
            .addCase(validateSettingsAuthenticator.rejected, (state, action) => {
                state.validateAuthenticator.loading = false;
                state.validateAuthenticator.success = false;
                state.validateAuthenticator.error = action.payload || "Failed to validate authenticator";
                state.validateAuthenticator.token = null;
            })

            // SMS OTP validation
            .addCase(validateSettingsSmsOtp.pending, (state) => {
                state.validateSmsOtp.loading = true;
                state.validateSmsOtp.error = null;
                state.validateSmsOtp.success = false;
            })
            .addCase(validateSettingsSmsOtp.fulfilled, (state, action) => {
                state.validateSmsOtp.loading = false;
                state.validateSmsOtp.success = true;
                state.validateSmsOtp.error = null;
                state.validateSmsOtp.token = action.payload.token;
                // Set current verification token
                state.currentVerification.token = action.payload.token;
            })
            .addCase(validateSettingsSmsOtp.rejected, (state, action) => {
                state.validateSmsOtp.loading = false;
                state.validateSmsOtp.success = false;
                state.validateSmsOtp.error = action.payload || "Failed to validate SMS OTP";
                state.validateSmsOtp.token = null;
            })

            // Clear state action
            .addCase(clearSettingsMfaState.fulfilled, (state, action) => {
                const stateType = action.meta.arg;
                switch (stateType) {
                    case "securityQuestion":
                        state.validateSecurityQuestion = initialState.validateSecurityQuestion;
                        break;
                    case "authenticator":
                        state.validateAuthenticator = initialState.validateAuthenticator;
                        break;
                    case "smsOtp":
                        state.validateSmsOtp = initialState.validateSmsOtp;
                        break;
                    case "all":
                        return initialState;
                }
            });
    },
});

export const { setCurrentMfaType, setCurrentToken, clearCurrentVerification, resetAllStates } =
    settingsMfaSlice.actions;

export default settingsMfaSlice.reducer;
