import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getOnboarding } from "../actions/digitalOnboardingAction";
import { BusinessInformationAccountForm } from "../types/digital-onboarding";
import { getSliceError } from "../utils";
import { BusinessInfoResponse } from "./onboarding/businessOwnersSlice";

interface OnboardingState {
    businessInfoBody: BusinessInformationAccountForm | null;
    businessInfoResponse: {
        data: BusinessInfoResponse | null;
        loading: boolean;
        error: string | null;
        success: boolean;
    };
    businessRegistrationNumber: string;
    taxIdentificationNumber: string;
    businessName: string;
}

const initialState: OnboardingState = {
    businessInfoBody: null,
    businessInfoResponse: {
        data: null,
        loading: false,
        error: null,
        success: false,
    },
    businessRegistrationNumber: "",
    taxIdentificationNumber: "",
    businessName: "",
};

const onboardingSlice = createSlice({
    name: "onboarding",
    initialState,
    reducers: {
        setBusinessInfoBody: (state, action: PayloadAction<BusinessInformationAccountForm | null>) => {
            state.businessInfoBody = action.payload;
        },
        setBusinessInfoResponse: (state, action: PayloadAction<BusinessInfoResponse | null>) => {
            state.businessInfoResponse.data = action.payload;
        },
        setBusinessIdentifiers: (
            state,
            action: PayloadAction<{
                businessRegistrationNumber: string;
                taxIdentificationNumber: string;
                businessName: string;
            }>
        ) => {
            state.businessRegistrationNumber = action.payload.businessRegistrationNumber;
            state.taxIdentificationNumber = action.payload.taxIdentificationNumber;
            state.businessName = action.payload.businessName;
        },
    },
    extraReducers: (builder) => {
        builder
            // Manual Account opening form upload
            .addCase(getOnboarding.pending, (state) => {
                state.businessInfoResponse.loading = true;
            })
            .addCase(getOnboarding.fulfilled, (state, action: PayloadAction<BusinessInfoResponse>) => {
                state.businessInfoResponse.loading = false;
                state.businessInfoResponse.success = true;
                state.businessInfoResponse.error = null;
                state.businessInfoResponse.data = action.payload;
            })
            .addCase(getOnboarding.rejected, (state, action) => {
                state.businessInfoResponse.error = getSliceError(action);
                state.businessInfoResponse.success = false;
                state.businessInfoResponse.loading = false;
            });

        return builder;
    },
});

export const { setBusinessInfoResponse, setBusinessIdentifiers, setBusinessInfoBody } = onboardingSlice.actions;
export default onboardingSlice.reducer;
