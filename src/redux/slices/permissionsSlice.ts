/**
 * Redux Permissions Slice
 *
 * Centralized state management for both system permissions and user permissions
 * with session-based caching and one-time fetch strategy.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { PermissionState } from "../types/permissions";
import {
    fetchSystemPermissions,
    fetchUserPermissions,
    initializePermissions,
    clearPermissions,
} from "../actions/permissionsActions";

// Initial state
const initialState: PermissionState = {
    // System permissions
    systemPermissions: [],
    systemPermissionsLoading: false,
    systemPermissionsError: null,
    systemPermissionsLastFetched: null,
    
    // User permissions
    userPermissions: [],
    userPermissionsLoading: false,
    userPermissionsError: null,
    userPermissionsLastFetched: null,
    
    // Cache and session
    isUsingCache: false,
    sessionId: null,
    
    // System state
    isInitialized: false,
    isReady: false,
};

export const permissionsSlice = createSlice({
    name: "permissions",
    initialState,
    reducers: {
        // Reset permissions state
        resetPermissions: (state) => {
            Object.assign(state, initialState);
        },
        
        // Set system ready state
        setSystemReady: (state, action: PayloadAction<boolean>) => {
            state.isReady = action.payload;
        },
        
        // Clear errors
        clearErrors: (state) => {
            state.systemPermissionsError = null;
            state.userPermissionsError = null;
        },
        
        // Update session ID
        updateSessionId: (state, action: PayloadAction<string>) => {
            state.sessionId = action.payload;
        },
    },
    extraReducers: (builder) => {
        // Fetch System Permissions
        builder
            .addCase(fetchSystemPermissions.pending, (state) => {
                state.systemPermissionsLoading = true;
                state.systemPermissionsError = null;
            })
            .addCase(fetchSystemPermissions.fulfilled, (state, action) => {
                state.systemPermissionsLoading = false;
                state.systemPermissions = action.payload.permissions;
                state.systemPermissionsLastFetched = action.payload.timestamp;
                state.systemPermissionsError = null;
            })
            .addCase(fetchSystemPermissions.rejected, (state, action) => {
                state.systemPermissionsLoading = false;
                state.systemPermissionsError = action.payload?.message || "Failed to fetch system permissions";
            });

        // Fetch User Permissions
        builder
            .addCase(fetchUserPermissions.pending, (state) => {
                state.userPermissionsLoading = true;
                state.userPermissionsError = null;
            })
            .addCase(fetchUserPermissions.fulfilled, (state, action) => {
                state.userPermissionsLoading = false;
                state.userPermissions = action.payload.permissions;
                state.userPermissionsLastFetched = action.payload.timestamp;
                state.sessionId = action.payload.sessionId;
                state.userPermissionsError = null;
            })
            .addCase(fetchUserPermissions.rejected, (state, action) => {
                state.userPermissionsLoading = false;
                state.userPermissionsError = action.payload?.message || "Failed to fetch user permissions";
            });

        // Initialize Permissions
        builder
            .addCase(initializePermissions.pending, (state) => {
                state.systemPermissionsLoading = true;
                state.userPermissionsLoading = true;
                state.systemPermissionsError = null;
                state.userPermissionsError = null;
            })
            .addCase(initializePermissions.fulfilled, (state, action) => {
                state.systemPermissionsLoading = false;
                state.userPermissionsLoading = false;
                
                state.systemPermissions = action.payload.systemPermissions;
                state.userPermissions = action.payload.userPermissions;
                state.sessionId = action.payload.sessionId;
                state.isUsingCache = action.payload.fromCache;
                
                const timestamp = Date.now();
                state.systemPermissionsLastFetched = timestamp;
                state.userPermissionsLastFetched = timestamp;
                
                state.isInitialized = true;
                state.isReady = true;
                
                state.systemPermissionsError = null;
                state.userPermissionsError = null;
            })
            .addCase(initializePermissions.rejected, (state, action) => {
                state.systemPermissionsLoading = false;
                state.userPermissionsLoading = false;
                
                const errorMessage = action.payload?.message || "Failed to initialize permissions";
                state.systemPermissionsError = errorMessage;
                state.userPermissionsError = errorMessage;
                
                state.isInitialized = false;
                state.isReady = false;
            });

        // Clear Permissions
        builder
            .addCase(clearPermissions.fulfilled, (state) => {
                Object.assign(state, initialState);
            });
    },
});

export const {
    resetPermissions,
    setSystemReady,
    clearErrors,
    updateSessionId,
} = permissionsSlice.actions;

export default permissionsSlice.reducer;
