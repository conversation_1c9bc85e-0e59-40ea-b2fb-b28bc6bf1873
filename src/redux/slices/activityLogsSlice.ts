import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getActivityLogs, exportActivityLogs } from "../actions/activityLogsActions";
import { ActivityLogsState, ActivityLogsResponse, ExportResponse } from "../types/activityLogs";

const initialState: ActivityLogsState = {
    getActivityLogs: {
        data: null,
        loading: false,
        error: null,
        message: null,
        success: false,
    },
    exportActivityLogs: {
        data: null,
        loading: false,
        error: null,
        message: null,
        success: false,
    },
};

const activityLogsSlice = createSlice({
    name: "activityLogs",
    initialState,
    reducers: {
        clearState: (state, action: PayloadAction<keyof typeof initialState>) => {
            if (state[action.payload]) {
                state[action.payload].error = null;
                state[action.payload].success = false;
                state[action.payload].loading = false;
                state[action.payload].message = null;
            }
        },
        clearExportSuccess: (state) => {
            state.exportActivityLogs.success = false;
        },
        clearExportError: (state) => {
            state.exportActivityLogs.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Get Activity Logs
            .addCase(getActivityLogs.pending, (state) => {
                state.getActivityLogs.loading = true;
                state.getActivityLogs.error = null;
            })
            .addCase(getActivityLogs.fulfilled, (state, action: PayloadAction<ActivityLogsResponse>) => {
                state.getActivityLogs.loading = false;
                state.getActivityLogs.data = action.payload;
                state.getActivityLogs.success = true;
                state.getActivityLogs.message = "Activity logs fetched successfully";
            })
            .addCase(getActivityLogs.rejected, (state, action) => {
                state.getActivityLogs.loading = false;
                state.getActivityLogs.error = action.payload;
            })
            // Export Activity Logs
            .addCase(exportActivityLogs.pending, (state) => {
                state.exportActivityLogs.loading = true;
                state.exportActivityLogs.error = null;
            })
            .addCase(exportActivityLogs.fulfilled, (state, action: PayloadAction<ExportResponse>) => {
                state.exportActivityLogs.loading = false;
                state.exportActivityLogs.data = action.payload;
                state.exportActivityLogs.success = true;
                state.exportActivityLogs.message = action.payload.message;
            })
            .addCase(exportActivityLogs.rejected, (state, action) => {
                state.exportActivityLogs.loading = false;
                state.exportActivityLogs.error = action.payload;
            });
    },
});

export const { clearState, clearExportSuccess, clearExportError } = activityLogsSlice.actions;

export default activityLogsSlice.reducer;
