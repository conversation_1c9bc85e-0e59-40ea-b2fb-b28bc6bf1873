import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getTrustedDevices, removeDevice, removeAllDevices } from "../actions/settingsTrustedDevices";
import { TrustedDevices, TrustedDevicesData, removeDeviceResponse } from "../types/trustedDevices";

const initialState: TrustedDevices = {
    getTrustedDevices: {
        data: null,
        loading: false,
        error: null,
        message: null,
        success: false,
    },
    removeDevice: {
        data: null,
        loading: false,
        error: null,
        message: null,
        success: false,
    },
    removeAllDevices: {
        data: null,
        loading: false,
        error: null,
        message: null,
        success: false,
    },
};

const trustedDevicesSlice = createSlice({
    name: "trustedDevices",
    initialState,
    reducers: {
        clearState: (state, action: PayloadAction<keyof typeof initialState>) => {
            if (state[action.payload]) {
                state[action.payload].error = null;
                state[action.payload].success = false;
                state[action.payload].loading = false;
                state[action.payload].message = null;
            }
        },
    },
    extraReducers: (builder) => {
        builder
            // Get Trusted Devices
            .addCase(getTrustedDevices.pending, (state) => {
                state.getTrustedDevices.loading = true;
                state.getTrustedDevices.error = null;
            })
            .addCase(getTrustedDevices.fulfilled, (state, action: PayloadAction<TrustedDevicesData[]>) => {
                state.getTrustedDevices.data = action.payload;
                state.getTrustedDevices.loading = false;
                state.getTrustedDevices.success = true;
                state.getTrustedDevices.message = "Trusted devices fetched successfully";
            })
            .addCase(getTrustedDevices.rejected, (state, action) => {
                state.getTrustedDevices.loading = false;
                state.getTrustedDevices.error = action.payload as string;
                state.getTrustedDevices.success = false;
            })

            // Remove Device
            .addCase(removeDevice.pending, (state) => {
                state.removeDevice.loading = true;
                state.removeDevice.error = null;
                state.removeDevice.success = false;
            })
            .addCase(removeDevice.fulfilled, (state, action: PayloadAction<removeDeviceResponse>) => {
                state.removeDevice.data = action.payload;
                state.removeDevice.loading = false;
                state.removeDevice.success = true;
                state.removeDevice.message = "Device removed successfully";
            })
            .addCase(removeDevice.rejected, (state, action) => {
                state.removeDevice.loading = false;
                state.removeDevice.error = action.payload as string;
                state.removeDevice.success = false;
            })

            // Remove All Devices
            .addCase(removeAllDevices.pending, (state) => {
                state.removeAllDevices.loading = true;
                state.removeAllDevices.error = null;
                state.removeAllDevices.success = false;
            })
            .addCase(removeAllDevices.fulfilled, (state, action: PayloadAction<removeDeviceResponse>) => {
                state.removeAllDevices.data = action.payload;
                state.removeAllDevices.loading = false;
                state.removeAllDevices.success = true;
                state.removeAllDevices.message = "All devices removed successfully";
            })
            .addCase(removeAllDevices.rejected, (state, action) => {
                state.removeAllDevices.loading = false;
                state.removeAllDevices.error = action.payload as string;
                state.removeAllDevices.success = false;
            });
    },
});

export const { clearState } = trustedDevicesSlice.actions;
export default trustedDevicesSlice.reducer;
