/**
 * Redux slice for managing user settings
 *
 * Handles state for various user settings including personal information.
 * Contains reducers for loading states, success states, and error handling.
 * Updated to support simplified password change flow using direct password validation.
 */
import { createSlice } from "@reduxjs/toolkit";
import { SettingsState } from "../types/settings";
import {
    fetchPersonalInfo,
    updatePersonalInfo,
    updatePersonalInfoWithToken,
    updateBusinessInfo,
    updateBusinessInfoWithToken,
    fetchBusinessInfo,
    uploadBusinessLogo,
    deleteBusinessLogo,
    fetchBusinessLogo,
    changePassword,
    changePasswordWithToken,
} from "../actions/settingsActions";

/**
 * Initial state for the settings slice
 *
 * Follows the pattern of having loading, data, and error fields
 * for each API-dependent section
 */
const initialState: SettingsState = {
    personalInfo: {
        data: null,
        loading: false,
        error: null,
        updateLoading: false,
        updateError: null,
    },
    businessInfo: {
        data: null,
        loading: false,
        error: null,
        updateLoading: false,
        updateError: null,
    },
    businessLogo: {
        uploadLoading: false,
        uploadError: null,
        deleteLoading: false,
        deleteError: null,
        fetchLoading: false,
        fetchError: null,
    },
    changePassword: {
        loading: false,
        success: false,
        error: null,
    },
};

export const settingsSlice = createSlice({
    name: "settings",
    initialState,
    reducers: {
        /**
         * Clears the error state for personal information
         * Used when retrying failed requests or resetting error state
         */
        clearPersonalInfoError: (state) => {
            state.personalInfo.error = null;
        },

        /**
         * Clears the update error state for personal information
         * Used when retrying failed update requests
         */
        clearPersonalInfoUpdateError: (state) => {
            state.personalInfo.updateError = null;
        },

        /**
         * Clears the error state for business information
         */
        clearBusinessInfoError: (state) => {
            state.businessInfo.error = null;
        },

        /**
         * Clears the update error state for business information
         */
        clearBusinessInfoUpdateError: (state) => {
            state.businessInfo.updateError = null;
        },

        /**
         * Clears the upload error state for business logo
         * Used when retrying failed logo upload requests
         */
        clearBusinessLogoUploadError: (state) => {
            state.businessLogo.uploadError = null;
        },

        /**
         * Clears the delete error state for business logo
         * Used when retrying failed logo delete requests
         */
        clearBusinessLogoDeleteError: (state) => {
            state.businessLogo.deleteError = null;
        },

        /**
         * Clears the fetch error state for business logo
         * Used when retrying failed logo fetch requests
         */
        clearBusinessLogoFetchError: (state) => {
            state.businessLogo.fetchError = null;
        },
        /**
         * Clears the error state for change password operation
         * Used when retrying failed password change requests
         */
        clearChangePasswordError: (state) => {
            state.changePassword.error = null;
        },
        /**
         * Clears the success state for change password operation
         * Used to reset the success feedback after displaying it
         */
        clearChangePasswordSuccess: (state) => {
            state.changePassword.success = false;
        },
    },
    extraReducers: (builder) => {
        builder
            // Handle fetchPersonalInfo action states
            .addCase(fetchPersonalInfo.pending, (state) => {
                state.personalInfo.loading = true;
            })
            .addCase(fetchPersonalInfo.fulfilled, (state, action) => {
                state.personalInfo.loading = false;
                state.personalInfo.data = action.payload;
                state.personalInfo.error = null;
            })
            .addCase(fetchPersonalInfo.rejected, (state, action) => {
                state.personalInfo.loading = false;
                state.personalInfo.error = action.payload as string;
            })

            // Handle updatePersonalInfo action states
            .addCase(updatePersonalInfo.pending, (state) => {
                state.personalInfo.updateLoading = true;
            })
            .addCase(updatePersonalInfo.fulfilled, (state, action) => {
                state.personalInfo.updateLoading = false;
                state.personalInfo.data = action.payload;
                state.personalInfo.updateError = null;
            })
            .addCase(updatePersonalInfo.rejected, (state, action) => {
                state.personalInfo.updateLoading = false;
                state.personalInfo.updateError = action.payload as string;
            })

            // Handle updatePersonalInfoWithToken action states
            .addCase(updatePersonalInfoWithToken.pending, (state) => {
                state.personalInfo.updateLoading = true;
            })
            .addCase(updatePersonalInfoWithToken.fulfilled, (state, action) => {
                state.personalInfo.updateLoading = false;
                state.personalInfo.data = action.payload;
                state.personalInfo.updateError = null;
            })
            .addCase(updatePersonalInfoWithToken.rejected, (state, action) => {
                state.personalInfo.updateLoading = false;
                state.personalInfo.updateError = action.payload as string;
            })

            // Handle fetchBusinessInfo action states
            .addCase(fetchBusinessInfo.pending, (state) => {
                state.businessInfo.loading = true;
            })
            .addCase(fetchBusinessInfo.fulfilled, (state, action) => {
                state.businessInfo.loading = false;
                state.businessInfo.data = action.payload;
                state.businessInfo.error = null;
            })
            .addCase(fetchBusinessInfo.rejected, (state, action) => {
                state.businessInfo.loading = false;
                state.businessInfo.error = action.payload as string;
            })

            // Handle updateBusinessInfo action states
            .addCase(updateBusinessInfo.pending, (state) => {
                state.businessInfo.updateLoading = true;
            })
            .addCase(updateBusinessInfo.fulfilled, (state, action) => {
                state.businessInfo.updateLoading = false;
                state.businessInfo.data = action.payload;
                state.businessInfo.updateError = null;
            })
            .addCase(updateBusinessInfo.rejected, (state, action) => {
                state.businessInfo.updateLoading = false;
                state.businessInfo.updateError = action.payload as string;
            })

            // Handle updateBusinessInfoWithToken action states
            .addCase(updateBusinessInfoWithToken.pending, (state) => {
                state.businessInfo.updateLoading = true;
            })
            .addCase(updateBusinessInfoWithToken.fulfilled, (state, action) => {
                state.businessInfo.updateLoading = false;
                state.businessInfo.data = action.payload;
                state.businessInfo.updateError = null;
            })
            .addCase(updateBusinessInfoWithToken.rejected, (state, action) => {
                state.businessInfo.updateLoading = false;
                state.businessInfo.updateError = action.payload as string;
            })

            // Handle uploadBusinessLogo action states
            .addCase(uploadBusinessLogo.pending, (state) => {
                state.businessLogo.uploadLoading = true;
                state.businessLogo.uploadError = null;
            })
            .addCase(uploadBusinessLogo.fulfilled, (state, action) => {
                state.businessLogo.uploadLoading = false;

                // If the response includes a logoUrl, update it in the business info
                if (action.payload.logoUrl && state.businessInfo.data) {
                    state.businessInfo.data.logoUrl = action.payload.logoUrl;
                }
            })
            .addCase(uploadBusinessLogo.rejected, (state, action) => {
                state.businessLogo.uploadLoading = false;
                state.businessLogo.uploadError = action.payload as string;
            })

            // Handle deleteBusinessLogo action states
            .addCase(deleteBusinessLogo.pending, (state) => {
                state.businessLogo.deleteLoading = true;
                state.businessLogo.deleteError = null;
            })
            .addCase(deleteBusinessLogo.fulfilled, (state) => {
                state.businessLogo.deleteLoading = false;

                // Clear the logo URL from business info
                if (state.businessInfo.data) {
                    state.businessInfo.data.logoUrl = null;
                }
            })
            .addCase(deleteBusinessLogo.rejected, (state, action) => {
                state.businessLogo.deleteLoading = false;
                state.businessLogo.deleteError = action.payload as string;
            })

            // Handle fetchBusinessLogo action states
            .addCase(fetchBusinessLogo.pending, (state) => {
                state.businessLogo.fetchLoading = true;
                state.businessLogo.fetchError = null;
            })
            .addCase(fetchBusinessLogo.fulfilled, (state, action) => {
                state.businessLogo.fetchLoading = false;
                if (state.businessInfo.data) {
                    state.businessInfo.data.logoUrl = action.payload.logoUrl;
                }
            })
            .addCase(fetchBusinessLogo.rejected, (state, action) => {
                state.businessLogo.fetchLoading = false;
                state.businessLogo.fetchError = action.payload as string;
            })

            // Handle changePassword action states
            .addCase(changePassword.pending, (state) => {
                state.changePassword.loading = true;
                state.changePassword.error = null;
                state.changePassword.success = false;
            })
            .addCase(changePassword.fulfilled, (state) => {
                state.changePassword.loading = false;
                state.changePassword.success = true;
            })
            .addCase(changePassword.rejected, (state, action) => {
                state.changePassword.loading = false;
                state.changePassword.error = action.payload as string;
            })

            // Handle changePasswordWithToken action states
            .addCase(changePasswordWithToken.pending, (state) => {
                state.changePassword.loading = true;
                state.changePassword.error = null;
                state.changePassword.success = false;
            })
            .addCase(changePasswordWithToken.fulfilled, (state) => {
                state.changePassword.loading = false;
                state.changePassword.success = true;
            })
            .addCase(changePasswordWithToken.rejected, (state, action) => {
                state.changePassword.loading = false;
                state.changePassword.error = action.payload as string;
            });
    },
});

export const {
    clearPersonalInfoError,
    clearPersonalInfoUpdateError,
    clearBusinessInfoError,
    clearBusinessInfoUpdateError,
    clearBusinessLogoUploadError,
    clearBusinessLogoDeleteError,
    clearBusinessLogoFetchError,
    clearChangePasswordError,
    clearChangePasswordSuccess,
} = settingsSlice.actions;
export default settingsSlice.reducer;
