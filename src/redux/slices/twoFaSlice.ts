import { createSlice } from "@reduxjs/toolkit";
import {
    getTeamMemberByEmail,
    getTestTeamMember,
    updatePreferredMFAMethod,
    disableMFA,
    MFAMethod,
} from "../actions/settingsTwoFa";
import { TeamMembersData } from "../types/teamMembers";

interface TwoFaState {
    preferredMethod: MFAMethod | null;
    enabledMethods: {
        AUTHENTICATOR: boolean;
        SMS: boolean;
        SECURITY_QUESTION: boolean;
    };
    teamMember: TeamMembersData | null; // Store the full team member object
    previousState?: {
        preferredMethod: MFAMethod | null;
        enabledMethods: {
            AUTHENTICATOR: boolean;
            SMS: boolean;
            SECURITY_QUESTION: boolean;
        };
    }; // Store previous state for rollback on failure
    updateMFAMethod: {
        loading: boolean;
        error: string | null;
        success: boolean;
    };
    getTeamMember: {
        loading: boolean;
        error: string | null;
        success: boolean;
    };
}

const initialState: TwoFaState = {
    preferredMethod: null,
    enabledMethods: {
        AUTHENTICATOR: false,
        SMS: false,
        SECURITY_QUESTION: false,
    },
    teamMember: null,
    updateMFAMethod: {
        loading: false,
        error: null,
        success: false,
    },
    getTeamMember: {
        loading: false,
        error: null,
        success: false,
    },
};

const twoFaSlice = createSlice({
    name: "twoFa",
    initialState,
    reducers: {
        clearUpdateMFAState: (state) => {
            state.updateMFAMethod = {
                loading: false,
                error: null,
                success: false,
            };
        },
        clearGetTeamMemberState: (state) => {
            state.getTeamMember = {
                loading: false,
                error: null,
                success: false,
            };
        },
        // Store the previous state to allow for rollback on failure
        storeOriginalState: (state) => {
            state.previousState = {
                preferredMethod: state.preferredMethod,
                enabledMethods: { ...state.enabledMethods },
            };
        },
        // Handle optimistic updates with the ability to recover
        optimisticUpdate: (state, action) => {
            const { preferredMethod, enabledMethods } = action.payload;
            state.preferredMethod = preferredMethod;
            state.enabledMethods = enabledMethods;
        },
        // Restore state if the update fails
        revertToPreviousState: (state) => {
            if (state.previousState) {
                state.preferredMethod = state.previousState.preferredMethod;
                state.enabledMethods = state.previousState.enabledMethods;
                state.previousState = undefined;
            }
        },
    },
    extraReducers: (builder) => {
        builder
            // Update preferred MFA method
            .addCase(updatePreferredMFAMethod.pending, (state) => {
                state.updateMFAMethod.loading = true;
                state.updateMFAMethod.error = null;
                state.updateMFAMethod.success = false;
            })
            .addCase(updatePreferredMFAMethod.fulfilled, (state, action) => {
                state.updateMFAMethod.loading = false;
                state.updateMFAMethod.success = true;

                const { mfaMethod } = action.meta.arg;

                // If we have response data, update based on that
                if (action.payload) {
                    // Get updated MFA status and preferred method from the response
                    const { preferredMfaMethod, mfaStatus } = action.payload;

                    // Update preferred method based on response
                    state.preferredMethod = mfaStatus && preferredMfaMethod ? (preferredMfaMethod as MFAMethod) : null;

                    // Update enabled methods based on response - only one method can be enabled
                    state.enabledMethods = {
                        AUTHENTICATOR: false,
                        SMS: false,
                        SECURITY_QUESTION: false,
                    };

                    // Only set the preferred method to true
                    if (mfaStatus && preferredMfaMethod) {
                        state.enabledMethods[preferredMfaMethod as MFAMethod] = true;
                    }
                } else {
                    // Fallback to client-side toggling if no response data

                    // First, disable all methods
                    state.enabledMethods = {
                        AUTHENTICATOR: false,
                        SMS: false,
                        SECURITY_QUESTION: false,
                    };

                    // Then enable only the selected method
                    state.enabledMethods[mfaMethod] = true;
                    state.preferredMethod = mfaMethod;
                }
            })
            .addCase(updatePreferredMFAMethod.rejected, (state, action) => {
                state.updateMFAMethod.loading = false;
                state.updateMFAMethod.error = action.payload as string;
                state.updateMFAMethod.success = false;

                // Note: We don't need to revert the state here because
                // the component will call revertToPreviousState action
                // when it catches the error
            })

            // Get team member by email
            .addCase(getTeamMemberByEmail.pending, (state) => {
                state.getTeamMember.loading = true;
                state.getTeamMember.error = null;
                state.getTeamMember.success = false;
            })
            .addCase(getTeamMemberByEmail.fulfilled, (state, action) => {
                state.getTeamMember.loading = false;
                state.getTeamMember.success = true;

                // Store the full team member object
                state.teamMember = action.payload;

                // Update state based on response
                if (action.payload) {
                    // Get MFA status and preferred method from the response
                    const { preferredMfaMethod, mfaStatus } = action.payload;

                    // Set the preferred method if MFA is enabled
                    state.preferredMethod = mfaStatus && preferredMfaMethod ? (preferredMfaMethod as MFAMethod) : null;

                    // Update enabled methods based on response - only one method can be enabled
                    state.enabledMethods = {
                        AUTHENTICATOR: false,
                        SMS: false,
                        SECURITY_QUESTION: false,
                    };

                    // Only set the preferred method to true
                    if (mfaStatus && preferredMfaMethod) {
                        state.enabledMethods[preferredMfaMethod as MFAMethod] = true;
                    }
                }
            })
            .addCase(getTeamMemberByEmail.rejected, (state, action) => {
                state.getTeamMember.loading = false;
                state.getTeamMember.error = action.payload as string;
                state.getTeamMember.success = false;
            })

            // Get test team member (with <EMAIL>)
            .addCase(getTestTeamMember.pending, (state) => {
                state.getTeamMember.loading = true;
                state.getTeamMember.error = null;
                state.getTeamMember.success = false;
            })
            .addCase(getTestTeamMember.fulfilled, (state, action) => {
                state.getTeamMember.loading = false;
                state.getTeamMember.success = true;

                // Store the full team member object
                state.teamMember = action.payload;

                // Update state based on response
                if (action.payload) {
                    // Get MFA status and preferred method from the response
                    const { preferredMfaMethod, mfaStatus } = action.payload;

                    // Set the preferred method if MFA is enabled
                    state.preferredMethod = mfaStatus && preferredMfaMethod ? (preferredMfaMethod as MFAMethod) : null;

                    // Update enabled methods based on response - only one method can be enabled
                    state.enabledMethods = {
                        AUTHENTICATOR: false,
                        SMS: false,
                        SECURITY_QUESTION: false,
                    };

                    // Only set the preferred method to true
                    if (mfaStatus && preferredMfaMethod) {
                        state.enabledMethods[preferredMfaMethod as MFAMethod] = true;
                    }
                }
            })
            .addCase(getTestTeamMember.rejected, (state, action) => {
                state.getTeamMember.loading = false;
                state.getTeamMember.error = action.payload as string;
                state.getTeamMember.success = false;
            })

            // Disable MFA
            .addCase(disableMFA.pending, (state) => {
                state.updateMFAMethod.loading = true;
                state.updateMFAMethod.error = null;
                state.updateMFAMethod.success = false;
            })
            .addCase(disableMFA.fulfilled, (state) => {
                state.updateMFAMethod.loading = false;
                state.updateMFAMethod.success = true;

                // Clear all MFA settings when disabled
                state.preferredMethod = null;
                state.enabledMethods = {
                    AUTHENTICATOR: false,
                    SMS: false,
                    SECURITY_QUESTION: false,
                };

                // Update team member MFA status if available
                if (state.teamMember) {
                    state.teamMember.mfaStatus = false;
                    state.teamMember.preferredMfaMethod = null;
                }
            })
            .addCase(disableMFA.rejected, (state, action) => {
                state.updateMFAMethod.loading = false;
                state.updateMFAMethod.error = action.payload as string;
                state.updateMFAMethod.success = false;
            });
    },
});

export const {
    clearUpdateMFAState,
    clearGetTeamMemberState,
    storeOriginalState,
    optimisticUpdate,
    revertToPreviousState,
} = twoFaSlice.actions;
export default twoFaSlice.reducer;
