import { createSlice } from "@reduxjs/toolkit";
import { getTeamMemberDetails, MFAMethod } from "../actions/transferMfaActions";
import { TransferMfaState } from "../types/transferMfa";

// interface TransferMfaState {
//     teamMember: TeamMembersData | null;
//     getTeamMemberDetails: {
//         loading: boolean;
//         error: string | null;
//         success: boolean;

//     };
// }

const initialState: TransferMfaState = {
    teamMember: null,
    getTeamMemberDetails: {
        loading: false,
        error: null,
        success: false,
    },
};

const transferMfaSlice = createSlice({
    name: "transferMfa",
    initialState,
    reducers: {
        clearGetTeamMemberState: (state) => {
            state.getTeamMemberDetails = {
                loading: false,
                error: null,
                success: false,
            };
            state.teamMember = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(getTeamMemberDetails.pending, (state) => {
                state.getTeamMemberDetails.loading = true;
                state.getTeamMemberDetails.error = null;
                state.getTeamMemberDetails.success = false;
            })
            .addCase(getTeamMemberDetails.fulfilled, (state, action) => {
                state.getTeamMemberDetails.loading = false;
                state.getTeamMemberDetails.success = true;
                state.teamMember = action.payload;
            })
            .addCase(getTeamMemberDetails.rejected, (state, action) => {
                state.getTeamMemberDetails.loading = false;
                state.getTeamMemberDetails.error = action.payload as string;
                state.getTeamMemberDetails.success = false;
            });
    },
});

export const { clearGetTeamMemberState } = transferMfaSlice.actions;
export default transferMfaSlice.reducer;
