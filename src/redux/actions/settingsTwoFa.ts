import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";

export type MFAMethod = "SECURITY_QUESTION" | "AUTHENTICATOR" | "SMS";

// Update preferred MFA method
export const updatePreferredMFAMethod = createAsyncThunk(
    "settings/updatePreferredMFAMethod",
    async ({ mfaMethod }: { mfaMethod: MFAMethod }, { rejectWithValue }) => {
        try {
            // The API endpoint expects mfaMethod as a query parameter
            const response = await userAxios.patch(`/v1/authenticator-app?mfaMethod=${mfaMethod}`);

            // Show success message
            sendFeedback(`${mfaMethod} 2FA authentication updated successfully`, "success");

            return response.data;
        } catch (error) {
            // Show error message
            sendCatchFeedback(error);

            // Return the error to be handled in the component
            return rejectWithValue(error);
        }
    }
);

// Get current user's team member data (includes MFA settings)
export const getTeamMemberByEmail = createAsyncThunk(
    "settings/getTeamMemberByEmail",
    async (_, { rejectWithValue }) => {
        try {
            // Use the current user's team member endpoint to avoid conflict with PATCH endpoint
            // This endpoint returns the current user's team member data including MFA settings
            const response = await userAxios.get("/v1/team-members");
            return response.data;
        } catch (error) {
            // Don't show error feedback here as this might be called on initial load
            return rejectWithValue(error);
        }
    }
);

// Get team member with test email (for development)
export const getTestTeamMember = createAsyncThunk("settings/getTestTeamMember", async (_, { rejectWithValue }) => {
    try {
        // Use the test email address
        const testEmail = "<EMAIL>";

        // Fetch the team member data
        const response = await userAxios.get(`/v1/team-members?emailAddress=${testEmail}`);
        return response.data;
    } catch (error) {
        return rejectWithValue(error);
    }
});

// Disable MFA completely
export const disableMFA = createAsyncThunk("settings/disableMFA", async (email: string, { rejectWithValue }) => {
    try {
        // Call API to disable MFA - this might need to be adjusted based on actual API
        const response = await userAxios.delete(`/v1/authenticator-app?email=${email}`);

        // Show success message
        sendFeedback("Two-factor authentication disabled successfully", "success");

        return response.data;
    } catch (error) {
        // Show error message
        sendCatchFeedback(error);

        // Return the error to be handled in the component
        return rejectWithValue(error);
    }
});
