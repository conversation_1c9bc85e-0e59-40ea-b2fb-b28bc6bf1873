import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";

export type MFAMethod = "SECURITY_QUESTION" | "AUTHENTICATOR" | "SMS";

// Get team member with test email (for development)
export const getTeamMemberDetails = createAsyncThunk(
    "transfer/getTeamMemberDetails",
    async (_, { rejectWithValue }) => {
        try {
            // Fetch the team member data
            const response = await userAxios.get("/v1/team-members");
            return response.data;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);
