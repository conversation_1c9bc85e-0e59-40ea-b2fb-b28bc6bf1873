/**
 * Purpose: Provides Redux async thunks for managing outgoing payment data and operations.
 *
 * Functionality: This file contains comprehensive Redux async thunks for fetching and managing outgoing
 * payment data across three payment types (instant, scheduled, recurring). It handles API communication
 * with the transfers endpoint, manages payment approval workflows, enables transfer status updates
 * (cancel, pause, resume), and provides receipt download functionality. All thunks implement proper
 * error handling with user feedback using sendCatchFeedback for user-initiated actions and silent
 * error logging for background operations. The file also includes utility functions for mapping API
 * responses to the application's transfer interface and formatting frequency data for recurring payments.
 *
 * Dependencies: Requires Redux Toolkit for async thunk creation, transferAxios instance for API calls,
 * outgoing types for TypeScript interfaces, feedback functions for user notifications, status mapping
 * utilities for data transformation, and Axios for error handling.
 *
 * Usage: Import and dispatch these thunks from React components to fetch transfer data, update transfer
 * statuses, download receipts, and manage payment approvals. All thunks return properly typed responses
 * and handle loading states, errors, and success scenarios through Redux state management.
 */

import { createAsyncThunk } from "@reduxjs/toolkit";
import { transferAxios } from "@/api/axios";
import { ITransfer, PaymentApprovalItem } from "@/components/page-components/dashboard/outgoing/types";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { AxiosError } from "axios";

import { getStatusMapping } from "@/utils/status-mapping";

// Define interfaces for API request and response
export interface OutgoingRequestParams {
    pageNo: number;
    pageSize: number;
    isUserInitiated?: boolean;
    paymentType?: "INSTANT" | "SCHEDULED" | "RECURRING";
    // Add filter parameters
    search?: string;
    startDate?: string;
    endDate?: string;
    minAmount?: string;
    maxAmount?: string;
    amount?: string;
}

// Updated to match the actual API response structure
export interface OutgoingResponseItem {
    transferScheduledId: number;
    paymentType: string;
    destinationAccount: string;
    destinationAccountName: string;
    firstPaymentDate: string | null;
    lastPaymentDate: string | null;
    nextPaymentDate: string | null;
    frequencyType: string;
    frequencyValue: number;
    numberOfOccurrence: number;
    numberOfCompletedOccurrence: number;
    scheduleStatus: string;
    totalTransfers: number;
    totalAmount: number;
    createdAt: string;
    transactionRef: string;
    bulkTransactionRef: string | null;
    transferNarration: string;
    bankName: string;
    transferStatus: string;
}

// Updated to match the actual API response structure
export interface OutgoingResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: {
        [key: string]: OutgoingResponseItem[];
    };
    number?: number; // Added as optional since it's not in the actual response
}

/**
 * Fetches instant outgoing payment data from the API
 * @param params - Request parameters including pagination and payment type
 * @param params.pageNo - Zero-indexed page number for pagination
 * @param params.pageSize - Number of items per page
 * @param params.isUserInitiated - Whether this is a user-initiated action (affects error feedback)
 * @param params.paymentType - Payment type filter (defaults to "INSTANT")
 * @returns Promise resolving to OutgoingResponse with instant payment data
 * @throws Will show user feedback for errors only if isUserInitiated is true
 */
export const fetchOutgoing = createAsyncThunk(
    "outgoing/fetchOutgoing",
    async (params: OutgoingRequestParams, { rejectWithValue }) => {
        try {
            const response = await transferAxios.get<OutgoingResponse>("/transfers/request-details", {
                params: {
                    pageNo: params.pageNo,
                    pageSize: params.pageSize,
                    paymentType: params.paymentType ?? "INSTANT", // Default to INSTANT if not provided
                    // Note: Filter parameters are handled client-side as per API specification
                    // The backend API does not support filter parameters
                },
            });

            // Add the page number to the response if it's not included
            const responseWithPage = {
                ...response.data,
                number: params.pageNo,
            };

            return responseWithPage;
        } catch (error: unknown) {
            const typedError = error as AxiosError;
            // Only show error feedback for user-initiated actions
            if (params.isUserInitiated) {
                sendCatchFeedback(typedError);
            }
            return rejectWithValue(typedError);
        }
    }
);

/**
 * Fetches scheduled outgoing payment data from the API
 * @param params - Request parameters including pagination
 * @param params.pageNo - Zero-indexed page number for pagination
 * @param params.pageSize - Number of items per page
 * @param params.isUserInitiated - Whether this is a user-initiated action (affects error feedback)
 * @returns Promise resolving to OutgoingResponse with scheduled payment data
 * @throws Will show user feedback for errors only if isUserInitiated is true
 */
export const fetchScheduledOutgoing = createAsyncThunk(
    "outgoing/fetchScheduledOutgoing",
    async (params: OutgoingRequestParams, { rejectWithValue }) => {
        try {
            const response = await transferAxios.get<OutgoingResponse>("/transfers/request-details", {
                params: {
                    pageNo: params.pageNo,
                    pageSize: params.pageSize,
                    paymentType: "SCHEDULED", // Always use SCHEDULED for this function
                    // Note: Filter parameters are handled client-side as per API specification
                    // The backend API does not support filter parameters
                },
            });

            // Add the page number to the response if it's not included
            const responseWithPage = {
                ...response.data,
                number: params.pageNo,
            };

            return responseWithPage;
        } catch (error: unknown) {
            const typedError = error as AxiosError;
            // Only show error feedback for user-initiated actions
            if (params.isUserInitiated) {
                sendCatchFeedback(typedError);
            }
            return rejectWithValue(typedError);
        }
    }
);

/**
 * Fetches recurring outgoing payment data from the API
 * @param params - Request parameters including pagination
 * @param params.pageNo - Zero-indexed page number for pagination
 * @param params.pageSize - Number of items per page
 * @param params.isUserInitiated - Whether this is a user-initiated action (affects error feedback)
 * @returns Promise resolving to OutgoingResponse with recurring payment data
 * @throws Will show user feedback for errors only if isUserInitiated is true
 */
export const fetchRecurringOutgoing = createAsyncThunk(
    "outgoing/fetchRecurringOutgoing",
    async (params: OutgoingRequestParams, { rejectWithValue }) => {
        try {
            const response = await transferAxios.get<OutgoingResponse>("/transfers/request-details", {
                params: {
                    pageNo: params.pageNo,
                    pageSize: params.pageSize,
                    paymentType: "RECURRING", // Always use RECURRING for this function
                    // Note: Filter parameters are handled client-side as per API specification
                    // The backend API does not support filter parameters
                },
            });

            // Add the page number to the response if it's not included
            const responseWithPage = {
                ...response.data,
                number: params.pageNo,
            };

            return responseWithPage;
        } catch (error: unknown) {
            const typedError = error as AxiosError;
            // Only show error feedback for user-initiated actions
            if (params.isUserInitiated) {
                sendCatchFeedback(typedError);
            }
            return rejectWithValue(typedError);
        }
    }
);

// Helper function to format frequency based on frequencyType and frequencyValue
const formatFrequency = (frequencyType?: string, frequencyValue?: number): string => {
    if (!frequencyType) return "One-time";

    // Convert to title case (e.g., "WEEKLY" to "Weekly")
    const formattedType = frequencyType.charAt(0).toUpperCase() + frequencyType.slice(1).toLowerCase();

    // If frequencyValue is 1 or undefined, just return the formatted type
    if (!frequencyValue || frequencyValue === 1) {
        return formattedType;
    }

    // For other values, return "Every X [Type]"
    return `Every ${frequencyValue} ${formattedType}`;
};

// Updated helper function to map API response to ITransfer interface
export const mapApiResponseToTransfer = (item: OutgoingResponseItem, counterpartyName: string): ITransfer => ({
    // Map transactionRef to id
    id: item.transactionRef || `tr-${item.transferScheduledId || Math.random().toString(36).substring(2, 10)}`,
    // Map transferScheduledId to both paymentRequestId and transferScheduledId
    // We need to ensure 0 is preserved as a valid value
    paymentRequestId: item.transferScheduledId,
    transferScheduledId: item.transferScheduledId,
    // Map transactionRef field for API calls
    transactionRef: item.transactionRef,
    // Map createdAt to date, keep it as is without any pre-formatting
    date: item.createdAt,
    // We're using a dummy value for scheduledDate in the component
    // scheduledDate: item.nextPaymentDate,
    // Map destinationAccountName to counterparty, fallback to the counterpartyName from the API response
    counterparty: item.destinationAccountName || counterpartyName,
    // Map destinationAccount to accountNumber
    accountNumber: item.destinationAccount,
    // Use transferNarration for the narration field if available, otherwise fallback to default
    narration:
        item.transferNarration ||
        (item.paymentType && item.paymentType !== "NONE" ? item.paymentType : "Regular Transfer"),
    // Always use transferStatus regardless of tab type
    // Map API status values to the expected UI format using centralized mapping
    status: getStatusMapping(item.transferStatus).text,
    // Map totalAmount to amount
    amount: item.totalAmount,
    // Default value as it's not provided in the API
    transferType: "Inter-bank",
    // Format frequency based on frequencyValue
    frequency: formatFrequency(item.frequencyType, item.frequencyValue),
    // Use bankName from API if available, otherwise default
    bank: item.bankName || "First Bank",
    // Store original values for future reference
    frequencyType: item.frequencyType,
    frequencyValue: item.frequencyValue,
    firstPaymentDate: item.firstPaymentDate,
    lastPaymentDate: item.lastPaymentDate,
    nextPaymentDate: item.nextPaymentDate,
    numberOfOccurrence: item.numberOfOccurrence,
    numberOfCompletedOccurrence: item.numberOfCompletedOccurrence,
    // Store the total number of transfers for bulk transfers
    totalTransfers: item.totalTransfers || 1,
    // Store the bulk transaction reference ID
    bulkTransactionRef: item.bulkTransactionRef,
});

// For backward compatibility, keep the old function names as aliases
export const fetchTransfers = fetchOutgoing;
export const fetchScheduledTransfers = fetchScheduledOutgoing;
export const fetchRecurringTransfers = fetchRecurringOutgoing;
export type TransferRequestParams = OutgoingRequestParams;
export type TransferResponseItem = OutgoingResponseItem;
export type TransferResponse = OutgoingResponse;

// New interfaces for payment approvals
export interface PaymentApprovalRequestParams {
    paymentRequestId: number;
    pageNo?: number;
    pageSize?: number;
}

export interface PaymentApprovalResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: PaymentApprovalItem[];
}

// Thunk for fetching payment approvals
export const fetchPaymentApprovals = createAsyncThunk(
    "outgoing/fetchPaymentApprovals",
    async (params: PaymentApprovalRequestParams, { rejectWithValue }) => {
        try {
            const { paymentRequestId = 82, pageNo = 0, pageSize = 100 } = params;

            const response = await transferAxios.get<PaymentApprovalResponse>(
                `/payment-requests/${paymentRequestId}/payment-approvals`,
                {
                    params: {
                        pageNo,
                        pageSize,
                    },
                }
            );

            return response.data;
        } catch (error: unknown) {
            const typedError = error as AxiosError;
            return rejectWithValue(typedError);
        }
    }
);

// Interface for update transfer status request
export interface UpdateTransferStatusRequest {
    transferScheduledId: number | string;
    transferScheduleStatus: "CANCELLED" | "PAUSED" | "ONGOING"; // Supported status values
    paymentType?: "INSTANT" | "SCHEDULED" | "RECURRING"; // Add payment type to identify transfer type
    isUserInitiated?: boolean; // Add for consistent user feedback handling
}

// Thunk for updating transfer status (cancel, pause, resume)
export const updateTransferStatus = createAsyncThunk(
    "outgoing/updateTransferStatus",
    async (params: UpdateTransferStatusRequest, { rejectWithValue }) => {
        try {
            const { transferScheduledId, transferScheduleStatus, paymentType } = params;

            // Validate that transferScheduledId is not null or undefined (but 0 is valid)
            if (transferScheduledId === undefined || transferScheduledId === null) {
                throw new Error("Invalid transferScheduledId. This transfer cannot be modified.");
            }

            // Check if this is an instant transfer, which should not be processed
            if (paymentType === "INSTANT") {
                throw new Error(
                    `${transferScheduleStatus.toLowerCase()} operation is not supported for instant transfers`
                );
            }

            // Use a single endpoint for all transfer types
            const endpoint = `/transfers/schedules/${transferScheduledId}`;

            // Make the API call using POST
            const response = await transferAxios.post(endpoint, { transferScheduleStatus });

            // If we get here, the API call was successful
            const actionText =
                {
                    CANCELLED: "cancelled",
                    PAUSED: "paused",
                    ONGOING: "resumed",
                }[transferScheduleStatus] || "updated";

            // Show success feedback
            sendFeedback(`Transfer has been successfully ${actionText}.`, "success");

            // Return response data
            return {
                transferScheduledId,
                transferScheduleStatus,
                success: true,
                data: response.data,
            };
        } catch (error: unknown) {
            const typedError = error as AxiosError;

            // Show error feedback since API call failed, but only if user initiated
            if (params.isUserInitiated) {
                sendCatchFeedback(typedError);
            }

            return rejectWithValue(typedError);
        }
    }
);

// Interface for download receipt request parameters
export interface DownloadReceiptParams {
    transactionId: string | number;
    isUserInitiated?: boolean;
}

// Thunk for downloading receipt
export const downloadReceipt = createAsyncThunk(
    "outgoing/downloadReceipt",
    async (params: DownloadReceiptParams, { rejectWithValue }) => {
        try {
            const { transactionId, isUserInitiated = true } = params;

            // Validate transaction ID - only reject if undefined or null, not if it's 0
            if (transactionId === undefined || transactionId === null) {
                const error = new Error("Transaction ID is required to download receipt");
                if (isUserInitiated) {
                    sendCatchFeedback(error);
                }
                return rejectWithValue({ message: error.message });
            }

            // Use axios to download the file with responseType blob
            const response = await transferAxios.get("/transfers/download-receipt", {
                params: { transactionId },
                responseType: "blob",
                disableEncryption: true,
            });

            // Validate that we got a valid response with content
            if (!response.data || response.data.size === 0) {
                throw new Error("No receipt data received from the server");
            }

            // Check if the response is JSON (error) instead of a blob
            const contentType = response.headers["content-type"];
            if (contentType?.includes("application/json")) {
                // This is likely an error response, not a file
                const reader = new FileReader();
                reader.onload = () => {
                    try {
                        const errorJson = JSON.parse(reader.result as string);
                        throw new Error(errorJson.message ?? "Unable to download receipt");
                    } catch {
                        throw new Error("Failed to parse error response");
                    }
                };
                reader.readAsText(response.data);
                throw new Error("Received error response instead of file");
            }

            // Create a URL for the blob and open it in a new tab
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", `transaction-${transactionId}-receipt.pdf`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success feedback if this is a user-initiated action
            if (isUserInitiated) {
                sendFeedback("Receipt downloaded successfully", "success");
            }

            return { success: true };
        } catch (error: unknown) {
            const typedError = error as AxiosError;

            // Only show error feedback for user-initiated actions
            if (params.isUserInitiated) {
                sendCatchFeedback(typedError);
            }

            return rejectWithValue(typedError);
        }
    }
);
