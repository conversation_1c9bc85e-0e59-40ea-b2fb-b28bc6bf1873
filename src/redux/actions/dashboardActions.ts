import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { endOfMonth, format, startOfMonth } from "date-fns";
import { DashboardTransactionType, GetAccountStatementBody, GetTransactionsBody } from "../types/dashboard";

export const getAccountStatement = createAsyncThunk(
    "dashboard/getAccountStatement",
    async (body: GetAccountStatementBody, { rejectWithValue }) => {
        try {
            const res = await acctsAxios.post("/api/v1/accounts/statements", body);
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

export const getTransactions = createAsyncThunk(
    "dashboard/getTransactions",
    async (body: GetTransactionsBody, { rejectWithValue }) => {
        try {
            const res = await acctsAxios.get(
                `/api/v1/accounts/${body.accountNumber}/transactions?pageNo=1&pageSize=20&startDate=${body.startDate}&endDate=${body.endDate}`
                // "/api/v1/accounts/**********/transactions?pageNo=1&pageSize=20&startDate=2018-09-07&endDate=2025-03-25"
            );
            return res.data?.content;
        } catch (error: unknown) {
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

export const getRecentTransactions = createAsyncThunk(
    "dashboard/getRecentTransactions",
    async (
        body: {
            accountNumber?: string;
        },
        { rejectWithValue }
    ) => {
        try {
            const res = await acctsAxios.get(
                `/api/v1/accounts/${body.accountNumber}/transactions?pageNo=1&pageSize=5&sortOrder=-1`
                // "/api/v1/accounts/**********/transactions?pageNo=1&pageSize=20&startDate=2018-09-07&endDate=2025-03-25"
            );
            return res.data?.content as DashboardTransactionType[];
        } catch (error: unknown) {
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

export const getAccountStatistics = createAsyncThunk(
    "dashboard/getAccountStatistics",
    async (body: { accountNumber: string }, { rejectWithValue }) => {
        try {
            const now = new Date();
            const startDate = format(startOfMonth(now), "yyyy-MM-dd");
            const endDate = format(endOfMonth(now), "yyyy-MM-dd");
            const res = await acctsAxios.get(
                `/api/v1/accounts/${body.accountNumber}/statistics?startDate=${startDate}&endDate=${endDate}`
            );

            if (res.data?.noOfTransactions === 0) {
                return {};
            }
            return res.data;
        } catch (error: unknown) {
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

export const getAccountDetails = createAsyncThunk(
    "dashboard/getAccountDetails",
    async (body: { accountNumber: string }, { rejectWithValue }) => {
        try {
            const res = await acctsAxios.get(`api/v1/accounts/${body.accountNumber}/details`);
            // const res = await acctsAxios.get("/api/v1/accounts/**********/details");
            return res.data;
        } catch (error: unknown) {
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);
