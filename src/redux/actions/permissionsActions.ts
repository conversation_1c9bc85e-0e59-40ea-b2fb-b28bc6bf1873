/**
 * Redux Permission Actions
 *
 * Centralized permission management actions for fetching and managing
 * both system permissions and user permissions with session-based caching.
 */

import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios } from "@/api/axios";
import { cookies } from "@/lib/cookies";
import { jwtDecode } from "jwt-decode";
import type {
    Permission,
    PermissionCache,
    PermissionErrorResponse,
    FetchSystemPermissionsPayload,
    FetchUserPermissionsPayload,
    InitializePermissionsPayload,
} from "../types/permissions";
import type { DecodedToken } from "@/utils/server-permission-check";
import { RootState } from "../index";

// Cache configuration
const PERMISSION_CACHE_KEY = "redux_permissions_cache";
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
const SESSION_CACHE_DURATION = 8 * 60 * 60 * 1000; // 8 hours for session-based cache

/**
 * Generate session ID from user token
 */
const generateSessionId = (): string => {
    const token = cookies.getToken();
    if (!token) return "";

    try {
        const decoded = jwtDecode(token) as DecodedToken;
        return `${decoded.userId}_${decoded.iat || Date.now()}`;
    } catch {
        return `session_${Date.now()}`;
    }
};

/**
 * Get user ID from token
 */
const getUserId = (): string => {
    const token = cookies.getToken();
    if (!token) {
        console.warn("🔍 getUserId - No token found");
        return "";
    }

    try {
        const decoded = jwtDecode(token) as DecodedToken;
        const userId = decoded.userId?.toString() || "";
        console.log("🔍 getUserId - Extracted userId:", userId, "from token:", { decoded });
        return userId;
    } catch (error) {
        console.error("❌ getUserId - Error decoding token:", error);
        return "";
    }
};

/**
 * Save permissions to cache
 */
const saveToCache = (cache: PermissionCache): void => {
    try {
        localStorage.setItem(PERMISSION_CACHE_KEY, JSON.stringify(cache));
    } catch (error) {
        console.error("Failed to save permissions to cache:", error);
    }
};

/**
 * Load permissions from cache
 */
const loadFromCache = (): PermissionCache | null => {
    try {
        const cached = localStorage.getItem(PERMISSION_CACHE_KEY);
        if (!cached) return null;

        const cache: PermissionCache = JSON.parse(cached);
        const now = Date.now();

        // Check if cache is expired
        if (now - cache.timestamp > CACHE_DURATION) {
            localStorage.removeItem(PERMISSION_CACHE_KEY);
            return null;
        }

        return cache;
    } catch (error) {
        console.error("Failed to load permissions from cache:", error);
        localStorage.removeItem(PERMISSION_CACHE_KEY);
        return null;
    }
};

/**
 * Clear permissions cache
 */
export const clearPermissionsCache = (): void => {
    try {
        localStorage.removeItem(PERMISSION_CACHE_KEY);
    } catch (error) {
        console.error("Failed to clear permissions cache:", error);
    }
};

/**
 * Fetch all system permissions
 */
export const fetchSystemPermissions = createAsyncThunk<
    FetchSystemPermissionsPayload,
    void,
    { rejectValue: PermissionErrorResponse }
>("permissions/fetchSystemPermissions", async (_, { rejectWithValue }) => {
    try {
        const response = await userAxios.get("/v1/permissions");
        const permissions: Permission[] = response.data;

        if (!Array.isArray(permissions)) {
            throw new Error("Invalid permissions response format");
        }

        return {
            permissions,
            timestamp: Date.now(),
        };
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch system permissions";
        const axiosError = error as { response?: { data?: { message?: string; details?: string } } };

        return rejectWithValue({
            message: axiosError.response?.data?.message || errorMessage,
            details: axiosError.response?.data?.details,
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * Fetch user permissions based on role
 */
export const fetchUserPermissions = createAsyncThunk<
    FetchUserPermissionsPayload,
    void,
    { rejectValue: PermissionErrorResponse }
>("permissions/fetchUserPermissions", async (_, { rejectWithValue }) => {
    try {
        const token = cookies.getToken();
        if (!token) {
            throw new Error("No authentication token found");
        }

        const decodedToken = jwtDecode(token) as DecodedToken;
        const roleId =
            typeof decodedToken?.roleid === "string" ? parseInt(decodedToken.roleid, 10) : decodedToken?.roleid;
        const userId = decodedToken?.userId;

        console.log("🔍 fetchUserPermissions - Token decoded:", {
            roleId,
            userId,
            roleIdType: typeof roleId,
            decodedToken,
        });

        if (roleId === undefined || roleId === null || !userId) {
            console.error("❌ fetchUserPermissions - Invalid token data:", { roleId, userId, decodedToken });
            throw new Error("Invalid token: missing role or user ID");
        }

        let userPermissionNames: string[];

        // Special handling for Super Admin (roleId 0)
        if (roleId === 0) {
            // Super Admin gets all available permissions
            const systemPermissionsResponse = await userAxios.get("/v1/permissions");
            const allPermissions: Permission[] = systemPermissionsResponse.data;

            if (!Array.isArray(allPermissions)) {
                throw new Error("Invalid system permissions response format");
            }

            // Grant all permissions to Super Admin
            userPermissionNames = allPermissions.map((p) => p.name);

            console.log(`Super Admin (roleId: 0) granted ${userPermissionNames.length} permissions`);
        } else {
            // Regular role - fetch role details including permissions
            const roleResponse = await userAxios.get(`/v1/role/${roleId}`);
            const roleData = roleResponse.data;

            if (!roleData || !Array.isArray(roleData.permissions)) {
                throw new Error("Invalid role response format");
            }

            // Get permission names from IDs
            const systemPermissionsResponse = await userAxios.get("/v1/permissions");
            const allPermissions: Permission[] = systemPermissionsResponse.data;

            userPermissionNames = roleData.permissions
                .map((permId: number) => {
                    const perm = allPermissions.find((p) => p.id === permId);
                    return perm?.name;
                })
                .filter(Boolean);
        }

        const sessionId = generateSessionId();

        return {
            permissions: userPermissionNames,
            timestamp: Date.now(),
            sessionId,
            userId: userId.toString(),
        };
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch user permissions";
        const axiosError = error as { response?: { data?: { message?: string; details?: string } } };

        return rejectWithValue({
            message: axiosError.response?.data?.message || errorMessage,
            details: axiosError.response?.data?.details,
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * Initialize permissions system - fetch both system and user permissions
 */
export const initializePermissions = createAsyncThunk<
    InitializePermissionsPayload,
    { forceRefresh?: boolean },
    { rejectValue: PermissionErrorResponse; state: RootState }
>("permissions/initialize", async ({ forceRefresh = false }, { rejectWithValue, dispatch }) => {
    try {
        const userId = getUserId();
        const sessionId = generateSessionId();

        if (!userId) {
            throw new Error("User not authenticated");
        }

        // Try to load from cache first (unless force refresh)
        if (!forceRefresh) {
            const cached = loadFromCache();
            if (cached && cached.userId === userId && cached.sessionId === sessionId) {
                // Validate cache is still fresh for this session
                const now = Date.now();
                if (now - cached.timestamp < SESSION_CACHE_DURATION) {
                    return {
                        systemPermissions: cached.systemPermissions,
                        userPermissions: cached.userPermissions,
                        sessionId: cached.sessionId,
                        userId: cached.userId,
                        fromCache: true,
                    };
                }
            }
        }

        // Fetch fresh data
        console.log("🔄 initializePermissions - Starting fresh data fetch for userId:", userId);

        try {
            const [systemResult, userResult] = await Promise.all([
                dispatch(fetchSystemPermissions()).unwrap(),
                dispatch(fetchUserPermissions()).unwrap(),
            ]);

            console.log("✅ initializePermissions - Successfully fetched permissions:", {
                systemPermissions: systemResult.permissions.length,
                userPermissions: userResult.permissions.length,
            });

            // Save to cache
            const cacheData: PermissionCache = {
                systemPermissions: systemResult.permissions,
                userPermissions: userResult.permissions,
                sessionId: userResult.sessionId,
                userId: userResult.userId,
                timestamp: Date.now(),
            };
            saveToCache(cacheData);

            return {
                systemPermissions: systemResult.permissions,
                userPermissions: userResult.permissions,
                sessionId: userResult.sessionId,
                userId: userResult.userId,
                fromCache: false,
            };
        } catch (fetchError) {
            console.error("❌ initializePermissions - Error during permission fetch:", fetchError);
            throw fetchError;
        }
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Failed to initialize permissions";
        const errorDetails =
            error instanceof Error && "details" in error ? (error as Error & { details?: string }).details : undefined;

        return rejectWithValue({
            message: errorMessage,
            details: errorDetails,
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * Clear permissions on logout
 */
export const clearPermissions = createAsyncThunk("permissions/clear", async () => {
    clearPermissionsCache();
    return true;
});

