import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios as axios } from "@/api/axios";
import { RootState } from "..";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";

export const getTrustedDevices = createAsyncThunk(
    "settings/getTrustedDevices",
    async (_, { rejectWithValue, getState }) => {
        try {
            const state = getState() as RootState;
            const { corporateId } = state.corporate;

            const res = await axios.get("/v1/trusted-device/user");
            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            sendCatchFeedback(error);
            return rejectWithValue(error.response?.data || error.message || "Failed to fetch trusted devices");
        }
    }
);

export const removeDevice = createAsyncThunk(
    "settings/removeDevice",
    async ({ deviceId }: { deviceId: number }, { rejectWithValue, dispatch }) => {
        try {
            // In a real implementation, this would be a DELETE request
            const res = await axios.delete(`/v1/trusted-device/${deviceId}`);
            sendFeedback("Device removed successfully", "success");

            // Refresh the devices list after successful removal
            dispatch(getTrustedDevices());

            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            sendCatchFeedback(error);
            return rejectWithValue(error.response?.data || error.message || "Failed to remove device");
        }
    }
);

export const removeAllDevices = createAsyncThunk(
    "settings/removeAllDevices",
    async (_, { rejectWithValue, dispatch }) => {
        try {
            // In a real implementation, this would be a DELETE request
            const res = await axios.delete("/v1/trusted-device");
            sendFeedback("All devices removed successfully", "success");

            // Refresh the devices list after successful removal
            dispatch(getTrustedDevices());

            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            sendCatchFeedback(error);
            return rejectWithValue(error.response?.data || error.message || "Failed to remove all devices");
        }
    }
);
