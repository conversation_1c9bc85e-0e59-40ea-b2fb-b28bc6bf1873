import { userAxios as axios } from "@/api/axios";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../index";

export const getAllRoles = createAsyncThunk("roles/getAllRoles", async (_, { rejectWithValue, getState }) => {
    try {
        const state = getState() as RootState;

        const res = await axios.get("/v1/role/corporate");
        return res.data;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
        return rejectWithValue(error.response.data);
    }
});

export const getRoleById = createAsyncThunk("roles/getRoleById", async (roleId: number, { rejectWithValue }) => {
    try {
        // Special handling for Super Admin (roleId 0)
        if (roleId === 0) {
            // Super Admin gets all available permissions
            const permissionsRes = await axios.get("/v1/permissions");
            const allPermissions = permissionsRes.data;

            if (!Array.isArray(allPermissions)) {
                throw new Error("Invalid permissions response format");
            }

            // Create a mock role data structure for Super Admin with all permissions
            const superAdminRole = {
                id: 0,
                name: "Super Admin",
                type: "ADMIN",
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                permissions: allPermissions.map((p: any) => p.id), // All permission IDs
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };

            console.log(`Super Admin role data created with ${allPermissions.length} permissions`);
            return superAdminRole;
        } else {
            // Regular role - fetch role details from API
            const res = await axios.get(`/v1/role/${roleId}`);
            return res.data;
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
        return rejectWithValue(error.response.data);
    }
});

// If your enum is defined like this:
export enum RoleType {
    CUSTOM = "CUSTOM",
    ADMIN = "ADMIN",
}

export type createRoleData = {
    name: string;
    type: RoleType.CUSTOM;
    // corporateId: number;
    permissions: number[]; // Alternative array syntax
};

// Then use it in your type definition:
export const createRole = createAsyncThunk(
    "roles/createRole",
    async (createRoleData: createRoleData, { rejectWithValue }) => {
        try {
            const res = await axios.post("/v1/role", createRoleData);
            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Then use it in your type definition:
export const editRole = createAsyncThunk(
    "roles/editRole",
    async ({ editRoleData, roleId }: { editRoleData: createRoleData; roleId: number }, { rejectWithValue }) => {
        try {
            // Special handling for Super Admin (roleId 0)
            if (roleId === 0) {
                // Super Admin role cannot be edited through normal API
                // Return an error or handle this case appropriately
                throw new Error("Super Admin role cannot be edited");
            }

            const res = await axios.put(`/v1/role/${roleId}`, editRoleData);
            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const getAllPermissions = createAsyncThunk("roles/getAllPermissions", async (_, { rejectWithValue }) => {
    try {
        const res = await axios.get("/v1/permissions");
        return res.data;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
        return rejectWithValue(error.response.data);
    }
});
