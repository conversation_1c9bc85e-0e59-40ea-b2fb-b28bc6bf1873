import { acctsAxios, transferAxios, userAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { AxiosError } from "axios";
import { BusinessInfoResponse, IBusinessOwner } from "../slices/onboarding/businessOwnersSlice";
import { setBusinessInfoResponse } from "../slices/onboardingSlice";
import {
    BusinessDocumentType,
    BusinessInformationAccountForm,
    BusinessValidationForm,
} from "../types/digital-onboarding";

export const getForeignCountries = createAsyncThunk("getForeignCountries", async (_, { rejectWithValue }) => {
    try {
        const response = await transferAxios.get("/foreign-beneficiaries/countries");
        return response.data;
    } catch (error) {
        sendCatchFeedback(error);
        return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
    }
});

export const getOnboarding = createAsyncThunk("getOnboarding", async (_, { rejectWithValue }) => {
    try {
        const response = await acctsAxios.get("/api/v1/digital-account/corporate");
        return response.data;
    } catch (error) {
        return rejectWithValue({
            message: (error as Error)?.message || "An error occurred",
        });
    }
});

export const deleteApplicationDraft = createAsyncThunk(
    "deleteApplicationDraft",
    async (body: { onboarding: BusinessInfoResponse | null }, { rejectWithValue }) => {
        try {
            const id = body.onboarding?.id;

            const response = await acctsAxios.delete(`/api/v1/digital-account/${id}`);
            return response.data;
        } catch (error) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

export const businessValidationForm = createAsyncThunk(
    "digitalOnboarding/businessValidationForm",
    async (body: BusinessValidationForm, { rejectWithValue }) => {
        try {
            const requestPayload = {
                rc: body.businessRegistrationNumber,
                tin: body.taxIdentificationNumber,
            };

            const res = await userAxios.post("/v1/corporate/validate-rc-tin", requestPayload, {
                headers: {
                    "Content-Type": "application/json",
                },
            });
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

// Mock behavior
// export const businessValidationForm = createAsyncThunk(
//     "digitalOnboarding/businessValidationForm",
//     async (body: BusinessValidationForm, { rejectWithValue }) => {
//         try {
//             return await new Promise<{ tinSuccess: boolean; rcsuccess: boolean }>(
//                 (resolve) =>
//                     setTimeout(() => {
//                         resolve({
//                             tinSuccess: true,
//                             rcsuccess: true,
//                         });
//                     }, 3000) // Simulating 3 seconds delay
//             );
//         } catch (error: unknown) {
//             sendCatchFeedback(error);
//             return rejectWithValue({
//                 message: (error as Error)?.message || "An error occurred",
//             });
//         }
//     }
// );

export const businessInformationAccountForm = createAsyncThunk(
    "digitalOnboarding/businessInformationAccountForm",
    async (body: BusinessInformationAccountForm, { dispatch, rejectWithValue, getState }) => {
        try {
            const state = getState() as {
                onboarding: { businessRegistrationNumber: string; taxIdentificationNumber: string };
            };
            const businessRegNum = state.onboarding.businessRegistrationNumber;
            const taxIdNum = state.onboarding.taxIdentificationNumber;
            const formData = new FormData();
            const requestPayload = {
                legalBusinessName: body.legalBusinessName,
                isTradingNameSameAsBusinessName: body.isBusinessNameTradingName,
                tradingName: body.tradingName,
                countryOfRegistration: body.countryOfRegistration,
                businessIndustry: body.businessIndustry,
                businessCategory: body.businessCategory,
                businessDescription: body.businessDescription,
                registrationType: body.businessRegistrationType,
                businessEmail: body.businessEmailAddress,
                businessWebsite: body.businessWebsite,
                businessAddress: body.businessAddress,
                city: body.city,
                state: body.state,
                isAddressDifferentFromOperational: body.isAddressDifferentFromOperational,
                businessRegistrationNumber: businessRegNum,
                taxIdentificationNumber: taxIdNum,
                yearOfRegistration: body.yearOfRegistration,
            };

            formData.append("request", JSON.stringify(requestPayload));
            if (body.proofOfAddress instanceof File) {
                formData.append("proofOfAddress", body.proofOfAddress);
            }

            const res = await acctsAxios.post("/api/v1/digital-account", formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            dispatch(setBusinessInfoResponse(res.data));

            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

export const updateBusinessInformationAccountForm = createAsyncThunk(
    "digitalOnboarding/updateBusinessInformationAccountForm",
    async (
        body: BusinessInformationAccountForm & { onboarding?: BusinessInfoResponse | null },
        { dispatch, rejectWithValue }
    ) => {
        try {
            const id = body?.onboarding?.id;
            const businessRegNum = body?.onboarding?.businessRegistrationNumber;
            const taxIdNum = body?.onboarding?.taxIdentificationNumber;
            const formData = new FormData();
            const requestPayload = {
                legalBusinessName: body.legalBusinessName,
                isTradingNameSameAsBusinessName: body.isBusinessNameTradingName,
                tradingName: body.tradingName,
                countryOfRegistration: body.countryOfRegistration,
                businessIndustry: body.businessIndustry,
                businessCategory: body.businessCategory,
                businessDescription: body.businessDescription,
                registrationType: body.businessRegistrationType,
                businessEmail: body.businessEmailAddress,
                businessWebsite: body.businessWebsite,
                businessAddress: body.businessAddress,
                city: body.city,
                state: body.state,
                isAddressDifferentFromOperational: body.isAddressDifferentFromOperational,
                businessRegistrationNumber: businessRegNum,
                taxIdentificationNumber: taxIdNum,
                yearOfRegistration: body.yearOfRegistration,
            };

            formData.append("request", JSON.stringify(requestPayload));
            if (body.proofOfAddress instanceof File) {
                formData.append("proofOfAddress", body.proofOfAddress);
            }

            const res = await acctsAxios.put(`/api/v1/digital-account/${id}`, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            dispatch(setBusinessInfoResponse(res.data));

            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

export const businessOwnersAccountForm = createAsyncThunk(
    "digitalOnboarding/businessOwnersAccountForm",
    async (body: { businessOwners: IBusinessOwner[]; onboardingId: string }, { rejectWithValue }) => {
        try {
            const id = body?.onboardingId;

            const formData = new FormData();

            const requestPayload = {
                businessOwners: body.businessOwners.map((owner) => ({
                    // id: index + 1,
                    bvn: owner.bvn,
                    fullLegalName: owner.fullLegalName,
                    dateOfBirth: new Date(owner.dateOfBirth).toISOString().split("T")[0],
                    nationality: owner.nationality,
                    role: owner.role,
                    ownershipPercentage: parseFloat(owner.ownershipPercentage),
                    identityDocumentType: owner.identityDocumentType,
                    residentialAddress: owner.residentialAddress,
                    city: owner.city,
                    state: owner.state,
                    proofOfAddressType: owner.proofOfAddressType,
                    ...(owner.documents?.length > 0
                        ? {
                              documents: owner.documents?.map((item) => ({
                                  documentType: item.documentType,
                              })),
                          }
                        : {}),
                })),
            };

            formData.append("request", JSON.stringify(requestPayload));

            body.businessOwners.forEach((owner) => {
                owner.documents?.forEach((doc, index) => {
                    if (doc?.file) {
                        formData.append(`owners[${index}].documents[${index}].file`, doc?.file as File); // File is a Blob
                    } else {
                        // Assume it's FileMetadata (or another non-Blob type)
                        formData.append(`owners[${index}].documents[${index}].metadata`, JSON.stringify(doc?.file));
                    }
                });
            });

            const res = await acctsAxios.put(`/api/v1/digital-account/${id}`, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

export const uploadBusinessDocument = createAsyncThunk(
    "digitalOnboarding/uploadBusinessDocument",
    async (
        payload: {
            documentType: BusinessDocumentType;
            file: File;
            onboardingId: string;
        },
        { rejectWithValue }
    ) => {
        try {
            const id = payload.onboardingId;

            const formData = new FormData();
            formData.append("documentType", payload.documentType);
            formData.append("file", payload.file);

            const res = await acctsAxios.post(`/api/v1/digital-account/${id}/documents`, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: (error as Error)?.message || "An error occurred",
            });
        }
    }
);

export const submitDigitalApplication = createAsyncThunk(
    "digitalOnboarding/submitDigitalApplication",
    async (payload: { onboardingId?: number }, { rejectWithValue }) => {
        try {
            const res = await acctsAxios.post(`/api/v1/digital-account/${payload.onboardingId}/submit`);

            return res.data;
        } catch (err) {
            const error = err as AxiosError<{ message?: string }>;
            return rejectWithValue({
                message: error.response?.data?.message || "An error occurred",
            });
        }
    }
);
