import { createAsyncThunk } from "@reduxjs/toolkit";
import { billAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { BillCategoriesResponse, RecentBillsResponse } from "../types/billPayments";
import { RootState } from "..";

interface ApiErrorResponse {
    response?: {
        data?: {
            message?: string;
            globalisationMessageCode?: string;
        };
    };
    message?: string;
}

interface PaymentResponse {
    message?: string;
    transactionReference?: string;
    miscData?: string;
    pin?: string;
    responseCode?: string;
}

interface RawBillCategory {
    id?: number;
    categoryid?: number;
    categoryName?: string;
    categoryname?: string;
    description?: string;
    categorydescription?: string;
}

/**
 * Fetches bill categories from the API
 * Includes optimization to avoid redundant API calls by checking if data already exists
 *
 * @param params.page - The page number to fetch (defaults to 1)
 * @param params.size - The number of items per page (defaults to 100)
 * @param params.search - Optional search term to filter categories
 * @param params.forceRefresh - Force a refresh even if categories already exist (defaults to false)
 * @returns The API response containing paginated bill categories
 */
export const fetchBillCategories = createAsyncThunk(
    "billPayments/fetchCategories",
    async (
        params: { page?: number; size?: number; search?: string; forceRefresh?: boolean } | void,
        { rejectWithValue, getState }
    ) => {
        try {
            const { page = 1, size = 100, forceRefresh = false } = params || {};

            // Get current state
            const state = getState() as RootState;
            const { content: existingCategories = [] } = state.billPayments.categories || {};

            // Skip API call if categories already exist and forceRefresh is false
            if (!forceRefresh && existingCategories.length > 0) {
                // Return the existing data as if it came from the API
                return state.billPayments.categories;
            }

            // Otherwise, proceed with the API call
            const response = await billAxios.get<BillCategoriesResponse>("/api/v1/vas/biller-categories", {
                params: { page, size },
            });

            // Map the response to match our standardized camelCase interface
            if (response.data.content) {
                response.data.content = response.data.content.map((category: RawBillCategory) =>
                    // Convert any lowercase properties to camelCase
                    ({
                        id: category.id ?? category.categoryid,
                        categoryName: category.categoryName ?? category.categoryname,
                        description: category.description ?? category.categorydescription,
                    })
                );
            }

            return response.data;
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;

            // Check if this is a timeout error
            const isTimeoutError = typedError.message?.includes("timeout") || typedError.message?.includes("exceeded");

            // Create a user-friendly error message
            const errorMessage = isTimeoutError
                ? "Unable to load bill categories at this time"
                : typedError.response?.data?.message ||
                  typedError.response?.data?.globalisationMessageCode ||
                  "Failed to fetch bill categories";

            // Only show feedback for non-timeout errors
            // This follows the frontend guideline to not show error toasts for API calls
            // that don't directly affect user actions (like initial data loading)
            if (!isTimeoutError) {
                sendCatchFeedback(error);
            }

            return rejectWithValue(errorMessage);
        }
    }
);

/**
 * Fetches billers for a specific category with enhanced category-aware caching.
 * This thunk implements smart caching to prevent redundant API calls by checking
 * if cached data exists for the specific category and is still fresh.
 *
 * RULE COMPLIANCE:
 * - Uses axios instances from api/axios.ts
 * - Implements comprehensive caching logic to optimize performance for million+ users
 * - Uses sendCatchFeedback for user-initiated failures only
 * - Doesn't show error toasts for initial data loading
 *
 * @param params.categoryId - The category ID to fetch billers for (e.g., 4 for airtime)
 * @param params.forceRefresh - Optional flag to force a fresh API call even if data exists
 * @returns Object containing billers data array and categoryId for state management
 */
export const fetchBillersByCategory = createAsyncThunk(
    "billPayments/fetchBillersByCategory",
    async (params: { categoryId: number; forceRefresh?: boolean }, { rejectWithValue, getState }) => {
        try {
            const { categoryId, forceRefresh = false } = params;

            // Get current state for category-aware caching
            const state = getState() as RootState;
            const existingBillers = state.billPayments.biller.data;
            const lastFetched = state.billPayments.biller.lastFetched;
            const cachedCategoryId = state.billPayments.biller.categoryId;

            // Enhanced cache duration: 10 minutes for better performance
            const CACHE_DURATION = 10 * 60 * 1000;
            const isDataStale = lastFetched ? Date.now() - lastFetched > CACHE_DURATION : true;

            // Category-aware caching: only use cached data if it matches the requested category
            const isCategoryMatch = cachedCategoryId === categoryId;

            // Skip API call if data exists for the same category, is not stale, and forceRefresh is false
            // This prevents the inconsistent caching behavior observed in bill details components
            // FIXED: Removed existingBillers.length > 0 condition to properly cache empty arrays
            // Empty arrays are valid API responses and should be cached to prevent infinite fetching
            if (!forceRefresh && existingBillers !== null && !isDataStale && isCategoryMatch) {
                // Return cached data with categoryId for consistent state management
                return {
                    billers: existingBillers,
                    categoryId: categoryId,
                };
            }

            // Fetch fresh data from API
            const response = await billAxios.get("/api/v1/vas/billers", {
                params: {
                    categoryId,
                    page: 1,
                    size: 100,
                },
            });

            // Validate API response structure
            if (!response.data || !response.data.content || !Array.isArray(response.data.content)) {
                return rejectWithValue("Invalid response format from billers API");
            }

            // Return both billers data and categoryId for enhanced state management
            return {
                billers: response.data.content,
                categoryId: categoryId,
            };
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;
            // RULE COMPLIANCE: Don't show error toasts for initial data loading
            // Only log the error rather than showing it to the user
            return rejectWithValue(typedError.response?.data?.message || "Failed to fetch billers");
        }
    }
);

export const fetchPaymentItems = createAsyncThunk(
    "billPayments/fetchPaymentItems",
    async (params: { billerId: string; page?: number; size?: number }, { rejectWithValue }) => {
        try {
            const { billerId, page = 1, size = 50 } = params;

            // Ensure we're using the correct endpoint format
            const response = await billAxios.get("/api/v1/vas/bill-payment-item", {
                params: { billerId, page, size },
            });

            // Check if the response has the expected structure
            if (!response.data || !response.data.content) {
                throw new Error("Invalid response format from payment items API");
            }

            return response.data;
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;
            const errorMessage =
                typedError.response?.data?.message ||
                (error instanceof Error ? error.message : "Failed to fetch payment items");

            sendCatchFeedback({
                message: errorMessage,
                type: "error",
            });

            return rejectWithValue(errorMessage);
        }
    }
);

export const initiateBillPayment = createAsyncThunk(
    "billPayments/initiateBillPayment",
    async (
        params: {
            customerId: string;
            amount: number;
            paymentCode: string;
            customerMobile: string;
            accountNumber: string;
            customerEmail: string;
            requestReference: string;
            requiresApproval: boolean;
            biller: string;
            billType: string;
            narration: string;
            transactionPin: string;
            mfaToken?: string; // MFA token from 2FA verification
        },
        { rejectWithValue }
    ) => {
        try {
            // Extract mfaToken from params before making API call
            const { mfaToken, ...apiParams } = params;

            // Add MFA token as query parameter if provided
            const url = mfaToken ? `api/v1/vas/payments?token=${encodeURIComponent(mfaToken)}` : "api/v1/vas/payments";
            const response = await billAxios.post<PaymentResponse>(url, apiParams);

            if (response.data.message === "Your request is being processed") {
                sendFeedback("Your request is being processed", "success");
                return response.data;
            }

            if (response.data.transactionReference && response.data.responseCode) {
                if (response.data.responseCode !== "SUCCESS") {
                    const errorMessage = "Payment initiation failed";
                    sendCatchFeedback(new Error(errorMessage));
                    return rejectWithValue(errorMessage);
                }

                sendFeedback("Your request is being processed", "success");
                return response.data;
            }

            const errorMessage = "Unexpected payment response format";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;
            const errorMessage = typedError.response?.data?.message || "Failed to initiate payment";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }
    }
);

/**
 * Initiates bulk airtime payments
 * Following Core Principle #2: Use empty strings instead of null values for VAS endpoints
 * corporateId has been removed from parameters as it's no longer required
 * MFA token is required and always passed as query parameter for security
 *
 * @param params - Array of payment objects with recipient and payment details (must include mfaToken)
 * @returns The API response confirming payment processing
 */
export const initiateBulkAirtimePayment = createAsyncThunk(
    "billPayments/initiateBulkAirtimePayment",
    async (
        params: {
            paymentCode: string;
            customerId: string | null;
            customerMobile: string | null;
            customerEmail: string | null;
            amount: string;
            requestReference: string;
            accountNumber: string;
            requiresApproval: boolean;
            biller: string;
            billType: string;
            narration: string;
            transactionPin: string;
            mfaToken: string; // MFA token from 2FA verification (required)
        }[],
        { rejectWithValue }
    ) => {
        try {
            // Validate that we have payment objects
            if (!params || params.length === 0) {
                const errorMessage = "No payment requests provided";
                sendCatchFeedback(new Error(errorMessage));
                return rejectWithValue(errorMessage);
            }

            // Extract mfaToken from the first payment object (all payments in bulk should have same token)
            const { mfaToken } = params[0];

            // Validate that mfaToken is present and not empty
            if (!mfaToken || mfaToken.trim() === "") {
                const errorMessage = "MFA token is required for bulk payment processing";
                sendCatchFeedback(new Error(errorMessage));
                return rejectWithValue(errorMessage);
            }

            // Remove mfaToken from each payment object before sending to API
            const apiParams = params.map(({ mfaToken: _, ...payment }) => payment);

            // Always include MFA token as query parameter (required for security)
            const url = `api/v1/vas/bulk-payments?token=${encodeURIComponent(mfaToken)}`;

            const response = await billAxios.post(url, apiParams);

            if (response.data.message === "Your request is being processed") {
                sendFeedback("Your request is being processed", "success");
                return response.data;
            }

            const errorMessage = "Unexpected payment response format";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;
            const errorMessage = typedError.response?.data?.message || "Failed to initiate bulk payment";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }
    }
);

/**
 * Fetches recent bill payments from the API
 * Following Core Principle #4: Don't show error toasts for initial data loading
 *
 * @param params.pageNo - The page number to fetch (defaults to 1)
 * @param params.pageSize - The number of items per page (defaults to 15)
 * @returns The API response containing paginated recent bill payments
 */
export const fetchRecentBills = createAsyncThunk(
    "billPayments/fetchRecentBills",
    async (params: { pageNo?: number; pageSize?: number } | void, { rejectWithValue }) => {
        try {
            const { pageNo = 1, pageSize = 15 } = params || {};

            // Use billAxios instance for API calls as per Core Principle #1
            const response = await billAxios.get<RecentBillsResponse>("/api/v1/vas/payments", {
                params: { pageNo, pageSize },
            });

            // Return the full response to be processed in the slice
            return response.data;
        } catch (error: unknown) {
            const typedError = error as ApiErrorResponse;

            // Check if this is a timeout error
            const isTimeoutError = typedError.message?.includes("timeout") || typedError.message?.includes("exceeded");

            // Create a user-friendly error message
            const errorMessage = isTimeoutError
                ? "Unable to load recent bills at this time"
                : typedError.response?.data?.message ||
                  typedError.response?.data?.globalisationMessageCode ||
                  "Failed to fetch recent bills";

            // Following Core Principle #4: Don't show error toasts for initial data loading
            // We're not calling sendCatchFeedback here

            return rejectWithValue(errorMessage);
        }
    }
);
