import { transferAxios } from "@/api/axios";
import { isProductionENV } from "@/functions/environmentVariables";
import { sendCatchFeedback } from "@/functions/feedback";
import { AddLocalRecipientBody, IFetchRecipientDetailsBody, IGetRecipientQueryParams } from "@/redux/types/recipient";
import { createAsyncThunk } from "@reduxjs/toolkit";

// Add Local Recipient
export const addLocalRecipient = createAsyncThunk(
    "recipient/addLocalRecipient",
    async (body: AddLocalRecipientBody, { rejectWithValue }) => {
        try {
            const res = await transferAxios.post("/local-beneficiaries", body);
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Get Local Recipient
export const getLocalRecipientAction = createAsyncThunk(
    "recipient/getLocalRecipient",
    async ({ params }: { params: IGetRecipientQueryParams }, { rejectWithValue }) => {
        try {
            const res = await transferAxios.get("/local-beneficiaries", { params });
            return res.data;
        } catch (error) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Remove Local Recipient
export const removeLocalRecipientAction = createAsyncThunk(
    "recipient/removeLocalRecipient",
    async (recipientId: string, { rejectWithValue }) => {
        try {
            await transferAxios.delete(`/local-beneficiaries/${recipientId}`);
            return recipientId;
        } catch (error) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Get Local Banks
export const getLocalBanksAction = createAsyncThunk("recipient/getLocalBanksAction", async (_, { rejectWithValue }) => {
    try {
        const res = await transferAxios.get("banks");
        return res.data.data;
    } catch (error) {
        sendCatchFeedback(error);
        return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
    }
});

// Fetch Recipient Details
export const fetchRecipientDetails = createAsyncThunk(
    "recipient/fetchRecipientDetails",
    async (body: IFetchRecipientDetailsBody, { rejectWithValue }) => {
        try {
            if (!isProductionENV)
                // Mock the api call
                return await new Promise<RecipientDetailsResponse>(
                    (resolve) =>
                        setTimeout(() => {
                            resolve({
                                sessionID: "999214250304143102000001434375",
                                destinationInstitutionCode: "999998",
                                channelCode: "01",
                                accountNumber: "**********",
                                accountName: "Test Account",
                                bankVerificationNumber: "***********",
                                kycLevel: "1",
                                responseCode: "00",
                            });
                        }, 1000) // Simulating 1 second delay
                );
            const res = await transferAxios.post("/nip/name-enquiry", body);
            return res.data;
        } catch (error) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Define the expected response type
interface RecipientDetailsResponse {
    sessionID: string;
    destinationInstitutionCode: string;
    channelCode: string;
    accountNumber: string;
    accountName: string;
    bankVerificationNumber: string;
    kycLevel: string;
    responseCode: string;
}

// Mock Fetch Recipient Details
// export const fetchRecipientDetails = createAsyncThunk<RecipientDetailsResponse, IFetchRecipientDetailsBody>(
//     "recipient/fetchRecipientDetails",
//     async (body, { rejectWithValue }) => {
//         try {
//             return await new Promise<RecipientDetailsResponse>(
//                 (resolve) =>
//                     setTimeout(() => {
//                         resolve({
//                             sessionID: "999214250304143102000001434375",
//                             destinationInstitutionCode: "999998",
//                             channelCode: "01",
//                             accountNumber: "**********",
//                             accountName: "Test Account",
//                             bankVerificationNumber: "***********",
//                             kycLevel: "1",
//                             responseCode: "00",
//                         });
//                     }, 5000) // Simulating 5 seconds delay
//             );
//         } catch (error) {
//             return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
//         }
//     }
// );

export const getNameEnquiry = async () => {
    try {
        return await new Promise<RecipientDetailsResponse>(
            (resolve) =>
                setTimeout(() => {
                    resolve({
                        sessionID: "999214250304143102000001434375",
                        destinationInstitutionCode: "999998",
                        channelCode: "01",
                        accountNumber: "**********",
                        accountName: "OgeTest",
                        bankVerificationNumber: "***********",
                        kycLevel: "1",
                        responseCode: "00",
                    });
                }, 5000) // Simulating 5 seconds delay
        );
    } catch (error) {
        return error;
    }
};
