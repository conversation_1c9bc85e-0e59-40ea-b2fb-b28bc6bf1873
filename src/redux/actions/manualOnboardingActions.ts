import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { AccountFormUploadBody, FormSubmitBody } from "../types/manual-onboarding";

// Manual Account opening form upload
export const uploadManualAccountForm = createAsyncThunk(
    "manualOnboarding/uploadManualAccountForm",
    async (body: AccountFormUploadBody, { rejectWithValue }) => {
        try {
            const formData = new FormData();

            formData.append("file", body.file);
            formData.append("corporateId", "1");
            const res = await acctsAxios.post(
                `/api/v1/manual-account-opening/upload?fileName=${body.file.name}`,
                formData,
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                }
            );
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Submit documentation form
export const submitDocumentationForm = createAsyncThunk(
    "manualOnboarding/submitDocumentationForm",
    async (body: FormSubmitBody, { rejectWithValue }) => {
        try {
            const formData = new FormData();

            formData.append("file", body.file);

            const res = await acctsAxios.post(
                `/api/v1/manual-account-opening/supporting-documents?documentType=${body.documentType}&fileName=${body.file.name}`,
                formData,
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                }
            );
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);

// Conclude manual onboarding
export const concludeManualOnboarding = createAsyncThunk(
    "manualOnboarding/concludeManualOnboarding",
    async (body, { rejectWithValue }) => {
        try {
            const res = await acctsAxios.post("api/v1/manual-account-opening/update-business-application-status");
            return res.data;
        } catch (error: unknown) {
            sendCatchFeedback(error);
            return rejectWithValue({ message: (error as Error)?.message || "An error occurred" });
        }
    }
);
