import { userAxios as axios } from "@/api/axios";
import { exportAsCSV, exportAsPDF } from "@/functions/exportDocument";
import { sendCatchFeedback } from "@/functions/feedback";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { ActivityLogsFilters, DateRangeValues, ExportActivityLogsPayload } from "../types/activityLogs";

export const getActivityLogs = createAsyncThunk(
    "activityLogs/getActivityLogs",
    async (filters: ActivityLogsFilters = {}, { rejectWithValue }) => {
        try {
            const params = new URLSearchParams();

            // Add filters to params if they exist
            if (filters.startDate) params.append("startDate", filters.startDate);
            if (filters.endDate) params.append("endDate", filters.endDate);
            if (filters.roleId) params.append("roleId", filters.roleId.toString());
            if (filters.actionTypes) params.append("actionTypes", filters.actionTypes);
            if (filters.searchKeyword) params.append("searchKeyword", filters.searchKeyword);
            if (filters.page !== undefined) params.append("page", filters.page.toString());
            if (filters.size !== undefined) params.append("size", filters.size.toString());
            if (filters.sortBy) params.append("sortBy", filters.sortBy);
            if (filters.sortDirection) params.append("sortDirection", filters.sortDirection);

            const queryString = params.toString();
            const url = queryString ? `/v1/activity-logs?${queryString}` : "/v1/activity-logs";

            const res = await axios.get(url);
            return res.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: error.response?.data?.message || "Failed to fetch activity logs",
                details: error.response?.data?.details,
                timestamp: error.response?.data?.timestamp,
            });
        }
    }
);

export const exportActivityLogs = createAsyncThunk(
    "activityLogs/exportActivityLogs",
    async (payload: ExportActivityLogsPayload, { rejectWithValue }) => {
        try {
            const {
                selectedRows,
                startDate,
                endDate,
                format: exportFormat,
                roleId,
                actionTypes,
                searchKeyword,
                dateRange,
            } = payload;

            const columns = [
                { header: "Performed By", key: "performedBy" },
                { header: "Role", key: "role" },
                { header: "Action", key: "action" },
                { header: "Time", key: "timestamp" },
                { header: "Location", key: "location" },
                { header: "IP Address", key: "ipAddress" },
            ];

            let exportData = selectedRows;

            // If no selected rows, fetch from API
            if (!selectedRows || selectedRows.length === 0) {
                interface Filters {
                    startDate?: string;
                    endDate?: string;
                    roleId?: number | string;
                    actionTypes?: string;
                    searchKeyword?: string;
                    dateRange: DateRangeValues;
                }

                const filters: Filters = {
                    dateRange,
                };
                if (startDate) filters.startDate = startDate;
                if (endDate) filters.endDate = endDate;
                if (roleId) filters.roleId = roleId;
                if (actionTypes) filters.actionTypes = actionTypes;
                if (searchKeyword) filters.searchKeyword = searchKeyword;

                const url = "/v1/activity-logs/export/data";
                const response = await axios.post(url, filters);
                exportData = response.data;

                if (!exportData || exportData.length === 0) {
                    sendCatchFeedback("No data available for the selected period");
                    return rejectWithValue({ message: "No data found" });
                }
            }

            const exportPayload = {
                data: exportData || [],
                columns,
                startDate,
                endDate,
                documentTitle: "Activity Logs",
            };

            if (exportFormat === "pdf") {
                exportAsPDF(exportPayload);
            } else {
                exportAsCSV(exportPayload);
            }

            return {
                success: true,
                message: `Activity logs exported successfully as ${exportFormat.toUpperCase()}`,
            };
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            sendCatchFeedback(error);
            return rejectWithValue({
                message: error.response?.data?.message || error.message || "Failed to export activity logs",
                details: error.response?.data?.details,
                timestamp: error.response?.data?.timestamp,
            });
        }
    }
);
