import { setStoreGetter } from "@/api/axios";
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query/react";
import { FLUSH, PAUSE, PERSIST, persistReducer, persistStore, PURGE, REGISTER, REHYDRATE } from "redux-persist";
import storage from "redux-persist/lib/storage";
import clearStateMiddleware from "./clearStateMiddleware";
import billPaymentClearMiddleware from "./middleware/billPaymentClearMiddleware";
import permissionsClearMiddleware from "./middleware/permissionsClearMiddleware";
import accountsReducer from "./features/accounts";
import { bulkAirtimeSlice } from "./features/bulkAirtime";
import { countriesSlice } from "./features/countries";
import multiPartyApprovalReducer from "./features/multiPartyApproval";
import { sendMoneyDialogSlice } from "./features/sendMoneyDialog";
import { supportDialogSlice } from "./features/supportDialog";
import uiDialogReducer from "./features/uiDialogSlice";
import { userSlice } from "./features/user";
import accountReducer from "./slices/accountSlice";
import approvalRuleReducer from "./slices/approvalRuleSlice";
import { resetPasswordSlice } from "./slices/auth/resetPasswordSlice";
import { signinSlice } from "./slices/auth/signinSlice";
import { signupSlice } from "./slices/auth/signupSlice";
import signupSliceReducer from "./slices/auth/signupSlice2";
import billPaymentsReducer from "./slices/billPaymentSlice";
import { corporateSlice } from "./slices/corporateSlice";
import { dashboardSlice } from "./slices/dashboardSlice";
import { manualOnboardingSlice } from "./slices/manualOnboardingSlice";
import businessOwnersReducer from "./slices/onboarding/businessOwnersSlice";
import outgoingFilterReducer from "./slices/outgoingFilterSlice";
import paymentRequestReducer from "./slices/payments/requestSlice";
import { recipientSlice } from "./slices/recipientsSlice";
import { securitySlice } from "./slices/securitySlice";
import { sendMoneySlice } from "./slices/sendMoneySlice";
import settingsReducer from "./slices/settingsSlice";
import singleBillPaymentReducer from "./slices/singleBillPayment";
import teamMembersReducer from "./slices/teamMembersSlice";
import transactionReducer from "./slices/transactionSlice";
import transferReducer from "./slices/transferSlice";
import onboardingReducer from "./slices/onboardingSlice";
import rolesReducer from "./slices/rolesSlice";
import transactionLimitsReducer from "./slices/transactionLimitsSlice";
import trustedDevicesReducer from "./slices/trustedDevices";
import twoFaReducer from "./slices/twoFaSlice";
import { roles } from "@/components/page-components/dashboard/onboarding/libs/mock-data";
import transferMfaSlice from "./slices/transferMfaSlice";
import activityLogsReducer from "./slices/activityLogsSlice";
import settingsMfaReducer from "./slices/settingsMfaSlice";
import permissionsReducer from "./slices/permissionsSlice";

export const rootReducer = combineReducers({
    [userSlice.name]: userSlice.reducer,
    [bulkAirtimeSlice.name]: bulkAirtimeSlice.reducer,
    [corporateSlice.name]: corporateSlice.reducer,
    [supportDialogSlice.name]: supportDialogSlice.reducer,
    [sendMoneyDialogSlice.name]: sendMoneyDialogSlice.reducer,
    singleBillPayment: singleBillPaymentReducer,
    accounts: accountsReducer,
    transaction: transactionReducer,
    billPayments: billPaymentsReducer,
    [signupSlice?.name]: signupSlice?.reducer,
    [signinSlice?.name]: signinSlice?.reducer,
    [resetPasswordSlice?.name]: resetPasswordSlice?.reducer,
    [recipientSlice?.name]: recipientSlice?.reducer,
    [countriesSlice.name]: countriesSlice.reducer,
    [dashboardSlice.name]: dashboardSlice.reducer,
    multiPartyApproval: multiPartyApprovalReducer,
    [sendMoneySlice.name]: sendMoneySlice.reducer,
    account: accountReducer,
    uiDialog: uiDialogReducer,
    businessOwners: businessOwnersReducer,
    paymentRequest: paymentRequestReducer,
    transfer: transferReducer,
    outgoingFilter: outgoingFilterReducer,
    approvalRule: approvalRuleReducer,
    teamMembers: teamMembersReducer,
    [securitySlice.name]: securitySlice.reducer,
    signupNew: signupSliceReducer,
    roles: rolesReducer,
    transactionLimits: transactionLimitsReducer,
    [manualOnboardingSlice.name]: manualOnboardingSlice.reducer,
    onboarding: onboardingReducer,
    settings: settingsReducer,
    trustedDevices: trustedDevicesReducer,
    twoFa: twoFaReducer,
    transferMfaSlice: transferMfaSlice,
    activityLogs: activityLogsReducer,
    settingsMfa: settingsMfaReducer,
    permissions: permissionsReducer,
});

const persistConfig = {
    key: "root",
    storage,
    whitelist: [
        "bulkAirtime",
        "singleBillPayment",
        "accounts",
        "billPayments",
        "multiPartyApproval",
        "signupNew",
        "permissions",
    ],
    blacklist: ["transaction", "transfer"],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
    preloadedState: {},
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
            },
        }).concat(clearStateMiddleware, billPaymentClearMiddleware, permissionsClearMiddleware),
});

// Set up store getter for axios
setStoreGetter(() => store);

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
setupListeners(store.dispatch);
