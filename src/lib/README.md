# Storage Manager

The `storageManager` is a utility for handling localStorage operations in a consistent way across the application.

## Features

- Automatically handles JSON parsing and stringifying
- Provides a consistent API for localStorage operations
- Handles server-side rendering (SSR) gracefully
- Includes error handling for JSON parsing

## Usage

### Storing Data

```typescript
import { storageManager } from "@/lib/custom";

// Store a string
storageManager.setItem("myString", "Hello World");

// Store an object (automatically stringified)
storageManager.setItem("myObject", { name: "<PERSON>", age: 30 });

// Store an array (automatically stringified)
storageManager.setItem("myArray", [1, 2, 3, 4, 5]);
```

### Retrieving Data

```typescript
import { storageManager } from "@/lib/custom";

// Get a string
const myString = storageManager.getItem("myString"); // "Hello World"

// Get an object (automatically parsed)
const myObject = storageManager.getItem("myObject"); // { name: "<PERSON>", age: 30 }

// Get an array (automatically parsed)
const myArray = storageManager.getItem("myArray"); // [1, 2, 3, 4, 5]

// Get a non-existent item
const nonExistent = storageManager.getItem("nonExistent"); // null

// Access properties safely with optional chaining
const username = storageManager.getItem("preLoginData")?.username ?? "";
```

### Removing Data

```typescript
import { storageManager } from "@/lib/custom";

// Remove an item
storageManager.removeItem("myString");

// Clear all items
storageManager.clear();
```

## Implementation Details

The `storageManager` handles:

1. **JSON Parsing**: Automatically parses JSON strings when retrieving data
2. **JSON Stringifying**: Automatically stringifies objects when storing data
3. **Error Handling**: Falls back to returning the raw string if JSON parsing fails
4. **SSR Compatibility**: Checks if `window` is defined before accessing localStorage
