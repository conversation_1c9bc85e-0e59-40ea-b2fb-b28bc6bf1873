/**
 * Secure storage utility for handling sensitive data
 * Uses AES encryption for localStorage and secure HttpOnly cookies for critical auth data
 */
import Cookies from "js-cookie";
import CryptoJS from "crypto-js";

// Secret key for encryption - in production this should be an environment variable
// This is just a fallback for development
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "ROVA_SECURE_STORAGE_KEY";

// Cookie security settings
const COOKIE_OPTIONS = {
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict" as const,
    expires: 1, // 1 day
};

/**
 * Encrypts a value using AES encryption
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function encrypt(value: any): string {
    // Convert value to string if it's not already
    const valueStr = typeof value === "string" ? value : JSON.stringify(value);

    // Encrypt the value
    return CryptoJS.AES.encrypt(valueStr, ENCRYPTION_KEY).toString();
}

/**
 * Decrypts a value using AES encryption
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function decrypt(encryptedValue: string): any {
    try {
        // Decrypt the value
        const bytes = CryptoJS.AES.decrypt(encryptedValue, ENCRYPTION_KEY);
        const decryptedString = bytes.toString(CryptoJS.enc.Utf8);

        // Try to parse as JSON, return as string if not valid JSON
        try {
            return JSON.parse(decryptedString);
        } catch {
            return decryptedString;
        }
    } catch (error) {
        console.error("Failed to decrypt value:", error);
        return null;
    }
}

/**
 * Secure storage for browser localStorage with encryption
 */
export const secureLocalStorage = {
    /**
     * Get an item from localStorage and decrypt it
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getItem<T = any>(key: string): T | null {
        if (typeof window === "undefined") return null;

        try {
            const encryptedValue = localStorage.getItem(key);
            if (!encryptedValue) return null;

            return decrypt(encryptedValue);
        } catch (error) {
            console.error(`Error retrieving ${key} from secure storage:`, error);
            return null;
        }
    },

    /**
     * Set an item in localStorage with encryption
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setItem(key: string, value: any): void {
        if (typeof window === "undefined") return;

        try {
            const encryptedValue = encrypt(value);
            localStorage.setItem(key, encryptedValue);
        } catch (error) {
            console.error(`Error storing ${key} in secure storage:`, error);
        }
    },

    /**
     * Remove an item from localStorage
     */
    removeItem(key: string): void {
        if (typeof window === "undefined") return;

        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error(`Error removing ${key} from secure storage:`, error);
        }
    },

    /**
     * Clear all items from localStorage
     */
    clear(): void {
        if (typeof window === "undefined") return;

        try {
            localStorage.clear();
        } catch (error) {
            console.error("Error clearing secure storage:", error);
        }
    },
};

/**
 * Secure cookie storage with encryption for non-HttpOnly cookies
 */
export const secureCookies = {
    /**
     * Set a cookie with encryption
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    set(name: string, value: any, options: Partial<Cookies.CookieAttributes> = {}): void {
        try {
            const encryptedValue = encrypt(value);
            Cookies.set(name, encryptedValue, { ...COOKIE_OPTIONS, ...options });
        } catch (error) {
            console.error(`Error setting secure cookie ${name}:`, error);
        }
    },

    /**
     * Get a cookie and decrypt it
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    get<T = any>(name: string): T | null {
        try {
            const encryptedValue = Cookies.get(name);
            if (!encryptedValue) return null;

            return decrypt(encryptedValue);
        } catch (error) {
            console.error(`Error getting secure cookie ${name}:`, error);
            return null;
        }
    },

    /**
     * Remove a cookie
     */
    remove(name: string, options: Partial<Cookies.CookieAttributes> = {}): void {
        try {
            Cookies.remove(name, { ...options, path: options.path || COOKIE_OPTIONS.path });
        } catch (error) {
            console.error(`Error removing secure cookie ${name}:`, error);
        }
    },
};

/**
 * Auth token management with secure cookies
 */
export const authTokenManager = {
    // Key for the auth token cookie
    TOKEN_KEY: "auth_token",

    /**
     * Set the authentication token
     */
    setToken(token: string, expiresInDays: number = 7): void {
        secureCookies.set(this.TOKEN_KEY, token, {
            expires: expiresInDays,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
        });
    },

    /**
     * Get the authentication token
     */
    getToken(): string | null {
        return secureCookies.get(this.TOKEN_KEY);
    },

    /**
     * Clear the authentication token
     */
    clearToken(): void {
        secureCookies.remove(this.TOKEN_KEY);
    },
};
