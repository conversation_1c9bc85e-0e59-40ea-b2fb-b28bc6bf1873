import { IBusinessOwner } from "@/redux/slices/onboarding/businessOwnersSlice";
import { BusinessInformationAccountForm, BusinessValidationForm } from "@/redux/types/digital-onboarding";
import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";

const TOKEN_KEY = "auth_token";
const REFRESH_TOKEN_KEY = "refresh_token";
const TOKEN_EXPIRES = 7;
const REFRESH_TOKEN_EXPIRES = 30; // Refresh token typically has a longer expiry
const FORM_DRAFT_KEY = "form_draft";

interface DocumentCookieData {
    fileName: string;
    completed: boolean;
    uploadedAt?: string;
}

export interface BusinessDocumentsCookie {
    [key: string]: DocumentCookieData;
}

export interface FormDraft {
    businessValidation?: {
        completed: boolean;
        values: BusinessValidationForm;
    };
    businessInformation?: {
        completed: boolean;
        values: BusinessInformationAccountForm;
    };
    businessOwners?: {
        completed: boolean;
        values: Record<string, string>;
        ownersData?: IBusinessOwner[];
    };
    businessDocuments?: BusinessDocumentsCookie;
}

export const cookies = {
    /**
     * Set the access token in cookies
     * @param token The access token to store
     */
    setToken(token: string) {
        // Set cookie with expiry
        Cookies.set(TOKEN_KEY, token, {
            expires: TOKEN_EXPIRES,
            secure: false,
            //   secure: env.appEnv === "production",
            sameSite: "strict",
        });
    },

    /**
     * Get the access token from cookies
     * @returns The access token or null if not found
     */
    getToken(): string | null {
        const token = Cookies.get(TOKEN_KEY) || null;
        return token;
    },

    /**
     * Clear the access token from cookies
     */
    clearToken() {
        Cookies.remove(TOKEN_KEY);
    },

    /**
     * Set the refresh token in cookies
     * @param token The refresh token to store
     */
    setRefreshToken(token: string) {
        // Set cookie with expiry
        Cookies.set(REFRESH_TOKEN_KEY, token, {
            expires: REFRESH_TOKEN_EXPIRES,
            secure: false,
            //   secure: env.appEnv === "production",
            sameSite: "strict",
        });
    },

    /**
     * Get the refresh token from cookies
     * @returns The refresh token or null if not found
     */
    getRefreshToken(): string | null {
        const token = Cookies.get(REFRESH_TOKEN_KEY) || null;
        return token;
    },

    /**
     * Clear the refresh token from cookies
     */
    clearRefreshToken() {
        Cookies.remove(REFRESH_TOKEN_KEY);
    },

    /**
     * Clear both access and refresh tokens
     */
    clearAllTokens() {
        this.clearToken();
        this.clearRefreshToken();
        this.clearFormDraft();
    },

    /**
     * Check if the user is authenticated by validating the access token
     * @returns True if the user is authenticated, false otherwise
     */
    isAuthenticated(): boolean {
        const token = this.getToken();
        if (!token) return false;

        try {
            const decoded = jwtDecode(token);
            const isExpired = decoded.exp ? decoded.exp * 1000 < Date.now() : true;

            if (isExpired) {
                // Don't clear the token here, let the refresh token mechanism handle it
                return false;
            }

            return true;
        } catch {
            this.clearToken();
            return false;
        }
    },

    /**
     * Check if the token is expired
     * @param token The token to check
     * @returns True if the token is expired, false otherwise
     */
    isTokenExpired(token: string): boolean {
        try {
            const decoded = jwtDecode(token);
            return decoded.exp ? decoded.exp * 1000 < Date.now() : true;
        } catch {
            return true;
        }
    },

    /**
     * Get the form draft from cookies
     * @returns The form draft or null if not found
     */
    getFormDraft(): FormDraft | null {
        const draft = Cookies.get(FORM_DRAFT_KEY);
        return draft ? JSON.parse(draft) : null;
    },

    /**
     * Update the form draft in cookies
     * @param step The form step to update
     * @param data The data to update for the step
     */
    updateFormDraft(step: keyof FormDraft, data: Partial<FormDraft[keyof FormDraft]>) {
        const currentDraft = this.getFormDraft() || {};

        const updatedDraft = {
            ...currentDraft,
            [step]: {
                ...currentDraft[step],
                ...data,
            },
        };

        Cookies.set(FORM_DRAFT_KEY, JSON.stringify(updatedDraft));
    },

    /**
     * Clear the form draft from cookies
     */
    clearFormDraft() {
        Cookies.remove(FORM_DRAFT_KEY, {
            path: "/",
            domain: window.location.hostname,
            secure: true,
            sameSite: "strict",
        });

        // Fallback to native method
        document.cookie = `${FORM_DRAFT_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    },

    /**
     * Check if a specific form step is completed
     * @param step The form step to check
     * @returns True if completed, false otherwise
     */
    isStepCompleted(step: keyof FormDraft): boolean {
        const draft = this.getFormDraft();

        if (step === "businessDocuments") {
            return Object.values(draft?.businessDocuments || {}).every((doc) => doc.completed);
        }
        const stepData = draft?.[step];
        return stepData?.completed ?? false;
    },
};
