/**
 * Navigation utilities for authentication flows
 */
import { useRouter } from "next/navigation";
import { LoginStep, getLoginStepRoute, setLoginProgress } from "./login-utils";
import { SignupStep, getSignupStepRoute, setSignupProgress } from "./session-utils";

/**
 * Navigate to a login step
 * @param step The login step to navigate to
 * @param router Next.js router instance
 * @param updateCookie Whether to update the cookie with the new step
 */
export function navigateToLoginStep(
    step: LoginStep,
    router: ReturnType<typeof useRouter>,
    updateCookie: boolean = true
): void {
    const route = getLoginStepRoute(step);

    // Update the cookie if requested
    if (updateCookie) {
        setLoginProgress(step);
    }

    // Navigate to the route
    router.push(route);
}

/**
 * Navigate to the next login step
 * @param currentStep The current login step
 * @param router Next.js router instance
 * @param skipSteps Number of steps to skip (default: 0)
 */
export function navigateToNextLoginStep(
    currentStep: LoginStep,
    router: ReturnType<typeof useRouter>,
    skipSteps: number = 0
): void {
    const nextStep = currentStep + 1 + skipSteps;

    // Make sure we don't go beyond the last step
    if (nextStep <= LoginStep.COMPLETED) {
        navigateToLoginStep(nextStep as LoginStep, router);
    }
}

/**
 * Navigate to a signup step
 * @param step The signup step to navigate to
 * @param router Next.js router instance
 * @param updateCookie Whether to update the cookie with the new step
 */
export function navigateToSignupStep(
    step: SignupStep,
    router: ReturnType<typeof useRouter>,
    updateCookie: boolean = true
): void {
    const route = getSignupStepRoute(step);

    // Update the cookie if requested
    if (updateCookie) {
        setSignupProgress(step);
    }

    // Navigate to the route
    router.push(route);
}

/**
 * Navigate to the next signup step
 * @param currentStep The current signup step
 * @param router Next.js router instance
 * @param skipSteps Number of steps to skip (default: 0)
 */
export function navigateToNextSignupStep(
    currentStep: SignupStep,
    router: ReturnType<typeof useRouter>,
    skipSteps: number = 0
): void {
    const nextStep = currentStep + 1 + skipSteps;

    // Make sure we don't go beyond the last step
    if (nextStep <= SignupStep.COMPLETE) {
        navigateToSignupStep(nextStep as SignupStep, router);
    }
}
