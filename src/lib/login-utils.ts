// Helper functions to manage login progress in cookies
import Cookies from "js-cookie";

// Define the login progress steps
export enum LoginStep {
    CREDENTIALS = 1, // Password login
    MAGIC_CODE = 2, // Alternative login with magic code
    MFA_VERIFICATION = 3, // MFA verification (if enabled)
    COMPLETED = 4, // Login completed
}

// Cookie name for login progress
const LOGIN_PROGRESS_COOKIE = "login_progress";

// Set the current login step in a cookie
export function setLoginProgress(step: LoginStep): void {
    Cookies.set(LOGIN_PROGRESS_COOKIE, step.toString(), {
        expires: 1, // 1 day
        path: "/",
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
    });
}

// Get the current login step from the cookie
export function getLoginProgress(): LoginStep {
    const step = Cookies.get(LOGIN_PROGRESS_COOKIE);
    return step ? (Number.parseInt(step, 10) as LoginStep) : LoginStep.CREDENTIALS;
}

// Clear the login progress
export function clearLoginProgress(): void {
    Cookies.remove(LOGIN_PROGRESS_COOKIE);
}

// Get the route for a specific login step
export function getLoginStepRoute(step: LoginStep): string {
    const stepRoutes = {
        [LoginStep.CREDENTIALS]: "/auth/login",
        [LoginStep.MAGIC_CODE]: "/auth/login/magic-code",
        [LoginStep.MFA_VERIFICATION]: "/auth/login/two-fa-verification",
        [LoginStep.COMPLETED]: "/dashboard",
    };

    return stepRoutes[step] || "/auth/login";
}

// Check if a user can access a specific login step
export function canAccessLoginStep(currentStep: LoginStep, targetStep: LoginStep): boolean {
    // Can always go back to previous steps
    if (targetStep <= currentStep) {
        return true;
    }

    // Can only advance one step at a time
    return targetStep === currentStep + 1;
}
