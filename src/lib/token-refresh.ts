import { cookies } from "./cookies";
import { jwtDecode } from "jwt-decode";
import { userAxios } from "@/api/axios";
import { getSessionDetails } from "@/functions/userSession";

// Flag to prevent multiple refresh requests
let isRefreshing = false;
// Queue of callbacks to execute after token refresh
let refreshSubscribers: ((token: string) => void)[] = [];

/**
 * Subscribe to token refresh
 * @param callback Function to call when token is refreshed
 */
export const subscribeTokenRefresh = (callback: (token: string) => void) => {
    refreshSubscribers.push(callback);
};

/**
 * Notify all subscribers that token has been refreshed
 * @param token The new access token
 */
export const onTokenRefreshed = (token: string) => {
    refreshSubscribers.forEach((callback) => callback(token));
    refreshSubscribers = [];
};

/**
 * Log token expiry information for debugging
 * @param token The token to check
 */
export const logTokenExpiry = (token: string | null): void => {
    if (!token) {
        console.log("No token available");
        return;
    }

    try {
        const decoded = jwtDecode(token);
        const currentTime = Date.now() / 1000;
        const timeUntilExpiry = (decoded.exp || 0) - currentTime;
        const expiryDate = new Date((decoded.exp || 0) * 1000);

        console.log(`Token expiry info: ${timeUntilExpiry} seconds until expiry (${expiryDate.toLocaleString()})`);
    } catch (error) {
        console.error("Error decoding token for expiry info:", error);
    }
};

/**
 * Check if token needs refresh (expires within 5 minutes)
 * @param token The token to check
 * @returns boolean indicating if refresh is needed
 */
export const shouldRefreshToken = (token: string | null): boolean => {
    if (!token) return false;

    try {
        const decoded = jwtDecode(token);
        const currentTime = Date.now() / 1000;
        const timeUntilExpiry = (decoded.exp || 0) - currentTime;

        console.log(`Token expires in ${timeUntilExpiry} seconds, refresh needed: ${timeUntilExpiry < 300}`);

        // Refresh if token expires within 5 minutes (300 seconds)
        return timeUntilExpiry < 300;
    } catch (error) {
        console.error("Error checking token expiry:", error);
        return true; // If we can't decode, assume it needs refresh
    }
};

/**
 * Refresh the access token using the refresh token
 * @param forceRefresh Force refresh even if token is not expired
 * @returns A promise that resolves to the new access token
 */
export const refreshAccessToken = async (forceRefresh = false): Promise<string | null> => {
    const currentToken = cookies.getToken();
    const session = getSessionDetails();

    console.log(
        `Token refresh attempt - forceRefresh: ${forceRefresh}, hasToken: ${!!currentToken}, hasSession: ${!!session}`
    );

    // Check if refresh is needed
    if (!forceRefresh && !shouldRefreshToken(currentToken)) {
        console.log("Token refresh not needed, returning current token");
        return currentToken;
    }

    // Prevent multiple refresh requests
    if (isRefreshing) {
        console.log("Token refresh already in progress, subscribing to result");
        return new Promise((resolve) => {
            subscribeTokenRefresh((token) => {
                resolve(token);
            });
        });
    }

    console.log("Starting token refresh process - will call API");
    isRefreshing = true;

    try {
        const refreshToken = cookies.getRefreshToken();

        if (!refreshToken) {
            console.log("No refresh token available, clearing tokens");
            // No refresh token available, clear tokens and return null
            cookies.clearAllTokens();
            return null;
        }

        console.log("Calling refresh token API...");
        // Call the refresh token endpoint
        const response = await userAxios.post("/v1/authentication/refresh", {
            username: session?.email,
            refreshToken,
        });

        const { access_token: accessToken, refresh_token: newRefreshToken } = response.data;

        // store.dispatch(signinActions.updateToken({ accessToken, refreshToken: newRefreshToken }));
        // store.dispatch(updateToken({ token: accessToken }));

        if (!accessToken) {
            throw new Error("No access token received from refresh endpoint");
        }

        console.log("Token refresh successful, storing new tokens");

        // Store the new tokens
        cookies.setToken(accessToken);

        // If a new refresh token is provided, store it
        if (newRefreshToken) {
            cookies.setRefreshToken(newRefreshToken);
        }

        // Notify subscribers
        onTokenRefreshed(accessToken);

        return accessToken;
    } catch (error) {
        console.error("Token refresh failed:", error);

        return null;
    } finally {
        isRefreshing = false;
    }
};

/**
 * Validate token and refresh if needed
 * @returns Promise<boolean> indicating if user is authenticated
 */
export const validateAndRefreshToken = async (): Promise<boolean> => {
    const token = cookies.getToken();

    if (!token) {
        return false;
    }

    try {
        const newToken = await refreshAccessToken();
        return !!newToken;
    } catch (error) {
        console.error("Token validation failed:", error);
        return false;
    }
};
