/**
 * Utility functions to handle clearing auth-related data
 * This is used in conjunction with the middleware to clear login/signup data
 * when navigating away from auth routes
 */
import { clearLoginProgress } from "./login-utils";
import { clearSignupProgress } from "./session-utils";

/**
 * Check for auth cleanup headers and clear data if needed
 * This should be called on page load in a client-side component
 */
export function checkAndClearAuthData(): void {
    if (typeof window === "undefined") {
        return; // Only run on client side
    }

    // Check for login data cleanup header
    if (shouldClearLoginData()) {
        console.log("Clearing login data based on middleware header");
        clearLoginData();
    }

    // Check for signup data cleanup header
    if (shouldClearSignupData()) {
        console.log("Clearing signup data based on middleware header");
        clearSignupData();
    }
}

/**
 * Check if login data should be cleared
 */
function shouldClearLoginData(): boolean {
    // Check for the header set by middleware
    const headerValue = getResponseHeader("X-Clear-Login-Data");
    return headerValue === "true";
}

/**
 * Check if signup data should be cleared
 */
function shouldClearSignupData(): boolean {
    // Check for the header set by middleware
    const headerValue = getResponseHeader("X-Clear-Signup-Data");
    return headerValue === "true";
}

/**
 * Clear all login-related data
 */
export function clearLoginData(): void {
    // Clear login progress cookie
    clearLoginProgress();

    // Clear any localStorage items related to login
    if (typeof window !== "undefined") {
        // Clear preLoginData from localStorage
        const storageManager = {
            getItem: (key: string) => {
                try {
                    const item = localStorage.getItem(key);
                    return item ? JSON.parse(item) : null;
                } catch (error) {
                    console.error(`Error getting item ${key} from localStorage:`, error);
                    return null;
                }
            },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            setItem: (key: string, value: any) => {
                try {
                    localStorage.setItem(key, JSON.stringify(value));
                } catch (error) {
                    console.error(`Error setting item ${key} in localStorage:`, error);
                }
            },
            removeItem: (key: string) => {
                try {
                    localStorage.removeItem(key);
                } catch (error) {
                    console.error(`Error removing item ${key} from localStorage:`, error);
                }
            },
        };

        // Remove preLoginData
        storageManager.removeItem("preLoginData");

        // Clear any other login-related data in localStorage
        // This includes any temporary data stored during the login process
        const keysToRemove = [
            "preLoginData",
            "loginEmail",
            "loginUsername",
            "loginPhone",
            "mfaMethod",
            "deviceVerification",
            "magicCodeData",
        ];

        keysToRemove.forEach((key) => {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error(`Error removing ${key} from localStorage:`, error);
            }
        });

        // Reset any Redux state related to login if needed
        // This would typically be done through a Redux action
        // but we can dispatch an event that components can listen for
        try {
            const event = new CustomEvent("clearLoginState");
            window.dispatchEvent(event);
        } catch (error) {
            console.error("Error dispatching clearLoginState event:", error);
        }
    }
}

/**
 * Clear all signup-related data
 */
export function clearSignupData(): void {
    // Clear signup progress cookie
    clearSignupProgress();

    // Clear any localStorage items related to signup
    if (typeof window !== "undefined") {
        // Clear signup-related data from localStorage
        const keysToRemove = [
            "signupData",
            "personalInfo",
            "businessInfo",
            "signupEmail",
            "signupPhone",
            "verificationCode",
            "businessVerificationData",
            "mfaSetupData",
        ];

        keysToRemove.forEach((key) => {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error(`Error removing ${key} from localStorage:`, error);
            }
        });

        // Reset any Redux state related to signup if needed
        try {
            const event = new CustomEvent("clearSignupState");
            window.dispatchEvent(event);
        } catch (error) {
            console.error("Error dispatching clearSignupState event:", error);
        }
    }
}

/**
 * Get a response header value
 * This is a helper function to get headers from the current response
 */
function getResponseHeader(headerName: string): string | null {
    // In Next.js, we can't directly access response headers in client-side code
    // We need to check if the header was set in the current request
    if (typeof window === "undefined") {
        return null;
    }

    // For simplicity, we'll use localStorage to check if we need to clear data
    // This is a workaround since we can't access response headers directly
    // The middleware will set cookies that we can check here
    const currentPath = window.location.pathname;

    if (headerName === "X-Clear-Login-Data") {
        // If we're not on a login route, we should clear login data
        return !currentPath.startsWith("/auth/login") ? "true" : null;
    }

    if (headerName === "X-Clear-Signup-Data") {
        // If we're not on a signup route, we should clear signup data
        return !currentPath.startsWith("/auth/sign-up") ? "true" : null;
    }

    return null;
}
