"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTokenRefresh } from "./useTokenRefresh";
import { PATH_AUTH } from "@/routes/path";

export const useRouteGuard = () => {
    const router = useRouter();
    const { checkAndRefreshToken } = useTokenRefresh();

    useEffect(() => {
        const handleRouteChange = async (url: string) => {
            // Skip auth routes
            if (url.startsWith("/auth/")) {
                return;
            }

            // Check and refresh token before route change
            const isAuthenticated = await checkAndRefreshToken();

            if (!isAuthenticated) {
                // Prevent navigation and redirect to login
                router.push(PATH_AUTH.login);
                return;
            }
        };

        // Listen for route changes
        const handleBeforeUnload = () => {
            // Optional: Check token before page unload
            checkAndRefreshToken();
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [router, checkAndRefreshToken]);

    return { checkAndRefreshToken };
};
