"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { cookies } from "@/lib/cookies";
import { useLogout } from "./useLogout";
import { usePathname } from "next/navigation";

interface UseSessionTimeoutOptions {
    inactivityTimeout?: number; // in milliseconds (default: 5 minutes)
    warningTimeout?: number; // in milliseconds (default: 30 seconds)
    onWarning?: () => void;
    onTimeout?: () => void;
    excludeRoutes?: string[]; // Routes to exclude from session timeout
}

export const useSessionTimeout = (options: UseSessionTimeoutOptions = {}) => {
    const {
        inactivityTimeout = 5 * 60 * 1000, // 5 minutes
        warningTimeout = 30 * 1000, // 30 seconds
        onWarning,
        onTimeout,
        excludeRoutes = [],
    } = options;

    const pathname = usePathname();
    const { logout, loading, isError, success } = useLogout();
    const [showWarning, setShowWarning] = useState(false);
    const [timeLeft, setTimeLeft] = useState(Math.floor(warningTimeout / 1000));
    const [isWarningActive, setIsWarningActive] = useState(false);
    const [isAuthenticated, setIsAuthenticated] = useState(false);

    const inactivityTimerRef = useRef<NodeJS.Timeout>();
    const warningTimerRef = useRef<NodeJS.Timeout>();
    const countdownTimerRef = useRef<NodeJS.Timeout>();

    // Use refs to store the latest callback values to avoid stale closures
    const onWarningRef = useRef(onWarning);
    const onTimeoutRef = useRef(onTimeout);

    // Check if current route should be excluded
    const isRouteExcluded = useCallback(() => {
        if (!pathname) return false;
        return excludeRoutes.some((route) => pathname.startsWith(route));
    }, [pathname, excludeRoutes]);

    useEffect(() => {
        onWarningRef.current = onWarning;
        onTimeoutRef.current = onTimeout;
    }, [onWarning, onTimeout]);

    // Check authentication status
    useEffect(() => {
        const checkAuth = () => {
            const authStatus = cookies.isAuthenticated();
            setIsAuthenticated(authStatus);
            return authStatus;
        };

        // Initial check
        checkAuth();

        // Set up periodic auth check
        const authCheckInterval = setInterval(checkAuth, 1000);

        return () => clearInterval(authCheckInterval);
    }, []);

    const clearAllTimers = useCallback(() => {
        if (inactivityTimerRef.current) {
            clearTimeout(inactivityTimerRef.current);
            inactivityTimerRef.current = undefined;
        }
        if (warningTimerRef.current) {
            clearTimeout(warningTimerRef.current);
            warningTimerRef.current = undefined;
        }
        if (countdownTimerRef.current) {
            clearInterval(countdownTimerRef.current);
            countdownTimerRef.current = undefined;
        }
    }, []);

    const handleLogout = useCallback(() => {
        if (isError || success) {
            clearAllTimers();
            setShowWarning(false);
            setIsWarningActive(false);
            setTimeLeft(Math.floor(warningTimeout / 1000));
        }

        logout();

        onTimeoutRef.current?.();
    }, [clearAllTimers, warningTimeout, logout, isError, success]);

    const startWarningCountdown = useCallback(() => {
        // Don't start warning if route is excluded
        if (isRouteExcluded()) {
            return;
        }

        // Clear any existing timers first
        clearAllTimers();

        const initialTime = Math.floor(warningTimeout / 1000);
        setIsWarningActive(true);
        setTimeLeft(initialTime);
        setShowWarning(true);
        onWarningRef.current?.();

        // Start countdown timer
        countdownTimerRef.current = setInterval(() => {
            setTimeLeft((prev) => {
                const newTime = prev - 1;

                if (newTime <= 0) {
                    handleLogout();
                    return 0;
                }
                return newTime;
            });
        }, 1000);

        // Set backup timeout (in case interval fails)
        warningTimerRef.current = setTimeout(() => {
            handleLogout();
        }, warningTimeout + 1000); // Add 1 second buffer
    }, [warningTimeout, clearAllTimers, handleLogout, isRouteExcluded]);

    const resetInactivityTimer = useCallback(() => {
        // Don't do anything if user is not authenticated or route is excluded
        if (!isAuthenticated || isRouteExcluded()) {
            clearAllTimers();
            setShowWarning(false);
            setIsWarningActive(false);
            return;
        }

        // Don't reset if warning is currently active - user must explicitly choose continue or logout
        if (isWarningActive) {
            return;
        }

        clearAllTimers();
        setShowWarning(false);

        inactivityTimerRef.current = setTimeout(() => {
            startWarningCountdown();
        }, inactivityTimeout);
    }, [clearAllTimers, inactivityTimeout, startWarningCountdown, isWarningActive, isAuthenticated, isRouteExcluded]);

    const continueSession = useCallback(() => {
        clearAllTimers();
        setShowWarning(false);
        setIsWarningActive(false);
        setTimeLeft(Math.floor(warningTimeout / 1000));

        // Reset the inactivity timer only if still authenticated and route is not excluded
        if (isAuthenticated && !isRouteExcluded()) {
            inactivityTimerRef.current = setTimeout(() => {
                startWarningCountdown();
            }, inactivityTimeout);
        }
    }, [clearAllTimers, inactivityTimeout, startWarningCountdown, warningTimeout, isAuthenticated, isRouteExcluded]);

    // Event listeners for user activity - only when authenticated, warning not active, and route not excluded
    useEffect(() => {
        const events = ["mousedown", "mousemove", "keypress", "scroll", "touchstart", "click"];

        const handleUserActivity = () => {
            // Only reset timer if authenticated, warning is not active, and route is not excluded
            if (isAuthenticated && !isWarningActive && !isRouteExcluded()) {
                resetInactivityTimer();
            }
        };

        // Add event listeners
        events.forEach((event) => {
            document.addEventListener(event, handleUserActivity, true);
        });

        return () => {
            // Clean up event listeners
            events.forEach((event) => {
                document.removeEventListener(event, handleUserActivity, true);
            });
        };
    }, [isAuthenticated, isWarningActive, resetInactivityTimer, isRouteExcluded]);

    // Start initial timer when authenticated and route is not excluded
    useEffect(() => {
        if (isAuthenticated && !isWarningActive && !isRouteExcluded()) {
            resetInactivityTimer();
        } else if (!isAuthenticated || isRouteExcluded()) {
            clearAllTimers();
            setShowWarning(false);
            setIsWarningActive(false);
        }

        return () => {
            if (!isAuthenticated || isRouteExcluded()) {
                clearAllTimers();
            }
        };
    }, [isAuthenticated, isWarningActive, resetInactivityTimer, clearAllTimers, isRouteExcluded]);

    // Clean up on unmount
    useEffect(
        () => () => {
            clearAllTimers();
        },
        [clearAllTimers]
    );

    return {
        showWarning,
        timeLeft,
        continueSession,
        handleLogout,
        resetInactivityTimer,
        isWarningActive,
        isAuthenticated,
        loading,
    };
};
