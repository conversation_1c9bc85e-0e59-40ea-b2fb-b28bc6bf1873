# OTP Hooks

This directory contains custom hooks for handling OTP (One-Time Password) operations in the application. These hooks replace the Redux-based OTP management with local state management.

## Available Hooks

### `useOtp`

The base hook for OTP operations. It provides functions for sending, validating, and resending OTPs.

```typescript
import { useOtp } from "@/hooks/useOtp";

const {
    // Send OTP
    sendOtp,
    sendOtpLoading,
    sendOtpSuccess,
    sendOtpError,

    // Validate OTP
    validateOtp,
    validateOtpLoading,
    validateOtpSuccess,
    validateOtpError,

    // Resend OTP
    resendOtp,
    resendOtpLoading,
    resendOtpSuccess,
    resendOtpError,

    // Utilities
    clearOtpStates,
    clearOtpState,
} = useOtp();
```

### `useEmailVerification`

A specialized hook for email verification with OTP.

```typescript
import { useEmailVerification } from "@/hooks/useEmailVerification";

const { otpValue, setOtpValue, isLoading, isResendLoading, isSuccess, error, validateOtp, resendOtp } =
    useEmailVerification({
        email: "<EMAIL>",
        onVerificationSuccess: () => console.log("Email verified"),
        onVerificationError: (error) => console.error(error),
        redirectPath: "/next-page",
    });
```

### `usePhoneVerification`

A specialized hook for phone number verification with OTP.

```typescript
import { usePhoneVerification } from "@/hooks/usePhoneVerification";

const { otpValue, setOtpValue, isLoading, isResendLoading, isSuccess, error, validateOtp, resendOtp } =
    usePhoneVerification({
        phoneNumber: "+1234567890",
        onVerificationSuccess: () => console.log("Phone verified"),
        onVerificationError: (error) => console.error(error),
        redirectPath: "/next-page",
    });
```

## Benefits of Using Hooks

1. **Local State Management**: OTP state is managed locally in the component, reducing Redux boilerplate.
2. **Reusability**: Hooks can be reused across different components.
3. **Separation of Concerns**: Each hook handles a specific concern, making the code more maintainable.
4. **Simplified Testing**: Hooks are easier to test than Redux actions and reducers.
5. **Reduced Bundle Size**: Fewer Redux actions and reducers means a smaller bundle size.

## Migration from Redux

These hooks replace the following Redux actions and state:

- `sendOTP`
- `validateOtp`
- `resendOtp`
- `clearOTPState`

To migrate a component from Redux to hooks:

1. Remove Redux imports and useAppDispatch/useAppSelector hooks
2. Import the appropriate OTP hook
3. Replace Redux state with hook state
4. Replace Redux actions with hook functions

## Example Migration

### Before (with Redux)

```tsx
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { sendOTP, validateOtp, resendOtp } from '@/redux/actions/auth/signupActions';
import { clearOTPState } from '@/redux/slices/auth/signupSlice2';

const MyComponent = () => {
  const dispatch = useAppDispatch();
  const { loading, error, success } = useAppSelector(state => state.signupNew.validateOTP);
  const [otpValue, setOtpValue] = useState('');

  const handleSubmit = () => {
    dispatch(validateOtp({ receiver: '<EMAIL>', otp: otpValue }));
  };

  const handleResend = () => {
    dispatch(resendOtp('<EMAIL>'));
  };

  useEffect(() => {
    dispatch(sendOTP({ receiver: '<EMAIL>', receiverType: 'EMAIL' }));
  }, []);

  useEffect(() => {
    if (success) {
      // Handle success
      dispatch(clearOTPState('validateOTP'));
    }
  }, [success]);

  return (
    // Component JSX
  );
};
```

### After (with Hooks)

```tsx
import { useEmailVerification } from '@/hooks/useEmailVerification';

const MyComponent = () => {
  const {
    otpValue,
    setOtpValue,
    isLoading,
    isSuccess,
    error,
    validateOtp: handleSubmit,
    resendOtp: handleResend,
  } = useEmailVerification({
    email: '<EMAIL>',
    onVerificationSuccess: () => {
      // Handle success
    },
  });

  return (
    // Component JSX
  );
};
```
