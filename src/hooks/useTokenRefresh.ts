"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { refreshAccessToken, validateAndRefreshToken } from "@/lib/token-refresh";
import { cookies } from "@/lib/cookies";
import { PATH_AUTH } from "@/routes/path";

export const useTokenRefresh = () => {
    const router = useRouter();

    const checkAndRefreshToken = useCallback(async (): Promise<boolean> => {
        try {
            const isValid = await validateAndRefreshToken();
            if (!isValid) {
                // Token refresh failed, redirect to login
                router.push(PATH_AUTH.login);
                return false;
            }
            return true;
        } catch (error) {
            console.error("Token refresh error:", error);
            router.push(PATH_AUTH.login);
            return false;
        }
    }, [router]);

    const forceRefresh = useCallback(async (): Promise<string | null> => {
        try {
            return await refreshAccessToken(true);
        } catch (error) {
            console.error("Force refresh error:", error);
            return null;
        }
    }, []);

    // Listen for auth logout events
    // useEffect(() => {
    //   const handleAuthLogout = (event: CustomEvent) => {
    //     router.push(PATH_AUTH.login)
    //   }

    //   window.addEventListener("auth:logout", handleAuthLogout as EventListener)

    //   return () => {
    //     window.removeEventListener("auth:logout", handleAuthLogout as EventListener)
    //   }
    // }, [router])

    return {
        checkAndRefreshToken,
        forceRefresh,
        isAuthenticated: cookies.isAuthenticated(),
    };
};
