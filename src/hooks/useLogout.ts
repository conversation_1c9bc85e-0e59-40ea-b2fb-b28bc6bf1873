import { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch } from "@/redux/hooks";
import { signOut } from "@/redux/features/user";
import { PATH_AUTH } from "@/routes/path";
import { cookies } from "@/lib/cookies";
import { clearLoginData, clearSignupData } from "@/lib/auth-cleanup";
import { clearLoginProgress } from "@/lib/login-utils";
import { clearSignupProgress } from "@/lib/session-utils";
import { userAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { handleError } from "@/lib/utils";
import { ReduxPermissionsService } from "@/services/permissionsService";

export const useLogout = () => {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [isError, setIsError] = useState(false);

    const clearAllUserData = useCallback(() => {
        clearLoginData();
        clearSignupData();
        clearLoginProgress();
        clearSignupProgress();

        // Clear any other sensitive data from localStorage
        const keysToRemove = [
            "preLoginData",
            "loginEmail",
            "loginUsername",
            "loginPhone",
            "mfaMethod",
            "deviceVerification",
            "magicCodeData",
            "sessionDetails",
            "tokenDetails",
        ];
        keysToRemove.forEach((key) => {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error(`Error removing ${key} from localStorage:`, error);
            }
        });

        // Clear all cookies
        document.cookie.split(";").forEach((cookie) => {
            const [name] = cookie.split("=");
            document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        });
    }, []);

    const logout = useCallback(
        async (options?: { redirect?: boolean; feedback?: boolean }) => {
            const token = cookies.getToken();
            setIsError(false);
            setSuccess(false);
            try {
                setLoading(true);
                await userAxios.post("/v1/authentication/invalidate", {
                    token,
                    reason: "Logout",
                });
                clearAllUserData();

                // Clear permissions via Redux service
                try {
                    const reduxPermissionsService = ReduxPermissionsService.getInstance();
                    await reduxPermissionsService.clearPermissions();
                } catch (error) {
                    console.error("Failed to clear permissions during logout:", error);
                }

                dispatch(signOut());
                if (options?.feedback !== false) sendFeedback("Logged out successfully", "success");
                if (options?.redirect !== false) router.push(PATH_AUTH.login);
                setSuccess(true);
            } catch (error) {
                sendCatchFeedback(handleError(error));
                setIsError(true);
            } finally {
                setLoading(false);
                setIsError(false);
                setSuccess(false);
            }
        },
        [clearAllUserData, dispatch, router]
    );

    return { logout, loading, success, isError };
};
