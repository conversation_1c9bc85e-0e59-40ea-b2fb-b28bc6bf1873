/**
 * Permission Constants Hook
 *
 * This hook provides a type-safe way to access dynamic permission constants
 * that are fetched from the backend. It eliminates hardcoded permission strings
 * while maintaining type safety and providing fallbacks.
 *
 * Usage:
 * ```typescript
 * const { PERMISSIONS, getPermission, isLoaded } = usePermissionConstants();
 *
 * // Use in withPermissionCheck
 * const ProtectedPage = withPermissionCheck(Component, {
 *     requiredPermissions: [PERMISSIONS.TRANSACTIONS.VIEW_ALL],
 *     requireAll: false,
 * });
 * ```
 */

import { useAppSelector } from "@/redux/hooks";
import { selectSystemPermissions, selectIsPermissionsReady } from "@/redux/selectors/permissionSelectors";
import { useMemo } from "react";

// Permission categories for better organization
export interface PermissionConstants {
    TRANSACTIONS: {
        VIEW_ALL: string;
        VIEW_OWN: string;
        CREATE: string;
        EDIT: string;
        DELETE: string;
        EXPORT: string;
        APPROVE: string;
        VIEW_DETAILS: string;
        GENERATE_RECEIPTS: string;
    };
    TRANSFERS: {
        CREATE: string;
        APPROVE: string;
        VIEW_ALL: string;
        VIEW_OWN: string;
        CANCEL: string;
    };
    USERS: {
        VIEW_ALL: string;
        CREATE: string;
        EDIT: string;
        DELETE: string;
        MANAGE_ROLES: string;
        VIEW_PROFILE: string;
    };
    ROLES: {
        VIEW_ALL: string;
        CREATE: string;
        EDIT: string;
        DELETE: string;
        ASSIGN: string;
    };
    SETTINGS: {
        VIEW: string;
        EDIT: string;
        MANAGE_LIMITS: string;
        CHANGE_PASSWORD: string;
        CHANGE_PIN: string;
    };
    DASHBOARD: {
        VIEW: string;
        VIEW_ANALYTICS: string;
        VIEW_REPORTS: string;
    };
    ACCOUNTS: {
        VIEW_ALL: string;
        VIEW_OWN: string;
        MANAGE: string;
        VIEW_DETAILS: string;
        DOWNLOAD_STATEMENTS: string;
        VIEW_TRANSACTION_HISTORY: string;
    };
    REPORTS: {
        VIEW: string;
        GENERATE: string;
        EXPORT: string;
    };
    TEAM_MANAGEMENT: {
        VIEW_TEAM_MEMBERS: string;
        INVITE_TEAM_MEMBERS: string;
        EDIT_TEAM_MEMBERS: string;
        REMOVE_TEAM_MEMBERS: string;
        VIEW_AUTHENTICATION_STATUS: string;
        VIEW_ROLES: string;
        CREATE_ROLES: string;
        MANAGE_PERMISSIONS: string;
        SET_ROLE_RESTRICTIONS: string;
        VIEW_APPROVAL_RULES: string;
        MANAGE_APPROVAL_RULES: string;
        FILTER_ACTIVITY_LOGS: string;
    };
    SEND_MONEY: {
        INITIATE_SINGLE_PAYMENTS: string;
        INITIATE_BULK_PAYMENTS: string;
        VIEW_SCHEDULED_PAYMENTS: string;
        VIEW_PAYMENT_LIMITS: string;
        CANCEL_PENDING_PAYMENTS: string;
        SCHEDULE_PAYMENTS: string;
    };
    BILL_PAYMENTS: {
        VIEW_CATEGORIES: string;
        INITIATE_BILL_PAYMENT: string;
        BULK_AIRTIME_PAYMENTS: string;
        CANCEL_SCHEDULED_BILL: string;
        VIEW_PAYMENT_HISTORY: string;
        GENERATE_RECEIPTS: string;
    };
    BENEFICIARIES: {
        VIEW_LIST: string;
        ADD_NEW: string;
        EDIT_DETAILS: string;
        DELETE: string;
        VIEW_ACTIVITY_HISTORY: string;
    };
    OUTGOING_PAYMENTS: {
        VIEW_LIST: string;
        VIEW_STATUS: string;
        FILTER_BY_TYPE: string;
        MANAGE_APPROVAL_ACTIONS: string;
    };
    ONBOARDING: {
        COMPLETE_DIGITAL_FORM: string;
        SUBMIT_MANUAL_FORMS: string;
        UPLOAD_DOCUMENTS: string;
    };
    OVERVIEW: {
        VIEW_ACCOUNT_BALANCES: string;
        VIEW_PAYMENT_APPROVAL_REQUESTS: string;
    };
}

/**
 * Hook to get dynamic permission constants from the backend
 */
export const usePermissionConstants = () => {
    const systemPermissions = useAppSelector(selectSystemPermissions);
    const isLoaded = useAppSelector(selectIsPermissionsReady);

    // Create a lookup map for faster permission resolution
    const permissionLookup = useMemo(
        () =>
            systemPermissions.reduce(
                (acc, permission) => {
                    acc[permission.name] = permission;
                    return acc;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                },
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                {} as Record<string, any>
            ),
        [systemPermissions]
    );

    // Helper function to get permission name with fallback and validation
    const getPermission = useMemo(
        () =>
            (expectedName: string, fallback?: string): string => {
                // If permissions are loaded and permission exists, return it
                if (isLoaded && permissionLookup[expectedName]) {
                    return expectedName;
                }

                // If not loaded yet, return the expected name (optimistic)
                if (!isLoaded) {
                    return expectedName;
                }

                // Permission doesn't exist in system - warn in development
                if (process.env.NODE_ENV === "development") {
                    console.warn(
                        `Permission "${expectedName}" not found in system permissions.`,
                        fallback ? `Using fallback: "${fallback}"` : "No fallback provided."
                    );
                }

                return fallback || expectedName;
            },
        [isLoaded, permissionLookup]
    );

    // Dynamic permission constants based on common patterns
    const PERMISSIONS = useMemo(
        (): PermissionConstants => ({
            TRANSACTIONS: {
                VIEW_ALL: getPermission("View all transactions (CIB + external channels)"),
                VIEW_OWN: getPermission("View own transactions"),
                CREATE: getPermission("Create transaction"),
                EDIT: getPermission("Edit transaction"),
                DELETE: getPermission("Delete transaction"),
                EXPORT: getPermission("Generate transaction receipts"),
                APPROVE: getPermission("Approve transaction"),
                VIEW_DETAILS: getPermission("View transaction details"),
                GENERATE_RECEIPTS: getPermission("Generate transaction receipts"),
            },
            TRANSFERS: {
                CREATE: getPermission("Create transfer"),
                APPROVE: getPermission("Approve transfer"),
                VIEW_ALL: getPermission("View all transfers"),
                VIEW_OWN: getPermission("View own transfers"),
                CANCEL: getPermission("Cancel transfer"),
            },
            USERS: {
                VIEW_ALL: getPermission("View all users"),
                CREATE: getPermission("Create user"),
                EDIT: getPermission("Edit user"),
                DELETE: getPermission("Delete user"),
                MANAGE_ROLES: getPermission("Manage user roles"),
                VIEW_PROFILE: getPermission("View user profile"),
            },
            ROLES: {
                VIEW_ALL: getPermission("View all roles"),
                CREATE: getPermission("Create role"),
                EDIT: getPermission("Edit role"),
                DELETE: getPermission("Delete role"),
                ASSIGN: getPermission("Assign roles"),
            },
            SETTINGS: {
                VIEW: getPermission("View settings"),
                EDIT: getPermission("Edit settings"),
                MANAGE_LIMITS: getPermission("Manage transaction limits"),
                CHANGE_PASSWORD: getPermission("Change password"),
                CHANGE_PIN: getPermission("Change PIN"),
            },
            DASHBOARD: {
                VIEW: getPermission("View dashboard"),
                VIEW_ANALYTICS: getPermission("View analytics"),
                VIEW_REPORTS: getPermission("View reports"),
            },
            ACCOUNTS: {
                VIEW_ALL: getPermission("View list of accounts"),
                VIEW_OWN: getPermission("View own accounts"),
                MANAGE: getPermission("Manage accounts"),
                VIEW_DETAILS: getPermission("View account details (name, number, type)"),
                DOWNLOAD_STATEMENTS: getPermission("Download account statements"),
                VIEW_TRANSACTION_HISTORY: getPermission("View account transaction history"),
            },
            REPORTS: {
                VIEW: getPermission("View reports"),
                GENERATE: getPermission("Generate reports"),
                EXPORT: getPermission("Export reports"),
            },
            TEAM_MANAGEMENT: {
                VIEW_TEAM_MEMBERS: getPermission("View team members"),
                INVITE_TEAM_MEMBERS: getPermission("Invite new team members"),
                EDIT_TEAM_MEMBERS: getPermission("Edit team member details"),
                REMOVE_TEAM_MEMBERS: getPermission("Remove team member"),
                VIEW_AUTHENTICATION_STATUS: getPermission("View authentication status"),
                VIEW_ROLES: getPermission("View and edit roles"),
                CREATE_ROLES: getPermission("Create custom roles"),
                MANAGE_PERMISSIONS: getPermission("Define custom permissions"),
                SET_ROLE_RESTRICTIONS: getPermission("Set role-based restrictions"),
                VIEW_APPROVAL_RULES: getPermission("View approval rules (for payments)"),
                MANAGE_APPROVAL_RULES: getPermission("Create/modify/delete approval rules (for payments)"),
                FILTER_ACTIVITY_LOGS: getPermission("Filter and search activity logs"),
            },
            SEND_MONEY: {
                INITIATE_SINGLE_PAYMENTS: getPermission("Initiate single payments"),
                INITIATE_BULK_PAYMENTS: getPermission("Initiate bulk payments"),
                VIEW_SCHEDULED_PAYMENTS: getPermission("View scheduled and recurring payments"),
                VIEW_PAYMENT_LIMITS: getPermission("View payment limits"),
                CANCEL_PENDING_PAYMENTS: getPermission("Cancel pending payments"),
                SCHEDULE_PAYMENTS: getPermission("Schedule a payment"),
            },
            BILL_PAYMENTS: {
                VIEW_CATEGORIES: getPermission("View available bill categories"),
                INITIATE_BILL_PAYMENT: getPermission("Initiate bill payment (e.g., airtime, data, utilities)"),
                BULK_AIRTIME_PAYMENTS: getPermission("Perform bulk airtime payments"),
                CANCEL_SCHEDULED_BILL: getPermission("Cancel a scheduled bill"),
                VIEW_PAYMENT_HISTORY: getPermission("View bill payment history"),
                GENERATE_RECEIPTS: getPermission("Generate receipts for bill payments"),
            },
            BENEFICIARIES: {
                VIEW_LIST: getPermission("View beneficiary list"),
                ADD_NEW: getPermission("Add new beneficiary (local or foreign)"),
                EDIT_DETAILS: getPermission("Edit beneficiary details"),
                DELETE: getPermission("Delete a beneficiary"),
                VIEW_ACTIVITY_HISTORY: getPermission("View beneficiary activity history"),
            },
            OUTGOING_PAYMENTS: {
                VIEW_LIST: getPermission("View outgoing payments list"),
                VIEW_STATUS: getPermission("View payment status (pending, failed, successful, etc.)"),
                FILTER_BY_TYPE: getPermission("Filter payments by type (scheduled, recurring, bulk)"),
                MANAGE_APPROVAL_ACTIONS: getPermission("Manage payment approval actions"),
            },
            ONBOARDING: {
                COMPLETE_DIGITAL_FORM: getPermission("Complete digital account opening form"),
                SUBMIT_MANUAL_FORMS: getPermission("Submit manual onboarding forms"),
                UPLOAD_DOCUMENTS: getPermission("Upload compliance documents"),
            },
            OVERVIEW: {
                VIEW_ACCOUNT_BALANCES: getPermission("View account balances by account"),
                VIEW_PAYMENT_APPROVAL_REQUESTS: getPermission("View payment approval requests"),
            },
        }),
        [getPermission]
    );

    // Helper function to check if a permission exists in the system
    const permissionExists = useMemo(
        () =>
            (permissionName: string): boolean =>
                isLoaded ? !!permissionLookup[permissionName] : true, // Optimistic when not loaded
        [isLoaded, permissionLookup]
    );

    // Get permissions by module/category
    const getPermissionsByModule = useMemo(
        () => (moduleName: string) =>
            systemPermissions.filter(
                (p) =>
                    p.appModule?.toLowerCase() === moduleName.toLowerCase() ||
                    p.name.toLowerCase().includes(moduleName.toLowerCase())
            ),
        [systemPermissions]
    );

    // Validation helper for development
    const validatePermissions = useMemo(
        () =>
            (requiredPermissions: string[]): { valid: string[]; invalid: string[] } => {
                if (!isLoaded) {
                    return { valid: requiredPermissions, invalid: [] }; // Optimistic when not loaded
                }

                const valid: string[] = [];
                const invalid: string[] = [];

                requiredPermissions.forEach((permission) => {
                    if (permissionLookup[permission]) {
                        valid.push(permission);
                    } else {
                        invalid.push(permission);
                    }
                });

                return { valid, invalid };
            },
        [isLoaded, permissionLookup]
    );

    return {
        PERMISSIONS,
        getPermission,
        permissionExists,
        getPermissionsByModule,
        validatePermissions,
        systemPermissions,
        isLoaded,
        isLoading: !isLoaded,

        // Utility functions
        getAllPermissionNames: () => systemPermissions.map((p) => p.name),
        getPermissionCount: () => systemPermissions.length,
    };
};

/**
 * Common permission groups for convenience
 */
export const useCommonPermissionGroups = () => {
    const { PERMISSIONS } = usePermissionConstants();

    return useMemo(
        () => ({
            // Admin permissions
            ADMIN: [
                PERMISSIONS.USERS.VIEW_ALL,
                PERMISSIONS.USERS.CREATE,
                PERMISSIONS.USERS.EDIT,
                PERMISSIONS.USERS.DELETE,
                PERMISSIONS.ROLES.VIEW_ALL,
                PERMISSIONS.ROLES.CREATE,
                PERMISSIONS.ROLES.EDIT,
                PERMISSIONS.SETTINGS.EDIT,
            ],

            // Manager permissions
            MANAGER: [
                PERMISSIONS.TRANSACTIONS.VIEW_ALL,
                PERMISSIONS.TRANSFERS.APPROVE,
                PERMISSIONS.REPORTS.VIEW,
                PERMISSIONS.REPORTS.GENERATE,
                PERMISSIONS.TEAM_MANAGEMENT.VIEW_TEAM_MEMBERS,
            ],

            // Regular user permissions
            USER: [
                PERMISSIONS.DASHBOARD.VIEW,
                PERMISSIONS.TRANSACTIONS.VIEW_OWN,
                PERMISSIONS.TRANSFERS.CREATE,
                PERMISSIONS.ACCOUNTS.VIEW_OWN,
            ],

            // Read-only permissions
            READ_ONLY: [
                PERMISSIONS.DASHBOARD.VIEW,
                PERMISSIONS.TRANSACTIONS.VIEW_OWN,
                PERMISSIONS.ACCOUNTS.VIEW_OWN,
                PERMISSIONS.REPORTS.VIEW,
            ],
        }),
        [PERMISSIONS]
    );
};

export default usePermissionConstants;

