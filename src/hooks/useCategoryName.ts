import { useEffect, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchBillCategories } from "@/redux/actions/billPaymentThunks";

export const useCategoryName = (categoryId: string | string[]) => {
    const dispatch = useAppDispatch();
    const { content: categories = [] } = useAppSelector((state) => state.billPayments.categories) || {};
    const hasFetched = categories.length > 0;

    useEffect(() => {
        if (!hasFetched) {
            dispatch(fetchBillCategories());
        }
    }, [dispatch, hasFetched]);

    return useMemo(() => {
        if (!categoryId) return "Bill Payment";
        const category = categories.find((cat) => cat.id?.toString() === categoryId.toString());
        return category?.categoryName ?? "Bill Payment";
    }, [categories, categoryId]);
};
