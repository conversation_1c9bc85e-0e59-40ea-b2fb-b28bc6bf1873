import React from "react";
import { ChromeIcon, SafariIcon, FirefoxIcon, EdgeIcon, OperaIcon, AppleIcon } from "@/components/icons/settings";

/**
 * Returns the corresponding browser icon component for a given browser name.
 */
const useBrowserIcon = (browser: string): React.ReactNode => {
    if (!browser) return null;
    const lowerCase = browser.toLowerCase();
    if (lowerCase.includes("chrome")) return <ChromeIcon />;
    if (lowerCase.includes("safari")) return <SafariIcon />;
    if (lowerCase.includes("firefox")) return <FirefoxIcon />;
    if (lowerCase.includes("edge") || lowerCase.includes("microsoft edge")) return <EdgeIcon />;
    if (lowerCase.includes("opera")) return <OperaIcon />;
    if (lowerCase.includes("apple")) return <AppleIcon />;
    return null;
};

export default useBrowserIcon;
