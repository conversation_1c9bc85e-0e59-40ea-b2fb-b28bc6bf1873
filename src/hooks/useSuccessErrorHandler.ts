import { useEffect, useRef, useCallback } from "react";
import { sendFeedback, sendCatchFeedback } from "@/functions/feedback";

interface UseSuccessErrorHandlerOptions {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data?: any;
    success?: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    error?: any;
    message?: string | null;
    loading?: boolean;
    onSuccess?: () => void;
    onError?: () => void;
    onCleanup?: () => void;
    successMessage?: string;
    preventDuplicateHandling?: boolean;
    clearStateAction?: () => void;
    clearStateDelay?: number;
}

/**
 * Custom hook to handle success and error states consistently across components
 * Eliminates code duplication for common patterns like:
 * - Showing success/error feedback
 * - Preventing duplicate processing
 * - Calling cleanup functions
 * - Clearing Redux state with timeout
 */
export const useSuccessErrorHandler = ({
    data,
    success,
    error,
    message,
    loading,
    onSuccess,
    onError,
    onCleanup,
    successMessage,
    preventDuplicateHandling = true,
    clearStateAction,
    clearStateDelay = 500,
}: UseSuccessErrorHandlerOptions) => {
    const hasProcessedResponseRef = useRef(false);

    // Reset processing flag when loading starts
    useEffect(() => {
        if (loading && preventDuplicateHandling) {
            hasProcessedResponseRef.current = false;
        }
    }, [loading, preventDuplicateHandling]);

    // Handle success responses
    useEffect(() => {
        const shouldProcessSuccess = preventDuplicateHandling
            ? data && success && !hasProcessedResponseRef.current
            : data && success;

        if (shouldProcessSuccess) {
            if (preventDuplicateHandling) {
                hasProcessedResponseRef.current = true;
            }

            // Show success message
            const messageToShow = message && message !== "null" ? String(message) : successMessage;

            if (messageToShow) {
                sendFeedback(messageToShow, "success");
            }

            // Call success callback
            if (onSuccess) {
                onSuccess();
            }

            // Clear Redux state after delay if action provided
            if (clearStateAction) {
                const timeoutId = setTimeout(() => {
                    clearStateAction();
                }, clearStateDelay);

                // Cleanup timeout
                return () => clearTimeout(timeoutId);
            }
        }
    }, [
        data,
        success,
        message,
        successMessage,
        onSuccess,
        clearStateAction,
        clearStateDelay,
        preventDuplicateHandling,
    ]);

    // Handle error responses
    useEffect(() => {
        const shouldProcessError = preventDuplicateHandling ? error && !hasProcessedResponseRef.current : error;

        if (shouldProcessError) {
            if (preventDuplicateHandling) {
                hasProcessedResponseRef.current = true;
            }

            // Show error feedback
            if (error.message) {
                sendFeedback(error.message, "error");
            } else {
                sendCatchFeedback(error);
            }

            // Call error callback
            if (onError) {
                onError();
            }
        }
    }, [error, onError, preventDuplicateHandling]);

    // Cleanup effect
    useEffect(
        () => () => {
            if (onCleanup) {
                onCleanup();
            }
        },
        [onCleanup]
    );

    // Return utilities
    const resetProcessingFlag = useCallback(() => {
        hasProcessedResponseRef.current = false;
    }, []);

    return {
        hasProcessedResponse: hasProcessedResponseRef.current,
        resetProcessingFlag,
    };
};

/**
 * Simplified version for basic success/error handling without data dependency
 */
export const useBasicSuccessErrorHandler = ({
    success,
    error,
    onSuccess,
    onError,
    successMessage,
    clearStateAction,
    clearStateDelay = 500,
}: Omit<UseSuccessErrorHandlerOptions, "data" | "message" | "loading" | "preventDuplicateHandling">) => {
    // Handle success
    useEffect(() => {
        if (success) {
            if (successMessage) {
                sendFeedback(successMessage, "success");
            }

            if (onSuccess) {
                onSuccess();
            }

            if (clearStateAction) {
                const timeoutId = setTimeout(() => {
                    clearStateAction();
                }, clearStateDelay);

                return () => clearTimeout(timeoutId);
            }
        }
    }, [success, successMessage, onSuccess, clearStateAction, clearStateDelay]);

    // Handle error
    useEffect(() => {
        if (error) {
            if (error.message) {
                sendFeedback(error.message, "error");
            } else {
                sendCatchFeedback(error);
            }

            if (onError) {
                onError();
            }
        }
    }, [error, onError]);
};
