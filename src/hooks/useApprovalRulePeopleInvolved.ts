/**
 * Purpose: Custom hook for managing approval rule people involved API calls with deduplication.
 *
 * Functionality: This hook provides a centralized way to fetch and manage approval data from the
 * /v1/approval-rule/people-involved endpoint. It implements caching and deduplication logic to prevent
 * multiple API calls with the same parameters, ensuring the endpoint is called only once when components
 * with identical parameters are mounted. The hook leverages the existing Redux infrastructure and
 * getTransferApprovers action while adding intelligent caching layer.
 *
 * Dependencies: React hooks (useEffect, useRef, useMemo), Redux hooks (useAppDispatch, useAppSelector),
 * getTransferApprovers action from sendMoneyActions, and TypeScript types for proper type safety.
 *
 * Usage: Components can call this hook with transfer parameters (amount, type, reference) and receive
 * approval data, loading state, and error information. The hook automatically handles API calls,
 * caching, and cleanup to optimize performance and prevent duplicate requests.
 */

import { useEffect, useRef, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getTransferApprovers } from "@/redux/actions/sendMoneyActions";
import { ApprovalModuleType } from "@/components/page-components/dashboard/send-money/data";
import { TransferApprovalType } from "@/types/user";

// Interfaces for the hook
interface UseApprovalRulePeopleInvolvedParams {
    amount?: number;
    type?: ApprovalModuleType;
    reference?: string;
    requestId?: string;
    enabled?: boolean; // Allow disabling the hook
}

interface UseApprovalRulePeopleInvolvedReturn {
    data: TransferApprovalType[] | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

interface ApprovalCacheKey {
    amount: number;
    type: ApprovalModuleType;
    reference?: string;
    requestId?: string;
}

interface ApprovalCacheEntry {
    key: string;
    timestamp: number;
    isLoading: boolean;
}

// Global cache to track ongoing requests and prevent duplicates
const requestCache = new Map<string, ApprovalCacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper function to create cache key
function createCacheKey(params: ApprovalCacheKey): string {
    const { amount, type, reference, requestId } = params;
    return `approval_${amount}_${type}_${reference ?? ""}_${requestId ?? ""}`;
}

// Helper function to check if cache entry is valid
function isCacheValid(entry: ApprovalCacheEntry): boolean {
    const now = Date.now();
    return now - entry.timestamp < CACHE_DURATION;
}

// Helper function to clean expired cache entries
function cleanExpiredCache() {
    const now = Date.now();
    for (const [key, entry] of requestCache.entries()) {
        if (now - entry.timestamp > CACHE_DURATION && !entry.isLoading) {
            requestCache.delete(key);
        }
    }
}

// Helper function to update cache entry
function updateCacheEntry(key: string, isLoading: boolean) {
    requestCache.set(key, {
        key,
        timestamp: Date.now(),
        isLoading,
    });
}

/**
 * Custom hook for managing approval rule people involved API calls with deduplication
 *
 * @param params - Parameters for the API call
 * @returns Object containing data, loading state, error, and refetch function
 */
export function useApprovalRulePeopleInvolved({
    amount,
    type = "TRANSFER",
    reference,
    requestId,
    enabled = true,
}: UseApprovalRulePeopleInvolvedParams): UseApprovalRulePeopleInvolvedReturn {
    const dispatch = useAppDispatch();
    const { data, loading, error } = useAppSelector((state) => state.sendMoney.getTransferApprovers);

    // Track if this hook instance has made a request
    const hasRequestedRef = useRef(false);
    const lastCacheKeyRef = useRef<string | null>(null);

    // Create cache key for current parameters
    const cacheKey = useMemo(() => {
        if (!amount || !enabled) return null;
        return createCacheKey({ amount, type, reference, requestId });
    }, [amount, type, reference, requestId, enabled]);

    // Function to make API request
    const makeRequest = useMemo(
        () => async (key: string) => {
            if (!amount || !enabled) return;

            // Mark this request as loading in cache
            updateCacheEntry(key, true);

            try {
                await dispatch(
                    getTransferApprovers({
                        amount,
                        type,
                        reference,
                        requestId,
                    })
                );

                // Mark request as completed
                updateCacheEntry(key, false);
            } catch (error) {
                // Remove from cache on error to allow retry
                requestCache.delete(key);
                throw error;
            }
        },
        [dispatch, amount, type, reference, requestId, enabled]
    );

    // Effect to handle API calls with deduplication
    useEffect(() => {
        if (!cacheKey || !enabled) return;

        // Clean expired cache entries periodically
        cleanExpiredCache();

        const cachedEntry = requestCache.get(cacheKey);

        // Check if we already have a valid cache entry or ongoing request
        if (cachedEntry) {
            if (isCacheValid(cachedEntry) || cachedEntry.isLoading) {
                // Valid cache exists or request is already in progress, no need to make request
                return;
            }
        }

        // Check if cache key has changed
        if (lastCacheKeyRef.current !== cacheKey) {
            hasRequestedRef.current = false;
            lastCacheKeyRef.current = cacheKey;
        }

        // Make request if we haven't already and no valid cache exists
        if (!hasRequestedRef.current) {
            hasRequestedRef.current = true;
            makeRequest(cacheKey);
        }
    }, [cacheKey, enabled, makeRequest]);

    // Cleanup function to remove loading state when component unmounts
    useEffect(
        () => () => {
            if (cacheKey && requestCache.has(cacheKey)) {
                const entry = requestCache.get(cacheKey);
                if (entry?.isLoading) {
                    // Component unmounted while loading, mark as not loading
                    requestCache.set(cacheKey, {
                        ...entry,
                        isLoading: false,
                    });
                }
            }
        },
        [cacheKey]
    );

    // Refetch function to manually trigger API call
    const refetch = useMemo(
        () => () => {
            if (!cacheKey || !enabled) return;

            // Clear cache entry to force new request
            requestCache.delete(cacheKey);
            hasRequestedRef.current = false;

            // Make new request
            makeRequest(cacheKey);
        },
        [cacheKey, enabled, makeRequest]
    );

    return {
        data,
        loading,
        error,
        refetch,
    };
}

// Export cache management functions for testing
export const cacheUtils = {
    getCacheSize: () => requestCache.size,
    clearCache: () => requestCache.clear(),
    getCacheEntry: (key: string) => requestCache.get(key),
};
