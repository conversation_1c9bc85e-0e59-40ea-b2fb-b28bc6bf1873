"use client";

import { usePermissions } from "@/contexts/PermissionContext";
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";

/**
 * Custom hook for checking permissions in functional components
 * Enhanced to work with dynamic permissions while maintaining backward compatibility
 *
 * @returns Object with functions to check permissions
 */
export const usePermissionCheck = () => {
    const {
        userPermissions,
        isLoadingPermissions,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        systemPermissions,
        isLoadingSystemPermissions,
        systemPermissionsError,
    } = usePermissions();

    // Get dynamic permissions for additional functionality
    const {
        permissions: dynamicPermissions,
        isLoaded: isDynamicPermissionsLoaded,
        error: dynamicPermissionsError,
    } = useDynamicPermissions();

    /**
     * Check if the current route should be accessible based on permissions
     *
     * @param requiredPermissions - Permissions required to access the route
     * @param requireAll - If true, all permissions are required. If false, any one is sufficient.
     * @returns Boolean indicating if the route is accessible
     */
    const canAccessRoute = (requiredPermissions: string[], requireAll: boolean = false): boolean => {
        if (requiredPermissions.length === 0) return true;

        return requireAll ? hasAllPermissions(requiredPermissions) : hasAnyPermission(requiredPermissions);
    };

    /**
     * Check if a specific action is allowed based on permissions
     *
     * @param requiredPermissions - Permissions required to perform the action
     * @param requireAll - If true, all permissions are required. If false, any one is sufficient.
     * @returns Boolean indicating if the action is allowed
     */
    const canPerformAction = (requiredPermissions: string | string[], requireAll: boolean = false): boolean => {
        if (typeof requiredPermissions === "string") {
            return hasPermission(requiredPermissions);
        }

        if (requiredPermissions.length === 0) return true;

        return requireAll ? hasAllPermissions(requiredPermissions) : hasAnyPermission(requiredPermissions);
    };

    /**
     * Check if a permission exists in the system (not just user permissions)
     * Useful for validating permission strings before using them
     */
    const permissionExists = (permissionName: string): boolean =>
        systemPermissions.some((p) => p.name === permissionName);

    /**
     * Get permission details by name
     */
    const getPermissionDetails = (permissionName: string) =>
        systemPermissions.find((p) => p.name === permissionName) ?? null;

    /**
     * Check if the permission system is ready for use
     * This includes both user permissions and system permissions being loaded
     */
    const isPermissionSystemReady = (): boolean =>
        !isLoadingPermissions && !isLoadingSystemPermissions && isDynamicPermissionsLoaded;

    /**
     * Get comprehensive loading state
     */
    const getLoadingState = () => ({
        isLoadingUserPermissions: isLoadingPermissions,
        isLoadingSystemPermissions,
        isLoadingDynamicPermissions: !isDynamicPermissionsLoaded,
        isAnyLoading: isLoadingPermissions ?? isLoadingSystemPermissions ?? !isDynamicPermissionsLoaded,
        isSystemReady: isPermissionSystemReady(),
    });

    /**
     * Get comprehensive error state
     */
    const getErrorState = () => ({
        systemPermissionsError,
        dynamicPermissionsError,
        hasAnyError: !!(systemPermissionsError ?? dynamicPermissionsError),
    });

    return {
        // Original API (backward compatible)
        userPermissions,
        isLoadingPermissions,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        canAccessRoute,
        canPerformAction,

        // Enhanced API for dynamic permissions
        systemPermissions,
        dynamicPermissions,
        permissionExists,
        getPermissionDetails,
        isPermissionSystemReady,
        getLoadingState,
        getErrorState,

        // Convenience flags
        isSystemReady: isPermissionSystemReady(),
        hasErrors: !!(systemPermissionsError ?? dynamicPermissionsError),
    };
};

export default usePermissionCheck;
