import { useState, useEffect } from "react";

// Custom hook to get current location
export const useCurrentLocation = () => {
    const [location, setLocation] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const getCurrentLocation = async () => {
        // Check if geolocation is supported
        if (!navigator.geolocation) {
            setError("Geolocation is not supported by your browser");
            setLoading(false);
            return;
        }

        try {
            // Get coordinates using browser's Geolocation API
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0,
                });
            });

            const { latitude, longitude } = position.coords;

            // Use reverse geocoding to get location name
            // Here we're using a free service - Nominatim by OpenStreetMap
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&addressdetails=1`,
                {
                    headers: {
                        "User-Agent": "FCMB CIB", // Required by Nominatim's usage policy
                    },
                }
            );

            if (!response.ok) {
                throw new Error("Failed to fetch location data");
            }

            const data = await response.json();

            // Format the location as "City, Country"
            const city =
                data.address.city ||
                data.address.town ||
                data.address.village ||
                data.address.hamlet ||
                data.address.county ||
                "Unknown";
            const country = data.address.country || "Unknown";

            setLocation(`${city}, ${country}`);
            setLoading(false);
        } catch (err) {
            console.error("Error getting location:", err);
            setError("Unable to retrieve your location");
            setLoading(false);
        }
    };

    useEffect(() => {
        getCurrentLocation();
    }, []);

    return { location, loading, error, refresh: getCurrentLocation };
};
