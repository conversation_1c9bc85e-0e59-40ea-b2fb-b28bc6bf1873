import { transferAxios } from "@/api/axios";
import { useEffect, useState } from "react";
import { useDebounce } from "./useDebounce";

export const useTransactionFees = ({ amount, type = "transfer" }: { amount: number; type?: "transfer" | "bill" }) => {
    const [transactionFees, setTransactionFees] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    const debouncedAmountValue = useDebounce(amount);

    const getTransactionFees = async () => {
        try {
            setLoading(true);
            const response = await transferAxios.get(`/fee?type=${type}&amount=${debouncedAmountValue}`);
            const fee = response.data.message;
            // The fee is rounded up to 2 decimal places before being sent
            const roundedFee = parseFloat((Math.ceil(fee * 100) / 100).toFixed(2));
            setTransactionFees(roundedFee);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
            setTransactionFees(0);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (debouncedAmountValue) {
            getTransactionFees();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [debouncedAmountValue]);

    return { fees: transactionFees, loading };
};
