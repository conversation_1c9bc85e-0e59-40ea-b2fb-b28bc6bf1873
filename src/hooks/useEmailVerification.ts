import { useState, useCallback, useEffect } from "react";
import { useOtp } from "./useOtp";
import { useRouter } from "next/navigation";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";

interface UseEmailVerificationProps {
    email: string;
    onVerificationSuccess?: () => void;
    onVerificationError?: (error: string) => void;
    redirectPath?: string;
}

/**
 * Custom hook for email verification with OTP
 */
export function useEmailVerification({
    email,
    onVerificationSuccess,
    onVerificationError,
    redirectPath,
}: UseEmailVerificationProps) {
    const router = useRouter();
    const [otpValue, setOtpValue] = useState("");

    const {
        // Send OTP
        sendOtp,
        sendOtpLoading,
        sendOtpSuccess,
        sendOtpError,

        // Validate OTP
        validateOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,

        // Resend OTP
        resendOtp,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,

        // Utilities
        clearOtpState,
    } = useOtp();

    // Send OTP when component mounts
    useEffect(() => {
        if (email) {
            sendOtp({
                receiver: email,
                receiverType: "EMAIL",
            }).catch(() => {
                // Error is handled in the hook
            });
        }
    }, [email, sendOtp]);

    // Handle OTP validation
    const handleValidateOtp = useCallback(async () => {
        if (!otpValue || otpValue.length !== 6) {
            sendCatchFeedback("Please enter a valid 6-digit code");
            return;
        }

        try {
            await validateOtp({
                receiver: email,
                otp: otpValue,
            });

            // Clear OTP value on success
            setOtpValue("");

            // Call success callback if provided
            if (onVerificationSuccess) {
                onVerificationSuccess();
            }

            // Redirect if path is provided
            if (redirectPath) {
                router.push(redirectPath);
            }
        } catch (error) {
            // Error is handled in the hook
            if (onVerificationError && validateOtpError) {
                onVerificationError(validateOtpError);
            }
        }
    }, [
        email,
        otpValue,
        validateOtp,
        onVerificationSuccess,
        onVerificationError,
        redirectPath,
        router,
        validateOtpError,
    ]);

    // Handle resend OTP
    const handleResendOtp = useCallback(async () => {
        try {
            await resendOtp(email);
        } catch (error) {
            // Error is handled in the hook
        }
    }, [email, resendOtp]);

    // Handle effects for success and error states
    useEffect(() => {
        if (validateOtpSuccess) {
            sendFeedback("Email verified successfully", "success", () => clearOtpState("validate"));
        }

        if (validateOtpError) {
            sendCatchFeedback(validateOtpError, () => clearOtpState("validate"));
            setOtpValue("");
        }

        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success", () => clearOtpState("resend"));
        }

        if (resendOtpError) {
            sendCatchFeedback(resendOtpError, () => clearOtpState("resend"));
        }
    }, [validateOtpSuccess, validateOtpError, resendOtpSuccess, resendOtpError, clearOtpState]);

    return {
        // OTP value
        otpValue,
        setOtpValue,

        // Loading states
        isLoading: validateOtpLoading,
        isResendLoading: resendOtpLoading,

        // Success states
        isSuccess: validateOtpSuccess,

        // Error states
        error: validateOtpError || sendOtpError || resendOtpError,

        // Actions
        validateOtp: handleValidateOtp,
        resendOtp: handleResendOtp,
    };
}
