import { userAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useCallback, useState } from "react";

export interface SendOTPParams {
    receiver: string;
    receiverType: "EMAIL" | "SMS" | "PHONE";
}

export interface ValidateOTPParams {
    receiver: string;
    otp: string;
    loginFlag?: boolean | null;
}

/**
 * Custom hook for handling OTP operations
 * Replaces Redux OTP actions with local state management
 */
export function useOtp() {
    // State for sending OTP
    const [sendOtpLoading, setSendOtpLoading] = useState(false);
    const [sendOtpSuccess, setSendOtpSuccess] = useState(false);
    const [sendOtpError, setSendOtpError] = useState<string | null>(null);

    // State for validating OTP
    const [validateOtpLoading, setValidateOtpLoading] = useState(false);
    const [validateOtpSuccess, setValidateOtpSuccess] = useState(false);
    const [validateOtpToken, setValidateOtpToken] = useState<string>("");
    const [validateOtpError, setValidateOtpError] = useState<string | null>(null);

    // State for resending OTP
    const [resendOtpLoading, setResendOtpLoading] = useState(false);
    const [resendOtpSuccess, setResendOtpSuccess] = useState(false);
    const [resendOtpError, setResendOtpError] = useState<string | null>(null);

    /**
     * Send OTP to the specified receiver
     */
    const sendOtp = useCallback(async (params: SendOTPParams) => {
        setSendOtpLoading(true);
        setSendOtpError(null);
        setSendOtpSuccess(false);

        try {
            const response = await userAxios.post("/v1/otp-manager", params);
            setSendOtpSuccess(true);
            return response.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorMessage = error?.response?.data?.message || error?.message || "Failed to send OTP";
            setSendOtpError(errorMessage);
            throw error;
        } finally {
            setSendOtpLoading(false);
        }
    }, []);

    /**
     * Validate OTP
     */
    const validateOtp = useCallback(async (params: ValidateOTPParams) => {
        setValidateOtpLoading(true);
        setValidateOtpError(null);
        setValidateOtpSuccess(false);
        setValidateOtpToken("");

        try {
            const response = await userAxios.post("/v1/otp-manager/validate", params);
            setValidateOtpSuccess(true);
            setValidateOtpToken(response.data.token || "");
            return response.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorMessage = error?.response?.data?.message || error?.message || "Invalid OTP";
            setValidateOtpError(errorMessage);
            throw error;
        } finally {
            setValidateOtpLoading(false);
        }
    }, []);

    /**
     * Resend OTP to the specified receiver
     */
    const resendOtp = useCallback(async (receiver: string) => {
        setResendOtpLoading(true);
        setResendOtpError(null);
        setResendOtpSuccess(false);

        try {
            const response = await userAxios.get(`v1/otp-manager/resend?receiver=${receiver}`);
            setResendOtpSuccess(true);
            sendFeedback("OTP has been sent successfully", "success");
            return response.data;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorMessage = error?.response?.data?.message || error?.message || "Failed to resend OTP";
            setResendOtpError(errorMessage);
            sendCatchFeedback(errorMessage);
            throw error;
        } finally {
            setResendOtpLoading(false);
        }
    }, []);

    /**
     * Clear all OTP states
     */
    const clearOtpStates = useCallback(() => {
        // Clear send OTP state
        setSendOtpLoading(false);
        setSendOtpSuccess(false);
        setSendOtpError(null);

        // Clear validate OTP state
        setValidateOtpLoading(false);
        setValidateOtpSuccess(false);
        setValidateOtpToken("");
        setValidateOtpError(null);

        // Clear resend OTP state
        setResendOtpLoading(false);
        setResendOtpSuccess(false);
        setResendOtpError(null);
    }, []);

    /**
     * Clear specific OTP state
     */
    const clearOtpState = useCallback((type: "send" | "validate" | "resend") => {
        switch (type) {
            case "send":
                setSendOtpLoading(false);
                setSendOtpSuccess(false);
                setSendOtpError(null);
                break;
            case "validate":
                setValidateOtpLoading(false);
                setValidateOtpSuccess(false);
                setValidateOtpToken("");
                setValidateOtpError(null);
                break;
            case "resend":
                setResendOtpLoading(false);
                setResendOtpSuccess(false);
                setResendOtpError(null);
                break;
        }
    }, []);

    return {
        // Send OTP
        sendOtp,
        sendOtpLoading,
        sendOtpSuccess,
        sendOtpError,

        // Validate OTP
        validateOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,
        validateOtpToken,

        // Resend OTP
        resendOtp,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,

        // Utilities
        clearOtpStates,
        clearOtpState,
    };
}
