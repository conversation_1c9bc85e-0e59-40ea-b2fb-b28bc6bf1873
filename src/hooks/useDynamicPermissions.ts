/**
 * Dynamic Permissions Hook
 *
 * This hook provides access to dynamically fetched permissions from the API,
 * replacing the static permissions file. It maintains backward compatibility
 * with existing code while providing live permissions data.
 */

import { useEffect, useState } from "react";
import permissionsService, { type PermissionsServiceState } from "@/services/permissionsService";
import { transformToBackwardCompatibleFormat } from "@/utils/permissionTransforms";
import type { DynamicPermissions } from "@/types/permissions";

// Re-export for backward compatibility
export type { DynamicPermissions };

export interface UseDynamicPermissionsReturn {
    permissions: DynamicPermissions;
    isLoading: boolean;
    isLoaded: boolean;
    error: string | null;
    refresh: () => Promise<void>;
    lastFetched: number | null;
}

/**
 * Hook to access dynamic permissions
 */
export const useDynamicPermissions = (): UseDynamicPermissionsReturn => {
    const [state, setState] = useState<PermissionsServiceState>(permissionsService.getState());

    useEffect(() => {
        // Initialize permissions service
        permissionsService.initialize();

        // Subscribe to state changes
        const unsubscribe = permissionsService.subscribe(setState);

        return unsubscribe;
    }, []);

    // Transform permissions to backward-compatible format
    const permissions = transformToBackwardCompatibleFormat(state.allPermissions);

    return {
        permissions,
        isLoading: state.isLoading,
        isLoaded: state.isLoaded,
        error: state.error,
        refresh: permissionsService.refresh.bind(permissionsService),
        lastFetched: state.lastFetched,
    };
};

/**
 * Hook to get a specific permission module
 */
export const usePermissionModule = (moduleName: string) => {
    const [state, setState] = useState<PermissionsServiceState>(permissionsService.getState());

    useEffect(() => {
        permissionsService.initialize();
        const unsubscribe = permissionsService.subscribe(setState);
        return unsubscribe;
    }, []);

    return {
        permissions: permissionsService.getModulePermissions(moduleName),
        isLoading: state.isLoading,
        isLoaded: state.isLoaded,
        error: state.error,
    };
};

/**
 * Hook to check if a specific permission exists in the system
 */
export const usePermissionExists = (permissionName: string) => {
    const [state, setState] = useState<PermissionsServiceState>(permissionsService.getState());

    useEffect(() => {
        permissionsService.initialize();
        const unsubscribe = permissionsService.subscribe(setState);
        return unsubscribe;
    }, []);

    return {
        exists: permissionsService.hasPermission(permissionName),
        isLoading: state.isLoading,
        isLoaded: state.isLoaded,
    };
};

/**
 * Hook to get all permissions as a flat list
 */
export const useAllPermissionsList = () => {
    const [state, setState] = useState<PermissionsServiceState>(permissionsService.getState());

    useEffect(() => {
        permissionsService.initialize();
        const unsubscribe = permissionsService.subscribe(setState);
        return unsubscribe;
    }, []);

    return {
        permissions: permissionsService.getAllPermissionsList(),
        isLoading: state.isLoading,
        isLoaded: state.isLoaded,
        error: state.error,
    };
};

// Export the service for direct access if needed
export { permissionsService };

export default useDynamicPermissions;
