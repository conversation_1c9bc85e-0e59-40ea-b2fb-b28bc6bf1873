import { useEffect, useState, useRef, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getTrustedDevices, removeDevice, removeAllDevices } from "@/redux/actions/settingsTrustedDevices";
import { clearState } from "@/redux/slices/trustedDevices";

/**
 * Custom hook to manage trusted devices
 * - Fetches devices on first mount only
 * - Provides functions to remove individual devices and all devices
 * - Tracks loading states for individual device removal
 */
export const useTrustedDevices = () => {
    const dispatch = useAppDispatch();

    // Get trusted devices state
    const {
        data: trustedDevices,
        loading: trustedDevicesLoading,
        error: trustedDevicesError,
    } = useAppSelector((state) => state.trustedDevices.getTrustedDevices);

    // Get remove device states
    const { loading: removeDeviceLoading, success: removeDeviceSuccess } = useAppSelector(
        (state) => state.trustedDevices.removeDevice
    );

    // Get remove all devices states
    const { loading: removeAllDevicesLoading, success: removeAllDevicesSuccess } = useAppSelector(
        (state) => state.trustedDevices.removeAllDevices
    );

    // Track which device is being removed
    const [removingDeviceId, setRemovingDeviceId] = useState<number | null>(null);

    // Track if component is mounted
    const isMounted = useRef(false);

    // Fetch trusted devices only on first mount
    useEffect(() => {
        if (!isMounted.current) {
            dispatch(getTrustedDevices());
            isMounted.current = true;
        }
    }, [dispatch]);

    // Clear success state after removing a device
    useEffect(() => {
        if (removeDeviceSuccess) {
            dispatch(clearState("removeDevice"));
            setRemovingDeviceId(null); // Reset the removing device ID
        }
    }, [removeDeviceSuccess, dispatch]);

    // Clear success state after removing all devices
    useEffect(() => {
        if (removeAllDevicesSuccess) {
            dispatch(clearState("removeAllDevices"));
        }
    }, [removeAllDevicesSuccess, dispatch]);

    // Handle removing a single device
    const handleRemoveDevice = useCallback(
        (deviceId: number) => {
            setRemovingDeviceId(deviceId);
            dispatch(removeDevice({ deviceId }));
        },
        [dispatch]
    );

    // Handle removing all devices
    const handleRemoveAllDevices = useCallback(() => {
        setRemovingDeviceId(null); // Reset any removing device ID
        dispatch(removeAllDevices());
    }, [dispatch]);

    return {
        // Data
        trustedDevices,

        // Loading states
        trustedDevicesLoading,
        removeDeviceLoading,
        removeAllDevicesLoading,

        // Error state
        trustedDevicesError,

        // Tracking state
        removingDeviceId,

        // Actions
        handleRemoveDevice,
        handleRemoveAllDevices,

        // Utility to manually refresh devices if needed
        refreshDevices: () => dispatch(getTrustedDevices()),
    };
};
