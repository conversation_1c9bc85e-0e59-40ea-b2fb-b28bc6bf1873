"use client";
import { getSessionDetails } from "@/functions/userSession";
import { PATH_PROTECTED } from "@/routes/path";
import Cookies from "js-cookie";

const useRouteAfterLogin = (): string => {
    const session = getSessionDetails();

    let route: string;
    if (session && session?.hasAccount === "true") {
        route = Cookies.get("redirectTo") || PATH_PROTECTED.root;
    } else {
        route = PATH_PROTECTED.onboarding;
    }

    return route;
};

export default useRouteAfterLogin;
