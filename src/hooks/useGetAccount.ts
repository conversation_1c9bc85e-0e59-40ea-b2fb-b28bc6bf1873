import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { AccountDetailsType } from "@/redux/types/dashboard";
import { useState, useCallback } from "react";

const useGetAccountDetails = () => {
    const [loading, setLoading] = useState(false);
    const [details, setDetails] = useState<AccountDetailsType | undefined>(undefined);

    // Memoize getDetails function to prevent infinite re-renders
    // This function makes API calls to fetch account details and should maintain stable reference
    const getDetails = useCallback(async (accountNumber: string): Promise<AccountDetailsType | undefined> => {
        try {
            setLoading(true);

            const res = await acctsAxios.get(`/api/v1/accounts/${accountNumber}/details`);

            setDetails(res.data);
            return res.data;
        } catch (error) {
            sendCatchFeedback(error);
            setDetails(undefined);
            return undefined;
        } finally {
            setLoading(false);
        }
    }, []); // Empty dependency array since function doesn't depend on any changing values

    return {
        details,
        loading,
        getDetails,
    };
};

export default useGetAccountDetails;
