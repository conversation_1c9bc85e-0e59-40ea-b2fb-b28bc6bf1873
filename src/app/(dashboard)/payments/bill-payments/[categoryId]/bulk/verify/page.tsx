"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import VerifyBulkDetails from "@/components/page-components/dashboard/bill-payments/bulk-payment/verify-details";
import { useCategoryDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-details";
import { PATH_PROTECTED } from "@/routes/path";

const VerifyBulkDetailsPage = () => {
    const { categoryId } = useParams();
    const router = useRouter();

    const { categoryName } = useCategoryDetails(categoryId);

    // Convert categoryId to string if it's an array (Next.js can return string | string[])
    const normalizedCategoryId = Array.isArray(categoryId) ? categoryId[0] : categoryId;

    // Use the proper routing function to navigate to the payment info page
    // This prevents the [object Object] URL issue by using the predefined path function
    const handleContinue = () => {
        router.push(PATH_PROTECTED.billPayments.bulk.paymentInfo(normalizedCategoryId));
    };

    // Use the proper routing function to navigate back to the bulk payment root page
    // This ensures consistent routing throughout the application
    const handleBack = () => {
        router.push(PATH_PROTECTED.billPayments.bulk.root(normalizedCategoryId));
    };

    return <VerifyBulkDetails onContinue={handleContinue} onBack={handleBack} categoryName={categoryName} />;
};

export default VerifyBulkDetailsPage;
