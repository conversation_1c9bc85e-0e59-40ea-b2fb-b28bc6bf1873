"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAppSelector } from "@/redux/hooks";
import PaymentInfo from "@/components/page-components/dashboard/bill-payments/bulk-payment/payment-info";
import { useCategoryDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-details";
import { PATH_PROTECTED } from "@/routes/path";

const BulkPaymentInfoPage = () => {
    const { categoryId } = useParams();
    const router = useRouter();
    const { validEntriesCount, totalValidAmount } = useAppSelector((state) => state.bulkAirtime);

    const { categoryName } = useCategoryDetails(categoryId);

    // Convert categoryId to string if it's an array (Next.js can return string | string[])
    const normalizedCategoryId = Array.isArray(categoryId) ? categoryId[0] : categoryId;

    // Use the proper routing function to navigate to the review page
    // This prevents the [object Object] URL issue by using the predefined path function
    const handleContinue = () => {
        router.push(PATH_PROTECTED.billPayments.bulk.review(normalizedCategoryId));
    };

    // Use the proper routing function to navigate back to the verify page
    // This ensures consistent routing throughout the application
    const handleBack = () => {
        router.push(PATH_PROTECTED.billPayments.bulk.verify(normalizedCategoryId));
    };

    return (
        <PaymentInfo
            onContinue={handleContinue}
            onBack={handleBack}
            categoryId={Number(Array.isArray(categoryId) ? categoryId[0] : categoryId) || 0}
            categoryName={categoryName}
            validEntriesCount={validEntriesCount}
            totalValidAmount={totalValidAmount}
        />
    );
};

export default BulkPaymentInfoPage;
