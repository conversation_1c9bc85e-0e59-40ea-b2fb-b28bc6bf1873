"use client";

import React from "react";
import { useParams } from "next/navigation";
import UploadBulkDetails from "@/components/page-components/dashboard/bill-payments/bulk-payment/upload-details";
import { useCategoryDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-details";

const BulkBillPaymentPage = () => {
    const { categoryId } = useParams();
    const { cleanedCategoryName } = useCategoryDetails(categoryId);

    // Determine which component to render based on categoryId
    const renderComponent = () => {
        // First, try to match by category ID
        if (!categoryId) return null;

        const categoryIdNum = Number(categoryId);

        // Currently, only Airtime supports bulk payments, but this structure
        // allows for easy extension to other categories in the future

        // Match components by category ID
        // Note: Currently, all bulk payments go through the same upload process
        // This can be extended later when other bulk payment types have unique requirements
        // For now, all bulk payments use the same upload component
        // The category ID is passed as a prop to help the component adapt
        return <UploadBulkDetails categoryId={categoryIdNum} categoryName={cleanedCategoryName} />;
    };

    return <div>{renderComponent()}</div>;
};

export default BulkBillPaymentPage;
