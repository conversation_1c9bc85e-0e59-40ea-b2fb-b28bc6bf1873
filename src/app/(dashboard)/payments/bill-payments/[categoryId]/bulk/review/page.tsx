"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAppSelector } from "@/redux/hooks";
import ReviewBill from "@/components/page-components/dashboard/bill-payments/bulk-payment/review-bill";
import { useCategoryDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-details";
import { PATH_PROTECTED } from "@/routes/path";

const BulkReviewPage = () => {
    const { categoryId } = useParams();
    const router = useRouter();
    const { categoryName } = useCategoryDetails(categoryId);

    const entries = useAppSelector((state) => state.bulkAirtime.entries);
    const validEntries = entries.filter((entry) => entry.status === "Valid");
    const totalAmount = validEntries.reduce((sum, entry) => sum + entry.amount, 0);

    // Convert categoryId to string if it's an array (Next.js can return string | string[])
    const normalizedCategoryId = Array.isArray(categoryId) ? categoryId[0] : categoryId;

    // Use the proper routing function to navigate back to the payment info page
    // This prevents the [object Object] URL issue by using the predefined path function
    const handleBack = () => {
        router.push(PATH_PROTECTED.billPayments.bulk.paymentInfo(normalizedCategoryId));
    };

    // Use the proper routing function to navigate to the bill payments dashboard
    // This follows the frontend guideline to use predefined paths
    const handleConfirm = () => {
        // Make API call to process payment here
        router.push(PATH_PROTECTED.billPayments.root);
    };

    return (
        <ReviewBill
            onBack={handleBack}
            onConfirm={handleConfirm}
            totalAmount={totalAmount}
            recipientsCount={validEntries.length}
            categoryName={categoryName}
        />
    );
};

export default BulkReviewPage;
