import React from "react";

interface CategoryIdLayoutProps {
    children: React.ReactNode;
    params: {
        categoryId: string;
    };
}

/**
 * Layout component for [categoryId] route that provides support for dynamic category routing.
 * All category IDs are valid, allowing different bill payment types (utility and normal)
 * to be displayed based on the category.
 */
export default function CategoryIdLayout({ children, params }: Readonly<CategoryIdLayoutProps>) {
    return <>{children}</>;
}
