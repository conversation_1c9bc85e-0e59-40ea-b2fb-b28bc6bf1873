"use client";

import React from "react";
import SingleReviewBill from "@/components/page-components/dashboard/bill-payments/single-payment/review-bill";
import { useCategoryBillDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-bill-details";

const SingleReviewPage = () => {
    const {
        billType,
        billDetails,
        paymentInfo,
        cleanedCategoryName,
        navigateToPaymentInfo,
        navigateToRoot,
        navigateToBillPayments,
    } = useCategoryBillDetails();

    return (
        <SingleReviewBill
            billType={billType}
            phoneNumber={billDetails?.phoneNumber}
            identificationNumber={billDetails?.identificationNumber}
            smartCardNumber={billDetails?.smartCardNumber}
            amount={billDetails?.amount ?? 0}
            networkProvider={billDetails?.networkProvider}
            serviceProvider={billDetails?.serviceProvider}
            package={billDetails?.package}
            serviceID={billDetails?.serviceID}
            customerDetails={billDetails?.customerDetails}
            onBack={navigateToPaymentInfo}
            onConfirm={navigateToBillPayments}
            paymentInfo={paymentInfo}
            onChangeBillDetails={navigateToRoot}
            categoryName={cleanedCategoryName}
        />
    );
};

export default SingleReviewPage;
