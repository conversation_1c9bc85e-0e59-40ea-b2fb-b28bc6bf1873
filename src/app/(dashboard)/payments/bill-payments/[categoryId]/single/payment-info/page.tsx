"use client";

import React from "react";
import PaymentInfo, {
    BillDetails,
} from "@/components/page-components/dashboard/bill-payments/single-payment/payment-info";
import { useCategoryBillDetails } from "@/components/page-components/dashboard/bill-payments/hooks/use-category-bill-details";

const SinglePaymentInfoPage = () => {
    const { cleanedCategoryName, billType, billDetails, paymentInfo, navigateToReview, navigateToRoot } =
        useCategoryBillDetails();

    // Create empty default values object
    const defaultValues = {
        narration: "",
        attachment: null,
        accountNumber: "",
    };

    // Create bill details object with correct type
    const billDetailsWithType = {
        type: billType,
        amount: billDetails?.amount ?? 0,
        serviceProvider: billDetails?.serviceProvider ?? "",
        package: billDetails?.package ?? "",
        ...(billType === "airtime" && { phoneNumber: billDetails?.phoneNumber ?? "" }),
        ...(billType === "electricity" && { identificationNumber: billDetails?.identificationNumber ?? "" }),
        ...(billType === "cable-tv" && { smartCardNumber: billDetails?.smartCardNumber ?? "" }),
    } as BillDetails;

    return (
        <PaymentInfo
            onBack={navigateToRoot}
            onContinue={navigateToReview}
            billDetails={billDetailsWithType}
            defaultValues={paymentInfo || defaultValues}
            categoryName={cleanedCategoryName}
        />
    );
};

export default SinglePaymentInfoPage;
