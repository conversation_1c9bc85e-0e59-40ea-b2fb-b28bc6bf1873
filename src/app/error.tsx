// app/error.tsx
"use client";

import { Button } from "@/components/common/buttonv3";
import { ArrowBigLeftDash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function GlobalError({ error, reset }: { error: Error; reset: () => void }) {
    const router = useRouter();
    useEffect(() => {
        console.error("Global error boundary caught:", error);
    }, [error]);

    return (
        <div className="flex items-center justify-center min-h-screen p-4 flex-col text-center relative">
            <div className="absolute left-4 top-4">
                <Button leftIcon={<ArrowBigLeftDash />} onClick={() => router.back()}>
                    Back
                </Button>
            </div>
            <h2 className="text-2xl font-bold text-red-600">Uh-oh! Something didn’t go as planned.</h2>
            <p className="text-base text-gray-600 mt-2 mb-3">
                It looks like something went off-track. Let’s give it another go!
            </p>
            <Button onClick={reset}>Try again</Button>
        </div>
    );
}
