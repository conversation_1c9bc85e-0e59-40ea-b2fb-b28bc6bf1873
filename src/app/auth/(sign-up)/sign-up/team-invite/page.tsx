"use client";
import AuthLayout from "@/components/layout/auth-layout";
import { PATH_AUTH } from "@/routes/path";
import TeamInvite from "@/components/page-components/auth/sign-up/team-invite";
import { useSearchParams } from "next/navigation";

export default function TeamInvitePage() {
    const params = useSearchParams();
    const emailAddress = params.get("email");

    if (!emailAddress) {
        return null;
    }

    return (
        <AuthLayout questionTitle="Have an account?" btnTitle="Login" route={PATH_AUTH.login}>
            <TeamInvite />
        </AuthLayout>
    );
}
