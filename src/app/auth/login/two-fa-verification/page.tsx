"use client";

// PROJECT IMPORTS
import AuthLayout from "@/components/layout/auth-layout";
import { PATH_AUTH } from "@/routes/path";
import TwoFaForm from "@/components/page-components/auth/log-in/two-fa-form";
import { useSearchParams } from "next/navigation";
import SecurityQuestion from "@/components/page-components/auth/log-in/security-question";
import SmsVerification from "@/components/page-components/auth/log-in/sms-verification";

const TwoFaVerification = () => {
    const params = useSearchParams();
    const selectedOption = params.get("mfa");

    if (!selectedOption) {
        return null;
    }

    const questionTitle = selectedOption === "sms" ? "" : "Have an account?";
    const btnLink = selectedOption === "sms" ? "" : PATH_AUTH.login;
    const btnTitle = selectedOption === "sms" ? "Get support" : "Log in";

    return (
        <AuthLayout btnTitle={btnTitle} questionTitle={questionTitle} route={btnLink}>
            {selectedOption === "authenticator" && <TwoFaForm />}
            {selectedOption === "security-question" && <SecurityQuestion />}
            {selectedOption === "sms" && <SmsVerification />}
        </AuthLayout>
    );
};

export default TwoFaVerification;
