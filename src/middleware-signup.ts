import { NextResponse, type NextRequest } from "next/server";
import { PATH_AUTH } from "./routes/path";
import { SignupStep, getSignupStepRoute } from "./lib/session-utils";

// Define your signup routes and their required steps
const SIGNUP_ROUTES: Record<string, SignupStep> = {
    [PATH_AUTH.signup]: SignupStep.PERSONAL_INFO,
    [PATH_AUTH.verifyPersonalEmail]: SignupStep.VERIFY_PERSONAL_EMAIL,
    [PATH_AUTH.verifyPhoneNumber]: SignupStep.VERIFY_PHONE_NUMBER,
    [PATH_AUTH.businessInfo]: SignupStep.BUSINESS_INFO,
    [PATH_AUTH.validateBusiness]: SignupStep.VALIDATE_BUSINESS,
    [PATH_AUTH.verifyBusinessEmail]: SignupStep.VERIFY_BUSINESS_EMAIL,
    [PATH_AUTH.setUpMfa]: SignupStep.MFA_SETUP,
};

/**
 * Signup flow middleware function
 * Handles route guarding for the signup flow
 */
export function handleSignupMiddleware(request: NextRequest) {
    const pathname = request.nextUrl.pathname;
    console.log(`Handling signup flow for path: ${pathname}`);

    // Find the exact route match or the most specific match
    let matchedRoute: string | undefined;
    let requiredStep: SignupStep | undefined;

    // Find the most specific matching route
    for (const route of Object.keys(SIGNUP_ROUTES)) {
        if (pathname === route || pathname.startsWith(`${route}/`)) {
            // If we haven't matched a route yet, or this route is more specific (longer)
            if (!matchedRoute || route.length > matchedRoute.length) {
                matchedRoute = route;
                requiredStep = SIGNUP_ROUTES[route];
            }
        }
    }

    // If no route matched, allow the request
    if (!matchedRoute || requiredStep === undefined) {
        console.log("No matching signup route found, allowing access");
        return NextResponse.next();
    }

    console.log(`Matched signup route: ${matchedRoute}, required step: ${requiredStep}`);

    // Get the current signup step from the cookie
    const signupProgressCookie = request.cookies.get("signup_progress")?.value;
    let currentStep = SignupStep.PERSONAL_INFO; // Default to first step

    if (signupProgressCookie) {
        // Parse the cookie value
        const parsedStep = Number.parseInt(signupProgressCookie, 10);

        // Validate the step value
        if (!isNaN(parsedStep) && parsedStep >= SignupStep.PERSONAL_INFO && parsedStep <= SignupStep.MFA_SETUP) {
            currentStep = parsedStep;
        }
    }

    console.log(`Current signup step: ${currentStep}`);

    // If user is trying to access a step they haven't reached yet
    if (requiredStep > currentStep && requiredStep !== SignupStep.PERSONAL_INFO) {
        console.log(`User trying to access step ${requiredStep} but has only reached step ${currentStep}`);

        // Get the appropriate route for their current step
        const redirectRoute = getSignupStepRoute(currentStep);
        console.log(`Redirecting to appropriate signup step: ${redirectRoute}`);

        const redirectResponse = NextResponse.redirect(new URL(redirectRoute, request.url));

        // Ensure the cookie is set with the current step
        redirectResponse.cookies.set("signup_progress", currentStep.toString(), {
            path: "/",
            maxAge: 60 * 60 * 24, // 1 day
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
        });

        return redirectResponse;
    }

    // If this is a new step the user is allowed to access, update the progress
    if (requiredStep > currentStep) {
        const response = NextResponse.next();
        response.cookies.set("signup_progress", requiredStep.toString(), {
            path: "/",
            maxAge: 60 * 60 * 24, // 1 day
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
        });
        return response;
    }

    console.log("Allowing access to current signup step");
    return NextResponse.next();
}
