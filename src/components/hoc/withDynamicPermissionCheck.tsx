/**
 * Dynamic Permission Check HOC
 *
 * This HOC provides dynamic permission checking that automatically updates
 * when backend permissions change. It eliminates the need for hardcoded
 * permission strings while maintaining type safety.
 *
 * Usage:
 * ```typescript
 * const ProtectedPage = withDynamicPermissionCheck(Component, {
 *     permissions: (PERMISSIONS) => [PERMISSIONS.TRANSACTIONS.VIEW_ALL],
 *     requireAll: false,
 *     redirectTo: PATH_PROTECTED.root,
 * });
 * ```
 */

"use client";

import React, { ComponentType, useMemo } from "react";
import withPermissionCheck from "./withPermissionCheck";
import { usePermissionConstants, type PermissionConstants } from "@/hooks/usePermissionConstants";
import { PATH_PROTECTED } from "@/routes/path";

// Loading component for permission resolution
const PermissionResolutionLoader: React.FC = () => (
    <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4" />
            <p className="text-gray-600">Resolving permissions...</p>
        </div>
    </div>
);

interface WithDynamicPermissionCheckProps {
    /**
     * Function that receives the PERMISSIONS object and returns required permissions
     * This allows for dynamic permission resolution based on backend data
     */
    permissions: (PERMISSIONS: PermissionConstants) => string[];

    /**
     * If true, all permissions are required. If false, any one is sufficient.
     */
    requireAll?: boolean;

    /**
     * Route to redirect to if the user doesn't have the required permissions
     */
    redirectTo?: string;

    /**
     * Optional fallback permissions to use if dynamic permissions fail to load
     * This provides a safety net for critical pages
     */
    fallbackPermissions?: string[];

    /**
     * Whether to show loading state while permissions are being resolved
     * If false, will use fallback permissions immediately
     */
    showLoadingState?: boolean;
}

/**
 * Higher-order component that provides dynamic permission checking
 *
 * This HOC automatically handles:
 * - Dynamic permission resolution from backend
 * - Loading states during permission fetch
 * - Fallback permissions for safety
 * - Automatic updates when permissions change
 *
 * @param Component - The component to wrap
 * @param config - Configuration object for permission checking
 * @returns The wrapped component with dynamic permission check
 */
export const withDynamicPermissionCheck = <P extends object>(
    Component: ComponentType<P>,
    config: WithDynamicPermissionCheckProps
) => {
    const {
        permissions: getPermissions,
        requireAll = false,
        redirectTo = PATH_PROTECTED.root,
        fallbackPermissions = [],
        showLoadingState = true,
    } = config;

    const DynamicPermissionWrapper: React.FC<P> = (props) => {
        const { PERMISSIONS, isLoaded, validatePermissions } = usePermissionConstants();

        // Resolve the required permissions dynamically
        const resolvedPermissions = useMemo(() => {
            if (!isLoaded) {
                // If not loaded and we have fallback permissions, use them
                if (fallbackPermissions.length > 0) {
                    return fallbackPermissions;
                }
                // Otherwise return empty array to trigger loading state
                return [];
            }

            try {
                const dynamicPermissions = getPermissions(PERMISSIONS);

                // Validate permissions in development
                if (process.env.NODE_ENV === "development") {
                    const validation = validatePermissions(dynamicPermissions);
                    if (validation.invalid.length > 0) {
                        console.warn(
                            "withDynamicPermissionCheck: Invalid permissions detected:",
                            validation.invalid,
                            "Valid permissions:",
                            validation.valid
                        );
                    }
                }

                return dynamicPermissions;
            } catch (error) {
                console.error("Error resolving dynamic permissions:", error);

                // Fall back to fallback permissions if available
                if (fallbackPermissions.length > 0) {
                    console.warn("Using fallback permissions due to resolution error");
                    return fallbackPermissions;
                }

                // If no fallback, return empty array (will deny access)
                return [];
            }
        }, [isLoaded, PERMISSIONS, validatePermissions]);

        // Show loading state if permissions are not resolved and loading state is enabled
        if (!isLoaded && resolvedPermissions.length === 0 && showLoadingState) {
            return <PermissionResolutionLoader />;
        }

        // If we have no permissions to check (and no fallback), deny access
        if (resolvedPermissions.length === 0) {
            console.warn("withDynamicPermissionCheck: No permissions resolved, denying access");
            // Return null or redirect - the underlying withPermissionCheck will handle this
        }

        // Create the protected component with resolved permissions
        const ProtectedComponent = useMemo(
            () =>
                withPermissionCheck(Component, {
                    requiredPermissions: resolvedPermissions,
                    requireAll,
                    redirectTo,
                }),
            [resolvedPermissions]
        );

        return <ProtectedComponent {...props} />;
    };

    // Set display name for debugging
    DynamicPermissionWrapper.displayName = `withDynamicPermissionCheck(${Component.displayName || Component.name})`;

    return DynamicPermissionWrapper;
};

/**
 * Convenience function for common permission patterns
 */
export const createPermissionChecker = {
    /**
     * Check for a single permission
     */
    single: (permissionGetter: (PERMISSIONS: PermissionConstants) => string) => (PERMISSIONS: PermissionConstants) => [
        permissionGetter(PERMISSIONS),
    ],

    /**
     * Check for multiple permissions (any one required)
     */
    any:
        (permissionGetters: Array<(PERMISSIONS: PermissionConstants) => string>) =>
        (PERMISSIONS: PermissionConstants) =>
            permissionGetters.map((getter) => getter(PERMISSIONS)),

    /**
     * Check for multiple permissions (all required)
     */
    all:
        (permissionGetters: Array<(PERMISSIONS: PermissionConstants) => string>) =>
        (PERMISSIONS: PermissionConstants) =>
            permissionGetters.map((getter) => getter(PERMISSIONS)),

    /**
     * Check for module-based permissions
     */
    module: (moduleName: keyof PermissionConstants, permissionKeys: string[]) => (PERMISSIONS: PermissionConstants) => {
        const modulePermissions = PERMISSIONS[moduleName] as Record<string, string>;
        return permissionKeys.map((key) => modulePermissions[key]).filter(Boolean);
    },
};

/**
 * Pre-configured permission checkers for common use cases
 */
export const PERMISSION_CHECKERS = {
    // Transaction permissions
    VIEW_ALL_TRANSACTIONS: createPermissionChecker.single((P) => P.TRANSACTIONS.VIEW_ALL),
    VIEW_OWN_TRANSACTIONS: createPermissionChecker.single((P) => P.TRANSACTIONS.VIEW_OWN),
    MANAGE_TRANSACTIONS: createPermissionChecker.any([
        (P) => P.TRANSACTIONS.CREATE,
        (P) => P.TRANSACTIONS.EDIT,
        (P) => P.TRANSACTIONS.DELETE,
    ]),

    // Transfer permissions
    CREATE_TRANSFERS: createPermissionChecker.single((P) => P.TRANSFERS.CREATE),
    APPROVE_TRANSFERS: createPermissionChecker.single((P) => P.TRANSFERS.APPROVE),
    MANAGE_TRANSFERS: createPermissionChecker.any([
        (P) => P.TRANSFERS.CREATE,
        (P) => P.TRANSFERS.APPROVE,
        (P) => P.TRANSFERS.CANCEL,
    ]),

    // Admin permissions
    USER_MANAGEMENT: createPermissionChecker.any([
        (P) => P.USERS.VIEW_ALL,
        (P) => P.USERS.CREATE,
        (P) => P.USERS.EDIT,
        (P) => P.USERS.DELETE,
    ]),

    ROLE_MANAGEMENT: createPermissionChecker.any([
        (P) => P.ROLES.VIEW_ALL,
        (P) => P.ROLES.CREATE,
        (P) => P.ROLES.EDIT,
        (P) => P.ROLES.DELETE,
    ]),

    // Team Management permissions - matches actual backend permissions
    TEAM_MANAGEMENT_PERMISSIONS: createPermissionChecker.any([
        (P) => P.TEAM_MANAGEMENT.VIEW_TEAM_MEMBERS,
        (P) => P.TEAM_MANAGEMENT.INVITE_TEAM_MEMBERS,
        (P) => P.TEAM_MANAGEMENT.EDIT_TEAM_MEMBERS,
        (P) => P.TEAM_MANAGEMENT.REMOVE_TEAM_MEMBERS,
        (P) => P.TEAM_MANAGEMENT.VIEW_ROLES,
        (P) => P.TEAM_MANAGEMENT.CREATE_ROLES,
        (P) => P.TEAM_MANAGEMENT.MANAGE_PERMISSIONS,
    ]),

    // Settings permissions
    SETTINGS_ACCESS: createPermissionChecker.any([(P) => P.SETTINGS.VIEW, (P) => P.SETTINGS.EDIT]),

    // Dashboard permissions
    DASHBOARD_ACCESS: createPermissionChecker.single((P) => P.DASHBOARD.VIEW),

    // Send Money permissions
    SEND_MONEY_ACCESS: createPermissionChecker.any([
        (P) => P.SEND_MONEY.INITIATE_SINGLE_PAYMENTS,
        (P) => P.SEND_MONEY.INITIATE_BULK_PAYMENTS,
        (P) => P.SEND_MONEY.VIEW_SCHEDULED_PAYMENTS,
    ]),

    // Bill Payments permissions
    BILL_PAYMENTS_ACCESS: createPermissionChecker.any([
        (P) => P.BILL_PAYMENTS.VIEW_CATEGORIES,
        (P) => P.BILL_PAYMENTS.INITIATE_BILL_PAYMENT,
        (P) => P.BILL_PAYMENTS.VIEW_PAYMENT_HISTORY,
    ]),

    // Beneficiaries permissions
    BENEFICIARIES_ACCESS: createPermissionChecker.any([
        (P) => P.BENEFICIARIES.VIEW_LIST,
        (P) => P.BENEFICIARIES.ADD_NEW,
        (P) => P.BENEFICIARIES.EDIT_DETAILS,
    ]),

    // Accounts permissions
    ACCOUNTS_ACCESS: createPermissionChecker.any([
        (P) => P.ACCOUNTS.VIEW_ALL,
        (P) => P.ACCOUNTS.VIEW_DETAILS,
        (P) => P.ACCOUNTS.VIEW_TRANSACTION_HISTORY,
    ]),
};

export default withDynamicPermissionCheck;

