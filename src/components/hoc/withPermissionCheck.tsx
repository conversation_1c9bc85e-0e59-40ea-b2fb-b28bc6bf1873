"use client";

import React, { ComponentType, useEffect, useState, useRef, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { PATH_PROTECTED } from "@/routes/path";
import { usePermissions } from "@/contexts/PermissionContext";

// Loading skeleton component for permission checks
const PermissionLoadingSkeleton: React.FC = () => (
    <div className="min-h-screen bg-white">
        <div className="animate-pulse">
            {/* Header skeleton */}
            <div className="h-16 bg-gray-200 mb-8" />

            {/* Content skeleton */}
            <div className="px-8">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-6" />
                <div className="space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-4 bg-gray-200 rounded w-1/2" />
                    <div className="h-4 bg-gray-200 rounded w-5/6" />
                </div>

                {/* Table/content skeleton */}
                <div className="mt-8 space-y-3">
                    <div className="h-12 bg-gray-200 rounded" />
                    <div className="h-12 bg-gray-200 rounded" />
                    <div className="h-12 bg-gray-200 rounded" />
                    <div className="h-12 bg-gray-200 rounded" />
                </div>
            </div>
        </div>
    </div>
);

// Error state component for permission failures
const PermissionErrorState: React.FC<{ onRetry: () => void }> = ({ onRetry }) => (
    <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
            <div className="mb-6">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                    </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Security Verification Required</h3>
                <p className="text-gray-600 mb-4">
                    For your security, we need to verify your access permissions before proceeding.
                </p>
                <p className="text-sm text-gray-500 mb-6">
                    This ensures the safety of your financial data and transactions.
                </p>
                <button
                    onClick={onRetry}
                    className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
                >
                    Refresh & Verify
                </button>
            </div>
        </div>
    </div>
);

interface WithPermissionCheckProps {
    requiredPermissions: string[];
    requireAll?: boolean;
    redirectTo?: string;
}

/**
 * Higher-order component that checks if the user has the required permissions
 * to access a component/page. If not, redirects to a specified route.
 *
 * @param Component - The component to wrap
 * @param requiredPermissions - Array of permissions required to access the component
 * @param requireAll - If true, all permissions are required. If false, any one is sufficient.
 * @param redirectTo - Route to redirect to if the user doesn't have the required permissions
 * @returns The wrapped component with permission check
 */
const withPermissionCheck = <P extends object>(
    Component: ComponentType<P>,
    { requiredPermissions, requireAll = false, redirectTo = PATH_PROTECTED.root }: WithPermissionCheckProps
) => {
    const WithPermissionCheck: React.FC<P> = (props) => {
        const router = useRouter();
        const pathname = usePathname();
        const {
            userPermissions,
            isLoadingPermissions,
            isUsingCachedPermissions,
            permissionFetchFailed,
            isPermissionSystemReady,
        } = usePermissions();
        const { canAccessRoute, permissionExists } = usePermissionCheck();
        const [accessChecked, setAccessChecked] = useState(false);
        const [hasAccess, setHasAccess] = useState(false);
        const previousRouteRef = useRef<string | null>(null);

        // Track previous route for better redirect UX
        useEffect(() => {
            // Store the current route as previous route when component mounts
            // This will be used if user needs to be redirected
            const storedPreviousRoute = sessionStorage.getItem("rbac_previous_route");
            if (storedPreviousRoute && storedPreviousRoute !== pathname) {
                previousRouteRef.current = storedPreviousRoute;
            }

            // Update the stored route for future navigation
            sessionStorage.setItem("rbac_previous_route", pathname);
        }, [pathname]);

        // Helper function to determine the best redirect route
        const getRedirectRoute = useCallback((): string => {
            const previousRoute = previousRouteRef.current;

            // Don't redirect to the same route we're currently on
            if (previousRoute && previousRoute !== pathname) {
                // Don't redirect to auth routes or other protected routes that might also fail
                const isAuthRoute = previousRoute.startsWith("/auth");
                const isProtectedRoute =
                    previousRoute.startsWith("/dashboard") ||
                    previousRoute.startsWith("/transactions") ||
                    previousRoute.startsWith("/teams") ||
                    previousRoute.startsWith("/accounts");

                // If previous route is safe (not auth and not another protected route), use it
                if (!isAuthRoute && !isProtectedRoute) {
                    return previousRoute;
                }

                // If previous route was also protected, redirect to dashboard
                if (isProtectedRoute && previousRoute !== redirectTo) {
                    return redirectTo;
                }
            }

            // Fallback to default redirect

            return redirectTo;
        }, [pathname]);

        // Enhanced permission checking with better race condition handling
        useEffect(() => {
            // Wait for the permission system to be ready
            if (!isPermissionSystemReady) {
                return;
            }

            // Validate that required permissions exist in the system (development check)
            if (process.env.NODE_ENV === "development") {
                const invalidPermissions = requiredPermissions.filter((p) => !permissionExists(p));
                if (invalidPermissions.length > 0) {
                    console.warn("withPermissionCheck: Invalid permissions detected:", invalidPermissions);
                }
            }

            // Permission system is ready, now check access

            // Check if the user has the required permissions
            const hasRequiredAccess = canAccessRoute(requiredPermissions, requireAll);

            // STRICT SECURITY: Only allow access with fresh, valid permissions
            // For financial applications, we cannot risk unauthorized access

            setAccessChecked(true);

            // SECURITY RULE 1: Block access if using cached permissions (potentially stale)
            if (isUsingCachedPermissions) {
                setHasAccess(false);
                return;
            }

            // SECURITY RULE 2: Block access if permission fetch failed (cannot verify permissions)
            if (permissionFetchFailed) {
                setHasAccess(false);
                return;
            }

            // SECURITY RULE 3: Only allow access if we have fresh permissions AND user has required access
            if (hasRequiredAccess && !isUsingCachedPermissions && !permissionFetchFailed) {
                setHasAccess(true);
            } else if (!hasRequiredAccess && !isUsingCachedPermissions && !permissionFetchFailed) {
                // User lacks permissions with fresh data - redirect
                const redirectRoute = getRedirectRoute();

                setHasAccess(false);
                router.push(redirectRoute);
            } else {
                // Default deny for any uncertain state

                setHasAccess(false);
            }
        }, [
            isPermissionSystemReady,
            userPermissions,
            isUsingCachedPermissions,
            permissionFetchFailed,
            router,
            canAccessRoute,
            getRedirectRoute,
            isLoadingPermissions,
            accessChecked,
            permissionExists,
        ]);

        // Show loading state while permission system is not ready
        if (!isPermissionSystemReady) {
            return <PermissionLoadingSkeleton />;
        }

        // If we haven't completed access check yet, show loading
        if (!accessChecked) {
            return <PermissionLoadingSkeleton />;
        }

        // Show error state for any permission failures or cached permissions
        // In financial apps, we must show errors instead of allowing uncertain access
        if (!hasAccess && (permissionFetchFailed || isUsingCachedPermissions)) {
            return <PermissionErrorState onRetry={() => window.location.reload()} />;
        }

        // Only render the component if the user has verified fresh permissions
        // No optimistic rendering for financial applications - security first
        if (hasAccess) {
            return <Component {...props} />;
        }

        return null;
    };

    // Set display name for debugging
    const displayName = Component?.displayName ?? Component?.name ?? "Component";
    WithPermissionCheck.displayName = `withPermissionCheck(${displayName})`;

    return WithPermissionCheck;
};

export default withPermissionCheck;
