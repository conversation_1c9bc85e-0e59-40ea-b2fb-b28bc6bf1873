import React from "react";

export const LightbulbIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="lightbulb-01">
            <path
                id="Icon"
                d="M15 16.5V19C15 19.9319 15 20.3978 14.8478 20.7654C14.6448 21.2554 14.2554 21.6448 13.7654 21.8478C13.3978 22 12.9319 22 12 22C11.0681 22 10.6022 22 10.2346 21.8478C9.74458 21.6448 9.35523 21.2554 9.15224 20.7654C9 20.3978 9 19.9319 9 19V16.5M15 16.5C17.6489 15.3427 19.5 12.5755 19.5 9.5C19.5 5.35786 16.1421 2 12 2C7.85786 2 4.5 5.35786 4.5 9.5C4.5 12.5755 6.35114 15.3427 9 16.5M15 16.5H9"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const BuildingIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="building-07">
            <path
                id="Icon"
                d="M7.5 11H4.6C4.03995 11 3.75992 11 3.54601 11.109C3.35785 11.2049 3.20487 11.3578 3.10899 11.546C3 11.7599 3 12.0399 3 12.6V21M16.5 11H19.4C19.9601 11 20.2401 11 20.454 11.109C20.6422 11.2049 20.7951 11.3578 20.891 11.546C21 11.7599 21 12.0399 21 12.6V21M16.5 21V6.2C16.5 5.0799 16.5 4.51984 16.282 4.09202C16.0903 3.71569 15.7843 3.40973 15.408 3.21799C14.9802 3 14.4201 3 13.3 3H10.7C9.57989 3 9.01984 3 8.59202 3.21799C8.21569 3.40973 7.90973 3.71569 7.71799 4.09202C7.5 4.51984 7.5 5.0799 7.5 6.2V21M22 21H2M11 7H13M11 11H13M11 15H13"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const Repeat01Icon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="repeat-01">
            <path
                id="Icon"
                d="M17 2L21 6M21 6L17 10M21 6H7.8C6.11984 6 5.27976 6 4.63803 6.32698C4.07354 6.6146 3.6146 7.07354 3.32698 7.63803C3 8.27976 3 9.11984 3 10.8V11M3 18H16.2C17.8802 18 18.7202 18 19.362 17.673C19.9265 17.3854 20.3854 16.9265 20.673 16.362C21 15.7202 21 14.8802 21 13.2V13M3 18L7 22M3 18L7 14"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const FlagIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="flag-01">
            <path
                id="Icon"
                d="M4 15C4 15 5 14 8 14C11 14 13 16 16 16C19 16 20 15 20 15V3C20 3 19 4 16 4C13 4 11 2 8 2C5 2 4 3 4 3L4 22"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const HeartHandIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="heart-hand">
            <path
                id="Icon"
                d="M6 20.087H8.61029C8.95063 20.087 9.28888 20.1275 9.61881 20.2085L12.3769 20.8788C12.9753 21.0246 13.5988 21.0387 14.2035 20.9213L17.253 20.328C18.0585 20.1711 18.7996 19.7853 19.3803 19.2204L21.5379 17.1216C22.154 16.5233 22.154 15.5523 21.5379 14.953C20.9832 14.4133 20.1047 14.3526 19.4771 14.8102L16.9626 16.6447C16.6025 16.908 16.1643 17.0497 15.7137 17.0497H13.2855L14.8311 17.0497C15.7022 17.0497 16.4079 16.3632 16.4079 15.5158V15.209C16.4079 14.5054 15.9156 13.8919 15.2141 13.7218L12.8286 13.1416C12.4404 13.0475 12.0428 12.9999 11.6431 12.9999C10.6783 12.9999 8.93189 13.7987 8.93189 13.7987L6 15.0248M2 14.5999L2 20.3999C2 20.9599 2 21.24 2.10899 21.4539C2.20487 21.642 2.35785 21.795 2.54601 21.8909C2.75992 21.9999 3.03995 21.9999 3.6 21.9999H4.4C4.96005 21.9999 5.24008 21.9999 5.45399 21.8909C5.64215 21.795 5.79513 21.642 5.89101 21.4539C6 21.24 6 20.9599 6 20.3999V14.5999C6 14.0398 6 13.7598 5.89101 13.5459C5.79513 13.3577 5.64215 13.2048 5.45399 13.1089C5.24008 12.9999 4.96005 12.9999 4.4 12.9999H3.6C3.03995 12.9999 2.75992 12.9999 2.54601 13.1089C2.35785 13.2048 2.20487 13.3577 2.10899 13.5459C2 13.7598 2 13.9598 2 14.5999Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const Send03Icon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="send-03">
            <path
                id="Icon"
                d="M10.5004 12H5.00043M4.91577 12.2915L2.58085 19.2662C2.39742 19.8142 2.3057 20.0881 2.37152 20.2569C2.42868 20.4034 2.55144 20.5145 2.70292 20.5567C2.87736 20.6054 3.14083 20.4869 3.66776 20.2497L20.3792 12.7296C20.8936 12.4981 21.1507 12.3824 21.2302 12.2216C21.2993 12.082 21.2993 11.9181 21.2302 11.7784C21.1507 11.6177 20.8936 11.5019 20.3792 11.2705L3.66193 3.74776C3.13659 3.51135 2.87392 3.39315 2.69966 3.44164C2.54832 3.48375 2.42556 3.59454 2.36821 3.74078C2.30216 3.90917 2.3929 4.18255 2.57437 4.72931L4.91642 11.7856C4.94759 11.8795 4.96317 11.9264 4.96933 11.9744C4.97479 12.0171 4.97473 12.0602 4.96916 12.1028C4.96289 12.1508 4.94718 12.1977 4.91577 12.2915Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const BriefcaseIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="briefcase-01">
            <path
                id="Icon"
                d="M16 7C16 6.07003 16 5.60504 15.8978 5.22354C15.6204 4.18827 14.8117 3.37962 13.7765 3.10222C13.395 3 12.93 3 12 3C11.07 3 10.605 3 10.2235 3.10222C9.18827 3.37962 8.37962 4.18827 8.10222 5.22354C8 5.60504 8 6.07003 8 7M5.2 21H18.8C19.9201 21 20.4802 21 20.908 20.782C21.2843 20.5903 21.5903 20.2843 21.782 19.908C22 19.4802 22 18.9201 22 17.8V10.2C22 9.07989 22 8.51984 21.782 8.09202C21.5903 7.71569 21.2843 7.40973 20.908 7.21799C20.4802 7 19.9201 7 18.8 7H5.2C4.07989 7 3.51984 7 3.09202 7.21799C2.71569 7.40973 2.40973 7.71569 2.21799 8.09202C2 8.51984 2 9.07989 2 10.2V17.8C2 18.9201 2 19.4802 2.21799 19.908C2.40973 20.2843 2.71569 20.5903 3.09202 20.782C3.51984 21 4.0799 21 5.2 21Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const TicketIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="ticket-01">
            <path
                id="Icon"
                d="M10 8V7M10 12.5V11.5M10 17V16M5.2 4H18.8C19.9201 4 20.4802 4 20.908 4.21799C21.2843 4.40973 21.5903 4.71569 21.782 5.09202C22 5.51984 22 6.0799 22 7.2V8.5C20.067 8.5 18.5 10.067 18.5 12C18.5 13.933 20.067 15.5 22 15.5V16.8C22 17.9201 22 18.4802 21.782 18.908C21.5903 19.2843 21.2843 19.5903 20.908 19.782C20.4802 20 19.9201 20 18.8 20H5.2C4.0799 20 3.51984 20 3.09202 19.782C2.71569 19.5903 2.40973 19.2843 2.21799 18.908C2 18.4802 2 17.9201 2 16.8V15.5C3.933 15.5 5.5 13.933 5.5 12C5.5 10.067 3.933 8.5 2 8.5V7.2C2 6.0799 2 5.51984 2.21799 5.09202C2.40973 4.71569 2.71569 4.40973 3.09202 4.21799C3.51984 4 4.0799 4 5.2 4Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const ShoppingCartIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="shopping-cart-01">
            <path
                id="Icon"
                d="M2 2H3.30616C3.55218 2 3.67519 2 3.77418 2.04524C3.86142 2.08511 3.93535 2.14922 3.98715 2.22995C4.04593 2.32154 4.06333 2.44332 4.09812 2.68686L4.57143 6M4.57143 6L5.62332 13.7314C5.75681 14.7125 5.82355 15.2031 6.0581 15.5723C6.26478 15.8977 6.56108 16.1564 6.91135 16.3174C7.30886 16.5 7.80394 16.5 8.79411 16.5H17.352C18.2945 16.5 18.7658 16.5 19.151 16.3304C19.4905 16.1809 19.7818 15.9398 19.9923 15.6342C20.2309 15.2876 20.3191 14.8247 20.4955 13.8988L21.8191 6.94969C21.8812 6.62381 21.9122 6.46087 21.8672 6.3335C21.8278 6.22177 21.7499 6.12768 21.6475 6.06802C21.5308 6 21.365 6 21.0332 6H4.57143ZM10 21C10 21.5523 9.55228 22 9 22C8.44772 22 8 21.5523 8 21C8 20.4477 8.44772 20 9 20C9.55228 20 10 20.4477 10 21ZM18 21C18 21.5523 17.5523 22 17 22C16.4477 22 16 21.5523 16 21C16 20.4477 16.4477 20 17 20C17.5523 20 18 20.4477 18 21Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const BusIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="bus">
            <path
                id="Icon"
                d="M8.5 19V21.2C8.5 21.48 8.5 21.62 8.4455 21.727C8.39757 21.8211 8.32108 21.8976 8.227 21.9455C8.12004 22 7.98003 22 7.7 22H5.8C5.51997 22 5.37996 22 5.273 21.9455C5.17892 21.8976 5.10243 21.8211 5.0545 21.727C5 21.62 5 21.48 5 21.2V19M19 19V21.2C19 21.48 19 21.62 18.9455 21.727C18.8976 21.8211 18.8211 21.8976 18.727 21.9455C18.62 22 18.48 22 18.2 22H16.3C16.02 22 15.88 22 15.773 21.9455C15.6789 21.8976 15.6024 21.8211 15.5545 21.727C15.5 21.62 15.5 21.48 15.5 21.2V19M3 12H21M3 5.5H21M6.5 15.5H8M16 15.5H17.5M7.8 19H16.2C17.8802 19 18.7202 19 19.362 18.673C19.9265 18.3854 20.3854 17.9265 20.673 17.362C21 16.7202 21 15.8802 21 14.2V6.8C21 5.11984 21 4.27976 20.673 3.63803C20.3854 3.07354 19.9265 2.6146 19.362 2.32698C18.7202 2 17.8802 2 16.2 2H7.8C6.11984 2 5.27976 2 4.63803 2.32698C4.07354 2.6146 3.6146 3.07354 3.32698 3.63803C3 4.27976 3 5.11984 3 6.8V14.2C3 15.8802 3 16.7202 3.32698 17.362C3.6146 17.9265 4.07354 18.3854 4.63803 18.673C5.27976 19 6.11984 19 7.8 19Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const DotsHorizontalIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="dots-horizontal">
            <g id="Icon">
                <path
                    d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
                    stroke="#90909D"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <path
                    d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z"
                    stroke="#90909D"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <path
                    d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z"
                    stroke="#90909D"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </g>
        </g>
    </svg>
);

export const Send01Icon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="send-01">
            <path
                id="Icon"
                d="M10.4995 13.5001L20.9995 3.00005M10.6271 13.8281L13.2552 20.5861C13.4867 21.1815 13.6025 21.4791 13.7693 21.566C13.9139 21.6414 14.0862 21.6415 14.2308 21.5663C14.3977 21.4796 14.5139 21.1821 14.7461 20.587L21.3364 3.69925C21.5461 3.16207 21.6509 2.89348 21.5935 2.72185C21.5437 2.5728 21.4268 2.45583 21.2777 2.40604C21.1061 2.34871 20.8375 2.45352 20.3003 2.66315L3.41258 9.25349C2.8175 9.48572 2.51997 9.60183 2.43326 9.76873C2.35809 9.91342 2.35819 10.0857 2.43353 10.2303C2.52043 10.3971 2.81811 10.5128 3.41345 10.7444L10.1715 13.3725C10.2923 13.4195 10.3527 13.443 10.4036 13.4793C10.4487 13.5114 10.4881 13.5509 10.5203 13.596C10.5566 13.6468 10.5801 13.7073 10.6271 13.8281Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const CoinsStackedIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="coins-stacked-01">
            <path
                id="Icon"
                d="M12 17C12 19.7614 14.2386 22 17 22C19.7614 22 22 19.7614 22 17C22 14.2386 19.7614 12 17 12C14.2386 12 12 14.2386 12 17ZM12 17C12 15.8742 12.3721 14.8353 13 13.9995V5M12 17C12 17.8254 12.2 18.604 12.5541 19.2901C11.7117 20.0018 9.76584 20.5 7.5 20.5C4.46243 20.5 2 19.6046 2 18.5V5M13 5C13 6.10457 10.5376 7 7.5 7C4.46243 7 2 6.10457 2 5M13 5C13 3.89543 10.5376 3 7.5 3C4.46243 3 2 3.89543 2 5M2 14C2 15.1046 4.46243 16 7.5 16C9.689 16 11.5793 15.535 12.4646 14.8618M13 9.5C13 10.6046 10.5376 11.5 7.5 11.5C4.46243 11.5 2 10.6046 2 9.5"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const PiggyBankIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="piggy-bank-01">
            <path
                id="Icon"
                d="M4.99993 13C4.99993 14.6484 5.66466 16.1415 6.74067 17.226C6.84445 17.3305 6.89633 17.3828 6.92696 17.4331C6.95619 17.4811 6.9732 17.5224 6.98625 17.5771C6.99993 17.6343 6.99993 17.6995 6.99993 17.8298V20.2C6.99993 20.48 6.99993 20.62 7.05443 20.727C7.10236 20.8211 7.17885 20.8976 7.27293 20.9455C7.37989 21 7.5199 21 7.79993 21H9.69993C9.97996 21 10.12 21 10.2269 20.9455C10.321 20.8976 10.3975 20.8211 10.4454 20.727C10.4999 20.62 10.4999 20.48 10.4999 20.2V19.8C10.4999 19.52 10.4999 19.38 10.5544 19.273C10.6024 19.1789 10.6789 19.1024 10.7729 19.0545C10.8799 19 11.0199 19 11.2999 19H12.6999C12.98 19 13.12 19 13.2269 19.0545C13.321 19.1024 13.3975 19.1789 13.4454 19.273C13.4999 19.38 13.4999 19.52 13.4999 19.8V20.2C13.4999 20.48 13.4999 20.62 13.5544 20.727C13.6024 20.8211 13.6789 20.8976 13.7729 20.9455C13.8799 21 14.0199 21 14.2999 21H16.2C16.48 21 16.62 21 16.727 20.9455C16.8211 20.8976 16.8976 20.8211 16.9455 20.727C17 20.62 17 20.48 17 20.2V19.2243C17 19.0223 17 18.9212 17.0288 18.8401C17.0563 18.7624 17.0911 18.708 17.15 18.6502C17.2114 18.59 17.3155 18.5417 17.5237 18.445C18.5059 17.989 19.344 17.2751 19.9511 16.3902C20.0579 16.2346 20.1112 16.1568 20.1683 16.1108C20.2228 16.0668 20.2717 16.0411 20.3387 16.021C20.4089 16 20.4922 16 20.6587 16H21.2C21.48 16 21.62 16 21.727 15.9455C21.8211 15.8976 21.8976 15.8211 21.9455 15.727C22 15.62 22 15.48 22 15.2V11.7857C22 11.5192 22 11.3859 21.9505 11.283C21.9013 11.181 21.819 11.0987 21.717 11.0495C21.6141 11 21.4808 11 21.2143 11C21.0213 11 20.9248 11 20.8471 10.9738C20.7633 10.9456 20.7045 10.908 20.6437 10.8438C20.5874 10.7842 20.5413 10.6846 20.4493 10.4855C20.1538 9.84622 19.7492 9.26777 19.2593 8.77404C19.1555 8.66945 19.1036 8.61716 19.073 8.56687C19.0437 8.51889 19.0267 8.47759 19.0137 8.42294C19 8.36567 19 8.30051 19 8.17018V7.06058C19 6.70053 19 6.52051 18.925 6.39951C18.8593 6.29351 18.7564 6.21588 18.6365 6.18184C18.4995 6.14299 18.3264 6.19245 17.9802 6.29136L15.6077 6.96922C15.5673 6.98074 15.5472 6.9865 15.5267 6.99054C15.5085 6.99414 15.4901 6.99671 15.4716 6.99826C15.4508 7 15.4298 7 15.3879 7H14.959M4.99993 13C4.99993 10.6959 6.29864 8.6952 8.20397 7.6899M4.99993 13H4C2.89543 13 2 12.1046 2 11C2 10.2597 2.4022 9.61337 3 9.26756M15 6.5C15 8.433 13.433 10 11.5 10C9.567 10 8 8.433 8 6.5C8 4.567 9.567 3 11.5 3C13.433 3 15 4.567 15 6.5Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const CreditCardIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="credit-card-02">
            <path
                id="Icon"
                d="M22 10H2M11 14H6M2 8.2L2 15.8C2 16.9201 2 17.4802 2.21799 17.908C2.40973 18.2843 2.71569 18.5903 3.09202 18.782C3.51984 19 4.07989 19 5.2 19L18.8 19C19.9201 19 20.4802 19 20.908 18.782C21.2843 18.5903 21.5903 18.2843 21.782 17.908C22 17.4802 22 16.9201 22 15.8V8.2C22 7.0799 22 6.51984 21.782 6.09202C21.5903 5.7157 21.2843 5.40974 20.908 5.21799C20.4802 5 19.9201 5 18.8 5L5.2 5C4.0799 5 3.51984 5 3.09202 5.21799C2.7157 5.40973 2.40973 5.71569 2.21799 6.09202C2 6.51984 2 7.07989 2 8.2Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const HomeIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="home-03">
            <path
                id="Icon"
                d="M9 21V13.6C9 13.0399 9 12.7599 9.109 12.546C9.20487 12.3578 9.35785 12.2049 9.54601 12.109C9.75993 12 10.04 12 10.6 12H13.4C13.9601 12 14.2401 12 14.454 12.109C14.6422 12.2049 14.7951 12.3578 14.891 12.546C15 12.7599 15 13.0399 15 13.6V21M2 9.5L11.04 2.72C11.3843 2.46181 11.5564 2.33271 11.7454 2.28294C11.9123 2.23902 12.0877 2.23902 12.2546 2.28295C12.4436 2.33271 12.6157 2.46181 12.96 2.72L22 9.5M4 8V17.8C4 18.9201 4 19.4802 4.21799 19.908C4.40974 20.2843 4.7157 20.5903 5.09202 20.782C5.51985 21 6.0799 21 7.2 21H16.8C17.9201 21 18.4802 21 18.908 20.782C19.2843 20.5903 19.5903 20.2843 19.782 19.908C20 19.4802 20 18.9201 20 17.8V8L13.92 3.44C13.2315 2.92361 12.8872 2.66542 12.5091 2.56589C12.1754 2.47804 11.8246 2.47804 11.4909 2.56589C11.1128 2.66542 10.7685 2.92361 10.08 3.44L4 8Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const DownloadIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="download-01">
            <path
                id="Icon"
                d="M21 15V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V15M17 10L12 15M12 15L7 10M12 15V3"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const PhoneCallIcon: React.FC<
    React.SVGProps<SVGSVGElement> & { color?: string; size?: number; strokeWidth?: number }
> = ({ color = "#90909D", size = 24, strokeWidth = 2, ...props }) => (
    <svg width={size} height={size} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="phone-call-01">
            <path
                id="Icon"
                d="M14.1747 6C15.1514 6.19057 16.049 6.66826 16.7527 7.37194C17.4564 8.07561 17.9341 8.97326 18.1247 9.95M14.1747 2C16.2039 2.22544 18.0963 3.13417 19.5409 4.57701C20.9856 6.01984 21.8967 7.91101 22.1247 9.94M10.3516 13.8631C9.15006 12.6615 8.20127 11.3028 7.50528 9.85323C7.44541 9.72854 7.41548 9.66619 7.39248 9.5873C7.31076 9.30695 7.36946 8.96269 7.53947 8.72526C7.58731 8.65845 7.64447 8.60129 7.75878 8.48698C8.10838 8.13737 8.28319 7.96257 8.39747 7.78679C8.82847 7.1239 8.82847 6.26932 8.39747 5.60643C8.28319 5.43065 8.10838 5.25585 7.75878 4.90624L7.56391 4.71137C7.03247 4.17993 6.76674 3.91421 6.48136 3.76987C5.9138 3.4828 5.24354 3.4828 4.67598 3.76987C4.3906 3.91421 4.12487 4.17993 3.59343 4.71137L3.4358 4.86901C2.90617 5.39863 2.64136 5.66344 2.43911 6.02348C2.21469 6.42298 2.05333 7.04347 2.0547 7.5017C2.05592 7.91464 2.13603 8.19687 2.29624 8.76131C3.15721 11.7947 4.78168 14.6571 7.16966 17.045C9.55764 19.433 12.42 21.0575 15.4534 21.9185C16.0178 22.0787 16.3001 22.1588 16.713 22.16C17.1712 22.1614 17.7917 22 18.1912 21.7756C18.5513 21.5733 18.8161 21.3085 19.3457 20.7789L19.5033 20.6213C20.0348 20.0898 20.3005 19.8241 20.4448 19.5387C20.7319 18.9712 20.7319 18.3009 20.4448 17.7333C20.3005 17.448 20.0348 17.1822 19.5033 16.6508L19.3085 16.4559C18.9589 16.1063 18.7841 15.9315 18.6083 15.8172C17.9454 15.3862 17.0908 15.3862 16.4279 15.8172C16.2521 15.9315 16.0773 16.1063 15.7277 16.4559C15.6134 16.5702 15.5563 16.6274 15.4894 16.6752C15.252 16.8453 14.9078 16.904 14.6274 16.8222C14.5485 16.7992 14.4862 16.7693 14.3615 16.7094C12.9119 16.0134 11.5532 15.0646 10.3516 13.8631Z"
                stroke={color}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const CableIcon: React.FC<React.SVGProps<SVGSVGElement> & { color?: string; strokeWidth?: number }> = ({
    color = "#90909D",
    strokeWidth = 2,
    ...props
}) => (
    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="tv-03">
            <path
                id="Icon"
                d="M17.875 3L12.875 7L7.875 3M7.675 21H18.075C19.7552 21 20.5952 21 21.237 20.673C21.8015 20.3854 22.2604 19.9265 22.548 19.362C22.875 18.7202 22.875 17.8802 22.875 16.2V11.8C22.875 10.1198 22.875 9.27976 22.548 8.63803C22.2604 8.07354 21.8015 7.6146 21.237 7.32698C20.5952 7 19.7552 7 18.075 7H7.675C5.99484 7 5.15476 7 4.51303 7.32698C3.94854 7.6146 3.4896 8.07354 3.20198 8.63803C2.875 9.27976 2.875 10.1198 2.875 11.8V16.2C2.875 17.8802 2.875 18.7202 3.20198 19.362C3.4896 19.9265 3.94854 20.3854 4.51303 20.673C5.15476 21 5.99484 21 7.675 21Z"
                stroke={color}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const TaxesIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="elements">
            <path
                id="Rectangle 2274 (Stroke)"
                d="M8.98456 0.762065C9.72595 0.438228 10.396 0.25 11.125 0.25C11.854 0.25 12.524 0.438228 13.2654 0.762065C13.9844 1.07612 14.8182 1.54014 15.8669 2.12379L20.0091 4.42893C20.5855 4.74971 21.0679 5.15261 21.3993 5.69585C21.7323 6.24181 21.875 6.86731 21.875 7.56907C21.8754 7.82453 21.876 8.14972 21.8144 8.38151C21.6595 8.9648 21.2902 9.34075 20.8379 9.54224C20.4305 9.72375 19.9879 9.75 19.6557 9.75H2.59426C2.26202 9.75 1.81942 9.72375 1.41202 9.54224C0.959763 9.34076 0.5905 8.9648 0.435541 8.38151C0.373963 8.14972 0.374513 7.82452 0.374977 7.56907C0.374977 6.86731 0.51763 6.24181 0.850682 5.69585C1.18208 5.15261 1.66448 4.74971 2.2409 4.42893L6.38305 2.12378L6.38306 2.12377C7.43178 1.54014 8.26556 1.07612 8.98456 0.762065Z"
                fill="#90909D"
            />
            <path
                id="Rectangle 2277 (Stroke)"
                d="M0.375 20.5C0.375 18.4289 2.05393 16.75 4.125 16.75H10.125C10.5392 16.75 10.875 17.0858 10.875 17.5V21C10.875 21.4142 10.5392 21.75 10.125 21.75H1.625C0.934643 21.75 0.375 21.1904 0.375 20.5Z"
                fill="#90909D"
            />
            <path
                id="Vector 3082 (Stroke)"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.9179 21.2071C12.5274 20.8166 12.5274 20.1834 12.9179 19.7929L19.9179 12.7929C20.3084 12.4024 20.9416 12.4024 21.3321 12.7929C21.7226 13.1834 21.7226 13.8166 21.3321 14.2071L14.3321 21.2071C13.9416 21.5976 13.3084 21.5976 12.9179 21.2071Z"
                fill="#90909D"
            />
            <path
                id="Ellipse 2017 (Stroke)"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.625 14C12.625 13.1716 13.2966 12.5 14.125 12.5C14.9534 12.5 15.625 13.1716 15.625 14C15.625 14.8284 14.9534 15.5 14.125 15.5C13.2966 15.5 12.625 14.8284 12.625 14Z"
                fill="#90909D"
            />
            <path
                id="Ellipse 2018 (Stroke)"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.625 20C18.625 19.1716 19.2966 18.5 20.125 18.5C20.9534 18.5 21.625 19.1716 21.625 20C21.625 20.8284 20.9534 21.5 20.125 21.5C19.2966 21.5 18.625 20.8284 18.625 20Z"
                fill="#90909D"
            />
            <path
                id="Subtract"
                d="M2.37482 16.0825V10.7461L7.87482 10.7497V15.7498H4.12482C3.50681 15.7498 2.91638 15.8678 2.37482 16.0825Z"
                fill="#90909D"
            />
        </g>
    </svg>
);

export const InsuranceIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="umbrella-01">
            <path
                id="Icon"
                d="M17.375 19.4C17.375 20.8359 16.2557 22 14.875 22C13.4943 22 12.375 20.8359 12.375 19.4V12M2.50133 10.4063C3.26454 5.64091 7.39459 2 12.375 2C17.3555 2 21.4855 5.64091 22.2487 10.4063C22.3232 10.8714 22.3605 11.1039 22.2669 11.3523C22.1925 11.55 22.0104 11.7636 21.827 11.8684C21.5965 12 21.3227 12 20.775 12H3.97503C3.42738 12 3.15356 12 2.92308 11.8684C2.73969 11.7636 2.55752 11.55 2.48311 11.3523C2.38959 11.1039 2.42684 10.8714 2.50133 10.4063Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const PlaneIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g id="plane">
            <path
                id="Icon"
                d="M18.3698 2.81249C19.3345 1.81601 20.9286 1.80312 21.9093 2.78387C22.8632 3.73775 22.8809 5.27872 21.9493 6.25432L19.1706 9.16409C18.9528 9.39216 18.844 9.50619 18.7768 9.63975C18.7174 9.75799 18.6821 9.88683 18.6728 10.0188C18.6624 10.1679 18.6978 10.3216 18.7688 10.6289L20.4967 18.1164C20.5694 18.4318 20.6058 18.5895 20.5941 18.7421C20.5837 18.8771 20.546 19.0086 20.4832 19.1286C20.4123 19.2642 20.2979 19.3787 20.069 19.6076L19.6982 19.9783C19.0921 20.5845 18.789 20.8875 18.4788 20.9424C18.208 20.9903 17.9293 20.9245 17.7085 20.7606C17.4556 20.5728 17.32 20.1661 17.049 19.3529L15.0392 13.3236L11.6939 16.669C11.4942 16.8687 11.3944 16.9685 11.3276 17.0861C11.2684 17.1902 11.2284 17.3042 11.2096 17.4225C11.1883 17.556 11.2039 17.6964 11.235 17.977L11.4187 19.6304C11.4499 19.9111 11.4655 20.0514 11.4442 20.185C11.4254 20.3033 11.3854 20.4172 11.3262 20.5214C11.2594 20.6389 11.1596 20.7388 10.9599 20.9385L10.7624 21.136C10.2893 21.609 10.0528 21.8456 9.78996 21.9141C9.55942 21.9742 9.31499 21.95 9.10071 21.8458C8.8564 21.7271 8.67085 21.4488 8.29975 20.8921L6.73143 18.5396C6.66513 18.4402 6.63198 18.3905 6.59349 18.3454C6.5593 18.3053 6.52201 18.268 6.48194 18.2338C6.43684 18.1953 6.38712 18.1622 6.28767 18.0959L3.93518 16.5276C3.37854 16.1565 3.10021 15.9709 2.98149 15.7266C2.87736 15.5123 2.85316 15.2679 2.91324 15.0374C2.98174 14.7745 3.21827 14.538 3.69133 14.0649L3.88884 13.8674C4.08852 13.6677 4.18837 13.5679 4.30595 13.5011C4.41011 13.4419 4.52406 13.402 4.64236 13.3831C4.77589 13.3618 4.91623 13.3774 5.1969 13.4086L6.85029 13.5923C7.13096 13.6235 7.2713 13.6391 7.40483 13.6178C7.52313 13.5989 7.63708 13.5589 7.74124 13.4997C7.85882 13.433 7.95866 13.3331 8.15835 13.1334L11.5037 9.78811L5.47439 7.77835C4.66116 7.50727 4.25455 7.37174 4.06676 7.11883C3.90277 6.89799 3.837 6.61935 3.8849 6.34849C3.93977 6.0383 4.24284 5.73523 4.84899 5.12909L5.21976 4.75831C5.44865 4.52942 5.5631 4.41497 5.69869 4.34408C5.8187 4.28134 5.9502 4.24362 6.08523 4.23323C6.23778 4.22148 6.39549 4.25787 6.7109 4.33066L14.17 6.052C14.48 6.12353 14.635 6.15929 14.7846 6.14865C14.9291 6.13838 15.0696 6.09685 15.1964 6.02693C15.3278 5.95452 15.4384 5.84025 15.6597 5.61171L18.3698 2.81249Z"
                stroke="#90909D"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export const ListIcon: React.FC<React.SVGProps<SVGSVGElement> & { color?: string; strokeWidth?: number }> = ({
    color = "#90909D",
    strokeWidth = 2,
    ...props
}) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M8 6L21 6M8 12H21M8 18H21M3 6H3.01M3 12H3.01M3 18H3.01"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export const ReceiptIcon: React.FC<React.SVGProps<SVGSVGElement> & { color?: string; strokeWidth?: number }> = ({
    color = "#90909D",
    strokeWidth = 2,
    ...props
}) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M4 7.8C4 6.11984 4 5.27976 4.32698 4.63803C4.6146 4.07354 5.07354 3.6146 5.63803 3.32698C6.27976 3 7.11984 3 8.8 3H15.2C16.8802 3 17.7202 3 18.362 3.32698C18.9265 3.6146 19.3854 4.07354 19.673 4.63803C20 5.27976 20 6.11984 20 7.8V21L17.25 19L14.75 21L12 19L9.25 21L6.75 19L4 21V7.8Z"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
