"use client";

import { openSupportDialog } from "@/redux/features/supportDialog";
import { useAppDispatch } from "@/redux/hooks";
import { QuestionMarkCircleIcon, RocketLaunchIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { <PERSON><PERSON> } from "../common/buttonv3";
import QuickActionContainer from "./quick-action";

const Header = () => {
    const dispatch = useAppDispatch();
    return (
        <div className="flex h-16 items-center justify-between p-4 border-b border-zinc-200">
            <div className="flex items-center space-x-4 md:hidden">
                <Link href="/" className="flex flex-row space-x-3 items-center justify-center">
                    <img className="w-8 h-8" src="https://www.fcmb.com/themes/fcmb/logo.png" alt="logo" />
                </Link>
            </div>

            <div />

            <div className="justify-start items-center gap-2 flex">
                <Button onClick={() => dispatch(openSupportDialog())} variant="outline">
                    <QuestionMarkCircleIcon className="w-4" /> Support
                </Button>

                <Button variant="outline">
                    <RocketLaunchIcon className="w-4" /> Changelog
                </Button>
                <QuickActionContainer />
            </div>
        </div>
    );
};

export default Header;
