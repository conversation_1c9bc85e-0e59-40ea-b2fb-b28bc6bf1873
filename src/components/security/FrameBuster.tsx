"use client";

import { useEffect } from "react";

/**
 * Frame<PERSON>uster Component
 *
 * Provides client-side protection against clickjacking attacks by:
 * 1. Detecting if the page is loaded in an iframe
 * 2. Breaking out of the iframe if detected
 * 3. Redirecting to the top-level window
 *
 * This is a backup protection layer in addition to X-Frame-Options header
 */
export default function FrameBuster() {
    useEffect(() => {
        // Check if we're running in a frame/iframe
        const isInFrame = () => {
            try {
                return window.self !== window.top;
            } catch (e) {
                // If we can't access window.top due to cross-origin restrictions,
                // we're likely in a frame
                return true;
            }
        };

        // If we detect we're in a frame, break out of it
        if (isInFrame()) {
            console.warn("Clickjacking attempt detected! Breaking out of frame.");

            try {
                // Try to redirect the top window to our current location
                window.top!.location.href = window.location.href;
            } catch (e) {
                // If we can't access window.top, try to break out using other methods
                console.error("Unable to break out of frame:", e);

                // Alternative method: replace the current frame content
                document.body.innerHTML = `
                    <div style="
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: #fff;
                        z-index: 999999;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-family: Arial, sans-serif;
                        text-align: center;
                        padding: 20px;
                        box-sizing: border-box;
                    ">
                        <div>
                            <h1 style="color: #d32f2f; margin-bottom: 20px;">Security Warning</h1>
                            <p style="font-size: 18px; margin-bottom: 20px;">
                                This page cannot be displayed in a frame for security reasons.
                            </p>
                            <p style="margin-bottom: 30px;">
                                Please visit our website directly:
                            </p>
                            <a 
                                href="${window.location.href}" 
                                target="_blank"
                                style="
                                    display: inline-block;
                                    padding: 12px 24px;
                                    background: #1976d2;
                                    color: white;
                                    text-decoration: none;
                                    border-radius: 4px;
                                    font-weight: bold;
                                "
                            >
                                Open Secure Page
                            </a>
                        </div>
                    </div>
                `;
            }
        }

        // Additional protection: Monitor for frame changes
        const frameChangeHandler = () => {
            if (isInFrame()) {
                console.warn("Frame change detected - potential clickjacking attempt!");
                // You could implement additional logging or security measures here
            }
        };

        // Listen for resize events that might indicate frame manipulation
        window.addEventListener("resize", frameChangeHandler);

        // Cleanup
        return () => {
            window.removeEventListener("resize", frameChangeHandler);
        };
    }, []);

    // This component doesn't render anything visible
    return null;
}
