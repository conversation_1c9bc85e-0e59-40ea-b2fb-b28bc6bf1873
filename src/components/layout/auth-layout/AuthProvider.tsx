"use client";

import type React from "react";
// eslint-disable-next-line no-duplicate-imports
import { useEffect } from "react";
import { useSessionTimeout } from "@/hooks/useSessionTimeout";
import SessionTimeoutModal from "./SessionTimeoutModal";
import { useTokenRefresh } from "@/hooks/useTokenRefresh";
import { cookies } from "@/lib/cookies";
import { useRouteGuard } from "@/hooks/useRouteGuard";
import { useAppDispatch } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { updateToken } from "@/redux/features/user";
import { usePathname } from "next/navigation";
import { refreshAccessToken, logTokenExpiry } from "@/lib/token-refresh";

interface AuthProviderProps {
    children: React.ReactNode;
}

const AuthProvider = ({ children }: AuthProviderProps) => {
    const { checkAndRefreshToken } = useTokenRefresh();
    useRouteGuard();
    const dispatch = useAppDispatch();
    const pathname = usePathname();

    // Check if current route is a login route
    const isLoginRoute = pathname?.startsWith("/auth/");

    const { showWarning, timeLeft, continueSession, handleLogout, resetInactivityTimer, isAuthenticated, loading } =
        useSessionTimeout({
            inactivityTimeout: 5 * 60 * 1000, // 5 minutes - longer than token refresh interval
            warningTimeout: 30 * 1000,
            excludeRoutes: ["/auth/"], // Exclude login routes from session timeout
            onWarning: () => {
                // console.log("Session timeout warning triggered")
            },
            onTimeout: () => {
                // console.log("Session timed out")
            },
        });

    // Token refresh effect - runs independently of session timeout
    useEffect(() => {
        if (cookies.isAuthenticated() && !isLoginRoute) {
            console.log("Setting up token refresh interval for authenticated user");

            // Log current token expiry for debugging
            const currentToken = cookies.getToken();
            logTokenExpiry(currentToken);

            // Initial token check - force refresh to ensure fresh token
            const initialRefresh = async () => {
                try {
                    const accessToken = cookies.getToken();
                    const refreshToken = cookies.getRefreshToken();

                    if (accessToken && refreshToken) {
                        const newToken = await refreshAccessToken(true);
                        if (newToken) {
                            dispatch(signinActions.updateToken({ accessToken: newToken, refreshToken }));
                            dispatch(updateToken({ token: newToken }));
                            console.log("Initial token refresh completed");
                        }
                    }
                } catch (error) {
                    console.error("Initial token refresh failed:", error);
                }
            };

            initialRefresh();

            // Set up periodic token check (every 4 minutes) - force refresh to ensure API is called
            const interval = setInterval(
                () => {
                    console.log("Running periodic token refresh check");
                    // Log current token expiry before refresh
                    const currentToken = cookies.getToken();
                    logTokenExpiry(currentToken);

                    // Force refresh every 4 minutes to ensure token stays fresh
                    const forceRefresh = async () => {
                        try {
                            const accessToken = cookies.getToken();
                            const refreshToken = cookies.getRefreshToken();

                            if (accessToken && refreshToken) {
                                // Force refresh the token
                                const newToken = await refreshAccessToken(true);
                                if (newToken) {
                                    dispatch(signinActions.updateToken({ accessToken: newToken, refreshToken }));
                                    dispatch(updateToken({ token: newToken }));
                                    console.log("Periodic token refresh completed");
                                }
                            }
                        } catch (error) {
                            console.error("Periodic token refresh failed:", error);
                        }
                    };

                    forceRefresh();
                },
                4 * 60 * 1000
            );

            return () => {
                console.log("Clearing token refresh interval");
                clearInterval(interval);
            };
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoginRoute]);

    // Session timeout effect - only for non-login routes
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden && isAuthenticated && !isLoginRoute) {
                // Force refresh token when user becomes active
                const visibilityRefresh = async () => {
                    try {
                        const accessToken = cookies.getToken();
                        const refreshToken = cookies.getRefreshToken();

                        if (accessToken && refreshToken) {
                            const newToken = await refreshAccessToken(true);
                            if (newToken) {
                                dispatch(signinActions.updateToken({ accessToken: newToken, refreshToken }));
                                dispatch(updateToken({ token: newToken }));
                            }
                        }
                    } catch (error) {
                        console.error("Visibility change token refresh failed:", error);
                    }
                };

                visibilityRefresh();
                resetInactivityTimer();
            }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            document.removeEventListener("visibilitychange", handleVisibilityChange);
        };

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [resetInactivityTimer, isAuthenticated, isLoginRoute]);

    return (
        <>
            {children}
            {/* Only show modal if user is authenticated and not on login route */}
            {isAuthenticated && !isLoginRoute && (
                <SessionTimeoutModal
                    isOpen={showWarning}
                    timeLeft={timeLeft}
                    onContinue={continueSession}
                    onLogout={handleLogout}
                    loading={loading}
                />
            )}
        </>
    );
};

export default AuthProvider;
