"use client";

import { But<PERSON> } from "@/components/common/buttonv3";
import CustomModal from "@/components/common/custom-modal";
import { Clock, AlertTriangle } from "lucide-react";

interface SessionTimeoutModalProps {
    isOpen: boolean;
    timeLeft: number;
    onContinue: () => void;
    onLogout: () => void;
    loading: boolean;
}

const SessionTimeoutModal = ({ isOpen, timeLeft, onContinue, onLogout, loading }: SessionTimeoutModalProps) => {
    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onRequestClose={() => {}} // Prevent closing by clicking outside
            width="400px"
        >
            <div className="text-center">
                <div className="flex justify-center mb-4">
                    <div className="bg-yellow-100 p-3 rounded-full">
                        <AlertTriangle className="h-8 w-8 text-yellow-600" />
                    </div>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-2">Session Timeout Warning</h3>

                <p className="text-gray-600 mb-4">
                    Your session will expire due to inactivity. Do you want to continue?
                </p>

                <div className="flex items-center justify-center mb-6 text-red-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="text-xl font-mono font-bold">{formatTime(timeLeft)}</span>
                </div>

                <div className="flex gap-3 justify-center">
                    <Button
                        loading={loading}
                        disabled={loading}
                        variant="outline-destructive"
                        onClick={onLogout}
                        fullWidth
                    >
                        Logout
                    </Button>
                    <Button onClick={onContinue} disabled={loading} fullWidth>
                        Continue Session
                    </Button>
                </div>
            </div>
        </CustomModal>
    );
};

export default SessionTimeoutModal;
