"use client";

import { checkAndClearAuthData } from "@/lib/auth-cleanup";
import { useEffect } from "react";

/**
 * Component to handle auth data cleanup based on middleware headers
 * This component should be included in the root layout
 */
const AuthCleanup = () => {
    useEffect(() => {
        // Check for auth cleanup headers and clear data if needed
        checkAndClearAuthData();
    }, []);

    return null; // This component doesn't render anything
};

export default AuthCleanup;
