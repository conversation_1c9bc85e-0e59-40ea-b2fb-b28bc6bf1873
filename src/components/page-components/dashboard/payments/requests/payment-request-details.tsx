"use client";

import { X } from "lucide-react";
import SideDrawer from "@/components/common/drawer";
import React, { useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/common/Avatar";
import TabSwitch from "@/components/common/tab-switch";
import TabDetails from "./tab-details";
import { Button } from "@/components/common/buttonv3";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { formatNumberToNaira, getNameInitials } from "@/functions/stringManipulations";
import { formatDate } from "@/functions/date";
import { paymentRequestActions } from "@/redux/slices/payments/requestSlice";
import dynamic from "next/dynamic";

const ApprovalFlow = dynamic(() => import("@/components/common/approval-flow"), { ssr: false });

interface TransactionDetailsProps {
    requestId: number;
    isOpen: boolean;
    handleCloseDetails: () => void;
    handleApproveRequest: (requestId: number) => void;
    handleDeclineRequest: (requestId: number) => void;
}

export function PaymentRequestDetails({
    handleCloseDetails,
    isOpen,
    handleApproveRequest,
    requestId,
    handleDeclineRequest,
}: Readonly<TransactionDetailsProps>) {
    const dispatch = useAppDispatch();
    const { request: paymentRequest, get } = useAppSelector((state) => state.paymentRequest ?? {});
    const { loading, error } = get ?? {};

    useEffect(() => {
        if (requestId) {
            dispatch(paymentRequestActions.getPaymentRequest(requestId));
        }
        // eslint-disable-next-line
    }, [requestId]);

    return (
        <div>
            <SideDrawer isOpen={isOpen} className="!max-w-[488px]">
                {paymentRequest && !loading && !error ? (
                    <div className="flex flex-col justify-between h-full">
                        <div className="px-6 flex-1 flex flex-col">
                            <div className="border-b border-[#E3E5E8] py-[19px]">
                                <div>
                                    <button
                                        onClick={handleCloseDetails}
                                        className="ml-auto block"
                                        data-testid="close-btn"
                                    >
                                        <X color="#90909D" />
                                    </button>
                                </div>
                                <div className="flex justify-between mt-[36px]">
                                    <div className="flex gap-[12px] items-center">
                                        <div>
                                            <Avatar className="w-[64px] h-[64px]">
                                                <AvatarImage src="" alt="" />
                                                <AvatarFallback className="text-[20px] leading-[26px] font-medium text-subText">
                                                    {getNameInitials(paymentRequest.counterpartyTitle)}
                                                </AvatarFallback>
                                            </Avatar>
                                        </div>
                                        <div className="flex flex-col gap-[12px]">
                                            {paymentRequest.counterpartyTitle && (
                                                <h4 className="text-[18px] leading-6 font-semibold text-black">
                                                    {paymentRequest.counterpartyTitle}
                                                </h4>
                                            )}
                                            {paymentRequest.narration && (
                                                <p className="text-sm leading-[18px] font-normal text-subText">
                                                    {paymentRequest.narration}
                                                </p>
                                            )}
                                            {paymentRequest.createdDate && (
                                                <p className="text-sm leading-[18px] font-normal text-subText">
                                                    {formatDate(paymentRequest.createdDate)}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-[12px] items-end">
                                        <h4 className={"text-[18px] leading-6 font-semibold text-black"}>
                                            {formatNumberToNaira(paymentRequest.amount)}
                                        </h4>
                                        <PaymentRequestBadge paymentRequestType={"pending"} />
                                    </div>
                                </div>
                            </div>

                            <div className="flex-1 mb-[20px]">
                                <TabSwitch
                                    tabs={["Details", "People involved", "Attachments"]}
                                    panels={[
                                        <TabDetails key="details" request={paymentRequest} />,
                                        <ApprovalFlow
                                            amount={paymentRequest.amount}
                                            type={
                                                paymentRequest.paymentRequestType?.includes("BILL_PAYMENT")
                                                    ? "BILL_PAYMENT"
                                                    : "TRANSFER"
                                            }
                                            requestId={String(requestId)}
                                            key="approval-flow"
                                        />,
                                        // <div key="people involved" />,
                                        <div key="attachments" />,
                                    ]}
                                    panelClassName="flex-1"
                                    className="!h-full flex flex-col"
                                    tabSpacing="h-[32px]"
                                />
                            </div>
                        </div>

                        <div className="flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-4 px-6">
                            <Button
                                type="button"
                                variant="outline-destructive"
                                onClick={() => handleDeclineRequest(requestId)}
                            >
                                Decline
                            </Button>
                            <Button type="button" onClick={() => handleApproveRequest(requestId)}>
                                Approve
                            </Button>
                        </div>
                    </div>
                ) : null}
            </SideDrawer>
        </div>
    );
}

type IPaymentRequestType = "pending" | "canceled" | "approved";

export const PaymentRequestBadge = ({ paymentRequestType }: { paymentRequestType: IPaymentRequestType }) => {
    const getTransactionTypeColor = (status: IPaymentRequestType): { color: string; bgColor: string } => {
        switch (status) {
            case "pending":
                return { color: "#B54708", bgColor: "#FFFAEB" };
            case "canceled":
                return { color: "#B42318", bgColor: "#FEF3F2" };
            case "approved":
                return { color: "#027A48", bgColor: "#ECFDF3" };
            default:
                return { color: "", bgColor: "" };
        }
    };

    return (
        <div
            className="rounded-full py-[6px] px-[10px] max-w-max capitalize text-[14px] leading-[18px] font-medium leading-4]"
            style={{
                backgroundColor: getTransactionTypeColor(paymentRequestType).bgColor,
                color: getTransactionTypeColor(paymentRequestType).color,
            }}
        >
            {paymentRequestType}
        </div>
    );
};
