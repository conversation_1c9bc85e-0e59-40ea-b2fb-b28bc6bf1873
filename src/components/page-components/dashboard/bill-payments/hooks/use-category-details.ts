/**
 * @file use-category-details.ts
 *
 * @purpose Provides standardized access to bill payment category data while minimizing redundant API calls.
 *
 * @functionality This hook centralizes the logic for fetching and accessing bill payment category
 * data across the payment flow. It ensures categories are loaded only once from the API and then
 * cached in Redux state for subsequent access. The hook normalizes category IDs from Next.js dynamic
 * routes, finds the appropriate category name based on the ID, and provides a cleaned version of
 * the category name for display purposes. This approach prevents unnecessary network requests as
 * users navigate through different steps of the payment flow.
 *
 * @dependencies
 * - Redux: For state management and API data fetching
 * - fetchBillCategories: Redux thunk for API calls
 * - cleanCategoryName: Utility for normalizing category names
 *
 * @usage Import this hook in bill payment components that need to access category information.
 * Pass a category ID parameter and access the returned category name, cleaned category name,
 * complete categories array, and loading state for UI rendering decisions.
 */

import { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchBillCategories } from "@/redux/actions/billPaymentThunks";
import { cleanCategoryName } from "../utils/utils";

/**
 * Custom hook for managing bill payment category details.
 * Centralizes the logic for fetching and accessing category data
 * to prevent redundant API calls as users navigate through the payment flow.
 *
 * This follows the Redux Implementation Standards by leveraging existing Redux
 * state management while eliminating duplicate API calls.
 *
 * @param categoryId - The ID of the current category
 * @returns Object containing categoryName, cleanedCategoryName, categories array, and loading state
 */
export const useCategoryDetails = (categoryId: string | string[] | undefined) => {
    const dispatch = useAppDispatch();
    const [categoryName, setCategoryName] = useState<string>("");
    const normalizedCategoryId = Array.isArray(categoryId) ? categoryId[0] : categoryId;

    // Get categories from Redux state
    const { content: categories = [], loading } = useAppSelector((state) => state.billPayments.categories) || {};

    // Fetch categories only if they don't exist in Redux
    useEffect(() => {
        if (categories.length === 0 && !loading) {
            // Using the existing optimized thunk that prevents redundant API calls
            dispatch(fetchBillCategories());
        }
    }, [dispatch, categories.length, loading]);

    // Find the current category name
    useEffect(() => {
        if (categories && normalizedCategoryId) {
            const category = categories.find((cat) => cat.id?.toString() === normalizedCategoryId.toString());
            const rawCategoryName = category?.categoryName ?? "Bill Payment";
            setCategoryName(rawCategoryName);
        }
    }, [categories, normalizedCategoryId]);

    return {
        categoryName,
        cleanedCategoryName: cleanCategoryName(categoryName),
        categories,
        loading,
    };
};
