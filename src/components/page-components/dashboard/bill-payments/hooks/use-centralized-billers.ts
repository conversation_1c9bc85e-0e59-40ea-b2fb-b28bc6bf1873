/**
 * Centralized Biller Management Hook
 *
 * PURPOSE:
 * This hook provides a single source of truth for biller fetching and caching across
 * all bill payment components. It eliminates the inconsistent caching behavior observed
 * in bill details components by centralizing all biller-related logic.
 *
 * FUNCTIONALITY:
 * - Manages category-aware caching to prevent cross-category cache pollution
 * - Provides consistent API call patterns throughout the bill payments module
 * - Eliminates race conditions between parent and child components
 * - Handles loading states and error management centrally
 * - Implements performance optimizations for million+ user scale
 *
 * DEPENDENCIES:
 * - Redux Toolkit for state management
 * - React hooks for memoization and lifecycle management
 * - Bill payment thunks for API calls
 *
 * USAGE:
 * Use this hook in any component that needs biller data instead of directly
 * dispatching fetchBillersByCategory or using the generic use-billers hook.
 * This ensures consistent caching behavior across the entire application.
 *
 * <AUTHOR> Payments Optimization Team
 * @version 1.0.0
 */

import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux";
import { fetchBillersByCategory } from "@/redux/actions/billPaymentThunks";
import { clearBillersCache as clearBillersCacheAction } from "@/redux/slices/billPaymentSlice";
import { Biller } from "../types";

/**
 * Return type for the useCentralizedBillers hook
 *
 * @interface UseCentralizedBillersReturn
 * @property {Biller[] | null} billers - Array of billers for the cached category, null if no data
 * @property {boolean} loading - Loading state for biller operations
 * @property {string | null} error - Error message if biller operations fail, null if no error
 * @property {number | null} cachedCategoryId - ID of the category for which billers are currently cached
 * @property {number | null} lastFetched - Timestamp of when billers were last fetched
 * @property {Function} fetchBillersForCategory - Function to fetch billers for a specific category
 * @property {Function} getBillersForCategory - Function to get cached billers for a category
 * @property {Function} clearBillersCache - Function to clear the billers cache
 * @property {Function} hasBillersForCategory - Function to check if billers exist for a category
 */
interface UseCentralizedBillersReturn {
    billers: Biller[] | null;
    loading: boolean;
    error: string | null;
    cachedCategoryId: number | null;
    lastFetched: number | null;
    fetchBillersForCategory: (categoryId: number, forceRefresh?: boolean) => Promise<unknown>;
    getBillersForCategory: (categoryId: number) => Biller[] | null;
    clearBillersCache: () => void;
    hasBillersForCategory: (categoryId: number) => boolean;
}

/**
 * Centralized hook for managing biller data across all bill payment components.
 *
 * RULE COMPLIANCE:
 * - Uses memoization techniques (useMemo, useCallback) to prevent unnecessary re-renders
 * - Implements null checks using optional chaining (?.) and nullish coalescing (??)
 * - Optimized for performance (CIB serves up to a million users)
 * - Follows component-based structure organized by feature
 *
 * This hook eliminates the race conditions and inconsistent caching behavior
 * that occurs when multiple components independently decide whether to fetch data.
 *
 * @returns {UseCentralizedBillersReturn} Object containing biller state and management functions
 */
export const useCentralizedBillers = (): UseCentralizedBillersReturn => {
    const dispatch = useDispatch<AppDispatch>();

    // Get biller state from Redux store with null safety
    const billerState = useSelector((state: RootState) => state.billPayments?.biller);

    // Memoized biller data extraction with null checks
    const billers = useMemo(() => billerState?.data ?? null, [billerState?.data]);

    // Memoized loading state with null safety
    const loading = useMemo(() => billerState?.loading ?? false, [billerState?.loading]);

    // Memoized error state with null safety
    const error = useMemo(() => billerState?.error ?? null, [billerState?.error]);

    // Memoized cached category ID with null safety
    const cachedCategoryId = useMemo(() => billerState?.categoryId ?? null, [billerState?.categoryId]);

    // Memoized last fetched timestamp with null safety
    const lastFetched = useMemo(() => billerState?.lastFetched ?? null, [billerState?.lastFetched]);

    /**
     * Fetches billers for a specific category with enhanced caching logic.
     * This function provides the single source of truth for all biller fetching operations.
     *
     * @param categoryId - The category ID to fetch billers for
     * @param forceRefresh - Optional flag to force fresh data even if cached data exists
     */
    const fetchBillersForCategory = useCallback(
        async (categoryId: number, forceRefresh = false) => {
            // Use the enhanced thunk that returns both billers and categoryId
            const result = await dispatch(fetchBillersByCategory({ categoryId, forceRefresh }));
            return result;
        },
        [dispatch]
    );

    /**
     * Gets billers for a specific category, using cached data if available and valid.
     * This function implements the same caching logic as the thunk for consistency.
     *
     * FIXED: Removed billers.length > 0 condition to properly handle empty arrays.
     * Empty arrays are valid cached data and should be returned to prevent re-fetching.
     *
     * @param categoryId - The category ID to get billers for
     * @returns Array of billers or null if not available
     */
    const getBillersForCategory = useCallback(
        (categoryId: number) => {
            // Check if we have cached data for the requested category
            if (cachedCategoryId === categoryId && billers !== null) {
                // Check if cached data is still fresh (10 minutes cache duration)
                const CACHE_DURATION = 10 * 60 * 1000;
                const isDataFresh = lastFetched ? Date.now() - lastFetched <= CACHE_DURATION : false;

                if (isDataFresh) {
                    return billers;
                }
            }

            // Return null if no valid cached data exists
            return null;
        },
        [cachedCategoryId, billers, lastFetched]
    );

    /**
     * Clears the billers cache by resetting the biller state.
     * This is useful when switching between different bill payment flows.
     */
    const clearBillersCache = useCallback(() => {
        // Dispatch the clearBillersCache action to reset biller state
        dispatch(clearBillersCacheAction());
    }, [dispatch]);

    /**
     * Checks if billers data has been fetched for a specific category.
     *
     * FIXED: Changed logic to return true when data has been fetched for the category,
     * regardless of whether the array is empty or not. This prevents infinite fetching
     * when the API legitimately returns an empty array for a category.
     *
     * @param categoryId - The category ID to check
     * @returns True if data has been fetched for the category (even if empty), false otherwise
     */
    const hasBillersForCategory = useCallback(
        (categoryId: number): boolean => cachedCategoryId === categoryId && billers !== null,
        [cachedCategoryId, billers]
    );

    // Return memoized object to prevent unnecessary re-renders
    return useMemo(
        () => ({
            billers,
            loading,
            error,
            cachedCategoryId,
            lastFetched,
            fetchBillersForCategory,
            getBillersForCategory,
            clearBillersCache,
            hasBillersForCategory,
        }),
        [
            billers,
            loading,
            error,
            cachedCategoryId,
            lastFetched,
            fetchBillersForCategory,
            getBillersForCategory,
            clearBillersCache,
            hasBillersForCategory,
        ]
    );
};
