/**
 * @file csv-utils.ts
 * @purpose Provides utilities for CSV file handling in the bulk payment flow.
 *
 * @functionality This file contains utilities for working with CSV files in the bill payments module:
 * - Parsing CSV content from strings to structured data
 * - Validating CSV files to ensure they meet the requirements for bulk payments
 * - Converting CSV data to BulkAirtimeEntry objects for use in the application
 * - Performing validation on CSV data including phone numbers, network providers, and amounts
 * The utilities ensure data quality and consistency when users upload bulk payment information.
 *
 * @dependencies None - This file uses native JavaScript without external dependencies.
 *
 * @usage Import these utilities when implementing CSV file upload functionality in the bulk
 * payment flow. The functions handle parsing, validation, and data transformation, making it
 * easier to process user-uploaded CSV files containing payment information.
 */

/**
 * CSV parsing and validation utilities for bill payments
 */

/**
 * Interface for CSV validation result
 */
export interface CsvValidationResult {
    isValid: boolean;
    errorMessage?: string;
    data?: Record<string, string>[];
}

/**
 * Parse CSV file content into a structured format
 * @param csvContent The raw CSV content as a string
 * @returns An array of objects representing the CSV data
 */
export const parseCSV = (csvContent: string): Record<string, string>[] => {
    try {
        // Handle BOM marker if present
        const content = csvContent.charCodeAt(0) === 0xfeff ? csvContent.slice(1) : csvContent;

        // Split the content into lines and handle both CRLF and LF
        const lines = content.split(/\r?\n/).filter((line) => line.trim() !== "");

        if (lines.length < 2) {
            return [];
        }

        // Extract headers (first line)
        const headers = lines[0].split(",").map((header) => header.trim());

        // Process data rows
        const data = lines.slice(1).map((line) => {
            const values = line.split(",").map((value) => value.trim());
            const row: Record<string, string> = {};

            headers.forEach((header, index) => {
                row[header] = index < values.length ? values[index] : "";
            });

            return row;
        });

        return data;
    } catch {
        // Return empty array on parsing errors - handled by validation layer
        return [];
    }
};

/**
 * Interface for bulk airtime entry data
 */
export interface BulkAirtimeEntry {
    id: string;
    phoneNumber: string;
    network: string;
    status: "Valid" | "Invalid";
    amount: number;
}

/**
 * Validates a CSV file for bill payments
 * @param file The file to validate
 * @returns A promise resolving to a validation result
 */
export const validateCsvFile = (file: File): Promise<CsvValidationResult> =>
    new Promise((resolve) => {
        // First check file size to prevent processing very large files
        if (file.size > 10 * 1024 * 1024) {
            // 10MB limit
            resolve({
                isValid: false,
                errorMessage: "File is too large. Maximum size is 10MB.",
            });
            return;
        }

        const reader = new FileReader();

        reader.onload = (event) => {
            try {
                const csvContent = event.target?.result as string;
                if (!csvContent) {
                    resolve({
                        isValid: false,
                        errorMessage: "CSV file is empty",
                    });
                    return;
                }

                const data = parseCSV(csvContent);
                if (data.length === 0) {
                    resolve({
                        isValid: false,
                        errorMessage: "CSV file contains no data rows",
                    });
                    return;
                }

                // Check if required columns exist
                const firstRow = data[0];
                const requiredColumns = ["Phone number", "Network", "Amount"];
                const missingColumns = requiredColumns.filter((column) => !(column in firstRow));

                if (missingColumns.length > 0) {
                    resolve({
                        isValid: false,
                        errorMessage: `CSV file is missing required columns: ${missingColumns.join(", ")}. Please download and use the template provided.`,
                    });
                    return;
                }

                // Check data types
                const invalidRows: number[] = [];
                const invalidReasons: string[] = [];

                data.forEach((row, index) => {
                    let rowHasError = false;

                    // Check Phone number - should be a positive number
                    const phoneNumber = row["Phone number"];
                    const phoneNum = Number(phoneNumber.replace(/\D/g, ""));
                    if (isNaN(phoneNum) || phoneNum <= 0) {
                        invalidRows.push(index + 2); // +2 because index is 0-based and we need to account for the header row
                        rowHasError = true;
                        if (!invalidReasons.includes("phone number format")) {
                            invalidReasons.push("phone number format");
                        }
                    }

                    // Check Network - should be non-empty text
                    const network = row["Network"];
                    if (!network || network.trim() === "") {
                        if (!rowHasError) {
                            invalidRows.push(index + 2);
                            rowHasError = true;
                        }
                        if (!invalidReasons.includes("network provider")) {
                            invalidReasons.push("network provider");
                        }
                    }

                    // Check Amount - should be a number or string that can be converted to a number
                    const amount = row["Amount"];
                    if (isNaN(Number(amount)) || Number(amount) <= 0) {
                        if (!rowHasError) {
                            invalidRows.push(index + 2);
                        }
                        if (!invalidReasons.includes("amount")) {
                            invalidReasons.push("amount");
                        }
                    }
                });

                if (invalidRows.length > 0) {
                    const uniqueRows = Array.from(new Set(invalidRows)).sort((a, b) => a - b);
                    const rowsText =
                        uniqueRows.length > 3
                            ? `${uniqueRows.slice(0, 3).join(", ")} and ${uniqueRows.length - 3} more`
                            : uniqueRows.join(", ");

                    resolve({
                        isValid: false,
                        errorMessage: `Invalid data found in rows: ${rowsText}. Please check ${invalidReasons.join(", ")} fields. Download and use the template for the correct format.`,
                    });
                    return;
                }

                // If we get here, the CSV is valid
                resolve({
                    isValid: true,
                    data: data,
                });
            } catch {
                resolve({
                    isValid: false,
                    errorMessage: "Error validating CSV file. Please ensure the file is a properly formatted CSV.",
                });
            }
        };

        reader.onerror = () => {
            resolve({
                isValid: false,
                errorMessage: "Error reading the CSV file. The file may be corrupted or inaccessible.",
            });
        };

        reader.readAsText(file);
    });

/**
 * Converts parsed CSV data to BulkAirtimeEntry format for Redux
 * @param csvData The parsed CSV data
 * @returns An array of BulkAirtimeEntry objects
 *
 * @description
 * This function converts raw CSV data into the BulkAirtimeEntry format required by the Redux store.
 * It performs validation on each entry and sets the status accordingly:
 * - Validates phone numbers to ensure they contain only digits and are exactly 10 digits
 * - Validates amounts to ensure they are positive numbers
 * - Validates network providers to ensure they are non-empty
 * - Status is determined based on validations rather than being read from the CSV
 *
 * Each entry is assigned a unique ID based on its index and timestamp.
 */
export const convertCsvToEntries = (csvData: Record<string, string>[]): BulkAirtimeEntry[] =>
    csvData.map((row, index) => {
        // Generate a unique ID for each entry
        const id = `csv-${index}-${Date.now()}`;

        // Check if phone number is valid (numeric and greater than 0)
        const phoneNumber = row["Phone number"] || "";
        const phoneNum = Number(phoneNumber.replace(/\D/g, ""));
        const isPhoneValid = !isNaN(phoneNum) && phoneNum > 0;

        // Check if amount is valid (can be converted to a number and is greater than 0)
        const amountStr = row["Amount"] || "0";
        const amount = Number(amountStr);
        const isAmountValid = !isNaN(amount) && amount > 0;

        // Check if network is valid (non-empty text)
        const network = row["Network"] || "";
        const isNetworkValid = network.trim().length > 0;

        // Determine status based solely on validation results
        // No longer checking the Status column since it's been removed
        let status: "Valid" | "Invalid" = "Invalid";

        if (isPhoneValid && isNetworkValid && isAmountValid) {
            status = "Valid";
        }

        return {
            id,
            phoneNumber,
            network,
            status,
            amount: isAmountValid ? amount : 0,
        };
    });
