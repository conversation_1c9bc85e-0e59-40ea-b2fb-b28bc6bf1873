/**
 * @file RecentBillsContext.tsx
 *
 * @purpose Provides centralized access to recent bill payment transactions throughout the application.
 *
 * @functionality This context provider encapsulates the logic for fetching, formatting, and
 * managing recent bill payment data from the API. It handles the complete data lifecycle including
 * loading states, error management, and data transformation. The context implements proper
 * formatting for currency amounts and status values, and provides sorting functionality to display
 * transactions in chronological order. It also enforces a limit of 15 items to ensure optimal
 * performance and user experience. The context includes a refetch mechanism to allow components
 * to trigger data refresh after new payments, ensuring the UI displays up-to-date transaction history.
 *
 * @dependencies
 * - React Context API: For state management across components
 * - billAxios: Custom Axios instance for API requests
 * - RecentBillsResponse/RecentBillApiItem: Type definitions for API responses
 *
 * @usage Wrap components requiring access to recent bills data with the RecentBillsProvider.
 * Then, import and use the useRecentBills hook in any child component to access the bills array,
 * loading state, error information, and refetch function. This approach prevents redundant API
 * calls and ensures consistent data across the bill payments module.
 */

import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode, useMemo } from "react";
import { billAxios } from "@/api/axios";
import { RecentBillsResponse, RecentBillApiItem } from "@/redux/types/billPayments";

// Define a bill interface that matches what the components expect
export interface RecentBill {
    id: string;
    iconType: string;
    provider: string;
    type: string;
    categoryName: string;
    reference: string;
    amount: string;
    date: string;
    status: string;
    serviceId?: string;
    serviceName?: string;
    account?: string;
    narration?: string;
    isBulkPayment?: boolean;
    customIcon?: React.FC<{ className?: string; color?: string; strokeWidth?: number }>;
    // Additional fields for repeat payment functionality
    paymentCode?: string;
    customerId?: string | null;
    customerMobile?: string | null;
}

interface RecentBillsContextType {
    bills: RecentBill[];
    loading: boolean;
    error: string | null;
    refetchRecentBills: () => Promise<void>;
}

// Create context with undefined default value
const RecentBillsContext = createContext<RecentBillsContextType | undefined>(undefined);

/**
 * Custom hook to access the recent bills context
 * Must be used within a RecentBillsProvider
 */
export const useRecentBills = (): RecentBillsContextType => {
    const context = useContext(RecentBillsContext);
    if (!context) {
        throw new Error("useRecentBills must be used within a RecentBillsProvider");
    }
    return context;
};

interface RecentBillsProviderProps {
    children: ReactNode;
}

/**
 * Provider component for recent bills data
 * Fetches and manages recent bills data from the API
 * Provides data and refetch function to child components
 * Enforces a limit of 15 items for optimal performance
 */
export const RecentBillsProvider: React.FC<RecentBillsProviderProps> = ({ children }) => {
    const [bills, setBills] = useState<RecentBill[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    /**
     * Formats a status string by capitalizing the first letter
     * and removing underscores
     */
    const formatStatus = (status: string): string => {
        if (!status) return "";

        // Split by underscore and capitalize first letter of first word
        const parts = status.split("_");
        const firstWord = parts[0].charAt(0).toUpperCase() + parts[0].slice(1).toLowerCase();
        const restWords = parts.slice(1).map((word) => word.toLowerCase());

        return [firstWord, ...restWords].join(" ");
    };

    /**
     * Format amount from string to currency format
     */
    const formatAmount = (amount: string): string => {
        if (!amount) return "₦0.00";

        // Convert to number and format with Naira symbol
        const numberAmount = parseFloat(amount);
        return `₦${numberAmount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    };

    /**
     * Fetches recent bills from the API
     * Sets loading and error states appropriately
     * Transforms API response to match UI format
     * Enforces a 15-item limit regardless of API response size
     */
    const fetchRecentBills = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            // Using billAxios instance as per Core Principle #1
            const response = await billAxios.get<RecentBillsResponse>("/api/v1/vas/payments", {
                params: { pageNo: 1, pageSize: 15 },
            });

            if (response.data?.content) {
                // Transform API response to match our UI format
                const transformedBills = response.data.content.map((item: RecentBillApiItem) => ({
                    id: String(item.id),
                    // Set proper icon type based on payment type
                    iconType: item.bulkPaymentAdviceDetails ? "bulk-payment" : "receipt",
                    provider: item.biller ?? "-", // Use the biller field for provider
                    type: item.billType ?? "-", // Use the billType field for type
                    categoryName: item.billType ?? "-", // Use the billType field for categoryName
                    reference: item.requestReference,
                    amount: formatAmount(item.amount),
                    date: item.createdAt ?? "",
                    status: formatStatus(item.status),
                    account: item.accountNumber,
                    narration: item.narration ?? "-", // Use the narration field
                    isBulkPayment: !!item.bulkPaymentAdviceDetails,
                    // Add fields needed for repeat payment
                    paymentCode: item.paymentCode,
                    customerId: item.customerId,
                    customerMobile: item.customerMobile,
                }));

                // Sort bills by date with the latest bills at the top
                transformedBills.sort((a: RecentBill, b: RecentBill) => {
                    // If either date is empty, treat it as oldest
                    if (!a.date) return 1;
                    if (!b.date) return -1;

                    // Compare dates - newer dates should come first
                    return new Date(b.date).getTime() - new Date(a.date).getTime();
                });

                // Enforce 15-item limit for recent bills display
                // This ensures consistent performance regardless of API response size
                const latestFifteenBills = transformedBills.slice(0, 15);

                setBills(latestFifteenBills);
            }
        } catch (error) {
            // Check if this is a timeout error and create a user-friendly message
            const isTimeoutError =
                error instanceof Error && (error.message.includes("timeout") ?? error.message.includes("exceeded"));

            const errorMessage = isTimeoutError
                ? "Unable to load recent bills at this time"
                : "Failed to fetch recent bills";

            setError(errorMessage);

            // Following Core Principle #4: Don't show error toasts for initial data loading
            // No sendCatchFeedback here as this is initial data loading
        } finally {
            setLoading(false);
        }
    }, []);

    // Fetch bills on initial mount
    useEffect(() => {
        fetchRecentBills();
    }, [fetchRecentBills]);

    // Provide context value
    const value = useMemo(
        () => ({
            bills,
            loading,
            error,
            refetchRecentBills: fetchRecentBills,
        }),
        [bills, loading, error, fetchRecentBills]
    );

    return <RecentBillsContext.Provider value={value}>{children}</RecentBillsContext.Provider>;
};
