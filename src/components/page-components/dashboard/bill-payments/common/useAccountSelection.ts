/**
 * @file useAccountSelection.ts
 *
 * @purpose Shared hook for account selection logic used across bill payment components
 */

import { useCallback, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setSelectedAccount } from "@/redux/features/accounts";
import { formatAccountDisplay, getAccountByNumber } from "./account-utils";
import useGetAccountDetails from "@/hooks/useGetAccount";

interface UseAccountSelectionProps {
    selectedAccount?: string;
    onAccountChange?: (value: string) => void;
}

// Cache entry interface for storing balance data with timestamps
interface CacheEntry {
    balance: number;
    timestamp: number;
}

// Cache TTL (Time To Live) in milliseconds - 5 minutes
const CACHE_TTL = 5 * 60 * 1000;

// Global cache that persists across component instances and navigation
// This ensures cache is shared between different pages/components
const globalBalanceCache: Record<string, CacheEntry> = {};

// Export utility function to clear global cache from anywhere in the app
export const clearGlobalBalanceCache = (accountNumber?: string) => {
    if (accountNumber) {
        delete globalBalanceCache[accountNumber];
    } else {
        Object.keys(globalBalanceCache).forEach((key) => delete globalBalanceCache[key]);
    }
};

export const useAccountSelection = ({ selectedAccount, onAccountChange }: UseAccountSelectionProps) => {
    const dispatch = useAppDispatch();
    const { accounts, loadingStatus } = useAppSelector((state) => state.account);
    const loading = loadingStatus === "loading";

    const { getDetails } = useGetAccountDetails();
    const [balances, setBalances] = useState<Record<string, number>>({});

    // Get current account based on selected account number
    const currentAccount = selectedAccount ? getAccountByNumber(accounts, selectedAccount) : undefined;

    // Helper function to get balance from cache or fetch from API
    // Uses global cache that persists across component instances and navigation
    const getCachedBalance = useCallback(
        async (accountNumber: string): Promise<number> => {
            const now = Date.now();
            const cached = globalBalanceCache[accountNumber];

            // Check if we have a valid cached entry
            if (cached && now - cached.timestamp < CACHE_TTL) {
                return cached.balance;
            }

            try {
                const details = await getDetails(accountNumber);
                const balance = details?.balance ?? 0;

                // Update global cache with new balance and timestamp
                globalBalanceCache[accountNumber] = {
                    balance,
                    timestamp: now,
                };

                return balance;
            } catch {
                // Cache the error state as 0 balance to prevent repeated failed calls
                globalBalanceCache[accountNumber] = {
                    balance: 0,
                    timestamp: now,
                };
                return 0;
            }
        },
        [getDetails]
    );

    // Function to invalidate global cache for specific account or all accounts
    // Useful when account details are known to have changed
    const invalidateCache = useCallback((accountNumber?: string) => {
        if (accountNumber) {
            delete globalBalanceCache[accountNumber];
        } else {
            Object.keys(globalBalanceCache).forEach((key) => delete globalBalanceCache[key]);
        }
    }, []);

    // Memoize the fetchBalances function to prevent recreation on every render
    // This function fetches account balances with caching to optimize performance
    const fetchBalances = useCallback(async () => {
        const results: Record<string, number> = {};
        for (const account of accounts || []) {
            results[account.accountNumber] = await getCachedBalance(account.accountNumber);
        }

        setBalances(results);
    }, [accounts, getCachedBalance]);

    // Fetch balances for all accounts
    useEffect(() => {
        if (accounts?.length) {
            fetchBalances();
        }
    }, [accounts, fetchBalances]); // Use the memoized fetchBalances function

    // Handle account selection
    const handleAccountChange = useCallback(
        (accountNumber: string) => {
            const account = getAccountByNumber(accounts, accountNumber);
            if (account) {
                const formattedDisplay = formatAccountDisplay(account);
                dispatch(setSelectedAccount(formattedDisplay));
                onAccountChange?.(accountNumber);
            }
        },
        [dispatch, onAccountChange, accounts]
    );

    return {
        accounts,
        loading,
        balances,
        currentAccount,
        handleAccountChange,
        invalidateCache,
    };
};
