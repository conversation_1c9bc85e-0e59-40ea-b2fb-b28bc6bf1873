/**
 * @file account-utils.ts
 *
 * @purpose Shared utility functions for account formatting and display logic
 * used across bill payment components.
 */

import { AccountType } from "@/types/standalone";

/**
 * Formats an account name for display
 * @param account - The account object
 * @returns Formatted account name
 */
export const formatAccountName = (account: AccountType): string => {
    if (account.accountName) {
        return account.accountName;
    }
    // Handle any scheme type string - make it readable by converting to proper case
    const schemeType = account.schemeType?.trim();
    if (!schemeType) {
        return "Account"; // Fallback if schemeType is also empty/undefined
    }
    const formattedScheme = schemeType.charAt(0).toUpperCase() + schemeType.slice(1).toLowerCase();
    return `${formattedScheme} account`;
};

/**
 * Formats an account number for display (masked)
 * @param accountNumber - The account number
 * @returns Formatted account number with last 4 digits
 */
export const formatAccountNumber = (accountNumber: string): string => {
    const lastFourDigits = accountNumber.slice(-4);
    return `****${lastFourDigits}`;
};

/**
 * Helper function to find an account by account number
 * @param accounts - Array of accounts
 * @param accountNumber - The account number to find
 * @returns The matching account or undefined
 */
export const getAccountByNumber = (accounts: AccountType[], accountNumber: string): AccountType | undefined =>
    accounts.find((acc) => acc.accountNumber === accountNumber);

/**
 * Formats a full account display string
 * @param account - The account object
 * @returns Formatted display string combining name and number
 */
export const formatAccountDisplay = (account: AccountType): string =>
    `${formatAccountName(account)} ${formatAccountNumber(account.accountNumber)}`;
