/**
 * @file error-boundary.tsx
 *
 * @purpose Provides error isolation and graceful failure handling for bill payment components.
 *
 * @functionality This component implements React's Error Boundary pattern to catch JavaScript
 * errors that occur during rendering in child components. It prevents the entire application
 * from crashing when unexpected errors occur in the bill payments module. The component offers
 * flexible error handling through customizable fallback UI, error reporting options, and a reset
 * mechanism to recover from errors. It supports both default and custom error displays, and can
 * optionally invoke error tracking functions for monitoring and debugging purposes.
 *
 * @dependencies
 * - React: For error boundary implementation (requires class component)
 * - sendCatchFeedback: Utility for displaying error messages to users
 *
 * @usage Wrap any bill payment component that might throw errors with this ErrorBoundary.
 * Provide an optional fallbackComponent prop to customize the error display, an onError
 * callback for error reporting, or a custom message. The component will catch errors in
 * its children and display the fallback UI instead of crashing the application.
 */

import { Component, ErrorInfo, ReactNode } from "react";
import { sendCatchFeedback } from "@/functions/feedback";

interface ErrorBoundaryProps {
    children: ReactNode;
    fallbackComponent?: ReactNode | ((resetError: () => void) => ReactNode);
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    message?: string;
}

/**
 * A reusable error boundary component that catches JavaScript errors in its child component tree
 * and displays a fallback UI instead of crashing the whole application.
 *
 * @example
 * // Basic usage
 * <ErrorBoundary>
 *   <ComponentThatMightError />
 * </ErrorBoundary>
 *
 * @example
 * // With custom fallback component
 * <ErrorBoundary
 *   fallbackComponent={
 *     <div className="error-container">Something went wrong!</div>
 *   }
 * >
 *   <ComponentThatMightError />
 * </ErrorBoundary>
 *
 * @example
 * // With custom error handling
 * <ErrorBoundary
 *   onError={(error, errorInfo) => {
 *     logErrorToService(error, errorInfo);
 *   }}
 * >
 *   <ComponentThatMightError />
 * </ErrorBoundary>
 */
export default function ErrorBoundary(props: Readonly<ErrorBoundaryProps>): ReactNode {
    // Use a minimal class component for the actual error boundary functionality
    // since React doesn't support error boundaries in functional components
    return <ErrorBoundaryInner {...props} />;
}

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    key?: number; // Add a key to force re-render on reset
}

// Internal class component that implements the actual error boundary
class ErrorBoundaryInner extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            key: 0,
        };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error, key: 0 };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        // Log the error or send it to an error reporting service
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        } else {
            sendCatchFeedback({
                message: this.props.message ?? "An unexpected error occurred. Please try again or contact support.",
                type: "error",
            });
        }
    }

    resetError = (): void => {
        // Increment key to force a complete re-render of children
        this.setState((prevState) => ({
            hasError: false,
            error: undefined,
            key: (prevState.key ?? 0) + 1,
        }));
    };

    render(): ReactNode {
        if (this.state.hasError) {
            // Use custom fallback component if provided
            if (this.props.fallbackComponent) {
                if (typeof this.props.fallbackComponent === "function") {
                    return this.props.fallbackComponent(this.resetError);
                }
                return this.props.fallbackComponent;
            }

            // Default fallback UI
            return (
                <div className="p-4 border border-red-300 rounded bg-red-50 text-red-800">
                    <h3 className="text-lg font-medium mb-2">Something went wrong</h3>
                    <p>We're sorry, but there was an error loading this component.</p>
                    <button
                        className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                        onClick={this.resetError}
                    >
                        Try again
                    </button>
                </div>
            );
        }

        // Add key to force complete re-mount of children when it changes
        return <div key={this.state.key}>{this.props.children}</div>;
    }
}
