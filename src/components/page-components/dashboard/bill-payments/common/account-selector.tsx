"use client";

// import { useMemo } from "react"

/**
 * @file account-selector.tsx
 *
 * @purpose Provides a standardized account selection component for bill payment flows.
 */

import { BankNoteIcon } from "@/components/icons/bill-payment-icons";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/common/select";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { useState } from "react";
import LoadingIndicator from "@/components/common/loading-indicator";
import { formatAccountName, formatAccountNumber, formatAccountDisplay } from "./account-utils";
import { useAccountSelection } from "./useAccountSelection";

interface AccountSelectorProps {
    selectedAccount?: string;
    onAccountChange?: (value: string) => void;
    label?: string;
    className?: string;
}

export default function AccountSelector({
    selectedAccount: propSelectedAccount,
    onAccountChange,
    label,
    className,
}: Readonly<AccountSelectorProps>) {
    const { accounts, loading, balances, currentAccount, handleAccountChange } = useAccountSelection({
        selectedAccount: propSelectedAccount,
        onAccountChange,
    });

    // Simplified state management - track only the selected account number
    const [selectedAccountNumber, setSelectedAccountNumber] = useState<string | undefined>(propSelectedAccount);

    // Note: Accounts are already fetched by GetUserAccounts component in dashboard layout
    // No need to fetch again here, just use the data from Redux state

    // Handle account selection with state update
    const handleAccountChangeWithState = (accountNumber: string) => {
        setSelectedAccountNumber(accountNumber);
        handleAccountChange(accountNumber);
    };

    // Display the account balance with proper loading state
    const displayBalance = () => {
        if (!currentAccount) return "₦ -";

        // Show loading indicator if balance is not yet loaded
        if (balances[currentAccount.accountNumber] === undefined) {
            return <LoadingIndicator size={16} />;
        }

        return formatNumberToNaira(balances[currentAccount.accountNumber] ?? 0);
    };

    if (loading) {
        return (
            <div className="space-y-2">
                <div className="text-[#151518] text-xs font-semibold leading-none tracking-tight">
                    {label ?? "Pay from"}
                </div>
                <div
                    className="w-full h-11 px-3 py-[18px] bg-white rounded-lg border border-[#dbdbe0] flex items-center justify-center"
                    aria-live="polite"
                    aria-busy="true"
                >
                    <LoadingIndicator size={20} align="center" />
                </div>
            </div>
        );
    }

    if (accounts.length === 0) {
        return (
            <div className="space-y-2">
                <div className="text-[#151518] text-xs font-semibold leading-none tracking-tight">
                    {label ?? "Pay from"}
                </div>
                <div
                    className="w-full h-11 px-3 py-[18px] bg-white rounded-lg border border-[#dbdbe0] flex items-center justify-center"
                    aria-live="polite"
                >
                    <span className="text-[#151518] text-sm">No accounts available</span>
                </div>
            </div>
        );
    }

    return (
        <div className={`space-y-2 ${className ?? ""}`}>
            <label
                htmlFor="account-select"
                className="text-[#151518] text-xs font-semibold leading-none tracking-tight"
            >
                {label ?? "Pay from"}
            </label>

            <Select
                value={selectedAccountNumber ?? currentAccount?.accountNumber ?? ""}
                onValueChange={handleAccountChangeWithState}
            >
                <SelectTrigger
                    id="account-select"
                    className="w-full h-11 px-3 py-[18px] bg-white rounded-lg border border-[#dbdbe0]"
                    aria-label="Select payment account"
                >
                    <div className="flex justify-between items-center w-full">
                        <div className="flex items-center gap-2 overflow-hidden">
                            <BankNoteIcon className="w-5 h-5 flex-shrink-0" aria-hidden="true" />
                            <span
                                className="text-[#151518] text-base font-medium leading-tight tracking-tight truncate"
                                title={currentAccount ? formatAccountDisplay(currentAccount) : "Select an account"}
                            >
                                {currentAccount ? formatAccountDisplay(currentAccount) : "Select an account"}
                            </span>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                            <span className="text-[#151518] text-base font-medium leading-tight tracking-tight pr-2">
                                {displayBalance()}
                            </span>
                        </div>
                    </div>
                </SelectTrigger>
                <SelectContent>
                    {accounts.length > 0 ? (
                        accounts.map((account) => {
                            const accountBalance = balances[account.accountNumber];
                            const balance =
                                accountBalance !== undefined ? (
                                    formatNumberToNaira(accountBalance ?? 0)
                                ) : (
                                    <LoadingIndicator size={14} />
                                );

                            return (
                                <SelectItem
                                    key={account.accountNumber}
                                    value={account.accountNumber}
                                    className="hover:bg-[#F9F9FA] active:bg-[#E3E5E8]"
                                >
                                    <div className="flex justify-between items-center w-full">
                                        <div className="flex items-center gap-2 overflow-hidden">
                                            <BankNoteIcon className="w-5 h-5 flex-shrink-0" aria-hidden="true" />
                                            <span className="truncate">
                                                {formatAccountName(account)}{" "}
                                                {formatAccountNumber(account.accountNumber)}
                                            </span>
                                        </div>
                                        <span className="flex-shrink-0 ml-6">{balance}</span>
                                    </div>
                                </SelectItem>
                            );
                        })
                    ) : (
                        <SelectItem
                            value="no-accounts"
                            disabled
                            className="hover:bg-[#F9F9FA]"
                            aria-label="No accounts available"
                        >
                            <div className="flex justify-center items-center w-full">
                                <span className="text-[#151518]">No accounts available</span>
                            </div>
                        </SelectItem>
                    )}
                </SelectContent>
            </Select>
        </div>
    );
}
