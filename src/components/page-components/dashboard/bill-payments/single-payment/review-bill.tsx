/**
 * @file Single Bill Review Component
 *
 * @purpose This file provides the final review and confirmation step in the single bill payment flow.
 *
 * @functionality The SingleReviewBill component presents a comprehensive summary of the bill payment
 * for review before confirmation. It displays recipient information, detailed bill information
 * (adapting to different bill types), and approval requirements. The component integrates with
 * the payment confirmation hook to handle PIN verification and payment submission. It implements
 * the dual-approach pattern to display specialized fields for utility/cable bills and standard
 * fields for other categories. The component also includes state management for processing states,
 * exit confirmation, and error handling.
 *
 * @dependencies
 * - Redux for state management
 * - Payment confirmation hook for PIN verification and submission
 * - UI components for bill information display (RecipientSection, BillDetails, ActionButtons)
 * - Display helper functions for formatting bill information
 * - Multi-party approval component for approval workflow display
 *
 * @usage This component serves as the final step in the single bill payment flow. It receives
 * all payment details (bill details and payment information) from previous steps, displays them
 * for review, and handles the final payment submission process including PIN verification.
 */

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { singlePaymentSteps, type SingleReviewBillProps } from "../utils";
import Stepper from "@/components/common/stepper";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { formatAccountName, formatAccountNumber, resetAccounts } from "@/redux/features/accounts";
import MultiPartyApproval from "../common/multi-party-approval";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { sendCatchFeedback } from "@/functions/feedback";
import { usePaymentConfirmation } from "./utils/use-payment-confirmation";
import { RecipientSection, BillDetails, ActionButtons } from "./components";
import {
    getBillIdentifier,
    getCustomerDetails,
    getBillTypeDisplay,
    getBillerValue,
    getFullCustomerDetails,
} from "./display-helpers";
import SettingsMfaVerification from "@/components/page-components/dashboard/settings/components/settings-mfa-verification";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";

export default function SingleReviewBill({
    billType,
    phoneNumber,
    identificationNumber,
    smartCardNumber,
    amount,
    networkProvider,
    serviceProvider,
    package: packageType,
    serviceID,
    customerDetails,
    onBack,
    onConfirm,
    paymentInfo,
    onChangeBillDetails,
    categoryName,
}: Readonly<SingleReviewBillProps>) {
    const dispatch = useAppDispatch();
    const { selectedAccount, accounts } = useAppSelector((state) => state.accounts);
    const [showApprovals] = useState(true);

    // Get PIN verification status from security slice
    const { open: isPinModalOpen } = useAppSelector((state) => state.security.verifyPin);

    // 2FA/MFA verification for bill payments
    const [initiateBillPaymentMfaFlow, setInitiateBillPaymentMfaFlow] = useState(false);
    const { success: teamMemberSuccess } = useAppSelector((state) => state.transferMfaSlice.getTeamMemberDetails);
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);

    const [mfaVerificationState, setMfaVerificationState] = useState({
        mfaVerified: false,
        showMfaModal: false,
        token: null as string | null,
    });

    // Get account number from selected account with more robust matching
    const account = accounts.find(
        (acc) => `${formatAccountName(acc)} ${formatAccountNumber(acc.accountNumber)}` === selectedAccount
    );

    // If we can't find the account by formatted string, try to use the paymentInfo.accountNumber
    // and find the corresponding account, or fall back to the first account
    let accountNumber = account?.accountNumber;
    if (!accountNumber) {
        if (paymentInfo.accountNumber) {
            // Try to find account by the stored account number
            const accountByNumber = accounts.find((acc) => acc.accountNumber === paymentInfo.accountNumber);
            accountNumber = accountByNumber?.accountNumber ?? paymentInfo.accountNumber;
        } else {
            // Fall back to first account if available
            accountNumber = accounts[0]?.accountNumber ?? "";
        }
    }

    const {
        isProcessing,
        currentStep,
        handleConfirm: originalHandleConfirm,
    } = usePaymentConfirmation({
        billType,
        amount,
        accountNumber,
        phoneNumber,
        identificationNumber,
        smartCardNumber,
        serviceID,
        paymentInfo,
        networkProvider,
        serviceProvider,
        packageType,
        customerDetails,
        categoryName,
        onConfirm,
        mfaToken: mfaVerificationState.token, // Pass MFA token to payment confirmation
    });

    // Handle Confirm Payment button click - initiate MFA verification first
    const handleConfirmPaymentClick = () => {
        dispatch(getTeamMemberDetails());
        setInitiateBillPaymentMfaFlow(true);
    };

    // Handle MFA verification completion
    const handleMfaVerified = useCallback(
        (token: string) => {
            setMfaVerificationState({
                mfaVerified: true,
                showMfaModal: false,
                token: token,
            });
            setInitiateBillPaymentMfaFlow(false);
            // Proceed with PIN verification and payment
            originalHandleConfirm();
        },
        [originalHandleConfirm]
    );

    // Handle MFA verification modal close
    const handleMfaVerificationClose = () => {
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            token: null,
        });
        setInitiateBillPaymentMfaFlow(false);
    };

    // Handle team member details response
    useEffect(() => {
        if (teamMemberSuccess && teamMember && initiateBillPaymentMfaFlow) {
            // Check if user has MFA enabled
            if (teamMember?.mfaStatus) {
                // Show MFA verification modal
                setMfaVerificationState({
                    mfaVerified: false,
                    showMfaModal: true,
                    token: null,
                });
            } else {
                // No MFA required, directly proceed to PIN verification
                handleMfaVerified(""); // Pass empty token when no MFA is required
            }
        }
    }, [teamMemberSuccess, teamMember, initiateBillPaymentMfaFlow, handleMfaVerified]);

    // Clean up MFA state when component unmounts or payment is successful
    useEffect(
        () => () => {
            if (mfaVerificationState.showMfaModal || mfaVerificationState.mfaVerified) {
                dispatch(resetAllStates());
            }
        },
        [dispatch, mfaVerificationState.showMfaModal, mfaVerificationState.mfaVerified]
    );

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => {
            dispatch(resetSingleBillPayment());
            dispatch(resetAccounts());
        },
    });

    const getSteps = () => singlePaymentSteps;

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={
                categoryName
                    ? `${categoryName} bill payment`
                    : `${getBillTypeDisplay(billType, categoryName)} bill payment`
            }
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <div className="flex-1 flex min-h-0 relative overflow-x-auto flex-col lg:flex-row">
                {/* Stepper navigation - Fixed width of 240px */}
                <nav
                    className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment steps"
                >
                    <Stepper steps={getSteps()} currentStep={currentStep} />
                </nav>

                {/* Main content area
                    Left padding: 240px (stepper width)
                    Right padding: Matches multi-party approval width which is:
                    - 435px for screens < 1450px
                    - 480px for screens >= 1450px
                    Using responsive classes to adjust as the right panel changes width
                */}
                <main
                    className="flex-1 flex justify-center overflow-y-auto overflow-x-auto relative pl-[240px] pr-[435px] min-[1450px]:pr-[480px]"
                    role="main"
                >
                    {/* Content width of 495px by default, 495px between 1340px-1449px, 515px for 1450px+ */}
                    <div className="w-[495px] min-[1450px]:w-[515px] flex flex-col pt-8 min-h-min">
                        <h2 className="text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-12 text-center">
                            Review bill payment
                        </h2>

                        <RecipientSection
                            billType={billType}
                            identifier={getBillIdentifier(billType, identificationNumber, smartCardNumber, phoneNumber)}
                            details={
                                billType === "electricity" || billType === "cable-tv"
                                    ? `${getCustomerDetails(billType, customerDetails)} • ₦${amount.toLocaleString()}.00`
                                    : `${networkProvider ?? ""} • ₦${amount.toLocaleString()}.00`
                            }
                            onChangeBillDetails={onChangeBillDetails}
                            categoryName={categoryName}
                        />

                        <BillDetails
                            billType={billType}
                            getBillTypeDisplay={(type) => getBillTypeDisplay(type, categoryName)}
                            getBillerValue={() =>
                                getBillerValue(billType, serviceProvider, packageType, networkProvider)
                            }
                            identificationNumber={identificationNumber}
                            smartCardNumber={smartCardNumber}
                            getFullCustomerDetails={() => getFullCustomerDetails(billType, customerDetails)}
                            amount={amount}
                            selectedAccount={selectedAccount}
                            categoryName={categoryName}
                        />

                        <ActionButtons
                            onBack={onBack}
                            handleConfirm={handleConfirmPaymentClick}
                            isProcessing={isProcessing || isPinModalOpen || mfaVerificationState.showMfaModal}
                        />
                    </div>

                    <MultiPartyApproval isOpen={showApprovals} amount={amount} type="BILL_PAYMENT" />
                </main>
            </div>

            {/* MFA Verification Modal */}
            {mfaVerificationState.showMfaModal && teamMember && (
                <SettingsMfaVerification
                    isOpen={mfaVerificationState.showMfaModal}
                    onClose={handleMfaVerificationClose}
                    onVerified={handleMfaVerified}
                    userMfaType={teamMember.preferredMfaMethod ?? "SMS"}
                    email={teamMember.email ?? ""}
                    phoneNumber={teamMember.phoneNumber ?? ""}
                />
            )}
        </FullScreenDrawer>
    );
}

export const handlePaymentError = (error: unknown, setCurrentStep: React.Dispatch<React.SetStateAction<number>>) => {
    if (error instanceof Error) {
        sendCatchFeedback(error);
    } else {
        sendCatchFeedback(new Error("Payment failed. Please try again"));
    }
    setCurrentStep(3);
};
