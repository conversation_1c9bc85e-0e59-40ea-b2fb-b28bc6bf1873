"use client";

import type React from "react";

/**
 * @file Base Bill Details Component
 *
 * @purpose This file provides a foundation component for bill payment detail forms across different bill categories.
 *
 * @functionality The BaseBillDetails component serves as a reusable base form for collecting bill details
 * in the single payment flow. It implements common form functionality including validation, customer ID verification,
 * package selection, and form submission. The component adapts to different bill categories through its props
 * system, allowing specialized versions (like UtilityBillDetails) to extend it with category-specific fields
 * and validation. It handles customer ID validation through a debounced API call and displays validation results
 * to the user.
 *
 * @dependencies
 * - Formik for form state management and validation
 * - Yup for validation schema definition
 * - Redux for state management and bill payment data
 * - UI components from common library (Button, LabelInput, FullScreenDrawer, Stepper)
 * - Custom hooks for exit confirmation and customer validation
 *
 * @usage Import and extend this component when creating category-specific bill details forms.
 * Provide the necessary props including validation schema, customer ID field configuration,
 * and submission handler. For example, UtilityBillDetails uses this component to create
 * specialized forms for electricity and cable TV bill payments.
 */

import { useFormik } from "formik";
import type * as Yup from "yup";
import { ValidationError } from "yup";
import { useExitHandlers } from "../hooks/useExitHandlers";
import { resetSingleBillPayment, updateSelectedPackage } from "@/redux/slices/singleBillPayment";
import { resetCustomerValidation } from "@/redux/slices/billPaymentSlice";
import { resetAccounts } from "@/redux/features/accounts";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import type { Biller, ServiceProviderChangeData } from "../types";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { BillDetailsForm } from "../common";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { useCustomerValidation } from "./hooks/use-customer-validation";
import { Loader2 } from "lucide-react";

export interface PaymentItem {
    paymentCode: string;
    paymentItemName: string;
    amount: string;
    amountFixed: boolean;
}

export interface Step {
    id: string;
    label: string;
    content?: React.ReactNode;
}

export interface BillFormValues {
    serviceProvider: string;
    package: string;
    amount: string;
    customerDetails: string;
    paymentCode: string;
    [key: string]: string;
}

export type BillSubmitValues<T extends string = string> = {
    serviceProvider: string;
    package: string;
    amount: number;
    customerDetails: string;
    type?: T;
    [key: string]: string | number | undefined;
};

export interface BaseBillDetailsProps<T extends string = string> {
    onSubmit: (values: BillSubmitValues<T>) => void;
    defaultValues: BillFormValues;
    categoryId: number;
    title: string;
    steps: Step[];
    currentStep: number;
    validationSchema: (biller: Biller | null) => Yup.ObjectSchema<Record<string, string | number>>;
    customerIdField: string;
    customerIdLabel: string;
    customerIdPlaceholder: string;
    validateCustomerId: (value: string) => boolean;
    useDynamicLabels?: boolean;
    categoryName: string;
    onServiceProviderChange?: (data: ServiceProviderChangeData) => void;
}

export function BaseBillDetails({
    onSubmit,
    defaultValues,
    categoryId,
    title,
    steps,
    currentStep,
    validationSchema,
    customerIdField,
    customerIdLabel,
    customerIdPlaceholder,
    validateCustomerId,
    useDynamicLabels = false,
    categoryName,
    onServiceProviderChange,
}: Readonly<BaseBillDetailsProps>) {
    const dispatch = useAppDispatch();

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => {
            if (showExitConfirmation) {
                dispatch(resetSingleBillPayment());
                dispatch(resetCustomerValidation());
                dispatch(resetAccounts());
            }
        },
    });

    const { data: billers } = useAppSelector((state) => state.billPayments.biller);

    // Get the selected package from Redux to check package selection state
    const selectedPackage = useAppSelector((state) => state.singleBillPayment.selectedPackage);

    const formik = useFormik<BillFormValues>({
        initialValues: defaultValues,
        validate: (values) => {
            // Find the selected biller from the array based on serviceProvider value
            const selectedBiller =
                billers && Array.isArray(billers) && values?.serviceProvider
                    ? billers.find(
                          (biller) =>
                              biller.billername?.toLowerCase() === values.serviceProvider.toLowerCase() ||
                              biller.billerName?.toLowerCase() === values.serviceProvider.toLowerCase()
                      )
                    : null;

            // Convert to the expected Biller format if found
            const billerForValidation = selectedBiller
                ? {
                      billerId: Number(selectedBiller.billerid ?? selectedBiller.billerId ?? 0),
                      billerName: selectedBiller.billername ?? selectedBiller.billerName ?? "",
                  }
                : null;

            // If no service provider is selected, don't show validation errors for other fields
            if (!values.serviceProvider) {
                return {};
            }

            // Create the validation schema with the current biller
            const schema = validationSchema(billerForValidation);

            try {
                schema.validateSync(values, { abortEarly: false });
                return {};
            } catch (err) {
                if (err instanceof ValidationError) {
                    const errors = err.inner.reduce((acc: Record<string, string>, curr: ValidationError) => {
                        // Only show errors for fields that have been touched or have values
                        const fieldPath = curr.path ?? "";
                        if (fieldPath === "serviceProvider" || values[fieldPath]) {
                            return {
                                ...acc,
                                [fieldPath]: curr.message,
                            };
                        }
                        return acc;
                    }, {});
                    return errors;
                }
                return {};
            }
        },
        onSubmit: (values) => {
            const { amount, ...rest } = values;
            // Ensuring empty strings are passed instead of null/undefined (per API Integration Guidelines)
            const customerDetailsString =
                customerDetails.name && customerDetails.description
                    ? `${customerDetails.name}, ${customerDetails.description}`
                    : customerDetails.name || "";

            onSubmit({
                ...rest,
                amount: Number(amount),
                customerDetails: customerDetailsString,
            });
        },
    });

    // Use the customer validation hook
    const {
        customerDetails,
        isValidationSuccessful,
        validationLoading,
        debouncedValidate,
        resetCustomerDetails,
        validationAttempted,
    } = useCustomerValidation({
        validateCustomerId,
        categoryName,
    });

    /**
     * Handles customer ID field changes.
     *
     * This function updates the field value and resets validation state
     * but doesn't trigger validation automatically - validation is deferred
     * until the Next button is clicked, following the pattern in normal-bill-details.tsx
     */
    const handleCustomerIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        formik.setFieldValue(customerIdField, value);

        // Clear customer details when input changes
        resetCustomerDetails();

        // Don't automatically validate - wait for Next button click
        // This matches the behavior in normal-bill-details.tsx
    };

    /**
     * Handles package selection.
     * Sets up the package and payment code fields but does not trigger validation.
     * Validation will be triggered when the Next button is clicked.
     */
    const handlePackageSelect = (pkg: { amount: string; amountFixed: boolean; paymentCode: string } | null) => {
        dispatch(updateSelectedPackage(pkg));
        resetCustomerDetails();

        if (pkg?.paymentCode) {
            formik.setFieldValue("paymentCode", pkg.paymentCode);
        }

        // Don't automatically validate - validation will happen on Next button click
    };

    /**
     * Handles Next button click with validation.
     *
     * This function:
     * 1. Checks if customer validation is needed
     * 2. Triggers validation if needed and not already done
     * 3. Shows loading state during validation
     * 4. Proceeds with form submission when validation is complete
     *
     * Following Frontend Guidelines for loading states and error handling
     */
    const handleNextClick = () => {
        // For categories that need validation, validate first if not already successful or in progress
        if (
            !isValidationSuccessful &&
            !validationLoading &&
            formik.values[customerIdField] &&
            formik.values.paymentCode &&
            validateCustomerId(formik.values[customerIdField])
        ) {
            // Only trigger validation if customer ID was changed or validation wasn't attempted
            if (!validationAttempted || formik.values[customerIdField] !== defaultValues[customerIdField]) {
                debouncedValidate(formik.values[customerIdField], formik.values.paymentCode);
                return;
            } else {
                // Validation was already attempted with the same customer ID - no action needed
            }
        } else {
            // No validation needed for this category or validation already successful
        }

        // Trigger form submission after validation is complete or if validation is not needed
        formik.handleSubmit();
    };

    // Wrapper function to ensure click events are captured
    const handleButtonClick = () => {
        handleNextClick();
    };

    const isPackageValid = (formik.values.package && !formik.errors.package) || selectedPackage;

    const selectedBiller =
        billers && Array.isArray(billers) && formik?.values?.serviceProvider
            ? billers.find(
                  (biller) =>
                      biller.billername?.toLowerCase() === formik.values.serviceProvider.toLowerCase() ||
                      biller.billerName?.toLowerCase() === formik.values.serviceProvider.toLowerCase()
              )
            : null;

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={title}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <main className="flex-1 flex min-h-0 relative overflow-x-auto flex-col lg:flex-row">
                <nav
                    className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment progress"
                >
                    <Stepper steps={steps} currentStep={currentStep} />
                </nav>

                <div className="flex-1 flex justify-center overflow-y-auto overflow-x-auto relative pl-[240px] px-primary lg:px-0">
                    <div className="w-full max-w-[470px] flex flex-col items-center pt-8">
                        <h2 className="text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-12">
                            Bill details
                        </h2>

                        <form onSubmit={formik.handleSubmit} className="w-full space-y-6" noValidate>
                            <BillDetailsForm
                                key={formik.values.serviceProvider} // Add this line
                                formik={formik}
                                categoryId={categoryId}
                                onPackageSelect={handlePackageSelect}
                                onServiceProviderChange={onServiceProviderChange}
                            >
                                <div className="space-y-1">
                                    <LabelInput
                                        data-testid={`${customerIdField}-input`}
                                        value={formik.values[customerIdField]}
                                        onChange={handleCustomerIdChange}
                                        onBlur={formik.handleBlur(customerIdField)}
                                        className={`bg-white ${!isPackageValid ? "bg-[#F7F7F8] text-[#9D9DAC] cursor-not-allowed" : ""}`}
                                        placeholder={
                                            customerIdPlaceholder ||
                                            (useDynamicLabels && selectedBiller
                                                ? `Enter ${selectedBiller.billerName} number`
                                                : "Enter number")
                                        }
                                        name={customerIdField}
                                        disabled={!isPackageValid}
                                        useFormik={false}
                                        showError={
                                            !!(formik.touched[customerIdField] && formik.errors[customerIdField])
                                        }
                                        // error={formik.errors[customerIdField] as string}
                                        label={
                                            customerIdLabel ||
                                            (useDynamicLabels && selectedBiller
                                                ? `${selectedBiller.billerName} number`
                                                : "Number")
                                        }
                                    />
                                    {validationLoading && <div className="text-sm text-[#3A3A41]">Validating...</div>}
                                    {!validationLoading && customerDetails.name && (
                                        <div className="text-sm text-[#3A3A41]">{customerDetails.name}</div>
                                    )}
                                </div>
                            </BillDetailsForm>

                            <div className="pt-4">
                                <Button
                                    type="button"
                                    onClick={handleButtonClick}
                                    variant="primary"
                                    size="sm"
                                    className="float-right"
                                    disabled={validationLoading}
                                    aria-label="Continue to payment information"
                                >
                                    {validationLoading ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Verifying details...
                                        </>
                                    ) : (
                                        "Next"
                                    )}
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </FullScreenDrawer>
    );
}
