/**
 * @file Normal Bill Details Component
 *
 * @purpose This file provides the bill details form for standard (non-utility) bill payment categories.
 *
 * @functionality The NormalBillDetails component renders a form for collecting payment details for
 * standard bill categories like airtime, taxes, and donations. It handles service provider selection,
 * recipient number input (with optional country code for mobile categories), and amount entry.
 * The component includes validation logic for all fields, dynamically adjusts field labels based
 * on the selected service provider, and provides responsive error messaging. It integrates with Redux
 * for state management and implements the exit confirmation pattern for form navigation. The component
 * relies on biller data being pre-loaded by its parent container to avoid redundant API calls.
 *
 * @dependencies
 * - Formik for form state management
 * - Redux for global state management (billers pre-loaded by parent)
 * - Button, CountryCode, FormField, and other UI components from common libraries
 * - Custom hook useExitHandlers for navigation confirmation
 * - Redux actions for fetching payment items (when needed)
 *
 * @usage This component is used as part of the single payment flow for all standard bill categories.
 * It's typically rendered by the SingleBillPayment container component based on category selection,
 * and forms the first step in the standard bill payment process. The parent container is responsible
 * for loading biller data before this component mounts.
 */

"use client";

import { useFormik } from "formik";
import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import Stepper from "@/components/common/stepper";
import { singlePaymentSteps } from "../utils";
import React from "react";
import CountryCode from "@/components/common/country-code";
import FormField from "../common/form-field";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import AmountInput from "@/components/common/amount-input";

import { ValidationError } from "yup";
import ServiceProviderSelect from "../common/service-provider-select";
import { BillPaymentFormValues, Biller } from "../types";
import { useCustomerValidation } from "./hooks/use-customer-validation";
import { getCustomerIdLabel, shouldSkipValidation } from "./utils/category-utils";
import { Loader2 } from "lucide-react";

// Define an extended biller interface that handles both naming conventions
interface ExtendedBiller {
    // Include all possible variants of field names
    billername?: string;
    billerName?: string;
    billerid?: number | string;
    billerId?: number | string;
    shortName?: string;
}

// Type for use in validation function
type BillerType = ExtendedBiller;

export interface NormalBillDetailsValues {
    networkProvider: string;
    phoneNumber: string;
    amount: number;
    customerDetails?: string;
}

/**
 * Interface for service provider change callback data
 * This enables parent components to react to service provider changes
 */
interface ServiceProviderChangeData {
    billerName: string;
    billerId: string | number;
    biller: Biller;
}

interface NormalBillDetailsProps {
    onSubmit: (values: NormalBillDetailsValues) => void;
    initialValues: {
        networkProvider: string;
        phoneNumber: string;
        amount: string;
    };
    categoryId: number;
    title?: string; // Optional title that can be customized based on category
    categoryName?: string; // Added categoryName for validation logic
    onServiceProviderChange?: (data: ServiceProviderChangeData) => void; // Callback for service provider changes
}

// Helper function for validation to reduce cognitive complexity
const validateAirtimeForm = (values: AirtimeFormValues, billers: BillerType[] | null, categoryName?: string) => {
    const errors: Record<string, string> = {};

    // Find the selected service provider for better error messages
    const serviceProvider =
        billers && Array.isArray(billers)
            ? billers.find((biller) => {
                  const billerName = biller.billername ?? biller.billerName;
                  return billerName?.toLowerCase() === values.serviceProvider.toLowerCase();
              })
            : null;

    // Use the same provider label logic as in the UI
    const providerLabel = serviceProvider ? (serviceProvider.billername ?? serviceProvider.billerName ?? "") : "";

    if (!values.serviceProvider) {
        errors.serviceProvider = "Service provider is required";
    }

    // Phone number validation with enhanced Mobile/Recharge category support
    // This validation ensures exactly 10 digits for Mobile/Recharge categories
    // while maintaining basic validation for other categories
    if (!values.phoneNumber) {
        // Use consistent naming with the form label
        errors.phoneNumber = providerLabel ? `${providerLabel} number is required` : "Number is required";
    } else if (shouldSkipValidation(categoryName ?? "")) {
        // Exactly 10 digits required for Mobile/Recharge categories
        // This addresses the specific requirement for stricter validation
        if (!/^\d{10}$/.test(values.phoneNumber)) {
            errors.phoneNumber = "Phone number must be exactly 10 digits";
        }
    } else if (values.phoneNumber.trim().length === 0) {
        // Basic validation for other categories
        // Ensures non-empty input for all other bill payment types
        errors.phoneNumber = "Phone number is required";
    }

    if (!values.amount) {
        errors.amount = "Amount is required";
    } else if (isNaN(Number(values.amount)) || Number(values.amount) <= 0) {
        errors.amount = "Amount must be a positive number";
    }

    return errors;
};

// Define a combined form values type to work with both ServiceProviderSelect and NormalBillDetailsValues
interface AirtimeFormValues extends BillPaymentFormValues {
    networkProvider: string;
    phoneNumber: string;
    paymentCode?: string;
}

export default function NormalBillDetails({
    onSubmit,
    initialValues,
    categoryId,
    title = "Airtime bill payment", // Default title
    categoryName,
    onServiceProviderChange,
}: Readonly<NormalBillDetailsProps>) {
    const dispatch = useAppDispatch();

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => dispatch(resetSingleBillPayment()),
    });

    // Get biller data from Redux - this data is now pre-loaded by the parent container
    // to prevent redundant API calls across the single payment flow
    const { data: billers } = useAppSelector((state) => state.billPayments.biller);
    const { data: paymentItems } = useAppSelector((state) => state.billPayments.paymentItems);

    /**
     * Validates phone number format based on category requirements
     *
     * This function applies different validation rules depending on the bill payment category:
     * - Mobile/Recharge categories: Exactly 10 digits
     * - Other categories: Non-empty validation
     *
     * @param value - Phone number string to validate
     * @returns boolean indicating if the phone number format is valid
     */
    const validatePhoneNumber = (value: string) => {
        // Enhanced validation for Mobile/Recharge categories
        if (shouldSkipValidation(categoryName ?? "")) {
            // Exactly 10 digits required for Mobile/Recharge
            const result = /^\d{10}$/.test(value);
            return result;
        }

        // For other categories, just ensure it's not empty
        const result = value.trim().length > 0;
        return result;
    };

    // Set up customer validation hook
    const {
        customerDetails,
        isValidationSuccessful,
        validationLoading,
        debouncedValidate,
        resetCustomerDetails,
        shouldSkipValidation: skipValidation,
        validationAttempted,
    } = useCustomerValidation({
        validateCustomerId: validatePhoneNumber,
        categoryName,
    });

    // Payment items are now fetched by the parent container (single-payment.tsx)
    // to prevent redundant API calls and improve performance. This component
    // simply consumes the data from Redux state following the container/presentation
    // pattern from Frontend Guidelines.

    const formik = useFormik<AirtimeFormValues>({
        initialValues: {
            networkProvider: initialValues.networkProvider,
            phoneNumber: initialValues.phoneNumber,
            amount: initialValues.amount,
            // Add required fields from BillPaymentFormValues
            serviceProvider: initialValues.networkProvider,
            package: "",
            paymentCode: "",
        },
        validateOnChange: false,
        validateOnBlur: false,
        validate: (values) => {
            try {
                // Use type assertion to ensure billers is treated as BillerType[]
                const errors = validateAirtimeForm(values, billers as BillerType[] | null, categoryName);
                return errors;
            } catch (err) {
                if (err instanceof Error && err.name === "ValidationError") {
                    // Convert Yup validation errors to Formik errors format
                    const validationErrors: Record<string, string> = {};
                    const yupError = err as ValidationError;

                    if (yupError.inner && Array.isArray(yupError.inner)) {
                        yupError.inner.forEach((error: ValidationError) => {
                            if (error.path && !validationErrors[error.path]) {
                                validationErrors[error.path] = error.message;
                            }
                        });
                    }
                    return validationErrors;
                }
                return {};
            }
        },
        onSubmit: (values) => {
            // For non-skipped categories, ensure validation is successful
            // Only apply validation checks if validation was actually attempted
            if (!skipValidation && validationAttempted) {
                if (!isValidationSuccessful && !validationLoading) {
                    formik.setFieldError("phoneNumber", "Please validate customer details first");
                    return;
                }

                // For categories that require validation, check if it's still in progress
                if (validationLoading) {
                    formik.setFieldError("phoneNumber", "Please wait for customer validation to complete");
                    return;
                }
            }

            // Prepare the values in the format expected by the onSubmit handler
            const submissionValues: NormalBillDetailsValues = {
                // Use serviceProvider for networkProvider to match what was selected in the dropdown
                networkProvider: values.serviceProvider,
                phoneNumber: values.phoneNumber,
                amount: Number(values.amount),
                customerDetails: customerDetails.name || "",
            };

            // Call the onSubmit handler provided by the parent component
            if (onSubmit) {
                onSubmit(submissionValues);
            }
        },
    });

    // Effect to sync networkProvider and serviceProvider fields
    // This ensures both fields stay in sync when service provider changes
    const prevServiceProvider = React.useRef(formik.values.serviceProvider);

    const setFieldValue = React.useCallback(
        (field: string, value: unknown) => {
            formik.setFieldValue(field, value);
        },
        [formik]
    );

    React.useEffect(() => {
        if (
            formik.values.serviceProvider !== formik.values.networkProvider &&
            formik.values.serviceProvider !== prevServiceProvider.current
        ) {
            setFieldValue("networkProvider", formik.values.serviceProvider);
        }
        prevServiceProvider.current = formik.values.serviceProvider;
    }, [formik.values.serviceProvider, formik.values.networkProvider, setFieldValue]);

    // Find the selected service provider to use in dynamic labels
    const serviceProvider =
        billers && Array.isArray(billers)
            ? billers.find(
                  (biller) =>
                      biller.billername?.toLowerCase() === formik.values.serviceProvider.toLowerCase() ||
                      biller.billerName?.toLowerCase() === formik.values.serviceProvider.toLowerCase()
              )
            : null;

    // Update payment code when payment items are loaded - moved to useEffect with proper guards
    const prevPaymentItemsLength = React.useRef(paymentItems?.length || 0);

    React.useEffect(() => {
        if (paymentItems && paymentItems.length > 0 && paymentItems[0].paymentCode && !formik.values.paymentCode) {
            setFieldValue("paymentCode", paymentItems[0].paymentCode);
        }
        prevPaymentItemsLength.current = paymentItems?.length || 0;
    }, [paymentItems, formik.values.paymentCode, setFieldValue]);

    const providerLabel = serviceProvider?.billername ?? serviceProvider?.billerName ?? "";

    // Handle phone number change with validation
    const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        formik.setFieldValue("phoneNumber", value);

        // Reset validation when phone number changes
        resetCustomerDetails();

        // Clear validation error if the phone number becomes valid
        if (formik.errors.phoneNumber && validatePhoneNumber(value)) {
            formik.setFieldError("phoneNumber", "");
        }
    };

    // Helper function to validate form fields before submission
    const validateFormFields = () => {
        if (!formik.values.paymentCode) {
            formik.setFieldError(
                "phoneNumber",
                "Payment code is required for validation. Please try again or contact support."
            );
            return false;
        }

        if (!formik.values.phoneNumber) {
            formik.setFieldError("phoneNumber", "Phone number is required");
            return false;
        }

        return true;
    };

    // Helper function to handle customer validation logic
    const handleCustomerValidation = () => {
        if (!isValidationSuccessful && !validationLoading) {
            // Only trigger validation if we haven't attempted yet or the customer ID was changed
            if (!validationAttempted || formik.values.phoneNumber !== initialValues.phoneNumber) {
                const paymentCode = formik.values.paymentCode ?? "";
                debouncedValidate(formik.values.phoneNumber, paymentCode);
                return false;
            }
        }

        if (validationLoading) {
            formik.setFieldError("phoneNumber", "Please wait for customer validation to complete");
            return false;
        }

        if (!isValidationSuccessful && validationAttempted) {
            formik.setFieldError("phoneNumber", "Please validate customer details first");
            return false;
        }

        return true;
    };

    // Function to manually validate and submit the form
    const handleNextClick = () => {
        // For categories that need validation, validate first if not already successful or in progress
        if (!skipValidation) {
            if (!validateFormFields() || !handleCustomerValidation()) {
                return;
            }
        }

        // Trigger form submission
        formik.handleSubmit();
    };

    // Wrapper function to ensure click events are captured
    const handleButtonClick = () => {
        handleNextClick();
    };

    // Get field label based on category
    const fieldLabel = getCustomerIdLabel(categoryName ?? "", "");
    const useCustomLabel = fieldLabel !== "Phone number";

    // Extract provider number label logic
    const providerNumberLabel = providerLabel ? `${providerLabel} number` : "Number";

    // Create the field label with proper formatting logic
    const formattedFieldLabel = useCustomLabel ? fieldLabel : providerNumberLabel;

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={title}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <div className="flex-1 flex min-h-0 relative flex-col lg:flex-row">
                <nav
                    className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment progress"
                >
                    <Stepper steps={singlePaymentSteps} currentStep={1} />
                </nav>

                <main className="flex-1 flex justify-center overflow-y-auto px-primary lg:px-0">
                    <div className="w-full max-w-screen-sm lg:max-w-[470px] flex flex-col items-center pt-8">
                        <h2
                            id="bill-payment-title"
                            className="text-[#151519] text-2xl font-semibold leading-[30px] tracking-tight mb-12"
                        >
                            Bill details
                        </h2>

                        <form onSubmit={formik.handleSubmit} className="w-full space-y-6" noValidate>
                            <ServiceProviderSelect
                                formik={formik}
                                categoryId={categoryId}
                                onServiceProviderChange={onServiceProviderChange}
                            />

                            <FormField
                                label={formattedFieldLabel}
                                error={formik.errors.phoneNumber}
                                touched={formik.touched.phoneNumber}
                                name="phoneNumber"
                            >
                                <div className="flex gap-2">
                                    {categoryId === 4 && (
                                        <CountryCode
                                            cardStyle={{
                                                backgroundColor: "#F7F7F8",
                                                borderColor: "#E3E5E8",
                                                textColor: "#9D9DAC",
                                                arrowColor: "#9D9DAC",
                                            }}
                                            className="h-11"
                                        />
                                    )}
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        {...formik.getFieldProps("phoneNumber")}
                                        onChange={handlePhoneNumberChange}
                                        className={`flex-1 h-11 px-3 bg-white rounded-lg border text-[16px] font-[500] leading-[20px] tracking-[0.32px] ${
                                            formik.touched.phoneNumber && formik.errors.phoneNumber
                                                ? "border-red-500"
                                                : "border-[#dbdbe0]"
                                        } focus:outline-none focus:ring-1 focus:ring-[#5C068C]`}
                                        placeholder={providerLabel ? `Enter ${providerLabel} number` : "Enter number"}
                                        aria-invalid={!!(formik.touched.phoneNumber && formik.errors.phoneNumber)}
                                    />
                                </div>
                                {/* Customer validation feedback */}
                                {validationLoading && (
                                    <div className="text-sm text-[#3A3A41] mt-1 flex items-center">
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" /> Verifying details...
                                    </div>
                                )}
                                {!validationLoading && customerDetails.name && (
                                    <div className="text-sm text-[#3A3A41] mt-1">{customerDetails.name}</div>
                                )}
                            </FormField>

                            <AmountInput formik={formik} label="Amount" name="amount" />

                            <div className="pt-4">
                                <Button
                                    type="button"
                                    onClick={handleButtonClick}
                                    variant="primary"
                                    size="medium"
                                    className="float-right"
                                    aria-label="Submit bill payment details"
                                    data-testid="submit-bill-payment-details"
                                    disabled={validationLoading || formik.isSubmitting}
                                >
                                    {validationLoading ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Verifying details...
                                        </>
                                    ) : (
                                        "Next"
                                    )}
                                </Button>
                            </div>
                        </form>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
}
