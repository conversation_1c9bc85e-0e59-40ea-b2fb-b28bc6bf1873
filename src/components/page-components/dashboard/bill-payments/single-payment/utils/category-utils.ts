/**
 * @file Category Utilities
 *
 * @purpose This file provides utility functions for handling bill payment categories.
 *
 * @functionality The category-utils module implements functions for working with bill payment
 * categories, including determining category types, checking validation requirements, and
 * handling category-specific behavior. These utilities ensure consistent handling of
 * categories across the bill payment flow.
 *
 * @dependencies None
 *
 * @usage Import these utilities when working with bill categories to determine
 * category-specific behavior or requirements.
 */

/**
 * Determines if customer validation should be skipped for a given bill category.
 *
 * Following the business requirements, customer validation is:
 * - Required for most bill payment categories (utility, cable TV, insurance, etc.)
 * - Not required for mobile/recharge categories where customers are validated differently
 *
 * @param categoryName - The name of the bill payment category
 * @returns true if validation should be skipped, false if validation is required
 */
export function shouldSkipValidation(categoryName: string): boolean {
    // Convert to lowercase for case-insensitive comparison
    const lowerCategoryName = categoryName.toLowerCase();

    // Skip validation only for categories that contain both "mobile" and "recharge"
    const containsMobile = lowerCategoryName.includes("mobile");
    const containsRecharge = lowerCategoryName.includes("recharge");

    const shouldSkip = containsMobile && containsRecharge;

    return shouldSkip;
}

/**
 * Gets the appropriate label for customer ID fields based on category and bill type.
 *
 * @param categoryName The name of the bill payment category
 * @param billType The type of bill (e.g., 'electricity', 'cable-tv')
 * @returns An appropriate label for the customer ID field
 */
export function getCustomerIdLabel(categoryName: string, billType: string): string {
    if (billType === "cable-tv") {
        return "Smart Card Number";
    }

    // For mobile/recharge categories, use "Phone number"
    if (shouldSkipValidation(categoryName)) {
        return "Phone number";
    }

    // For all other categories, use "Customer ID"
    return "Customer ID";
}
