/**
 * @file Payment Confirmation Hook
 *
 * @purpose This file provides a custom hook that manages the bill payment confirmation and submission process.
 *
 * @functionality The usePaymentConfirmation hook orchestrates the entire payment confirmation flow, including:
 * transaction PIN verification, payment parameter creation and validation, API call execution, and state management
 * for the payment process. It implements a multi-step approach: first triggering PIN verification, then
 * handling payment submission after successful PIN verification, and finally managing the success/error states
 * and feedback. The hook also ensures that payment items are properly fetched and formatted based on bill type,
 * and prevents duplicate API calls through state tracking.
 *
 * @dependencies
 * - Redux for state management (useAppDispatch, useAppSelector)
 * - Redux actions for payment initiation (initiateBillPayment, fetchPaymentItems)
 * - Security slice for PIN verification
 * - Feedback functions for success/error notifications
 * - Payment utility functions (createPaymentParams, validatePaymentParams)
 *
 * @usage Import and use this hook in bill payment review components to handle the payment submission process.
 * The hook accepts payment details as props and returns methods and state for managing the confirmation process,
 * including loading states, current step, and the confirmation handler.
 */

import { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { fetchPaymentItems, initiateBillPayment } from "@/redux/actions/billPaymentThunks";
import { createPaymentParams, validatePaymentParams, type PaymentItem as LocalPaymentItem } from "./payment-utils";
import { PaymentItem as ReduxPaymentItem } from "@/redux/types/billPayments";
import { openVerifyPinModal } from "@/redux/slices/securitySlice";

interface UsePaymentConfirmationProps {
    billType: string;
    amount: number;
    accountNumber: string;
    phoneNumber?: string;
    identificationNumber?: string;
    smartCardNumber?: string;
    serviceID?: string;
    paymentInfo: {
        narration?: string;
    };
    networkProvider?: string;
    serviceProvider?: string;
    packageType?: string;
    customerDetails?: string;
    categoryName?: string;
    onConfirm: () => void;
    refetchRecentBills?: () => Promise<void>;
    mfaToken?: string | null; // MFA token from 2FA verification
}

export const usePaymentConfirmation = ({
    billType,
    amount,
    accountNumber,
    phoneNumber,
    identificationNumber,
    smartCardNumber,
    serviceID,
    paymentInfo,
    networkProvider,
    serviceProvider,
    packageType: _packageType,
    customerDetails: _customerDetails,
    categoryName,
    onConfirm,
    refetchRecentBills,
    mfaToken,
}: UsePaymentConfirmationProps) => {
    const dispatch = useAppDispatch();
    const [isProcessing, setIsProcessing] = useState(false);
    const [currentStep, setCurrentStep] = useState(3);
    const [billerId, setBillerId] = useState<string | null>(null);
    // Add state to track PIN verification status
    const [isPinVerificationInitiated, setIsPinVerificationInitiated] = useState(false);
    const [hasProcessedPayment, setHasProcessedPayment] = useState(false);

    // Get billers and payment items from Redux state
    const { data: billers } = useAppSelector(
        (state) =>
            state.billPayments.biller as {
                data: Array<{
                    billerid?: string | number;
                    billerId?: string | number;
                    billername?: string;
                    billerName?: string;
                    shortName?: string;
                }> | null;
                loading: boolean;
                error: string | null;
            }
    );
    const paymentItemsState = useAppSelector((state) => state.billPayments.paymentItems);

    // Get the verified PIN from securitySlice
    const {
        pin: transactionPin,
        success: pinVerificationSuccess,
        open: isPinModalOpen,
    } = useAppSelector((state) => state.security.verifyPin);

    // When networkProvider changes, find the corresponding biller ID
    useEffect(() => {
        if (
            billType !== "electricity" &&
            billType !== "cable-tv" &&
            networkProvider &&
            billers &&
            Array.isArray(billers)
        ) {
            // Find the biller whose shortName matches the selected network provider
            const biller = billers.find(
                (b) =>
                    (b.shortName && b.shortName === networkProvider) ||
                    (b.billername && b.billername === networkProvider) ||
                    (b.billerName && b.billerName === networkProvider)
            );

            if (biller) {
                const billerIdValue = biller.billerid?.toString() ?? biller.billerId?.toString() ?? null;
                setBillerId(billerIdValue);

                // Fetch payment items for this biller
                if (billerIdValue) {
                    dispatch(
                        fetchPaymentItems({
                            billerId: billerIdValue,
                        })
                    );
                }
            }
        }
    }, [networkProvider, billers, billType, dispatch]);

    // Effect to process payment after successful PIN verification
    useEffect(() => {
        const processPaymentAfterPinVerification = async () => {
            // Only proceed if PIN verification was initiated, successful, and we haven't processed this payment yet
            if (!isPinVerificationInitiated || !pinVerificationSuccess || !transactionPin || hasProcessedPayment) {
                return;
            }

            try {
                // Mark payment as processed to prevent duplicate API calls
                setHasProcessedPayment(true);
                setIsProcessing(true);
                setCurrentStep(4);

                // Get payment items data for non-electricity/cable-tv payments
                let paymentItems = null;
                if (billType !== "electricity" && billType !== "cable-tv" && paymentItemsState?.data) {
                    // Convert Redux PaymentItem to Local PaymentItem, parsing amount from string to number
                    paymentItems = paymentItemsState.data.map(
                        (item: ReduxPaymentItem): LocalPaymentItem => ({
                            ...item,
                            amount: item.amount ? parseFloat(item.amount) : undefined,
                        })
                    );
                }

                // Create and validate payment params
                const paymentParams = createPaymentParams({
                    billType,
                    amount,
                    accountNumber,
                    phoneNumber,
                    identificationNumber,
                    smartCardNumber,
                    serviceID,
                    billerID: billerId ?? undefined,
                    paymentItems: paymentItems,
                    networkProvider,
                    serviceProvider, // Pass service provider for biller field
                    categoryName, // Pass categoryName for billType field
                    narration: paymentInfo.narration, // Pass narration from payment info
                    transactionPin, // Pass the verified transaction PIN
                    mfaToken: mfaToken ?? undefined, // Pass the MFA token from 2FA verification
                });

                const validationErrors = validatePaymentParams(paymentParams, billType);
                if (validationErrors.length > 0) {
                    sendCatchFeedback(new Error(validationErrors.join(", ")));
                    setCurrentStep(3);
                    return;
                }

                // Call the initiate bill payment thunk
                const result = await dispatch(initiateBillPayment(paymentParams)).unwrap();

                if (!result) {
                    throw new Error("Payment failed. Please try again.");
                }

                // Instead, call refetchRecentBills from context if available
                if (refetchRecentBills) {
                    await refetchRecentBills();
                }

                dispatch(resetSingleBillPayment());
                sendFeedback("Payment initiated successfully", "success");
                onConfirm();
            } catch (error) {
                handlePaymentError(error);
            } finally {
                setIsProcessing(false);
                // Reset PIN verification state for next payment
                setIsPinVerificationInitiated(false);
            }
        };

        processPaymentAfterPinVerification();
    }, [
        isPinVerificationInitiated,
        pinVerificationSuccess,
        transactionPin,
        hasProcessedPayment,
        billType,
        amount,
        accountNumber,
        phoneNumber,
        identificationNumber,
        smartCardNumber,
        serviceID,
        billerId,
        paymentItemsState,
        networkProvider,
        serviceProvider,
        categoryName,
        paymentInfo,
        dispatch,
        refetchRecentBills,
        onConfirm,
        mfaToken,
    ]);

    // Reset state when PIN modal is closed without verification
    useEffect(() => {
        if (isPinVerificationInitiated && !isPinModalOpen && !pinVerificationSuccess) {
            setIsPinVerificationInitiated(false);
            setIsProcessing(false);
        }
    }, [isPinVerificationInitiated, isPinModalOpen, pinVerificationSuccess]);

    const handlePaymentError = (error: unknown) => {
        if (error instanceof Error) {
            sendCatchFeedback(error);
        } else {
            sendCatchFeedback(new Error("Payment failed. Please try again"));
        }
        setCurrentStep(3);
    };

    const handleConfirm = () => {
        // First step: Initiate PIN verification
        if (!isPinVerificationInitiated) {
            setIsPinVerificationInitiated(true);
            setHasProcessedPayment(false);
            // Show transaction PIN modal
            dispatch(openVerifyPinModal());
        }

        // This is for cases where the handler is called after PIN verification
        // The actual payment processing is handled by the useEffect
    };

    return {
        isProcessing,
        currentStep,
        setCurrentStep,
        handleConfirm,
    };
};
