/**
 * File Restoration Hook
 *
 * Purpose: Manages the restoration, storage, and clearing of uploaded CSV files in the bulk airtime payment flow.
 *
 * Functionality: This custom React hook provides a complete solution for file state persistence across
 * page navigations and refreshes. It synchronizes file data between local state, localStorage, and Redux,
 * handling conversion between File objects and base64 strings for storage. The hook includes logic for
 * restoring files from storage, validating file metadata, storing new files, and clearing stored files.
 *
 * Dependencies:
 * - React hooks (useState, useEffect) for state management
 * - Redux (useAppDispatch, useAppSelector) for global state
 * - bulkAirtime Redux slice for file metadata storage
 * - File storage utilities from "../utils/file-storage"
 *
 * Usage: Used in bulk payment components to maintain file state persistence. Provides functions for
 * restoring, storing, and clearing files, along with state variables indicating upload status. This
 * ensures users don't lose their uploaded file data when navigating between pages in the payment flow.
 */

import { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setFileInfo } from "@/redux/features/bulkAirtime";
import { FILE_STORAGE_KEY, base64ToFile, fileToBase64, isFileMatchingInfo, type FileInfo } from "../utils/file-storage";

interface UseFileRestorationResult {
    uploadedFile: File | null;
    isUploadComplete: boolean;
    restoreFile: () => Promise<void>;
    clearFile: () => void;
    storeFile: (file: File) => Promise<void>;
}

/**
 * Hook for managing file restoration from localStorage and Redux state
 * Handles synchronization between local state, localStorage, and Redux
 */
export const useFileRestoration = (): UseFileRestorationResult => {
    const dispatch = useAppDispatch();
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [isUploadComplete, setIsUploadComplete] = useState(false);
    const [isRestorationAttempted, setIsRestorationAttempted] = useState(false);

    // Get file metadata from Redux store
    const fileInfo = useAppSelector((state) => state.bulkAirtime.fileInfo);

    /**
     * Attempts to restore a file from base64 data and metadata
     */
    const restoreFileFromData = async (storedFileData: string, fileInfo: FileInfo): Promise<boolean> => {
        try {
            const file = await base64ToFile(storedFileData, fileInfo.name, fileInfo.type);
            if (isFileMatchingInfo(file, fileInfo)) {
                setIsUploadComplete(true);
                setUploadedFile(file);
                return true;
            } else {
                return false;
            }
        } catch {
            // Clear any corrupted file info when restoration fails
            dispatch(setFileInfo(null));
            return false;
        }
    };

    /**
     * Stores a file in localStorage and updates Redux state
     */
    const storeFile = async (file: File): Promise<void> => {
        dispatch(
            setFileInfo({
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified,
            })
        );

        if (typeof window !== "undefined") {
            const base64Data = await fileToBase64(file);
            localStorage.setItem(FILE_STORAGE_KEY, base64Data);

            setUploadedFile(file);
            setIsUploadComplete(true);
        }
    };

    /**
     * Clears file data from all storage locations
     */
    const clearFile = () => {
        dispatch(setFileInfo(null));

        if (typeof window !== "undefined") {
            localStorage.removeItem(FILE_STORAGE_KEY);
        }

        setIsUploadComplete(false);
        setUploadedFile(null);
    };

    /**
     * Attempts to restore file from storage
     */
    const restoreFile = async (): Promise<void> => {
        try {
            if (isRestorationAttempted) {
                return; // Prevent multiple restoration attempts
            }

            setIsRestorationAttempted(true);

            if (!uploadedFile && fileInfo && typeof window !== "undefined") {
                const storedFileData = localStorage.getItem(FILE_STORAGE_KEY);

                if (storedFileData) {
                    const restored = await restoreFileFromData(storedFileData, fileInfo);
                    if (!restored) {
                        // File restoration failed - clear fileInfo
                        dispatch(setFileInfo(null));
                    }
                } else {
                    dispatch(setFileInfo(null));
                }
            }
        } catch {
            dispatch(setFileInfo(null));
            if (typeof window !== "undefined") {
                localStorage.removeItem(FILE_STORAGE_KEY);
            }
        }
    };

    // When fileInfo is loaded (by Redux Persist or through user action), attempt to restore the file
    useEffect(() => {
        // If we have fileInfo from the store and haven't attempted restoration yet
        if (fileInfo && !isRestorationAttempted) {
            restoreFile();
        }
    }, [fileInfo, isRestorationAttempted]);

    return {
        uploadedFile,
        isUploadComplete,
        restoreFile,
        clearFile,
        storeFile,
    };
};
