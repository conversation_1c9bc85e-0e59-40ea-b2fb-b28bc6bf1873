/**
 * Payment Processing Hook
 *
 * Purpose: Manages the complete payment processing workflow for bulk airtime payments,
 * focusing exclusively on valid entries to improve reliability and user experience.
 *
 * Functionality: This custom React hook orchestrates the payment submission process
 * for bulk airtime payments. It handles transaction PIN verification, fetches payment
 * codes only for network providers used by valid entries, constructs payment objects,
 * dispatches the payment API call through Redux thunks, and manages UI states. The hook
 * implements an optimized workflow that respects the validation done in previous steps.
 *
 * Dependencies:
 * - React hooks (useState, useEffect, useRef, useCallback) for state management
 * - Redux (useAppDispatch, useAppSelector) for global state and API dispatch
 * - API services (billAxios) for fetching payment codes
 * - Feedback functions for user notifications
 * - Security slice for PIN verification
 * - Bill payment thunks for payment submission
 *
 * Usage: Used in the bulk payment review page to handle the final submission of payments.
 * Takes payment data (entries, account, etc.) and provides processing states and a
 * confirmation function that initiates the payment workflow. Only processes entries with
 * "Valid" status as determined in the verification step.
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { billAxios } from "@/api/axios";
import { BulkAirtimeState } from "@/redux/features/bulkAirtime";
import { sendCatchFeedback } from "@/functions/feedback";
import { openVerifyPinModal } from "@/redux/slices/securitySlice";
import { initiateBulkAirtimePayment } from "@/redux/actions/billPaymentThunks";

interface Biller {
    billername: string;
    billerid: number;
    shortName: string;
    categoryid: number;
    categoryname: string;
}

interface NetworkPaymentMap {
    [network: string]: string;
}

/**
 * Result type for loadNetworkPaymentMapping function, containing both mappings and missing networks
 * Following tech stack requirements for proper TypeScript typing
 */
interface NetworkPaymentMappingResult {
    mapping: NetworkPaymentMap;
    missingNetworks: string[];
}

/**
 * Props for the usePaymentProcessing hook
 * Note: corporateId parameter has been removed as it's no longer required by the API
 */
interface UsePaymentProcessingProps {
    entries: BulkAirtimeState["entries"];
    paymentInfo: BulkAirtimeState["paymentInfo"];
    selectedAccount: string;
    categoryName?: string;
    onSuccess?: () => void;
    refetchRecentBills?: () => Promise<void>;
    mfaToken?: string | null; // MFA token from 2FA verification
}

export function usePaymentProcessing({
    entries,
    paymentInfo,
    selectedAccount,
    categoryName = "Airtime",
    onSuccess,
    refetchRecentBills,
    mfaToken,
}: UsePaymentProcessingProps) {
    const dispatch = useAppDispatch();

    // Clear Redux persist storage on component mount to fix serialization issues
    useEffect(() => {
        if (typeof window !== "undefined" && window.localStorage) {
            const persistedState = localStorage.getItem("persist:root");
            if (persistedState) {
                try {
                    const parsedState = JSON.parse(persistedState);
                    if (parsedState.bulkAirtime) {
                        const bulkAirtimeState = JSON.parse(parsedState.bulkAirtime);
                        if (bulkAirtimeState.uploadedFile) {
                            const cleanState = { ...bulkAirtimeState };
                            delete cleanState.uploadedFile;
                            parsedState.bulkAirtime = JSON.stringify(cleanState);
                            localStorage.setItem("persist:root", JSON.stringify(parsedState));
                            window.location.reload();
                        }
                    }
                } catch {
                    // Silently ignore JSON parsing errors for corrupted localStorage data
                }
            }
        }
    }, []);

    const [isProcessing, setIsProcessing] = useState(false);
    const [isLoadingMap, setIsLoadingMap] = useState(false);
    const [networkPaymentMap, setNetworkPaymentMap] = useState<NetworkPaymentMap>({});
    const [currentStepperStep, setCurrentStepperStep] = useState(4);
    const [processingPhase, setProcessingPhase] = useState<"idle" | "preparing" | "submitting" | "complete" | "error">(
        "idle"
    );
    // Add state to track PIN verification status
    const [isPinVerificationInitiated, setIsPinVerificationInitiated] = useState(false);
    const [hasProcessedPayment, setHasProcessedPayment] = useState(false);

    const abortControllerRef = useRef<AbortController | null>(null);
    const isLoadingPaymentCodesRef = useRef(false);

    // Get PIN verification state from the Redux store
    const {
        pin: transactionPin,
        success: pinVerificationSuccess,
        open: isPinModalOpen,
    } = useAppSelector((state) => state.security.verifyPin);

    // Get unique networks from VALID entries only
    // This ensures we only attempt to fetch payment codes for networks
    // that are actually needed for processing valid entries
    const uniqueNetworks = Array.from(
        new Set(entries.filter((entry) => entry.status === "Valid").map((entry) => entry.network))
    );

    /**
     * Loads network to payment code mapping for the given networks.
     * Only processes networks that are used by valid entries.
     *
     * @param networks - Array of network names to fetch payment codes for (from valid entries only)
     * @returns An object containing the payment code map and array of any missing networks
     */
    const loadNetworkPaymentMapping = useCallback(
        async (networks: string[]): Promise<NetworkPaymentMappingResult> => {
            if (isLoadingPaymentCodesRef.current) {
                throw new Error("Payment codes are already being loaded");
            }
            isLoadingPaymentCodesRef.current = true;
            setIsLoadingMap(true);
            try {
                const abortController = new AbortController();
                const signal = abortController.signal;
                if (networks.length === 0) {
                    return { mapping: {}, missingNetworks: [] };
                }

                // Filter out networks that already have payment codes to avoid redundant API calls
                const networksToFetch = networks.filter((network) => !networkPaymentMap[network]);
                if (networksToFetch.length === 0) {
                    // Return existing map if no new networks to fetch
                    // Verify all networks are covered by the existing mapping
                    const missingNetworks = networks.filter((network) => !networkPaymentMap[network]);
                    return {
                        mapping: networkPaymentMap,
                        missingNetworks,
                    };
                }

                // Fetch billers for airtime category (categoryId: 4)
                const billersResponse = await billAxios.get<{ content: Biller[] }>("/api/v1/vas/billers", {
                    params: {
                        page: 1,
                        size: 50,
                        categoryId: 4,
                    },
                    signal,
                });

                if (!billersResponse.data.content || !Array.isArray(billersResponse.data.content)) {
                    throw new Error("Invalid billers response format");
                }

                // Filter billers to match the networks we need to fetch
                const relevantBillers = billersResponse.data.content.filter((biller: Biller) =>
                    networksToFetch.includes(biller.billername)
                );

                if (relevantBillers.length === 0) {
                    // If no matching billers found, identify the missing networks
                    return {
                        mapping: { ...networkPaymentMap },
                        missingNetworks: networksToFetch,
                    };
                }

                const mapping: NetworkPaymentMap = { ...networkPaymentMap };

                // Fetch payment items only for the relevant billers (new networks)
                // Note: Individual API failures are handled by allowing missing networks
                // to be detected and reported after all requests complete
                for (const biller of relevantBillers) {
                    const paymentItemResponse = await billAxios.get("/api/v1/vas/bill-payment-item", {
                        params: {
                            page: 1,
                            size: 1,
                            billerId: biller.billerid,
                        },
                        signal,
                    });
                    if (paymentItemResponse.data?.content?.length > 0) {
                        const paymentItem = paymentItemResponse.data.content[0];
                        mapping[biller.billername] = paymentItem.paymentCode;
                    }
                }

                // After processing all networks, check for any that couldn't be mapped
                const missingNetworks = networks.filter((network) => !mapping[network]);

                return { mapping, missingNetworks };
            } finally {
                isLoadingPaymentCodesRef.current = false;
                setIsLoadingMap(false);
            }
        },
        [networkPaymentMap]
    );

    // Effect to process payment after successful PIN verification
    useEffect(() => {
        const processPaymentAfterPinVerification = async () => {
            // Only proceed if PIN verification was initiated, successful, and we haven't processed this payment yet
            if (!isPinVerificationInitiated || !pinVerificationSuccess || !transactionPin || hasProcessedPayment) {
                return;
            }

            try {
                // Mark payment as processed to prevent duplicate API calls
                setHasProcessedPayment(true);
                setIsProcessing(true);
                // Reset states at the beginning of processPaymentAfterPinVerification
                // Following proper state management practices for async operations
                setProcessingPhase("preparing");

                // Abort any in-flight requests
                if (abortControllerRef.current) {
                    abortControllerRef.current.abort();
                    abortControllerRef.current = null;
                }

                // Filter to only include valid entries
                // This is the key change: we only process entries marked as "Valid"
                // from the verification step, respecting the validation already done
                const validEntries = entries.filter((entry) => entry.status === "Valid");

                if (validEntries.length === 0) {
                    sendCatchFeedback(
                        "No valid airtime payments to process. Please ensure at least one entry is valid before proceeding."
                    );
                    setProcessingPhase("error");
                    setIsProcessing(false);
                    setHasProcessedPayment(false);
                    setIsPinVerificationInitiated(false);
                    return;
                }

                // Get unique networks only from valid entries - important to avoid
                // attempting to fetch payment codes for invalid networks
                const validNetworks = Array.from(new Set(validEntries.map((entry) => entry.network)));

                // Enhanced payment code loading with missing network tracking
                // This follows the API Integration Guidelines by handling errors appropriately
                const { mapping: updatedPaymentMap, missingNetworks } = await loadNetworkPaymentMapping(validNetworks);
                setNetworkPaymentMap(updatedPaymentMap);

                // Strict validation: Abort if any payment codes are missing for VALID entries
                if (missingNetworks.length > 0) {
                    // Use handlePaymentCodeError helper for consistent error handling
                    handlePaymentCodeError(missingNetworks);

                    // Reset states to allow retry
                    // Following Frontend Guidelines for proper state management during errors
                    setIsProcessing(false);
                    setHasProcessedPayment(false);
                    setIsPinVerificationInitiated(false);
                    setProcessingPhase("error");

                    // Don't proceed further with payment processing
                    return;
                }

                // All payment codes are available for valid entries, proceed with payment
                setProcessingPhase("submitting");

                // Validate MFA token is present before proceeding
                if (!mfaToken || mfaToken.trim() === "") {
                    const errorMessage = "MFA token is required for bulk payment processing";
                    sendCatchFeedback(errorMessage);
                    setProcessingPhase("error");
                    setIsProcessing(false);
                    setHasProcessedPayment(false);
                    setIsPinVerificationInitiated(false);
                    return;
                }

                // Construct payment requests for each valid entry that have payment codes
                // Following Core Principle #2: Use empty strings instead of null values
                const paymentRequests = validEntries.map((entry) => ({
                    paymentCode: updatedPaymentMap[entry.network] || "",
                    customerId: entry.phoneNumber,
                    customerMobile: "",
                    accountNumber: selectedAccount,
                    customerEmail: "",
                    amount: entry.amount.toString(),
                    requestReference: "", // Empty string as per API docs
                    requiresApproval: false, // False as per API docs
                    biller: entry.network,
                    billType: categoryName,
                    narration: paymentInfo.narration || "",
                    transactionPin,
                    mfaToken: mfaToken, // Always pass the required MFA token from 2FA verification
                }));

                // Dispatch the existing thunk from billPaymentThunks.ts
                // The thunk handles proper success/error feedback popup management
                const result = await dispatch(initiateBulkAirtimePayment(paymentRequests));

                // Handle the response - the success feedback is already handled by the thunk
                if (result.meta.requestStatus === "fulfilled") {
                    // Success case already displayed by the thunk
                    setCurrentStepperStep(5);
                    setProcessingPhase("complete");

                    // Refresh recent bills if the function exists
                    if (refetchRecentBills) {
                        await refetchRecentBills();
                    }

                    // Add a small delay to allow the stepper animation to be visible
                    setTimeout(() => {
                        setProcessingPhase("idle");
                        if (onSuccess) {
                            onSuccess();
                        }
                    }, 500);
                } else {
                    // Handle error case - the error feedback is already handled by the thunk
                    setProcessingPhase("error");
                }
            } catch (error) {
                setProcessingPhase("error");
                sendCatchFeedback(error);
            } finally {
                // Reset loading state after all API calls are complete
                setIsProcessing(false);
                // Reset PIN verification state for next payment
                setIsPinVerificationInitiated(false);
            }
        };

        processPaymentAfterPinVerification();
    }, [
        isPinVerificationInitiated,
        pinVerificationSuccess,
        transactionPin,
        hasProcessedPayment,
        entries,
        paymentInfo,
        selectedAccount,
        categoryName,
        onSuccess,
        refetchRecentBills,
        networkPaymentMap,
        uniqueNetworks,
        dispatch,
        loadNetworkPaymentMapping,
        mfaToken,
    ]);

    /**
     * Handles the error scenario when payment codes are missing for networks
     * used by valid entries that have already been confirmed by the user
     *
     * @param missingNetworks - Array of network names that are missing payment codes
     * @returns The error message that was displayed to the user
     */
    const handlePaymentCodeError = (missingNetworks: string[]): string => {
        // Create a user-friendly message that clearly identifies the problem networks
        const isSingleNetwork = missingNetworks.length === 1;
        const networkText = isSingleNetwork
            ? `the ${missingNetworks[0]} network`
            : `these networks: ${missingNetworks.join(", ")}`;

        // Following Frontend Guidelines for actionable error messages
        // that provide clear next steps for the user
        const errorMessage = `Payment canceled. Unable to retrieve payment information for ${networkText}.`;

        // Following API Integration Guidelines to use sendCatchFeedback for failed operations
        sendCatchFeedback(errorMessage);
        return errorMessage;
    };

    // Reset state when PIN modal is closed without verification
    useEffect(() => {
        if (isPinVerificationInitiated && !isPinModalOpen && !pinVerificationSuccess) {
            setIsPinVerificationInitiated(false);
            setIsProcessing(false);
            setProcessingPhase("idle");
        }
    }, [isPinVerificationInitiated, isPinModalOpen, pinVerificationSuccess, loadNetworkPaymentMapping]);

    /**
     * Handles the confirmation of the bulk airtime payment.
     * Initiates PIN verification before payment processing.
     */
    const handleConfirmPayment = () => {
        // First step: Initiate PIN verification
        if (!isPinVerificationInitiated) {
            setIsPinVerificationInitiated(true);
            setHasProcessedPayment(false);
            // Show transaction PIN modal
            dispatch(openVerifyPinModal());
        }

        // The actual payment processing is handled by the useEffect
    };

    return {
        isProcessing,
        isLoadingMap,
        currentStepperStep,
        processingPhase,
        handleConfirmPayment,
    };
}
