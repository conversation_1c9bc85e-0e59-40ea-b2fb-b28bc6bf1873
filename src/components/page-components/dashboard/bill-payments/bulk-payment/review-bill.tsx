/**
 * Review Bill Component
 *
 * Purpose: Provides the final review and payment confirmation step in the bulk airtime payment flow.
 *
 * Functionality: This component displays a comprehensive summary of the payment including total amount,
 * recipient count, and payment details before submission. It integrates with the payment processing hook
 * to handle transaction PIN verification and payment submission to the API. The component manages multiple
 * UI states during the payment process (idle, processing, error, etc.) and provides appropriate feedback
 * to users. It also includes approval flow integration for transactions that require multi-party approval.
 *
 * Dependencies:
 * - React hooks for state management
 * - Next.js router for navigation
 * - Redux for global state and API dispatch
 * - Common UI components (Button, Stepper, SummaryCard, etc.)
 * - usePaymentProcessing hook for payment submission and PIN verification
 * - MultiPartyApproval component for approval workflow
 * - PATH_PROTECTED for route management
 *
 * Usage: Used as the final step in the bulk payment flow. Receives payment summary data (total amount,
 * recipient count) and provides navigation callbacks. After successful payment submission, it either
 * redirects to a specified route or to the bill payments home page.
 */

"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import Stepper from "@/components/common/stepper";
import { bulkAirtimeSteps } from "../utils/data";
import { SummaryCard } from "@/components/common/summary-card";
import { MultiPartyApproval, BillSummary } from "../common";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { resetBulkAirtime } from "@/redux/features/bulkAirtime";
import { formatAccountName, formatAccountNumber } from "@/redux/features/accounts";
import { usePaymentProcessing } from "./hooks/use-payment-processing";
import { sendCatchFeedback } from "@/functions/feedback";
import LoadingIndicator from "@/components/common/loading-indicator";
import { PATH_PROTECTED } from "@/routes/path";
import SettingsMfaVerification from "@/components/page-components/dashboard/settings/components/settings-mfa-verification";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";

interface ReviewBillProps {
    onBack: () => void;
    onConfirm: () => void;
    totalAmount: number;
    recipientsCount: number;
    categoryName?: string;
}

export default function ReviewBill({
    onBack,
    onConfirm,
    totalAmount,
    recipientsCount,
    categoryName = "",
}: Readonly<ReviewBillProps>) {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const paymentInfo = useAppSelector((state) => state.bulkAirtime.paymentInfo);
    const { selectedAccount, accounts } = useAppSelector((state) => state.accounts);
    const entries = useAppSelector((state) => state.bulkAirtime.entries);
    const [showApprovals] = useState(true);

    // Get PIN verification status from security slice
    const { open: isPinModalOpen } = useAppSelector((state) => state.security.verifyPin);

    // 2FA/MFA verification for bulk bill payments
    const [initiateBulkPaymentMfaFlow, setInitiateBulkPaymentMfaFlow] = useState(false);
    const { success: teamMemberSuccess } = useAppSelector((state) => state.transferMfaSlice.getTeamMemberDetails);
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);

    const [mfaVerificationState, setMfaVerificationState] = useState({
        mfaVerified: false,
        showMfaModal: false,
        token: null as string | null,
    });

    // Determine the display account with fallback logic
    let displayAccount = selectedAccount;
    if (!displayAccount) {
        if (paymentInfo.accountFrom) {
            // Try to find account by the stored account number and format it
            const accountByNumber = accounts?.find((acc) => acc.accountNumber === paymentInfo.accountFrom);
            if (accountByNumber) {
                displayAccount = `${formatAccountName(accountByNumber)} ${formatAccountNumber(accountByNumber.accountNumber)}`;
            } else {
                // If account not found, create a fallback display using just the account number
                // Format: "Account ****1234" (where 1234 are the last 4 digits)
                displayAccount = `Account ${formatAccountNumber(paymentInfo.accountFrom)}`;
            }
        } else {
            // Fall back to first account if available
            const firstAccount = accounts?.[0];
            if (firstAccount) {
                displayAccount = `${formatAccountName(firstAccount)} ${formatAccountNumber(firstAccount.accountNumber)}`;
            } else {
                displayAccount = "";
            }
        }
    }

    // Add validation for required payment data
    // Note: corporateId is no longer required for payment processing or validation
    const isPaymentDataValid = Boolean(paymentInfo.accountFrom && entries.length > 0);

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => dispatch(resetBulkAirtime()),
    });

    const { isProcessing, isLoadingMap, currentStepperStep, processingPhase, handleConfirmPayment } =
        usePaymentProcessing({
            entries,
            paymentInfo,
            selectedAccount: paymentInfo.accountFrom,
            categoryName,
            mfaToken: mfaVerificationState.token, // Pass MFA token to payment processing
            onSuccess: () => {
                if (onConfirm) {
                    onConfirm();
                } else {
                    router.push(PATH_PROTECTED.billPayments.root);
                }
            },
        });

    // Handle Confirm Payment button click - initiate MFA verification first
    const handleConfirmPaymentClick = () => {
        dispatch(getTeamMemberDetails());
        setInitiateBulkPaymentMfaFlow(true);
    };

    // Handle MFA verification completion
    const handleMfaVerified = useCallback(
        (token: string) => {
            setMfaVerificationState({
                mfaVerified: true,
                showMfaModal: false,
                token: token,
            });
            setInitiateBulkPaymentMfaFlow(false);
            // Proceed with PIN verification and payment
            handleConfirmPayment();
        },
        [handleConfirmPayment]
    );

    // Handle MFA verification modal close
    const handleMfaVerificationClose = () => {
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            token: null,
        });
        setInitiateBulkPaymentMfaFlow(false);
    };

    // Handle team member details response
    useEffect(() => {
        if (teamMemberSuccess && teamMember && initiateBulkPaymentMfaFlow) {
            // Check if user has MFA enabled
            if (teamMember?.mfaStatus) {
                // Show MFA verification modal
                setMfaVerificationState({
                    mfaVerified: false,
                    showMfaModal: true,
                    token: null,
                });
            } else {
                // No MFA required, directly proceed to PIN verification
                handleMfaVerified(""); // Pass empty token when no MFA is required
            }
        }
    }, [teamMemberSuccess, teamMember, initiateBulkPaymentMfaFlow, handleMfaVerified]);

    // Clean up MFA state when component unmounts or payment is successful
    useEffect(
        () => () => {
            if (mfaVerificationState.showMfaModal || mfaVerificationState.mfaVerified) {
                dispatch(resetAllStates());
            }
        },
        [dispatch, mfaVerificationState.showMfaModal, mfaVerificationState.mfaVerified]
    );

    // Function to get appropriate button text based on the current state
    const getButtonText = () => {
        if (isPinModalOpen) return "Verifying PIN...";
        if (isLoadingMap) return "Loading payment details...";
        if (isProcessing) {
            if (processingPhase === "preparing") return "Preparing payment...";
            if (processingPhase === "submitting") return "Processing payment...";
            return "Processing payment...";
        }
        return "Confirm payment";
    };

    // Flag to determine if any API call is in progress
    const isAnyProcessingActive = isProcessing || isLoadingMap || isPinModalOpen || mfaVerificationState.showMfaModal;

    const getPageTitle = () => `${categoryName} bulk bill payment`;

    // Handle payment confirmation with validation
    function handlePaymentConfirmation(e: React.MouseEvent<HTMLButtonElement>) {
        e.preventDefault(); // Prevent any default behavior

        if (!isPaymentDataValid) {
            sendCatchFeedback(
                "Missing required payment information. Please check your account selection and try again."
            );
            return;
        }

        // Add a small delay to ensure UI updates before heavy processing
        setTimeout(() => {
            // If all validation passes, proceed with MFA flow first
            handleConfirmPaymentClick();
        }, 10);
    }

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={getPageTitle()}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <main className="flex-1 flex min-h-0 relative overflow-x-auto flex-col lg:flex-row">
                {/* Stepper navigation - Fixed width of 240px */}
                <nav
                    className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment steps"
                >
                    <Stepper steps={bulkAirtimeSteps} currentStep={currentStepperStep} />
                </nav>

                {/* Main content area */}
                <main className="flex-1 flex justify-center overflow-y-auto overflow-x-auto relative pl-[240px] pr-[435px] min-[1450px]:pr-[480px] px-primary lg:px-0">
                    {/* Content width of 495px by default, 495px between 1340px-1449px, 515px for 1450px+ */}
                    <div className="w-[495px] min-[1450px]:w-[515px] flex flex-col pt-8 min-h-min">
                        <h1 className="text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-12 text-center">
                            Review bill payment
                        </h1>
                        <div className="w-full flex flex-col items-center gap-[48px]">
                            <BillSummary
                                totalAmount={totalAmount}
                                recipientCount={recipientsCount}
                                categoryName={categoryName}
                            />
                            <SummaryCard
                                title="Bill details"
                                items={[
                                    {
                                        label: "Bill type",
                                        value: `Bulk ${categoryName}`,
                                        "data-testid": "bill-type-value",
                                    },
                                    {
                                        label: "Amount",
                                        value: `₦${totalAmount.toLocaleString()}.00`,
                                        "data-testid": "bill-details-amount",
                                    },
                                    {
                                        label: "Fees",
                                        value: "₦0.00",
                                        "data-testid": "bill-fees-value",
                                    },
                                    {
                                        label: "Pay from",
                                        value: displayAccount,
                                        "data-testid": "bill-pay-from-value",
                                    },
                                ]}
                                itemGap="32px"
                            />
                        </div>
                        <div className="flex justify-end gap-3 mt-12">
                            <Button onClick={onBack} variant="outline" size="medium">
                                Previous
                            </Button>
                            <Button
                                onClick={handlePaymentConfirmation}
                                variant="primary"
                                size="medium"
                                disabled={isAnyProcessingActive || !isPaymentDataValid}
                                data-testid="confirm-payment-button"
                                type="button"
                                rightIcon={isAnyProcessingActive ? <LoadingIndicator size={20} /> : undefined}
                            >
                                {getButtonText()}
                            </Button>
                        </div>
                    </div>
                </main>

                <MultiPartyApproval isOpen={showApprovals} amount={totalAmount} type="BILL_PAYMENT" />
            </main>

            {/* MFA Verification Modal */}
            {mfaVerificationState.showMfaModal && teamMember && (
                <SettingsMfaVerification
                    isOpen={mfaVerificationState.showMfaModal}
                    onClose={handleMfaVerificationClose}
                    onVerified={handleMfaVerified}
                    userMfaType={teamMember.preferredMfaMethod ?? "SMS"}
                    email={teamMember.email ?? ""}
                    phoneNumber={teamMember.phoneNumber ?? ""}
                />
            )}
        </FullScreenDrawer>
    );
}
