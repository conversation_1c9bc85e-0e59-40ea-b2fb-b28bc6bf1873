/**
 * Payment Information Component
 *
 * Purpose: Manages the payment information step in the bulk airtime payment flow.
 *
 * Functionality: This component handles the collection of payment details including account selection
 * and payment narration. It displays a summary of the payment (valid entries count and total amount),
 * provides account selection via a dropdown component, and captures optional narration text. The component
 * syncs form data with Redux to ensure persistence across navigation, and implements proper validation
 * and state management to guide users through the payment flow.
 *
 * Dependencies:
 * - React hooks for state and side effects
 * - Redux for global state management
 * - Common UI components (Button, Stepper, SummaryCard, etc.)
 * - FullScreenDrawer for the page layout
 * - Account selection component for payment source
 * - Text area component for narration
 *
 * Usage: Used as the third step in the bulk payment flow. Receives payment summary data (entry count
 * and total amount) and provides navigation callbacks for moving backward and forward in the flow.
 * The component ensures all required payment information is collected before allowing users to proceed.
 */

"use client";

import { useState, useEffect, useRef } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { But<PERSON> } from "@/components/common/buttonv3";
import Stepper from "@/components/common/stepper";
import { SummaryCard } from "@/components/common/summary-card";
import { bulkAirtimeSteps, type PaymentInfoProps } from "../utils/data";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { updatePaymentInfo, resetBulkAirtime } from "@/redux/features/bulkAirtime";
import { setSelectedAccount } from "@/redux/features/accounts";
import { InfoIcon } from "@/components/icons/auth";
import AccountSelector from "../common/account-selector";
import LabelTextArea from "@/components/common/text-area";
import { formatAccountName, formatAccountNumber } from "../common/account-utils";

// Validation schema for the payment form
const paymentValidationSchema = yup.object({
    narration: yup.string().trim().required("Narration is required"),
});

// Update the component interface to include the new props
interface ExtendedPaymentInfoProps extends PaymentInfoProps {
    categoryId?: number;
    categoryName?: string;
}

export default function PaymentInfo({
    onBack,
    onContinue,
    validEntriesCount,
    totalValidAmount,
    // Removed unused categoryId parameter
    categoryName = "Airtime",
}: Readonly<ExtendedPaymentInfoProps>) {
    const dispatch = useAppDispatch();
    const paymentInfo = useAppSelector((state) => state.bulkAirtime.paymentInfo);
    const { accounts, loadingStatus } = useAppSelector((state) => state.account);
    const accountsLoading = loadingStatus === "loading";

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => dispatch(resetBulkAirtime()),
    });

    const [narration, setNarration] = useState(paymentInfo.narration);

    // Initialize Formik for validation only
    const formik = useFormik({
        initialValues: {
            narration,
        },
        validationSchema: paymentValidationSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: async () => {
            // Validate form before submission
            const errors = await formik.validateForm();
            if (Object.keys(errors).length > 0) {
                // Touch all fields to show errors
                Object.keys(formik.values).forEach((field) => {
                    formik.setFieldTouched(field, true);
                });
                return;
            }

            // Proceed with submission if validation passes
            onContinue();
        },
    });

    // Sync Formik values with local state
    useEffect(() => {
        formik.setFieldValue("narration", narration);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [narration]); // Removed formik from dependency array to prevent infinite loops

    // Get the entries from Redux state to calculate summary data if not provided
    const entries = useAppSelector((state) => state.bulkAirtime.entries);

    // Calculate valid entries count and total amount if not provided
    const calculatedValidEntriesCount = validEntriesCount || entries.filter((entry) => entry.status === "Valid").length;
    const calculatedTotalAmount =
        totalValidAmount ||
        entries.filter((entry) => entry.status === "Valid").reduce((sum, entry) => sum + entry.amount, 0);

    // Get page title based on category
    const getPageTitle = () => `${categoryName} bulk bill payment`;

    // Track if the account sync has happened to prevent repeated updates
    const accountSyncedRef = useRef(false);

    // Auto-select first account when accounts are loaded and no account is selected
    useEffect(() => {
        if (accounts.length > 0 && !paymentInfo.accountFrom && !accountSyncedRef.current) {
            accountSyncedRef.current = true;
            const defaultAccount = accounts[0];
            dispatch(updatePaymentInfo({ accountFrom: defaultAccount.accountNumber }));

            // Also update accounts.selectedAccount with formatted display string
            const formattedDisplay = `${formatAccountName(defaultAccount)} ${formatAccountNumber(defaultAccount.accountNumber)}`;
            dispatch(setSelectedAccount(formattedDisplay));
        }
    }, [accounts, paymentInfo.accountFrom, dispatch]);

    // Track the last selected account to prevent redundant updates
    const lastAccountRef = useRef(paymentInfo.accountFrom);

    const handleAccountChange = (value: string) => {
        // Skip if the account hasn't changed
        if (value === lastAccountRef.current) {
            return;
        }

        lastAccountRef.current = value;
        dispatch(updatePaymentInfo({ accountFrom: value }));

        // Also update accounts.selectedAccount with formatted display string
        const selectedAccount = accounts.find((acc) => acc.accountNumber === value);
        if (selectedAccount) {
            const formattedDisplay = `${formatAccountName(selectedAccount)} ${formatAccountNumber(selectedAccount.accountNumber)}`;
            dispatch(setSelectedAccount(formattedDisplay));
        }
    };

    // Track the last narration to prevent redundant updates
    const lastNarrationRef = useRef(narration);
    const narrationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const handleNarrationChange = (value: string) => {
        // Skip if the narration hasn't changed
        if (value === lastNarrationRef.current) {
            return;
        }

        lastNarrationRef.current = value;
        setNarration(value);

        // Clear any existing timeout
        if (narrationTimeoutRef.current) {
            clearTimeout(narrationTimeoutRef.current);
        }

        // Debounce the Redux update to prevent excessive state changes
        narrationTimeoutRef.current = setTimeout(() => {
            dispatch(updatePaymentInfo({ narration: value }));
            narrationTimeoutRef.current = null;
        }, 300);
    };

    // Cleanup timeout on component unmount
    useEffect(
        () => () => {
            if (narrationTimeoutRef.current) {
                clearTimeout(narrationTimeoutRef.current);
            }
        },
        []
    );

    // Handle form submission with validation
    const handleFormSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate form before submission using Formik
        const errors = await formik.validateForm();
        if (Object.keys(errors).length > 0) {
            // Touch all fields to show errors
            Object.keys(formik.values).forEach((field) => {
                formik.setFieldTouched(field, true);
            });
            return;
        }

        // Proceed with submission if validation passes
        onContinue();
    };

    // Determine if continue button should be disabled
    // Following UI Guidelines to disable buttons when data is loading or unavailable
    // Prevent user from proceeding without a valid account selection to ensure proper payment processing
    const continueDisabled = accountsLoading || accounts.length === 0 || !paymentInfo.accountFrom;

    const summaryItems = [
        {
            label: "Valid phone numbers",
            value: calculatedValidEntriesCount,
            valueColorClass: "text-[#039855]",
        },
        {
            label: "Total valid amount",
            value: `₦${calculatedTotalAmount.toLocaleString()}`,
        },
    ];

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={getPageTitle()}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <div className="flex min-h-0 relative flex-1 flex-col lg:flex-row">
                <nav className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={bulkAirtimeSteps} currentStep={3} />
                </nav>

                <main className="flex-1 py-8 flex flex-col items-center px-primary lg:px-14">
                    <div className="w-full max-w-[470px]">
                        <h2
                            className="text-center text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-10"
                            data-testid="payment-info-title"
                        >
                            Payment information
                        </h2>

                        <div className="min-h-[516px] flex-col justify-start items-start gap-8 inline-flex w-full overflow-y-auto">
                            <SummaryCard items={summaryItems} itemGap="24px" />

                            <form
                                onSubmit={handleFormSubmit}
                                className="self-stretch flex-col justify-end items-end gap-8 flex"
                                data-testid="payment-form"
                            >
                                <div className="w-full">
                                    <AccountSelector
                                        selectedAccount={paymentInfo.accountFrom}
                                        onAccountChange={handleAccountChange}
                                    />
                                </div>

                                <div className="w-full space-y-2">
                                    <LabelTextArea
                                        name="narration"
                                        label="Narration *"
                                        placeholder="Write something here..."
                                        value={narration}
                                        onChange={(e) => handleNarrationChange(e.target.value)}
                                        minHeight="63px"
                                        maxHeight="200px"
                                    />
                                    {formik.touched.narration && formik.errors.narration && (
                                        <div className="flex items-center gap-[0.25rem] text-[#D92D20] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                                            <InfoIcon data-testid="info-icon" />
                                            <span className="text-[14px] font-medium leading-[18px] tracking-[0.28px] text-[#D92D20]">
                                                {formik.errors.narration}
                                            </span>
                                        </div>
                                    )}
                                </div>

                                <div className="justify-end items-center gap-3 inline-flex">
                                    <Button type="button" onClick={onBack} variant="outline" size="medium">
                                        Back
                                    </Button>
                                    <Button type="submit" variant="primary" size="medium" disabled={continueDisabled}>
                                        Continue
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
}
