/**
 * Upload Bulk Details Component
 *
 * Purpose: Handles the initial file upload step in the bulk airtime payment flow.
 *
 * Functionality: This component manages the CSV file upload process for bulk airtime payments,
 * including template download, file upload, validation, and data processing. It integrates with
 * file processing utilities to validate CSV content, extracts payment entries, and stores the file
 * data for persistence across sessions. The component displays appropriate loading states during
 * file processing and validation, and ensures only valid files can proceed to the next step.
 *
 * Dependencies:
 * - React hooks for state and effects
 * - Redux for global state management
 * - Next.js router for navigation
 * - Common UI components (Button, Stepper, FileAttachment, etc.)
 * - File processing utilities from "./utils/file-processing"
 * - File restoration hook for maintaining file state across sessions
 * - PATH_PROTECTED for route management
 *
 * Usage: Used as the first step in the bulk payment flow. Provides users with a CSV template
 * for download, handles file upload and validation, and allows navigation to the verify details
 * page once a valid file has been processed. The component maintains file state across page
 * refreshes using localStorage and Redux.
 */

"use client";

import { useState } from "react";
import { LayoutIcon, FileIcon, UploadIcon, CsvFileIcon } from "@/components/icons/bill-payment-icons";
import { But<PERSON> } from "@/components/common/buttonv3";
import Stepper from "@/components/common/stepper";
import FileAttachment from "@/components/common/file-attachment";
import { bulkAirtimeSteps } from "../utils";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { useAppDispatch } from "@/redux/hooks";
import { resetBulkAirtime, setEntries, updateFilterState } from "@/redux/features/bulkAirtime";
import { useRouter } from "next/navigation";
import { sendFeedback } from "@/functions/feedback";
import { generateSampleCSV } from "./table/utils";
import { validateAndProcessFile } from "./utils/file-processing";
import { useFileRestoration } from "./hooks/use-file-restoration";
import { PATH_PROTECTED } from "@/routes/path";

interface UploadBulkDetailsProps {
    onContinue?: () => void;
    categoryId?: number;
    categoryName?: string;
}

const LoadingIcon = () => (
    <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-3.5 h-3.5 flex-shrink-0 animate-spin"
    >
        <g clipPath="url(#clip0_556_1769)">
            <path
                d="M7 1.3125V2.77083M7 10.5V12.8333M3.35417 7H1.3125M12.3958 7H11.5208M10.7666 10.7666L10.3542 10.3542M10.8875 3.15921L10.0625 3.98417M2.87092 11.1291L4.52083 9.47917M2.99173 3.0384L4.22917 4.27583"
                stroke="#90909D"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
        <defs>
            <clipPath id="clip0_556_1769">
                <rect width="14" height="14" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

export default function UploadBulkDetails({
    onContinue,
    categoryId = 4,
    categoryName = "Airtime",
}: Readonly<UploadBulkDetailsProps>) {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const [isUploading, setIsUploading] = useState(false);
    const [isValidating, setIsValidating] = useState(false);

    const { uploadedFile, isUploadComplete, clearFile, storeFile } = useFileRestoration();

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => {
            clearFile();
            dispatch(resetBulkAirtime());
        },
    });

    const handleContinue = () => {
        if (onContinue) {
            onContinue();
        } else {
            router.push(PATH_PROTECTED.billPayments.bulk.verify(categoryId));
        }
    };

    const getPageTitle = () => `Bulk ${categoryName} bill payment`;

    const getDownloadFilename = () => `bulk_${categoryName.toLowerCase().replace(/\s+/g, "_")}_template.csv`;

    const handleCategoryDownload = () => {
        const csvContent = generateSampleCSV();
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", getDownloadFilename());
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const processUploadedFile = async (file: File) => {
        setIsValidating(true);
        setIsUploading(true);

        try {
            const result = await validateAndProcessFile(file);
            await storeFile(file);
            dispatch(setEntries(result.entries));
            dispatch(
                updateFilterState({
                    filter: "All",
                    networkFilter: "Network",
                    sortOrder: null,
                })
            );
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : "An error occurred while processing the CSV file";
            sendFeedback(errorMessage, "error");
            clearFile();
        } finally {
            setIsValidating(false);
            setIsUploading(false);
        }
    };

    const renderFileAttachment = () => (
        <FileAttachment
            headerText="Attachments"
            acceptedTypes={["csv"]}
            maxSize={10}
            onFilesSelected={(files) => {
                if (files && files.length > 0) {
                    processUploadedFile(files[0]);
                }
            }}
            onFileRemoved={() => {
                clearFile();
            }}
            icon={<FileIcon className="w-5 h-5" />}
            progressIcon={<CsvFileIcon />}
            className="w-full"
            width="100%"
            descriptionText="Supported format: CSV. File size up to 10MB"
            initialFile={uploadedFile}
        />
    );

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={getPageTitle()}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <div className="flex-1 flex min-h-0 relative flex-col lg:flex-row">
                <nav className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={bulkAirtimeSteps} currentStep={1} />
                </nav>

                <main className="flex-1 flex justify-center overflow-y-auto px-primary lg:px-0">
                    <div className="w-full max-w-[480px] flex flex-col items-center">
                        <h2 className="text-center text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-12 mt-8">
                            Upload bulk {categoryName} details
                        </h2>

                        <div className="w-[480px] p-8 flex flex-col items-end gap-8 bg-white rounded-[20px] border border-[#e3e5e7]">
                            <div className="w-full flex flex-col justify-start items-start gap-8">
                                <div className="w-full flex justify-start items-start gap-2">
                                    <div className="w-6 h-6 flex-shrink-0">
                                        <LayoutIcon className="w-full h-full" />
                                    </div>
                                    <div className="flex-1 flex flex-col justify-start items-start gap-5">
                                        <div className="w-full flex flex-col justify-start items-start gap-3">
                                            <h3 className="w-full text-[#151518] text-base font-semibold leading-tight tracking-tight">
                                                Download the {categoryName} CSV template
                                            </h3>
                                            <p className="w-full text-[#393941] text-sm font-normal leading-[18px] tracking-tight">
                                                Ensure your uploaded data matches the format in the sample template.
                                            </p>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="flex justify-center items-center gap-2.5"
                                            onClick={handleCategoryDownload}
                                        >
                                            <span className="text-center text-[#151518] text-sm font-semibold leading-[18px] tracking-tight">
                                                Download
                                            </span>
                                        </Button>
                                    </div>
                                </div>
                                <div className="w-full flex justify-start items-start gap-2">
                                    <div className="w-6 h-6 flex-shrink-0">
                                        <UploadIcon className="w-full h-full" />
                                    </div>
                                    <div className="flex-1 flex flex-col justify-start items-start gap-6">
                                        <div className="w-full flex flex-col justify-start items-start gap-3">
                                            <h3 className="w-full text-[#151518] text-base font-semibold leading-tight tracking-tight">
                                                Upload and verify
                                            </h3>
                                            <p className="w-full text-[#393941] text-sm font-normal leading-[18px] tracking-tight">
                                                Once you're done, upload the file for review and verification.
                                            </p>
                                        </div>

                                        <div className="flex flex-col gap-2 w-full">
                                            <div className="w-full">{renderFileAttachment()}</div>
                                            {isValidating && (
                                                <div className="w-full flex items-center gap-1 text-sm text-[#3A3A41] font-medium tracking-[0.02em] leading-[18px]">
                                                    <LoadingIcon />
                                                    <span className="flex-1">Validating your CSV file...</span>
                                                </div>
                                            )}
                                            {isUploading && uploadedFile && !isValidating && (
                                                <div className="w-full flex items-center gap-1 text-sm text-[#3A3A41] font-medium tracking-[0.02em] leading-[18px]">
                                                    <LoadingIcon />
                                                    <span className="flex-1">Hold on while we verify your file</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <Button
                            variant="primary"
                            size="medium"
                            className="mt-8 self-end disabled:bg-[#F7F7F8] disabled:!opacity-100 disabled:text-[#9D9DAC]"
                            disabled={!uploadedFile || !isUploadComplete || isValidating}
                            onClick={handleContinue}
                        >
                            <span>Continue</span>
                        </Button>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
}
