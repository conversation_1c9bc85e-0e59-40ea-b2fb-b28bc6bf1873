import { convertCsvToEntries, validateCsvFile } from "../../utils/csv-utils";
import { BulkAirtimeEntry } from "../../utils/data";
import { fileToBase64, FileInfo } from "./file-storage";

interface ProcessingResult {
    entries: BulkAirtimeEntry[];
    fileInfo: FileInfo;
    base64Data?: string;
    validCount: number;
    totalCount: number;
}

/**
 * Validates and processes an uploaded CSV file for bulk airtime payments
 *
 * @param file - The CSV file to process
 * @returns A promise that resolves to the processing result or rejects with an error
 */
export const validateAndProcessFile = async (file: File): Promise<ProcessingResult> => {
    // Validate the CSV file
    const validationResult = await validateCsvFile(file);

    if (!validationResult.isValid) {
        throw new Error(validationResult.errorMessage ?? "Invalid CSV file format");
    }

    if (!validationResult.data || validationResult.data.length === 0) {
        throw new Error("No valid data found in the CSV file");
    }

    // Convert valid CSV data to entries
    const entries = convertCsvToEntries(validationResult.data);
    const validEntries = entries.filter((entry) => entry.status === "Valid");

    // Create file info object
    const fileInfo: FileInfo = {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
    };

    // Convert file to base64 for storage
    let base64Data: string | undefined;
    try {
        base64Data = await fileToBase64(file);
    } catch {
        // Don't throw here - base64 conversion is optional
        // Silently ignore base64 conversion errors
    }

    return {
        entries,
        fileInfo,
        base64Data,
        validCount: validEntries.length,
        totalCount: entries.length,
    };
};

/**
 * Determines if the processing result indicates a successful validation
 *
 * @param result - The processing result to check
 * @returns true if the file was processed successfully and contains valid entries
 */
export const isProcessingSuccessful = (result: ProcessingResult): boolean =>
    result.entries.length > 0 && result.validCount > 0;

/**
 * Gets an appropriate feedback message based on the processing result
 *
 * @param result - The processing result to generate feedback for
 * @returns A feedback message object with text and type
 */
export const getProcessingFeedback = (
    result: ProcessingResult
): { message: string; type: "success" | "warning" | "error" } => {
    if (result.validCount === 0) {
        return {
            message: "File uploaded, but no valid entries were found. Please add entries manually.",
            type: "warning",
        };
    }

    if (result.validCount < result.totalCount) {
        return {
            message: `File processed successfully. ${result.validCount} out of ${result.totalCount} entries are valid.`,
            type: "warning",
        };
    }

    return {
        message: `File processed successfully. All ${result.totalCount} entries are valid.`,
        type: "success",
    };
};
