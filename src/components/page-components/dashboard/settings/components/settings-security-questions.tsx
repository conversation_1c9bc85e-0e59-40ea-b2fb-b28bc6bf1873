import { userAxios } from "@/api/axios";
import { But<PERSON> } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { Skeleton } from "@/components/ui/skeleton";
import { sendCatchFeedback } from "@/functions/feedback";
import { handleError } from "@/lib/utils";
import { validateSettingsSecurityQuestion } from "@/redux/actions/settingsMfaActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";

type SettingsSecurityQuestionProps = {
    email: string;
    onVerified: (token: string) => void;
};

const SettingsSecurityQuestion = ({ email, onVerified }: SettingsSecurityQuestionProps) => {
    const [question, setQuestion] = useState("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const {
        error: validateError,
        success: validateSuccess,
        loading: validateLoading,
        token,
    } = useAppSelector((state) => state.settingsMfa?.validateSecurityQuestion || {});

    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: {
            question,
            answer: "",
        },
        enableReinitialize: true,
        onSubmit: () => {
            handleFormSubmit();
        },
        validationSchema: yup.object({
            question: yup.string().required("Question is required"),
            answer: yup.string().required("Answer is required"),
        }),
    });

    const handleFormSubmit = () => {
        dispatch(
            validateSettingsSecurityQuestion({
                userEmail: email,
                question: question,
                answer: formik.values.answer,
            })
        );
    };

    const getRandomIndex = (length: number): number => Math.floor(Math.random() * length);

    useEffect(() => {
        const fetchQuestions = async () => {
            setLoading(true);
            try {
                const response = await userAxios(`/v1/user-security-questions?email=${email}`);

                // Generate the random index once
                const randomIndex = getRandomIndex(response.data.length);
                const selectedQuestion = response.data[randomIndex].question;

                // Use the same question for both state and formik
                setQuestion(selectedQuestion);
                formik.setFieldValue("question", selectedQuestion);

                setError(null);
            } catch (error) {
                setError(handleError(error));
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [email]);

    // Handle successful validation
    useEffect(() => {
        if (validateSuccess && token) {
            // Don't show MFA success feedback for settings operations
            // The actual operation success/failure will show appropriate feedback
            onVerified(token);
        }
    }, [validateSuccess, token, onVerified]);

    // Handle validation errors
    useEffect(() => {
        if (validateError) {
            sendCatchFeedback(new Error(validateError));
        }
    }, [validateError]);

    if (loading) {
        return (
            <div className="space-y-4 max-w-[50%] mx-auto">
                <div className="text-center mb-[40px]">
                    <h2 className="text-2xl font-bold text-black">Let's make sure it's you</h2>
                    <p className="text-base font-normal text-subText mt-[12px]">
                        Provide the answer to your security question below
                    </p>
                </div>
                <div className="grid gap-8">
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-12 w-full" />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-4 max-w-[50%] mx-auto">
                <div className="text-center mb-[40px]">
                    <h2 className="text-2xl font-bold text-red-600">Error</h2>
                    <p className="text-base font-normal text-subText mt-[12px]">{error}</p>
                </div>
            </div>
        );
    }

    if (!question) {
        return <div>No question available.</div>;
    }

    return (
        <div className="space-y-4 max-w-[50%] mx-auto">
            <div className="text-center mb-[40px]">
                <h2 className="text-2xl font-bold text-black">Let's make sure it's you</h2>
                <p className="text-base font-normal text-subText mt-[12px]">
                    Provide the answer to your security question below
                </p>
            </div>

            <form className="grid gap-8" onSubmit={formik.handleSubmit}>
                <LabelInput name="answer" label={question} placeholder="Enter your answer" formik={formik} />
                <Button loading={validateLoading} disabled={validateLoading} type="submit" fullWidth size="lg">
                    Continue
                </Button>
            </form>
        </div>
    );
};

export default SettingsSecurityQuestion;
