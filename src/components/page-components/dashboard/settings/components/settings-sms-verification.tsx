"use client";

import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useEffect, useState } from "react";
import OTPContainer from "@/components/page-components/auth/otp-container";
import { useOtp } from "@/hooks/useOtp";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { validateSettingsSmsOtp } from "@/redux/actions/settingsMfaActions";

type SettingsSmsVerificationProps = {
    phoneNumber: string;
    onVerified: (token: string) => void;
};

const SettingsSmsVerification = ({ phoneNumber, onVerified }: SettingsSmsVerificationProps) => {
    const [otpValue, setOtpValue] = useState("");
    const dispatch = useAppDispatch();

    const {
        error: validateError,
        success: validateSuccess,
        loading: validateLoading,
        token,
    } = useAppSelector((state) => state.settingsMfa?.validateSmsOtp || {});

    const { resendOtp, sendOtp, resendOtpLoading, resendOtpSuccess, resendOtpError, clearOtpState } = useOtp();

    const handleResendOTP = async () => {
        await resendOtp(String(phoneNumber));
    };

    const submitForm = async () => {
        await dispatch(
            validateSettingsSmsOtp({
                receiver: String(phoneNumber),
                otp: otpValue,
            })
        );
    };

    // Send OTP on mount
    useEffect(() => {
        clearOtpState("validate");
        clearOtpState("resend");
        sendOtp({
            receiver: String(phoneNumber),
            receiverType: "SMS",
        });
    }, [phoneNumber]);

    // Handle verification success
    useEffect(() => {
        if (validateSuccess && token) {
            onVerified(token);
            // Don't show MFA success feedback for settings operations
            // The actual operation success/failure will show appropriate feedback
        }
    }, [validateSuccess, token, onVerified]);

    // Handle resend success
    useEffect(() => {
        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success");
            clearOtpState("resend");
        }
    }, [resendOtpSuccess]);

    // Handle errors
    useEffect(() => {
        if (resendOtpError) {
            sendCatchFeedback(resendOtpError, () => clearOtpState("resend"));
        }

        if (validateError) {
            sendCatchFeedback(new Error(validateError));
            setOtpValue("");
        }
    }, [validateError, resendOtpError]);

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            onResend={handleResendOTP}
            loading={validateLoading}
            resendLoading={resendOtpLoading}
            variant="autoVerify"
            title="Check your mobile for OTP"
            type="phone"
            subtitle="Enter the SMS OTP sent to your mobile"
            resendText="Send New Code"
        />
    );
};

export default SettingsSmsVerification;
