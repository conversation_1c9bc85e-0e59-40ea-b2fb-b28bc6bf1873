"use client";

import { sendCatchFeedback } from "@/functions/feedback";
import { validateSettingsAuthenticator } from "@/redux/actions/settingsMfaActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useEffect, useState } from "react";
import OTPContainer from "@/components/page-components/auth/otp-container";

type SettingsAuthenticatorProps = {
    username: string;
    onVerified: (token: string) => void;
};

const SettingsAuthenticator = ({ username, onVerified }: SettingsAuthenticatorProps) => {
    const {
        error: validateError,
        success: validateSuccess,
        loading: validateLoading,
        token,
    } = useAppSelector((state) => state.settingsMfa?.validateAuthenticator || {});

    const dispatch = useAppDispatch();
    const [otpValue, setOtpValue] = useState("");

    const submitForm = async () => {
        await dispatch(
            validateSettingsAuthenticator({
                username,
                otp: otpValue,
            })
        );
    };

    // Handle successful validation
    useEffect(() => {
        if (validateSuccess && token) {
            onVerified(token);
            // Don't show MFA success feedback for settings operations
            // The actual operation success/failure will show appropriate feedback
        }
    }, [validateSuccess, token, onVerified]);

    // Handle validation errors
    useEffect(() => {
        if (validateError) {
            sendCatchFeedback(new Error(validateError));
        }
    }, [validateError]);

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            loading={validateLoading}
            variant="autoVerify"
            title="Enter your authenticator code"
            type="pin"
            subtitle="Open your authenticator app and enter the 6-digit code"
            resendText=""
        />
    );
};

export default SettingsAuthenticator;
