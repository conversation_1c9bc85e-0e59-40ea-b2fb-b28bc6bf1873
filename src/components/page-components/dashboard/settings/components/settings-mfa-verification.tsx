"use client";

import SettingsSecurityQuestion from "./settings-security-questions";
import SettingsSmsVerification from "./settings-sms-verification";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import SettingsAuthenticator from "./settings-authenticator";
import { useAppDispatch } from "@/redux/hooks";
import { setCurrentMfaType, clearCurrentVerification } from "@/redux/slices/settingsMfaSlice";
import { useEffect } from "react";

interface SettingsMfaVerificationProps {
    userMfaType?: string | null;
    onClose: () => void;
    isOpen: boolean;
    onSuccess?: () => void;
    onVerified?: (token: string) => void;
    email: string;
    phoneNumber: string;
}

const SettingsMfaVerification = ({
    userMfaType,
    onClose,
    isOpen,
    onSuccess,
    onVerified,
    email,
    phoneNumber,
}: SettingsMfaVerificationProps) => {
    const dispatch = useAppDispatch();

    // Set current MFA type when component mounts
    useEffect(() => {
        if (isOpen && userMfaType) {
            dispatch(setCurrentMfaType(userMfaType as "SECURITY_QUESTION" | "AUTHENTICATOR" | "SMS"));
        }
    }, [isOpen, userMfaType, dispatch]);

    // Clear MFA state when component unmounts or closes
    useEffect(() => {
        if (!isOpen) {
            dispatch(clearCurrentVerification());
        }
    }, [isOpen, dispatch]);

    const handleVerified = (token: string) => {
        if (onVerified) {
            onVerified(token);
        }
        if (onSuccess) {
            onSuccess();
        }
    };

    const handleClose = () => {
        dispatch(clearCurrentVerification());
        onClose();
    };

    if (!userMfaType && !email && !phoneNumber) {
        return null;
    }

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={handleClose} title="Verify MFA">
            {userMfaType === "AUTHENTICATOR" && <SettingsAuthenticator username={email} onVerified={handleVerified} />}
            {userMfaType === "SECURITY_QUESTION" && (
                <SettingsSecurityQuestion email={email} onVerified={handleVerified} />
            )}
            {userMfaType === "SMS" && <SettingsSmsVerification phoneNumber={phoneNumber} onVerified={handleVerified} />}
        </FullScreenDrawer>
    );
};

export default SettingsMfaVerification;
