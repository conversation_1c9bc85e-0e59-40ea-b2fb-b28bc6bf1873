import { memo, useCallback, useRef, useState, useEffect } from "react";
import { Button } from "@/components/common/buttonv3";
import { ImageIcon2 } from "@/components/icons/settings";
import Image from "next/image";

/**
 * Props for the LogoUpload component
 *
 * @param logoUrl - URL to the current business logo, if available
 * @param acceptedFileTypes - Array of accepted file extensions (e.g., ['.png', '.jpg'])
 * @param handleFileChange - Callback function when a file is selected
 * @param handleDelete - Callback function when the delete button is clicked
 * @param uploadLoading - Whether the logo upload is in progress
 * @param deleteLoading - Whether the logo deletion is in progress
 * @param businessName - Business name used for generating initials when no logo is available
 */
interface LogoUploadProps {
    logoUrl?: string | null;
    acceptedFileTypes: string[];
    handleFileChange: (file: File) => void;
    handleDelete: () => void;
    uploadLoading?: boolean;
    deleteLoading?: boolean;
    businessName?: string;
}

/**
 * Component for uploading and managing the business logo
 *
 * This component displays the current business logo or initials if no logo is available,
 * and provides functionality to upload a new logo or delete the existing one.
 *
 * Follows API integration guidelines by using proper loading states and providing
 * user feedback for upload/delete operations.
 */
export const LogoUpload = memo(
    ({
        logoUrl,
        acceptedFileTypes,
        handleFileChange,
        handleDelete,
        uploadLoading = false,
        deleteLoading = false,
        businessName = "",
    }: LogoUploadProps) => {
        // Reference to the file input
        const fileInputRef = useRef<HTMLInputElement>(null);

        // State to track image loading errors
        const [imgError, setImgError] = useState(false);
        const [retryCount, setRetryCount] = useState(0);
        const MAX_RETRIES = 3;

        // Reset image error when logoUrl changes
        useEffect(() => {
            if (logoUrl) {
                setImgError(false);
                setRetryCount(0);
            }
        }, [logoUrl]);

        /**
         * Handles the file selection event
         * Validates the file size to ensure it's less than or equal to 10MB and calls the parent component's handler if valid
         * Shows an alert if the file size exceeds the limit
         */
        const onFileChange = useCallback(
            (e: React.ChangeEvent<HTMLInputElement>) => {
                const files = e.target.files;
                if (files && files.length > 0) {
                    const file = files[0];
                    const maxSizeInBytes = 10 * 1024 * 1024; // 10MB in bytes
                    if (file.size > maxSizeInBytes) {
                        // Show alert for file size exceeding 10MB
                        alert("File size exceeds the maximum limit of 10MB. Please select a smaller file.");
                        // Reset the input value to clear the selection
                        if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                        }
                        return;
                    }
                    handleFileChange(file);

                    // Reset the input value to allow selecting the same file again
                    if (fileInputRef.current) {
                        fileInputRef.current.value = "";
                    }
                }
            },
            [handleFileChange]
        );

        /**
         * Get initials from business name for the default logo
         */
        const getInitials = useCallback(() => {
            if (!businessName) return "BR";

            // Split by space and get the first letter of each word, up to 2 letters
            const initials = businessName
                .split(" ")
                .filter((word) => word.length > 0)
                .map((word) => word[0])
                .slice(0, 2)
                .join("");

            return initials.toUpperCase() || "BR";
        }, [businessName]);

        /**
         * Handle image load error by showing initials instead
         */
        const handleImageError = useCallback(() => {
            // Attempt to retry loading the image a few times before giving up
            if (retryCount < MAX_RETRIES) {
                setRetryCount((prev) => prev + 1);
                // Force a re-render with a different timestamp to bypass cache
                setTimeout(() => {
                    setImgError(false);
                }, 1000); // Wait a second before retrying
            } else {
                setImgError(true);
            }
        }, [retryCount]);

        // Add cache-busting parameter to the URL only if it's not a data URL
        const getImageUrl = useCallback(() => {
            if (!logoUrl) return "";

            // If it's a data URL (starts with data:), return it as is
            if (logoUrl.startsWith("data:")) {
                return logoUrl;
            }

            // For regular URLs, add a timestamp query parameter to prevent caching
            const hasQuery = logoUrl.includes("?");
            return `${logoUrl}${hasQuery ? "&" : "?"}t=${Date.now()}-${retryCount}`;
        }, [logoUrl, retryCount]);

        // Determine if we should show the image or initials
        const showInitials = !logoUrl || imgError;

        return (
            <div className="flex items-center gap-4">
                {/* Logo display - either image or initials */}
                <div
                    className="h-16 w-16 rounded-full bg-[#F9F0FE] flex items-center justify-center text-lg font-semibold text-subText overflow-hidden"
                    aria-label={!showInitials ? "Business logo" : "Business logo placeholder with initials"}
                >
                    {!showInitials ? (
                        <Image
                            src={getImageUrl()}
                            alt="Business logo"
                            width={64}
                            height={64}
                            className="h-full w-full object-cover"
                            onError={handleImageError}
                            priority
                        />
                    ) : (
                        <span>{getInitials()}</span>
                    )}
                </div>

                {/* Upload controls */}
                <div>
                    <h2 className="text-base font-medium text-black">
                        Update your business logo{" "}
                        <span className="text-[#90909D] font-normal text-sm">(PNG, JPG files up to 10MB)</span>
                    </h2>
                    <div className="mt-2 flex gap-3">
                        <Button
                            variant="outline"
                            className={
                                "relative border rounded-[8px] border-[#DBDBE1] flex items-center py-2 px-[14px] cursor-pointer"
                            }
                            disabled={uploadLoading || deleteLoading}
                            aria-label="Upload business logo"
                        >
                            <input
                                type="file"
                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                data-testid="file-input"
                                onChange={onFileChange}
                                accept={acceptedFileTypes.join(",")}
                                id="file-upload"
                                ref={fileInputRef}
                                aria-label="Select logo file"
                                disabled={uploadLoading || deleteLoading}
                            />
                            <div className="flex gap-2 items-center justify-center">
                                <ImageIcon2 />
                                <p className="text-sm leading-[18px] font-normal text-black">
                                    {uploadLoading ? "Uploading..." : "Upload"}
                                </p>
                            </div>
                        </Button>
                        <Button
                            variant="outline"
                            data-testid="remove-logo-button"
                            onClick={handleDelete}
                            disabled={!logoUrl || imgError || uploadLoading || deleteLoading}
                            aria-label="Remove business logo"
                        >
                            {deleteLoading ? "Removing..." : "Remove"}
                        </Button>
                    </div>
                </div>
            </div>
        );
    }
);

LogoUpload.displayName = "LogoUpload";
