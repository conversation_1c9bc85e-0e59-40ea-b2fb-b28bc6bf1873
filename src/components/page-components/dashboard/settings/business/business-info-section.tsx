import { memo } from "react";
import { EditableField } from "../ui/editable-field";
import { BusinessInfo } from "@/redux/types/settings";

interface BusinessInfoSectionProps {
    handleSave: (field: string) => (value: string) => void;
    handleSaveWithToken?: (field: string) => (value: string, token: string) => void;
    businessInfo: BusinessInfo | null;
}

/**
 * Business information section component with editable fields
 * Displays business information retrieved from the API
 * Provides editable fields for tradingName, businessEmail, and businessPhone
 * Now includes 2FA support for secure field updates
 */
export const BusinessInfoSection = memo(
    ({ handleSave, handleSaveWithToken, businessInfo }: BusinessInfoSectionProps) => (
        <div className="border-b border-b-[#E3E5E8] pb-6">
            <h2 className="text-xl font-semibold text-black mb-6">Business information</h2>
            <div className="space-y-6">
                <div>
                    <h3 className="text-base font-medium text-black">Legal business name</h3>
                    <div className="flex justify-between items-center mt-1">
                        <p className="text-base text-subText">
                            {businessInfo?.businessName ?? "No business name available"}
                        </p>
                    </div>
                </div>

                <div>
                    <h3 className="text-base font-medium text-black">Business registration number</h3>
                    <div className="flex justify-between items-center mt-1">
                        <p className="text-base text-subText">
                            {businessInfo?.registrationNumber ?? "No registration number available"}
                        </p>
                    </div>
                </div>

                <div>
                    <EditableField
                        name="tradingName"
                        label="Trading name"
                        value={businessInfo?.tradingName ?? ""}
                        emptyValueText="No trading name available"
                        onSave={handleSave("tradingName")}
                        onSaveWithToken={handleSaveWithToken ? handleSaveWithToken("tradingName") : undefined}
                        requireMfa={true}
                        required
                    />
                </div>

                <div>
                    <h3 className="text-base font-medium text-black">Business email address</h3>
                    <div className="flex justify-between items-center mt-1">
                        <p className="text-base text-subText">{businessInfo?.email ?? "--"}</p>
                    </div>
                </div>

                <div>
                    <h3 className="text-base font-medium text-black">Business phone number</h3>
                    <div className="flex justify-between items-center mt-1">
                        <p className="text-base text-subText">
                            {businessInfo?.phoneNumber ?? "No business phone number available"}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
);

BusinessInfoSection.displayName = "BusinessInfoSection";
