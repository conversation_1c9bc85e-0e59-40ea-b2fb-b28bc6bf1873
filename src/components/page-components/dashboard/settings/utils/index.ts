/**
 * Extracts a human-readable error message from various error object formats
 * Handles string errors, standard Error objects, and Axios response errors
 *
 * @param error - The error object from API or other sources
 * @param defaultMessage - Default message to show if error format is unknown
 * @returns A human-readable error message string
 */
export const extractErrorMessage = (error: unknown, defaultMessage: string = "An error occurred"): string => {
    if (typeof error === "string") return error;
    if (error && typeof error === "object" && "message" in error && typeof error.message === "string")
        return error.message;
    if (
        error &&
        typeof error === "object" &&
        "response" in error &&
        error.response &&
        typeof error.response === "object" &&
        "data" in error.response &&
        error.response.data &&
        typeof error.response.data === "object" &&
        "message" in error.response.data &&
        typeof error.response.data.message === "string"
    ) {
        return error.response.data.message;
    }
    return defaultMessage;
};
