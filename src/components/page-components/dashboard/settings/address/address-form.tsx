import { memo, useState, useCallback, useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import { Button } from "@/components/common/buttonv3";
import Image from "next/image";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";
import { clearGetTeamMemberState } from "@/redux/slices/transferMfaSlice";
import SettingsMfaVerification from "@/components/page-components/dashboard/settings/components/settings-mfa-verification";
import { sendFeedback } from "@/functions/feedback";

// Define a type for countries
export interface CountryDetails {
    name: {
        common: string;
    };
    flags?: {
        svg?: string;
        png?: string;
        alt?: string;
    };
}

/**
 * Props for the AddressForm component
 * Includes all fields from the API address structure
 */
export interface AddressFormProps {
    number: string;
    address: string; // Street address
    city: string;
    state: string;
    country: string;
    countries: CountryDetails[] | null | undefined;
    isCountriesLoading: boolean;
    toggleAddressEdit: () => void;
    handleSaveAddress: (address: {
        number: string;
        street: string;
        city: string;
        state: string;
        country: string;
    }) => void;
    handleSaveAddressWithToken?: (
        token: string,
        address?: {
            number: string;
            street: string;
            city: string;
            state: string;
            country: string;
        }
    ) => void;
    updateLoading: boolean;
    requireMfa?: boolean;
}

// Validation schema for address form
const addressValidationSchema = Yup.object({
    houseNumber: Yup.string().required("House number is required"),
    address: Yup.string().min(3, "Address must be at least 3 characters").required("Address is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    country: Yup.string().required("Country is required"),
});

/**
 * Shared AddressForm component for both personal and business settings
 *
 * Allows editing of all address fields including house number, street, city, state, and country
 * Follows the address structure expected by the API
 * Places the house number input beside the address input for better space utilization
 */
export const AddressForm = memo(
    ({
        number,
        address,
        city,
        state,
        country,
        countries,
        isCountriesLoading,
        toggleAddressEdit,
        handleSaveAddressWithToken,
        updateLoading,
    }: AddressFormProps) => {
        const dispatch = useAppDispatch();
        const { success: teamMemberSuccess } = useAppSelector((state) => state.transferMfaSlice.getTeamMemberDetails);
        const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);
        const mfaEnabled = teamMember?.mfaStatus;

        // 2FA state management
        const [isCheckingMfa, setIsCheckingMfa] = useState(false);
        const [showMfaModal, setShowMfaModal] = useState(false);
        const [pendingAddress, setPendingAddress] = useState<{
            number: string;
            street: string;
            city: string;
            state: string;
            country: string;
        } | null>(null);

        const initialValues = {
            houseNumber: number,
            address: address,
            city: city,
            state: state,
            country: country,
        };

        /**
         * Resets all states to their initial values
         * This should be called after successful submission or when aborting the process
         */
        const resetAllComponentStates = useCallback(() => {
            setPendingAddress(null);
            setIsCheckingMfa(false);
            setShowMfaModal(false);
            // Reset Redux states
            dispatch(resetAllStates());
            dispatch(clearGetTeamMemberState());
        }, [dispatch]);

        // Handle MFA verification success
        const handleMfaVerified = useCallback(
            (token: string) => {
                if (pendingAddress && handleSaveAddressWithToken) {
                    handleSaveAddressWithToken(token, pendingAddress);
                }
                // Reset all states immediately after token use
                resetAllComponentStates();
            },
            [pendingAddress, handleSaveAddressWithToken, resetAllComponentStates]
        );

        // Handle MFA modal close
        const handleMfaClose = useCallback(() => {
            // Reset all states when modal is closed
            resetAllComponentStates();
        }, [resetAllComponentStates]);

        const handleSubmit = (values: typeof initialValues) => {
            const addressData = {
                number: values.houseNumber,
                street: values.address,
                city: values.city,
                state: values.state,
                country: values.country,
            };

            if (handleSaveAddressWithToken) {
                // Store the pending address and check MFA
                setPendingAddress(addressData);
                setIsCheckingMfa(true);
                dispatch(getTeamMemberDetails());
            } else {
                // If no token handler is provided, show error
                sendFeedback(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            }
        };

        // Generate country options
        const countryOptions =
            countries?.map((country) => ({
                label: (
                    <div className="flex items-center gap-2">
                        <Image
                            src={
                                country.flags?.svg ??
                                country.flags?.png ??
                                country.flags?.alt ??
                                "https://via.placeholder.com/28"
                            }
                            alt="country flag"
                            width={28}
                            height={28}
                            className="w-5 h-5 object-cover rounded-full"
                        />
                        <span className="font-medium text-black">{country.name.common}</span>
                    </div>
                ),
                value: country.name.common,
            })) || [];

        // Find selected country option
        const getSelectedCountry = (countryName: string) => {
            // Return the matching country from options if it exists
            const matchingCountry = countryOptions.find((option) => option.value === countryName);
            if (matchingCountry) return matchingCountry;

            // If countryName exists but doesn't match any country in options, create a default option
            if (countryName) {
                return {
                    label: (
                        <div className="flex items-center">
                            <span className="font-medium text-black">{countryName}</span>
                        </div>
                    ),
                    value: countryName,
                };
            }

            return null;
        };

        // Check if MFA is required and show modal
        useEffect(() => {
            if (isCheckingMfa && teamMemberSuccess && teamMember && mfaEnabled) {
                setShowMfaModal(true);
            } else if (isCheckingMfa && teamMemberSuccess && (!teamMember || !mfaEnabled)) {
                // If MFA is not enabled, reset states and show error
                resetAllComponentStates();
                sendFeedback(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            }
        }, [isCheckingMfa, teamMemberSuccess, teamMember, mfaEnabled, resetAllComponentStates]);

        return (
            <>
                <Formik
                    initialValues={initialValues}
                    validationSchema={addressValidationSchema}
                    onSubmit={handleSubmit}
                >
                    {(formik) => (
                        <Form className="space-y-4 mt-4">
                            {/* House number and address inputs group - displayed in a single row for better space utilization */}
                            <div className="flex gap-2 items-center">
                                <LabelInput
                                    type="text"
                                    name="houseNumber"
                                    label="House Number"
                                    placeholder="Number"
                                    className="w-[30%]"
                                    formik={formik}
                                    aria-label="house-number"
                                />
                                <LabelInput
                                    type="text"
                                    name="address"
                                    label="Address"
                                    placeholder="Enter your address"
                                    className="w-[70%]"
                                    formik={formik}
                                    aria-label="address"
                                />
                            </div>
                            <div className="flex gap-2 items-center">
                                <LabelInput
                                    type="text"
                                    name="city"
                                    label="City"
                                    placeholder="Enter your city"
                                    className="w-full"
                                    formik={formik}
                                    aria-label="city"
                                />
                                <LabelInput
                                    type="text"
                                    name="state"
                                    label="State"
                                    placeholder="Enter your state"
                                    className="w-full"
                                    formik={formik}
                                    aria-label="state"
                                />
                            </div>
                            <Dropdown
                                options={countryOptions}
                                name="country"
                                label="Country"
                                size="sm"
                                formik={formik}
                                placeholder={isCountriesLoading ? "Loading countries..." : "Select a country"}
                                isDisabled={isCountriesLoading}
                                aria-label="country"
                                value={getSelectedCountry(formik.values.country)}
                            />
                            <div className="flex justify-end space-x-2 items-center">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={toggleAddressEdit}
                                    aria-label="Cancel address edit"
                                    disabled={updateLoading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    aria-label="Save address"
                                    loading={updateLoading}
                                    disabled={!formik.isValid || !formik.dirty}
                                >
                                    Save
                                </Button>
                            </div>
                        </Form>
                    )}
                </Formik>

                {/* MFA Verification Modal */}
                {showMfaModal && teamMember && (
                    <SettingsMfaVerification
                        userMfaType={teamMember?.preferredMfaMethod}
                        onClose={handleMfaClose}
                        isOpen={true}
                        onVerified={handleMfaVerified}
                        email={teamMember?.email}
                        phoneNumber={teamMember?.phoneNumber}
                    />
                )}
            </>
        );
    }
);

AddressForm.displayName = "AddressForm";
