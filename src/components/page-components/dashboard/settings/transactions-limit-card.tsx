"use client";

import React, { type ReactNode, useState, useEffect, useRef, useCallback } from "react";
import CustomModal from "@/components/common/custom-modal";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { PencilIcon2, RightIcon } from "@/components/icons/settings";
import { formatNumber, formatNumberToNaira } from "@/functions/stringManipulations";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { sendFeedback } from "@/functions/feedback";
import SettingsMfaVerification from "./components/settings-mfa-verification";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";
import { clearGetTeamMemberState } from "@/redux/slices/transferMfaSlice";

interface TransactionLimit {
    daily: number;
    perTransaction: number;
    maximum: number;
}

interface TransactionLimitsCardProps {
    title: string;
    subtitle: string;
    icon: ReactNode;
    limits: TransactionLimit;
    onUpdateLimits: (limits: TransactionLimit, token?: string | null) => void;
}

export function TransactionLimitsCard({
    title,
    subtitle,
    limits: initialLimits,
    icon,
    onUpdateLimits,
}: Readonly<TransactionLimitsCardProps>) {
    const dispatch = useAppDispatch();
    const [isExpanded, setIsExpanded] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [limits, setLimits] = useState<TransactionLimit>(initialLimits);
    const [formLimits, setFormLimits] = useState<TransactionLimit>(initialLimits);
    const [displayLimits, setDisplayLimits] = useState<Record<keyof TransactionLimit, string>>({
        daily: initialLimits.daily?.toString(),
        perTransaction: initialLimits.perTransaction?.toString(),
        maximum: initialLimits.maximum?.toString(),
    });

    // Track if we've submitted a form update
    const hasSubmittedUpdate = useRef(false);

    // MFA verification for transaction limits adjustment
    const [initiateLimitsAdjustmentMfaFlow, setInitiateLimitsAdjustmentMfaFlow] = useState(false);
    const { success: teamMemberSuccess, loading: teamMemberLoading } = useAppSelector(
        (state) => state.transferMfaSlice.getTeamMemberDetails
    );
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);

    const [mfaVerificationState, setMfaVerificationState] = useState({
        mfaVerified: false,
        showMfaModal: false,
        token: null as string | null,
    });

    /**
     * Resets all states to their initial values
     * This should be called after successful submission or when aborting the process
     */
    const resetAllComponentStates = useCallback(() => {
        setInitiateLimitsAdjustmentMfaFlow(false);
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            token: null,
        });
        // Reset Redux states
        dispatch(resetAllStates());
        dispatch(clearGetTeamMemberState());
    }, [dispatch]);

    // Handle Adjust Limits button click - initiate MFA verification first
    const handleAdjustLimitsClick = () => {
        // Reset any previous states first
        resetAllComponentStates();
        dispatch(getTeamMemberDetails());
        setInitiateLimitsAdjustmentMfaFlow(true);
    };

    // Handle MFA verification completion
    const handleMfaVerified = useCallback((token: string) => {
        setMfaVerificationState({
            mfaVerified: true,
            showMfaModal: false,
            token: token,
        });
        setInitiateLimitsAdjustmentMfaFlow(false);
        // Open the transaction limits adjustment modal
        setIsEditing(true);
    }, []);

    // Handle MFA verification modal close
    const handleMfaVerificationClose = useCallback(() => {
        // Reset all states when modal is closed
        resetAllComponentStates();
    }, [resetAllComponentStates]);

    // Handle team member details response
    useEffect(() => {
        if (teamMemberSuccess && teamMember && initiateLimitsAdjustmentMfaFlow) {
            // Check if user has MFA enabled
            if (teamMember?.mfaStatus) {
                // Show MFA verification modal
                setMfaVerificationState({
                    mfaVerified: false,
                    showMfaModal: true,
                    token: null,
                });
            } else {
                // No MFA required, directly show limits adjustment modal
                handleMfaVerified(""); // Pass empty token when no MFA is required
            }
        } else if (teamMemberSuccess && initiateLimitsAdjustmentMfaFlow && (!teamMember || !teamMember?.mfaStatus)) {
            // If MFA is not enabled, reset states and show error
            resetAllComponentStates();
            sendFeedback(
                "Two-factor authentication (2FA) is required to adjust transaction limits. Please set up 2FA in your security settings first.",
                "error",
                undefined,
                "2FA Required"
            );
        }
    }, [teamMemberSuccess, teamMember, initiateLimitsAdjustmentMfaFlow, handleMfaVerified, resetAllComponentStates]);

    const {
        loading: updateTransactionLimitLoading,
        success: updateTransactionLimitSuccess,
        error: updateTransactionLimitError,
    } = useAppSelector((state) => state.transactionLimits.updateLimit);

    // Format number with Naira sign
    const formatWithNaira = (value: number | string): string => {
        if (typeof value === "string" && value === "") return "₦";
        const numValue = typeof value === "string" ? Number.parseInt(value.replace(/\D/g, ""), 10) : value;
        return `₦${formatNumber(numValue)}`;
    };

    // Update local state when props change
    useEffect(() => {
        setLimits(initialLimits);
        setFormLimits(initialLimits);
        setDisplayLimits({
            daily: formatWithNaira(initialLimits.daily ?? 0),
            perTransaction: formatWithNaira(initialLimits.perTransaction ?? 0),
            maximum: formatWithNaira(initialLimits.maximum ?? 0),
        });
    }, [initialLimits]);

    // Handle form closing based on success/error state
    useEffect(() => {
        // Only show success toast and close modal if we've actually submitted an update
        if (updateTransactionLimitSuccess && hasSubmittedUpdate.current) {
            // Show success feedback
            sendFeedback("Transaction limits updated successfully", "success");

            // Reset all states after successful operation
            resetAllComponentStates();

            // Close the form after a delay to allow user to see the success message
            const timer = setTimeout(() => {
                setIsEditing(false);
                // Reset the submission flag
                hasSubmittedUpdate.current = false;
            }, 1500); // 1.5 second delay

            return () => clearTimeout(timer);
        }

        if (updateTransactionLimitError) {
            // Error feedback is handled by the Redux action via sendCatchFeedback
            // Keep the form open so the user can try again
            // Reset the submission flag on error too
            hasSubmittedUpdate.current = false;

            // Reset all states after error to prevent stale success feedback
            resetAllComponentStates();

            const timer = setTimeout(() => {
                setIsEditing(false);
                // Reset the submission flag
                hasSubmittedUpdate.current = false;
            }, 1000); // 1 second delay

            return () => clearTimeout(timer);
        }
    }, [updateTransactionLimitSuccess, updateTransactionLimitError, resetAllComponentStates]);

    // Handle modal close - reset MFA state to require fresh verification for next action
    const handleModalClose = useCallback(() => {
        setIsEditing(false);
        // Reset all states when modal is closed
        resetAllComponentStates();
    }, [resetAllComponentStates]);

    const handleInputChange = (field: keyof TransactionLimit, value: string) => {
        // Remove any non-numeric characters and the Naira sign
        const numericValue = value.replace(/\D/g, "");

        // Format with Naira sign and commas for display
        const formattedValue = formatWithNaira(numericValue === "" ? "" : numericValue);

        // Update the display value with Naira sign and commas
        setDisplayLimits((prev) => ({
            ...prev,
            [field]: formattedValue,
        }));

        // Update the actual numeric value
        const numValue = numericValue === "" ? 0 : Number.parseInt(numericValue, 10);

        setFormLimits((prev) => ({
            ...prev,
            [field]: numValue,
        }));
    };

    // Type-safe event handler for input changes
    const handleChange = (field: keyof TransactionLimit) => (e: React.ChangeEvent<HTMLInputElement>) => {
        handleInputChange(field, e.target.value);
    };

    // Format display value with Naira sign and commas when input loses focus
    const handleBlur = (field: keyof TransactionLimit) => () => {
        // If the field is empty or just has the Naira sign when blurred, show 0 formatted
        if (displayLimits[field] === "" || displayLimits[field] === "₦") {
            setDisplayLimits((prev) => ({
                ...prev,
                [field]: formatWithNaira(0),
            }));
        }
    };

    // Function to determine text size class based on the amount
    const getLimitTextClass = (amount?: number): string => {
        const amountStr = amount?.toString();
        if (amountStr && amountStr.length > 12) return "text-sm";
        if (amountStr && amountStr.length > 9) return "text-base";
        return "text-xl";
    };

    const handleSave = () => {
        // Set the flag to indicate we've submitted an update
        hasSubmittedUpdate.current = true;

        setLimits(formLimits);
        // Pass the token to the update function
        onUpdateLimits(formLimits, mfaVerificationState.token);
        // Form will be closed by the useEffect that monitors updateTransactionLimitSuccess
        // If the update is successful, it will close after a delay
        // If there's an error, it will stay open
    };

    return (
        <>
            <button
                data-testid="expand-button"
                disabled={title === "Foreign/FX Transfer"}
                onClick={() => {
                    if (title === "Foreign/FX Transfer") return; // Disable expansion for Foreign/FX Transfer
                    setIsExpanded((prev) => !prev);
                }}
                className={`w-full p-4 ${
                    title === "Foreign/FX Transfer"
                        ? "bg-gray-200 cursor-not-allowed"
                        : "hover:bg-gray-50 cursor-pointer"
                } rounded-[20px] transition-colors border border-[#E3E5E8]`}
            >
                <div className="w-full flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div
                            className={` ${
                                title === "Foreign/FX Transfer" ? "bg-gray-200" : "bg-[#F9F0FE]"
                            } rounded-full p-2`}
                        >
                            {icon}
                        </div>
                        <div className="text-left">
                            <h3 className="text-[16px] font-medium text-black">{title}</h3>
                            <p className="text-sm text-subText mt-1">{subtitle}</p>
                        </div>
                    </div>
                    <RightIcon />
                </div>

                {isExpanded && (
                    <div>
                        <div className={"mt-6 grid grid-cols-2 gap-4"}>
                            <div className="bg-[#F9F9FA] rounded-lg p-3">
                                <p className="text-sm text-subText">Daily limit</p>
                                <div className="overflow-hidden" title={formatNumberToNaira(limits.daily)}>
                                    <p
                                        className={`font-semibold text-black mt-1 truncate ${getLimitTextClass(limits.daily)}`}
                                    >
                                        {formatNumberToNaira(limits.daily)}
                                    </p>
                                </div>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-3">
                                <p className="text-sm text-subText">Per transaction</p>
                                <div className="overflow-hidden" title={formatNumberToNaira(limits.perTransaction)}>
                                    <p
                                        className={`font-semibold text-black mt-1 truncate ${getLimitTextClass(limits.perTransaction)}`}
                                    >
                                        {formatNumberToNaira(limits.perTransaction)}
                                    </p>
                                </div>
                            </div>
                            {/* <div className="bg-gray-50 rounded-lg p-3">
                                <p className="text-sm text-subText">Maximum limit</p>
                                <div className="overflow-hidden" title={formatNumberToNaira(limits.maximum)}>
                                    <p
                                        className={`font-semibold text-black mt-1 truncate ${getLimitTextClass(limits.maximum)}`}
                                    >
                                        {formatNumberToNaira(limits.maximum)}
                                    </p>
                                </div>
                            </div> */}
                        </div>
                        <button
                            data-testid="adjust-limit"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleAdjustLimitsClick();
                            }}
                            className="mt-4 pl-3 flex gap-1 items-center"
                        >
                            <PencilIcon2 baseColor="#5C068C" />
                            <p className="text-sm text-[#5C068C] font-semibold">Adjust limits</p>
                        </button>
                    </div>
                )}
            </button>

            <CustomModal
                isOpen={isEditing}
                onRequestClose={handleModalClose}
                title="Adjust transaction limits"
                width="522px"
            >
                <p className="text-center w-full text-subText text-[14px] -mt-4">
                    Manage daily, maximum, and per transaction limits.
                </p>

                <form
                    className="space-y-4 py-4"
                    onSubmit={(e) => {
                        e.preventDefault();
                        handleSave();
                    }}
                >
                    <LabelInput
                        name="daily-limit"
                        label="Daily Limit"
                        type="text"
                        value={displayLimits.daily}
                        onChange={handleChange("daily")}
                        onBlur={handleBlur("daily")}
                    />
                    <LabelInput
                        name="per-transaction"
                        label="Per Transaction"
                        type="text"
                        value={displayLimits.perTransaction}
                        onChange={handleChange("perTransaction")}
                        onBlur={handleBlur("perTransaction")}
                    />
                    {/* <LabelInput
                        name="max-limit"
                        label="Maximum Limit"
                        type="text"
                        value={displayLimits.maximum}
                        onChange={handleChange("maximum")}
                        onBlur={handleBlur("maximum")}
                    /> */}
                </form>

                <div className="flex justify-end space-x-2 ">
                    <Button variant="outline" onClick={handleModalClose}>
                        Cancel
                    </Button>
                    <Button loading={updateTransactionLimitLoading} onClick={handleSave}>
                        Save changes
                    </Button>
                </div>
            </CustomModal>

            {/* MFA Verification Modal for Transaction Limits Adjustment */}
            {mfaVerificationState.showMfaModal && teamMember && (
                <SettingsMfaVerification
                    userMfaType={teamMember?.preferredMfaMethod}
                    onClose={handleMfaVerificationClose}
                    isOpen={true}
                    onVerified={handleMfaVerified}
                    email={teamMember?.email}
                    phoneNumber={teamMember?.phoneNumber}
                />
            )}
        </>
    );
}
