"use client";

/**
 * EditableField component
 *
 * A versatile form field component that can be displayed in both view and edit modes.
 * Supports text inputs, dropdowns, and date pickers with validation.
 * Now includes 2FA support for secure field updates.
 *
 * Features:
 * - Toggle between view and edit modes
 * - Form validation using Formik and Yup
 * - Support for required fields
 * - Support for different input types (text, dropdown, date picker)
 * - Customizable validation schemas
 * - Accessible design with appropriate ARIA attributes
 * - 2FA verification before saving (when user has MFA enabled)
 */
import React, { useState, useCallback, useEffect } from "react";
import { Formik, Form, type FormikValues } from "formik";
import * as Yup from "yup";
import { Button } from "@/components/common/buttonv3";
import { PencilIcon2 } from "@/components/icons/settings";
import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import DatePicker from "@/components/common/date-picker";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";
import { clearGetTeamMemberState } from "@/redux/slices/transferMfaSlice";
import SettingsMfaVerification from "../components/settings-mfa-verification";
import { sendFeedback } from "@/functions/feedback";

interface EditableFieldProps {
    label: string;
    value: string;
    name: string;
    isDropdown?: boolean;
    isDatePicker?: boolean;
    onSave: (value: string) => void;
    onSaveWithToken?: (value: string, token: string) => void;
    disabled?: boolean;
    required?: boolean;
    size?: "sm" | "md";
    hint?: string;
    className?: string;
    locked?: boolean;
    options?: { label: string; value: string }[];
    type?: string;
    emptyValueText?: string;
    validationSchema?: Yup.ObjectSchema<
        {
            [key: string]: Yup.StringSchema;
        },
        Yup.AnyObject,
        {
            [key: string]: string;
        },
        ""
    >;
    requireMfa?: boolean;
}

export function EditableField({
    label,
    name,
    value: initialValue,
    onSaveWithToken,
    disabled = false,
    required = false,
    hint,
    isDropdown = false,
    isDatePicker = false,
    locked = false,
    type = "text",
    size = "sm",
    options,
    className,
    emptyValueText,
    validationSchema,
}: Readonly<EditableFieldProps>) {
    const dispatch = useAppDispatch();
    const [isEditing, setIsEditing] = useState(false);
    const [localDateValue, setLocalDateValue] = useState(initialValue);
    const [pendingValue, setPendingValue] = useState<string | null>(null);
    const [showMfaModal, setShowMfaModal] = useState(false);
    const [isCheckingMfa, setIsCheckingMfa] = useState(false);

    // Get team member details for MFA verification
    const { success: teamMemberSuccess } = useAppSelector((state) => state.transferMfaSlice.getTeamMemberDetails);
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);
    const mfaEnabled = teamMember?.mfaStatus;

    // Update local state when prop values change
    useEffect(() => {
        setLocalDateValue(initialValue);
    }, [initialValue]);

    /**
     * Resets all states to their initial values
     * This should be called after successful submission or when aborting the process
     */
    const resetAllComponentStates = useCallback(() => {
        setPendingValue(null);
        setIsCheckingMfa(false);
        setShowMfaModal(false);
        setIsEditing(false);
        setLocalDateValue(initialValue);
        // Reset Redux states
        dispatch(resetAllStates());
        dispatch(clearGetTeamMemberState());
    }, [dispatch, initialValue]);

    /**
     * Handles form submission when the save button is clicked
     * @param values - The form values from Formik
     */
    const handleSubmit = useCallback(
        (values: FormikValues) => {
            const newValue = values[name] as string;

            if (onSaveWithToken) {
                // Store the pending value and check MFA
                setPendingValue(newValue);
                setIsCheckingMfa(true);
                dispatch(getTeamMemberDetails());
            } else {
                // If no token handler is provided, show error
                sendFeedback(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            }
        },
        [name, onSaveWithToken, dispatch]
    );

    /**
     * Handles MFA verification completion
     */
    const handleMfaVerified = useCallback(
        (token: string) => {
            if (pendingValue && onSaveWithToken) {
                onSaveWithToken(pendingValue, token);
            }
            // Reset all states immediately after token use
            resetAllComponentStates();
        },
        [pendingValue, onSaveWithToken, resetAllComponentStates]
    );

    /**
     * Handles MFA verification modal close
     */
    const handleMfaClose = useCallback(() => {
        // Reset all states when modal is closed
        resetAllComponentStates();
    }, [resetAllComponentStates]);
    /**
     * Check if MFA is required and show modal
     */
    useEffect(() => {
        if (isCheckingMfa && teamMemberSuccess && teamMember && mfaEnabled) {
            setShowMfaModal(true);
        } else if (isCheckingMfa && teamMemberSuccess && (!teamMember || !mfaEnabled)) {
            // If MFA is not enabled, reset states and show error
            resetAllComponentStates();
            sendFeedback(
                "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                "error",
                undefined,
                "2FA Required"
            );
        }
    }, [isCheckingMfa, teamMemberSuccess, teamMember, mfaEnabled, resetAllComponentStates]);

    /**
     * Creates the initial values object for Formik
     * @returns An object with the field name as the key and initialValue as the value
     */
    const createInitialValues = useCallback(() => ({ [name]: initialValue }) as FormikValues, [name, initialValue]);

    /**
     * Creates a validation schema for the field
     * @returns A Yup validation schema
     */
    const createValidationSchema = useCallback(() => {
        if (validationSchema) return validationSchema;

        const schemaObj: Record<string, Yup.StringSchema> = {};

        if (type === "tel") {
            schemaObj[name] = Yup.string()
                .matches(/^\d+$/, "Phone number must contain only digits")
                .max(11, "Phone number must be at most 11 digits")
                .required("This field is required");
        } else if (type === "email") {
            // Enhanced email validation with a more comprehensive regex pattern
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            schemaObj[name] = Yup.string()
                .matches(emailRegex, "Please enter a valid email address")
                .test("email-format", "Please enter a valid email address", (value) => {
                    if (!value) return true; // Skip validation if empty (handled by required)

                    // Additional validation checks
                    if (value.length > 254) return false; // Too long
                    if (value.indexOf("@") <= 0) return false; // No local part

                    const parts = value.split("@");
                    if (parts.length !== 2) return false; // Multiple @ symbols

                    const [local, domain] = parts;
                    if (local.length > 64) return false; // Local part too long
                    if (domain.length < 3) return false; // Domain too short

                    // Check for consecutive dots
                    if (value.includes("..")) return false;

                    // Check domain has at least one dot
                    if (!domain.includes(".")) return false;

                    // Check TLD is at least 2 characters
                    const tld = domain.split(".").pop() ?? "";
                    if (tld.length < 2) return false;

                    return true;
                })
                .required("This field is required");
        } else {
            schemaObj[name] = Yup.string().required("This field is required");
        }

        return Yup.object(schemaObj);
    }, [validationSchema, name, type]);

    /**
     * Cancels editing mode
     */
    const handleCancel = useCallback(() => {
        // Reset local date value when canceling
        setLocalDateValue(initialValue);
        // Reset all states
        resetAllComponentStates();
    }, [initialValue, resetAllComponentStates]);

    /**
     * Enables editing mode
     */
    const handleEdit = useCallback(() => {
        // if (!mfaEnabled) {
        //     sendFeedback(
        //         "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
        //         "error",
        //         undefined,
        //         "2FA Required"
        //     );
        //     return;
        // }
        setIsEditing(true);
    }, []);

    /**
     * Handles date changes with immediate UI updates
     */
    const handleDateChange = useCallback((date: Date | undefined) => {
        const formattedDate = date ? date.toISOString().split("T")[0] : "";

        // Update local state immediately for UI feedback
        setLocalDateValue(formattedDate);
    }, []);

    /**
     * Validates if the user is above 18 years old based on date of birth
     */
    const validateAge = useCallback((dateValue: string): boolean => {
        if (!dateValue) return false;

        const birthDate = new Date(dateValue);
        const today = new Date();

        // Calculate the difference in years
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        // Adjust age if birthday hasn't occurred this year
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        return age > 18;
    }, []);

    /**
     * Saves the date and exits edit mode
     */
    const handleDateSave = useCallback(() => {
        // Check if this is a date of birth field and validate age
        if (name === "date-of-birth" && !validateAge(localDateValue)) {
            sendFeedback(
                "You must be above 18 years old. Please enter a valid date of birth.",
                "error",
                undefined,
                "Invalid Age"
            );
            return;
        }

        if (onSaveWithToken) {
            // Store the pending value and check MFA
            setPendingValue(localDateValue);
            setIsCheckingMfa(true);
            dispatch(getTeamMemberDetails());
        } else {
            // If no token handler is provided, show error
            sendFeedback(
                "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                "error",
                undefined,
                "2FA Required"
            );
        }
    }, [localDateValue, onSaveWithToken, dispatch, name, validateAge]);

    // Add this function after the handleDateSave function
    const handlePhoneInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        // Only allow digits
        const value = e.target.value.replace(/\D/g, "");

        // Limit to 11 digits
        if (value.length <= 11) {
            e.target.value = value;
        } else {
            e.target.value = value.slice(0, 11);
        }
    }, []);

    if (!isEditing) {
        // Use local state for displaying date values
        const displayValue = isDatePicker ? localDateValue : initialValue;

        return (
            <div className="flex items-center justify-between">
                <div className="space-y-1">
                    <h3 className="text-base font-medium text-black" id={`${name}-label`}>
                        {label}
                    </h3>
                    <p className="text-base text-subText" aria-labelledby={`${name}-label`}>
                        {(displayValue || emptyValueText) ?? `No ${label.toLowerCase()} available`}
                    </p>
                </div>
                {!disabled && (
                    <Button
                        variant="text-neutral"
                        onClick={handleEdit}
                        className="text-gray-400 hover:text-gray-500"
                        aria-label={`Edit ${label.toLowerCase()}`}
                    >
                        <PencilIcon2 className="h-5 w-5" data-testid="pencil-icon" />
                    </Button>
                )}
            </div>
        );
    }

    if (isDropdown && isEditing) {
        return (
            <Formik
                initialValues={createInitialValues()}
                validationSchema={createValidationSchema()}
                onSubmit={handleSubmit}
            >
                {(formik) => (
                    <Form className="space-y-4">
                        <div className="space-y-2">
                            <Dropdown
                                options={options}
                                name={name}
                                label={label}
                                size={size}
                                formik={formik}
                                className={className}
                                aria-labelledby={`${name}-label`}
                            />
                        </div>
                        <div className="flex justify-end space-x-2 items-center">
                            <Button
                                type="button"
                                variant="outline"
                                aria-label={`Cancel ${label.toLowerCase()} edit`}
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                            <Button aria-label={`Save ${label.toLowerCase()}`} type="submit">
                                Save
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        );
    }

    if (isDatePicker && isEditing) {
        return (
            <>
                <div className="space-y-4 py-4">
                    <DatePicker
                        label={label}
                        value={localDateValue ? new Date(localDateValue) : undefined}
                        onChange={handleDateChange}
                        error=""
                        hint={hint}
                        disable={false}
                        aria-labelledby={`${name}-label`}
                    />
                    <div className="flex justify-end space-x-2 items-center">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancel}
                            aria-label={`Cancel ${label.toLowerCase()} edit`}
                        >
                            Cancel
                        </Button>
                        <Button type="button" onClick={handleDateSave} aria-label={`Save ${label.toLowerCase()}`}>
                            Save
                        </Button>
                    </div>
                </div>

                {/* MFA Verification Modal */}
                {showMfaModal && teamMember && (
                    <SettingsMfaVerification
                        userMfaType={teamMember?.preferredMfaMethod}
                        onClose={handleMfaClose}
                        isOpen={true}
                        onVerified={handleMfaVerified}
                        email={teamMember?.email}
                        phoneNumber={teamMember?.phoneNumber}
                    />
                )}
            </>
        );
    }

    return (
        <>
            <div className="space-y-4 py-4">
                <Formik
                    initialValues={createInitialValues()}
                    validationSchema={createValidationSchema()}
                    onSubmit={handleSubmit}
                    enableReinitialize={true}
                >
                    {(formik) => (
                        <Form className="space-y-4">
                            <div className="space-y-2">
                                <LabelInput
                                    formik={formik}
                                    name={name}
                                    label={label}
                                    required={required}
                                    hint={hint}
                                    locked={locked}
                                    type={type}
                                    placeholder={`Enter ${label.toLowerCase()}`}
                                    aria-labelledby={`${name}-label`}
                                    onInput={type === "tel" ? handlePhoneInput : undefined}
                                />
                            </div>
                            <div className="flex justify-end space-x-2 items-center">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    aria-label={`Cancel ${label.toLowerCase()} edit`}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" aria-label={`Save ${label.toLowerCase()}`}>
                                    Save
                                </Button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>

            {/* MFA Verification Modal */}
            {showMfaModal && teamMember && (
                <SettingsMfaVerification
                    userMfaType={teamMember?.preferredMfaMethod}
                    onClose={handleMfaClose}
                    isOpen={true}
                    onVerified={handleMfaVerified}
                    email={teamMember?.email}
                    phoneNumber={teamMember?.phoneNumber}
                />
            )}
        </>
    );
}
