import React from "react";
import { But<PERSON> } from "@/components/common/buttonv3";
import { PassCodeIcon, PinCodeIcon } from "@/components/icons/settings";

export interface LoginTransactionsSectionProps {
    onChangePassword: () => void;
    onChangePin: () => void;
}

const LoginTransactionsSection: React.FC<LoginTransactionsSectionProps> = ({ onChangePassword, onChangePin }) => (
    <div className="space-y-4 border-b-[#E3E5E8] border-b pb-6">
        <h2 className="text-lg font-semibold text-black">Log in & Transactions</h2>

        <div className="flex items-center justify-between">
            <div className="space-y-1">
                <div className="flex items-center gap-3">
                    <PassCodeIcon />
                    <div>
                        <h3 className="text-[16px] font-semibold text-black">Password</h3>
                        <p className="text-sm text-subText">Change your login password</p>
                    </div>
                </div>
            </div>
            <Button data-testid="change-password-button" onClick={onChangePassword} variant="outline">
                Change Password
            </Button>
        </div>

        <div className="flex items-center justify-between pt-2">
            <div className="space-y-1">
                <div className="flex items-center gap-3">
                    <PinCodeIcon />
                    <div>
                        <h3 className="text-[16px] font-semibold text-black">Transaction PIN</h3>
                        <p className="text-sm text-subText">Use an app to get authentication codes when prompted</p>
                    </div>
                </div>
            </div>
            <Button data-testid="change-pin-button" onClick={onChangePin} variant="outline">
                Change PIN
            </Button>
        </div>
    </div>
);

export default LoginTransactionsSection;
