import React from "react";
import { format, parseISO } from "date-fns";
import CustomModal from "@/components/common/custom-modal";
import useBrowserIcon from "@/hooks/useBrowserIcon";
import { NoticeIcon } from "@/components/icons/settings";
import { Button } from "@/components/common/buttonv3";
import { useTrustedDevices } from "@/hooks/useTrustedDevices";
import { TrustedDevicesData } from "@/redux/types/trustedDevices";

export interface ManageDevicesModalProps {
    isOpen: boolean;
    onRequestClose: () => void;
}

const ManageDevicesModal: React.FC<ManageDevicesModalProps> = ({ isOpen, onRequestClose }) => {
    const {
        trustedDevices,
        trustedDevicesLoading,
        removeDeviceLoading,
        removeAllDevicesLoading,
        removingDeviceId,
        handleRemoveDevice,
        handleRemoveAllDevices,
    } = useTrustedDevices();

    return (
        <CustomModal isOpen={isOpen} onRequestClose={onRequestClose} title="Manage trusted devices" width="522px">
            <p className="text-center w-full text-subText text-[14px] -mt-4">
                These devices can currently access your account. Remove any devices you don't recognize or no longer
                use.
            </p>

            <div className="space-y-4 mt-4">
                {/* Loading state */}
                {trustedDevicesLoading && (
                    <div className="flex justify-center py-4">
                        <div
                            data-testid="loading-spinner"
                            className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
                        />
                    </div>
                )}

                {/* Empty state */}
                {!trustedDevicesLoading && trustedDevices?.length === 0 && (
                    <div className="text-center py-4 text-subText">No trusted devices found</div>
                )}

                {/* Device list */}
                {!trustedDevicesLoading &&
                    trustedDevices &&
                    trustedDevices.length > 0 &&
                    trustedDevices.map((device) => (
                        <DeviceItem
                            key={device.id}
                            device={device}
                            onRemove={handleRemoveDevice}
                            isRemoving={removeDeviceLoading && removingDeviceId === device.id}
                            isDisabled={removeDeviceLoading && removingDeviceId !== device.id}
                        />
                    ))}
            </div>

            <div className="flex gap-3 items-center mt-8 bg-[#F9F9FA] p-3 rounded-[12px]">
                <span>
                    <NoticeIcon baseColor="#90909D" width="20px" height="20px" />
                </span>
                <p className="text-subText text-[13px] font-normal">
                    Don't recognise any of these devices? Remove them from this list and change your password
                    immediately.
                </p>
            </div>

            <Button
                fullWidth
                className="w-full mt-6"
                onClick={handleRemoveAllDevices}
                loading={removeAllDevicesLoading ? true : undefined}
                disabled={removeAllDevicesLoading || trustedDevices?.length === 0}
            >
                Remove all devices
            </Button>
        </CustomModal>
    );
};

interface DeviceItemProps {
    device: TrustedDevicesData;
    onRemove: (id: number) => void;
    isRemoving: boolean;
    isDisabled: boolean;
}

const DeviceItem: React.FC<DeviceItemProps> = ({ device, onRemove, isRemoving, isDisabled }) => {
    const icon = useBrowserIcon(device.browser || "Chrome");

    // Format the trusted until date
    const formattedDate = device.trustedUntil ? format(parseISO(device.trustedUntil), "MMMM d, yyyy") : "N/A";

    return (
        <div className="flex items-center justify-between bg-[#F9F9FA] rounded-[12px] p-4">
            <div className="flex items-center gap-3">
                {icon}
                <div>
                    <h3 className="text-base font-medium text-black">
                        {device.browser} ({device.os})
                    </h3>
                    {device.city && device.country ? (
                        <p className="text-sm text-subText">
                            {device.city}, {device.country}
                        </p>
                    ) : (
                        <p className="text-sm text-subText">
                            {device.city ?? ""} {device.country ?? ""}
                        </p>
                    )}
                    <p className="text-sm text-subText">Trust until {formattedDate}</p>
                </div>
            </div>

            <Button
                variant="outline"
                size="medium"
                className="bg-white"
                onClick={() => onRemove(device.id)}
                loading={isRemoving ? true : undefined}
                disabled={isDisabled}
            >
                Remove
            </Button>
        </div>
    );
};

export default ManageDevicesModal;
