import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import CustomModal from "@/components/common/custom-modal";
import LabelInput from "@/components/common/label-input";
import { Button } from "@/components/common/buttonv3";
import { NoticeIcon } from "@/components/icons/settings";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { changeTransactionPin, changeTransactionPinWithToken } from "@/redux/actions/securityActions";
import { securityActions } from "@/redux/slices/securitySlice";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";
import ResetPinModal from "./ResetPinModal";

export interface ChangePinModalProps {
    isOpen: boolean;
    onRequestClose: () => void;
    token?: string | null;
    onError?: () => void; // Callback to handle errors by closing modals and resetting state
}

const ChangePinModal: React.FC<ChangePinModalProps> = ({ isOpen, onRequestClose, token, onError }) => {
    const dispatch = useAppDispatch();
    const { changePin } = useAppSelector((state) => state.security);
    const [isResetPinModalOpen, setIsResetPinModalOpen] = useState(false);

    const formik = useFormik({
        initialValues: { currentPIN: "", newPIN: "" },
        validationSchema: yup.object({
            currentPIN: yup
                .string()
                .required("Current PIN is required")
                .matches(/^\d{6}$/, "PIN must be exactly 6 digits"),
            newPIN: yup
                .string()
                .required("New PIN is required")
                .matches(/^\d{6}$/, "PIN must be exactly 6 digits"),
        }),
        onSubmit: (values) => {
            if (token) {
                // Use token-based PIN change
                dispatch(
                    changeTransactionPinWithToken({
                        oldPin: values.currentPIN,
                        newPin: values.newPIN,
                        token: token,
                    })
                );
            } else {
                // Use regular PIN change
                dispatch(changeTransactionPin({ oldPin: values.currentPIN, newPin: values.newPIN }));
            }
        },
    });

    useEffect(() => {
        if (changePin.success) {
            onRequestClose();
            dispatch(securityActions.clearState("changePin"));
            dispatch(resetAllStates());
            formik.resetForm();
        }
    }, [changePin.success, dispatch, formik, onRequestClose]);

    // Handle error state for change PIN - if there's an error, close modals and reset state
    useEffect(() => {
        if (changePin.error && onError) {
            // Clear the error state
            dispatch(securityActions.clearState("changePin"));
            // Reset the form before closing
            formik.resetForm();
            // Close modals and reset state in parent component
            onError();
        }
    }, [changePin.error, dispatch, onError, formik]);

    const handleClose = () => {
        onRequestClose();
        formik.resetForm();
    };

    const handleResetPinModalClose = () => {
        setIsResetPinModalOpen(false);
        // The ResetPinModal will handle its own form reset via its handleClose function
    };

    return (
        <CustomModal isOpen={isOpen} onRequestClose={handleClose} title="Change transaction PIN" width="522px">
            <p className="text-center w-full text-subText text-[14px] -mt-4">
                This PIN will be required to authorize all transactions
            </p>
            <form id="changePinForm" onSubmit={formik.handleSubmit} className="space-y-6 mt-6">
                <LabelInput
                    formik={formik}
                    name="currentPIN"
                    label="Current PIN"
                    placeholder="Enter 6-digit PIN"
                    type="password"
                    inputMode="numeric"
                    pattern="[0-9]*"
                />
                <LabelInput
                    formik={formik}
                    name="newPIN"
                    label="New PIN"
                    placeholder="Enter 6-digit PIN"
                    type="password"
                    inputMode="numeric"
                    pattern="[0-9]*"
                />
            </form>
            <div className="flex gap-1 items-center mt-8">
                <NoticeIcon />
                <p className="text-black text-base font-semibold">Important</p>
            </div>
            <div className="mt-4 text-sm text-black bg-[#F9F9FA] rounded-[12px] p-4">
                <ul className="list-disc space-y-1 pl-5">
                    <li>You'll use this PIN to authorize all transactions</li>
                    <li>Make sure it's different from your phone's PIN</li>
                    <li>Keep this PIN private, and don't share it with anyone</li>
                </ul>
            </div>
            <div className="flex w-full mt-8 justify-end gap-3 items-center">
                <Button onClick={handleClose} variant="outline">
                    Cancel
                </Button>
                <Button
                    form="changePinForm"
                    type="submit"
                    disabled={changePin.loading || !formik.isValid}
                    loading={changePin.loading}
                >
                    Change PIN
                </Button>
            </div>

            {/* Forget PIN section */}
            <div className="flex justify-center mt-4">
                <p className="text-sm text-subText">
                    Forgot pin?{" "}
                    <button
                        type="button"
                        onClick={() => setIsResetPinModalOpen(true)}
                        className="text-primary font-medium hover:underline focus:outline-none"
                    >
                        Reset it
                    </button>
                </p>
            </div>

            {/* Reset PIN Modal */}
            <ResetPinModal
                isOpen={isResetPinModalOpen}
                onRequestClose={handleResetPinModalClose}
                token={token}
                onError={onError}
                handleCloseParent={handleClose}
            />
        </CustomModal>
    );
};

export default ChangePinModal;
