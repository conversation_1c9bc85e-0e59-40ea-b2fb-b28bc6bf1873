/**
 * ChangePasswordModal component
 *
 * Purpose: Provides a secure modal interface for users to change their account password
 * with proper validation and API integration using simplified single-step flow.
 *
 * Functionality:
 * - Renders a modal with form fields for current password, new password, and confirmation
 * - Implements comprehensive password validation (length, uppercase, numbers, special characters)
 * - Handles the simplified password change flow with direct password validation
 * - Manages loading states, success handling, and error feedback
 * - Automatically closes modal and resets form on successful password change
 * - Uses memoization techniques for optimal performance
 *
 * Dependencies:
 * - React hooks for lifecycle management and memoization
 * - Formik and Yup for form handling and validation
 * - Redux for state management and API dispatch
 * - Custom UI components (CustomModal, LabelInput, Button)
 * - Settings actions for password change API calls
 *
 * Usage: Triggered from the Security settings page when user clicks "Change Password"
 */
import React, { useEffect, useCallback } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import CustomModal from "@/components/common/custom-modal";
import LabelInput from "@/components/common/label-input";
import { Button } from "@/components/common/buttonv3";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { changePassword, changePasswordWithToken } from "@/redux/actions/settingsActions";
import { clearChangePasswordError, clearChangePasswordSuccess } from "@/redux/slices/settingsSlice";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";

export interface ChangePasswordModalProps {
    isOpen: boolean;
    onRequestClose: () => void;
    token?: string | null;
    onError?: () => void; // Callback to handle errors by closing modals and resetting state
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({ isOpen, onRequestClose, token, onError }) => {
    const dispatch = useAppDispatch();
    const { changePassword: changePasswordState } = useAppSelector((state) => state.settings);

    /**
     * Memoized form submission handler using useCallback for performance optimization
     * Handles the simplified single API call for password change
     */
    const handleSubmit = useCallback(
        async (values: { currentPassword: string; newPassword: string; confirmPassword: string }) => {
            try {
                if (token) {
                    // Use token-based password change
                    await dispatch(
                        changePasswordWithToken({
                            newPassword: values.newPassword,
                            oldPassword: values.currentPassword,
                            token: token,
                        })
                    ).unwrap();
                } else {
                    // Use regular password change
                    await dispatch(
                        changePassword({
                            newPassword: values.newPassword,
                            oldPassword: values.currentPassword,
                        })
                    ).unwrap();
                }
            } catch {
                // Error handling managed by Redux thunk
            }
        },
        [dispatch, token]
    );

    const formik = useFormik({
        initialValues: {
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
        },
        validationSchema: yup.object({
            currentPassword: yup.string().required("Current password is required"),
            newPassword: yup
                .string()
                .required("Password is required")
                .min(14, "Password must be at least 14 characters")
                .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
                .matches(/\d/, "Password must contain at least one number")
                .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
            confirmPassword: yup
                .string()
                .required("Confirm password is required")
                .oneOf([yup.ref("newPassword")], "Passwords must match"),
        }),
        onSubmit: handleSubmit,
    });

    /**
     * Effect to handle successful password change
     * Uses optional chaining for null safety
     */
    useEffect(() => {
        if (changePasswordState?.success) {
            onRequestClose();
            dispatch(clearChangePasswordSuccess());
            dispatch(resetAllStates());
            formik.resetForm();
        }
    }, [changePasswordState?.success, dispatch, formik, onRequestClose]);

    /**
     * Effect to handle error state - if there's an error, close modals and reset state
     */
    useEffect(() => {
        if (changePasswordState?.error && onError) {
            // Clear the error state
            dispatch(clearChangePasswordError());
            // Reset the form before closing
            formik.resetForm();
            // Close modals and reset state in parent component
            onError();
        }
    }, [changePasswordState?.error, dispatch, onError, formik]);

    /**
     * Memoized modal close handler with cleanup
     * Uses useCallback for performance optimization
     */
    const handleClose = useCallback(() => {
        onRequestClose();
        dispatch(clearChangePasswordError());
        formik.resetForm();
    }, [onRequestClose, dispatch, formik]);

    return (
        <CustomModal
            isOpen={isOpen}
            onRequestClose={handleClose}
            title="Change Password"
            width="522px"
            data-testid="password-modal"
        >
            <p className="text-center w-full text-subText text-[14px] -mt-4">
                Choose a unique and strong password to protect your account.
            </p>
            <form onSubmit={formik.handleSubmit} className="space-y-6 mt-6">
                <LabelInput formik={formik} name="currentPassword" label="Current password" type="password" />
                <LabelInput
                    formik={formik}
                    name="newPassword"
                    label="New password"
                    placeholder="Min. of 14 characters"
                    type="password"
                />
                <LabelInput
                    formik={formik}
                    name="confirmPassword"
                    label="Confirm new password"
                    placeholder="Min. of 14 characters"
                    type="password"
                />
                <div className="flex w-full justify-end gap-3 items-center">
                    <Button onClick={handleClose} variant="outline">
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        disabled={changePasswordState?.loading || !formik.isValid}
                        loading={changePasswordState?.loading}
                    >
                        Change password
                    </Button>
                </div>
            </form>
        </CustomModal>
    );
};

export default ChangePasswordModal;
