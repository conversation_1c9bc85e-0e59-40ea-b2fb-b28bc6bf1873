"use client";

import React, { useState, useEffect } from "react";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { userAxios } from "@/api/axios";
import { fetchPersonalInfo } from "@/redux/actions/settingsActions";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import OTPContainer from "@/components/page-components/auth/otp-container";
import { maskNumber } from "@/functions/stringManipulations";

// Custom styles to ensure the modal has the highest z-index
const customModalStyles = `
  .ReactModal__Overlay--after-open {
    z-index: 99999 !important;
  }
  .ReactModal__Content--after-open {
    z-index: 99999 !important;
  }
`;

interface SettingsSMSSetupModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSetupComplete: () => void;
}

const SettingsSMSSetupModal: React.FC<SettingsSMSSetupModalProps> = ({ isOpen, onClose, onSetupComplete }) => {
    const dispatch = useAppDispatch();
    const user = useAppSelector((state) => state.user.user);
    const userEmail = user?.email;

    // Get phone number from settings personal info
    const { data: personalInfo, loading: personalInfoLoading } = useAppSelector((state) => state.settings.personalInfo);
    const userPhoneNumber = personalInfo?.phoneNumber ?? "";

    const [otpValue, setOtpValue] = useState("");
    const [isVerifying, setIsVerifying] = useState(false);
    const [isSendingOTP, setIsSendingOTP] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [phoneNumber, setPhoneNumber] = useState(userPhoneNumber);

    // Fetch personal info when modal opens to get phone number
    useEffect(() => {
        if (isOpen && !personalInfo && !personalInfoLoading) {
            dispatch(fetchPersonalInfo());
        }
    }, [isOpen, personalInfo, personalInfoLoading, dispatch]);

    // Update phone number when personal info is loaded
    useEffect(() => {
        if (userPhoneNumber) {
            setPhoneNumber(userPhoneNumber);
        }
    }, [userPhoneNumber]);

    // Send OTP to phone number - using same endpoint as signup
    const handleSendOTP = async (phoneNumber: string) => {
        setIsSendingOTP(true);
        setError(null);
        try {
            // Use same endpoint and format as signup
            await userAxios.post("/v1/otp-manager", {
                receiver: phoneNumber.length === 10 && `0${phoneNumber}`,
                receiverType: "SMS",
            });

            sendFeedback("OTP sent to your phone number", "success");
        } catch (error) {
            setError("Failed to send OTP. Please try again.");
            sendCatchFeedback(error);
        } finally {
            setIsSendingOTP(false);
        }
    };

    // Verify OTP and update preferred MFA method
    const handleVerifyOTP = async (phoneNumber: string, otp: string) => {
        setIsVerifying(true);
        setError(null);
        try {
            // First validate the OTP
            await userAxios.post("/v1/otp-manager/validate", {
                receiver: phoneNumber.length === 10 && `0${phoneNumber}`,
                otp: otp,
            });

            // Then update the preferred MFA method
            if (userEmail) {
                await userAxios.patch(`/v1/team-members?emailAddress=${userEmail}&mfaMethod=SMS`);
            }

            sendFeedback("SMS 2FA setup completed successfully", "success");
            onSetupComplete();
        } catch (error) {
            setError("Invalid OTP. Please try again.");
            sendCatchFeedback(error);
            setOtpValue(""); // Clear the OTP input
        } finally {
            setIsVerifying(false);
        }
    };

    // Handle OTP submission
    const handleSubmit = async () => {
        if (otpValue.length === 6) {
            await handleVerifyOTP(phoneNumber, otpValue);
        }
    };

    // Handle resend OTP
    const handleResend = async () => {
        await handleSendOTP(phoneNumber);
    };

    // Send OTP when component mounts and phone number is available
    useEffect(() => {
        if (isOpen && phoneNumber) {
            handleSendOTP(phoneNumber);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen, phoneNumber]);

    return (
        <>
            {/* Inject custom styles to ensure highest z-index */}
            {isOpen && <style>{customModalStyles}</style>}

            <FullScreenDrawer
                isOpen={isOpen}
                onClose={onClose}
                title="Set up SMS authentication"
                showExitConfirmation={false}
                disablePadding={true}
                className="!z-[99999]"
            >
                <div className="flex items-center justify-center min-h-full px-primary py-[100px] bg-white">
                    <OTPContainer
                        value={otpValue}
                        setValue={setOtpValue}
                        onSubmit={handleSubmit}
                        onResend={handleResend}
                        loading={isVerifying}
                        resendLoading={isSendingOTP}
                        variant="withButton"
                        title="Verify your phone number"
                        type="phone"
                        error={error}
                        subtitle={
                            <p className="text-[#3A3A41]">
                                Enter the 6-digit code sent to your registered phone number ending in{" "}
                                <b>{phoneNumber ? maskNumber(phoneNumber) : "--"}</b>
                            </p>
                        }
                        buttonText="Complete setup"
                        resendText="Resend code"
                    />
                </div>
            </FullScreenDrawer>
        </>
    );
};

export default SettingsSMSSetupModal;
