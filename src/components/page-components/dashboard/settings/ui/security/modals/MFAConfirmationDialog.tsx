"use client";

import React from "react";
import { Button } from "@/components/common/buttonv3";
import ReactModal from "react-modal";
import CloseX from "@/components/common/close-x";

interface MFAConfirmationDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    loading?: boolean;
    title: string;
    subtitle?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
    confirmButtonVariant?: "primary" | "danger";
    width?: string;
    height?: string;
}

const MFAConfirmationDialog: React.FC<MFAConfirmationDialogProps> = ({
    isOpen,
    onClose,
    onConfirm,
    loading = false,
    title,
    subtitle,
    confirmButtonText = "Confirm",
    cancelButtonText = "Cancel",
    confirmButtonVariant = "primary",
    width = "464px",
    height = "auto",
}) => {
    const customStyles: ReactModal.Styles = {
        content: {
            top: "50%",
            left: "50%",
            right: "auto",
            bottom: "auto",
            marginRight: "-50%",
            transform: "translate(-50%, -50%)",
            overflow: "auto",
            width: width,
            maxWidth: "90vw",
            maxHeight: "95vh",
            height: height,
            backgroundColor: "#fff",
            color: "#000",
            transition: "all 0.3s",
            border: "none",
            borderRadius: 12,
            padding: 0,
        },
        overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            overscrollBehavior: "contain",
            zIndex: 100,
        },
    };

    return (
        <ReactModal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={customStyles}
            closeTimeoutMS={500}
            shouldCloseOnOverlayClick={true}
            ariaHideApp={false}
        >
            <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-black">{title}</h2>
                    <CloseX onClick={onClose} />
                </div>

                {/* Content */}
                <div className="space-y-4">
                    {subtitle && <p className="text-sm text-subText leading-relaxed">{subtitle}</p>}

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3 pt-4">
                        <Button variant="outline" onClick={onClose} disabled={loading}>
                            {cancelButtonText}
                        </Button>
                        <Button
                            variant={confirmButtonVariant === "danger" ? "destructive" : "primary"}
                            onClick={onConfirm}
                            loading={loading}
                            disabled={loading}
                        >
                            {confirmButtonText}
                        </Button>
                    </div>
                </div>
            </div>
        </ReactModal>
    );
};

export default MFAConfirmationDialog;
