"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import CustomModal from "@/components/common/custom-modal";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import Base64QRCode from "@/components/common/qr-code";
import { CopyIcon } from "@/components/icons/auth";
import { copyToClipboard } from "@/functions/stringManipulations";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppSelector } from "@/redux/hooks";
import { userAxios } from "@/api/axios";
import { handleError } from "@/lib/utils";
import { MFAMethod } from "@/redux/actions/settingsTwoFa";

interface SettingsAuthenticatorModalProps {
    isOpen: boolean;
    onClose: () => void;
    onBack: () => void;
    onSetupComplete: (method: MFAMethod) => void;
}

const SettingsAuthenticatorModal: React.FC<SettingsAuthenticatorModalProps> = ({
    isOpen,
    onClose,
    onBack,
    onSetupComplete,
}) => {
    const user = useAppSelector((state) => state.user.user);
    const userEmail = user?.email;

    // States for QR code - matching signup implementation
    const [qrCodeData, setQrCodeData] = useState<string | null>(null);
    const [qrCodeSecret, setQrCodeSecret] = useState<string>("BTWIIAP245A7A00");
    const [isLoadingQR, setIsLoadingQR] = useState<boolean>(false);
    const [qrError, setQrError] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

    const formik = useFormik({
        initialValues: {
            authOTP: "",
        },
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            authOTP: yup.string().required("Otp is required"),
        }),
    });

    // Fetch QR code when modal opens - matching signup implementation
    useEffect(() => {
        if (isOpen) {
            fetchQRCode();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    const fetchQRCode = async () => {
        setIsLoadingQR(true);
        setQrError(null);

        try {
            const response = await userAxios.post("/v1/mfa?command=generate-qr-code", {
                email: userEmail,
            });

            const data = response.data;

            if (data.status === "QR_CODE_GENERATED" && data.qrCode) {
                setQrCodeData(data.qrCode);

                if (data.secret) {
                    setQrCodeSecret(data.secret);
                }
            } else {
                throw new Error(data.message ?? "Failed to generate QR code");
            }
        } catch (err) {
            setQrError(handleError(err));
            sendCatchFeedback(handleError(err));
        } finally {
            setIsLoadingQR(false);
        }
    };

    const submitValues = async () => {
        if (!userEmail) return;

        setIsSubmitting(true);
        try {
            // Step 1: Setup authenticator (same as signup) - THIS WAS MISSING!
            await userAxios.post(`/v1/authentication/setup-authenticator/${userEmail}`);

            // Step 2: Verify the OTP to complete MFA setup (same as signup)
            await userAxios.post("/v1/mfa?command=verify-otp&verificationType=setup-mfa", {
                username: userEmail,
                otp: formik.values.authOTP,
            });

            // Step 3: Update preferred MFA method (same endpoint as signup)
            await userAxios.patch(`/v1/team-members?emailAddress=${userEmail}&mfaMethod=AUTHENTICATOR`);

            sendFeedback("Authenticator app setup successfully", "success");
            onSetupComplete("AUTHENTICATOR");
        } catch (error) {
            sendCatchFeedback(error);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Disable submit button logic - matching signup implementation
    const isSubmitDisabled = useMemo(() => !formik.isValid || !formik.dirty, [formik.isValid, formik.dirty]);

    return (
        <CustomModal isOpen={isOpen} onRequestClose={onClose} title="Set up Authenticator app" width="464px">
            <div className="space-y-6">
                {/* Back button */}
                <button
                    onClick={onBack}
                    className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors"
                >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M10 12l-4-4 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
                    </svg>
                    Back to methods
                </button>

                <div className="text-subText text-[0.875rem]">
                    <ul className="list-decimal list-inside">
                        <li className="mb-5">
                            Install an authenticator app. We recommend{" "}
                            <a
                                href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                Google Authenticator
                            </a>{" "}
                            or{" "}
                            <a
                                href="https://www.authy.com/download/"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                Authy
                            </a>{" "}
                            for iOs and Android devices.
                        </li>
                        <li>Scan the QR code below with your authenticator app.</li>

                        <div className="bg-[#F9F9FA] py-3 px-4 items-end flex gap-5 rounded-2xl my-5">
                            {/* QR Code with loading and error states */}
                            {isLoadingQR ? (
                                <div className="w-[112px] h-[112px] flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
                                </div>
                            ) : qrError ? (
                                <div className="w-[112px] h-[112px] flex items-center justify-center text-red-500 text-xs text-center">
                                    <div>
                                        Failed to load QR code
                                        <button className="text-primary underline mt-1 block" onClick={fetchQRCode}>
                                            Retry
                                        </button>
                                    </div>
                                </div>
                            ) : qrCodeData ? (
                                <Base64QRCode base64String={qrCodeData} width="112px" height="112px" />
                            ) : (
                                <div className="w-[112px] h-[112px] flex items-center justify-center text-gray-500 text-xs text-center">
                                    No QR code available
                                </div>
                            )}
                            <div className="flex flex-col gap-1">
                                <p className="text-sm text-[#000]">Trouble scanning? enter the code:</p>
                                <div className="flex gap-[10px] items-center">
                                    <p className="text-primary text-sm">{qrCodeSecret}</p>
                                    <button
                                        aria-label="Copy authenticator code"
                                        onClick={async () => await copyToClipboard(qrCodeSecret)}
                                        data-testid="copy-authenticator"
                                    >
                                        <CopyIcon />
                                    </button>
                                </div>
                            </div>
                        </div>
                        <li>Enter the 6 digit verification code on your authenticator app below.</li>
                        <LabelInput
                            className="w-[200px] mt-3"
                            name="authOTP"
                            formik={formik}
                            data-testid="authenticator-input"
                            disabled={isLoadingQR || !qrCodeData}
                        />
                    </ul>
                    <div className="flex justify-end items-center gap-3 mt-6">
                        <Button variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button
                            onClick={() => formik.handleSubmit()}
                            disabled={isSubmitDisabled || isLoadingQR || !qrCodeData}
                            loading={isSubmitting}
                        >
                            Set up
                        </Button>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default SettingsAuthenticatorModal;
