"use client";

import React, { useState } from "react";
import CustomModal from "@/components/common/custom-modal";
import { MFAMethod } from "@/redux/actions/settingsTwoFa";
import { FileShield, MessageIcon, PhoneIcon } from "@/components/icons/auth";
import SettingsAuthenticatorModal from "./SettingsAuthenticatorModal";
import SettingsSMSSetupModal from "./SettingsSMSSetupModal";
import SettingsSecurityQuestionsModal from "./SettingsSecurityQuestionsModal";

interface MFAOption {
    method: MFAMethod;
    icon: React.ReactNode;
    title: string;
    subtitle: string;
    recommended?: boolean;
}

const mfaOptions: MFAOption[] = [
    {
        method: "AUTHENTICATOR",
        icon: <PhoneIcon className="mt-1" />,
        title: "Authenticator app",
        subtitle: "Use an authenticator app to get authentication codes when prompted",
        recommended: true,
    },
    {
        method: "SMS",
        icon: <MessageIcon className="mt-1" />,
        title: "SMS OTP",
        subtitle: "Get authentication codes sent to your registered mobile number",
    },
    {
        method: "SECURITY_QUESTION",
        icon: <FileShield className="mt-1" />,
        title: "Security question",
        subtitle: "Answer a question to verify your identity when accessing your account",
    },
];

interface SettingsMFASetupModalProps {
    isOpen: boolean;
    onClose: () => void;
    preSelectedMethod?: MFAMethod | null;
    isFirstTimeSetup: boolean;
    onSetupComplete: (method: MFAMethod) => void;
}

const SettingsMFASetupModal: React.FC<SettingsMFASetupModalProps> = ({
    isOpen,
    onClose,
    preSelectedMethod,
    isFirstTimeSetup,
    onSetupComplete,
}) => {
    const [selectedMethod, setSelectedMethod] = useState<MFAMethod | null>(preSelectedMethod ?? null);
    const [showMethodSetup, setShowMethodSetup] = useState(!!preSelectedMethod);

    const handleMethodSelect = (method: MFAMethod) => {
        setSelectedMethod(method);
        setShowMethodSetup(true);
    };

    const handleSetupComplete = (method: MFAMethod) => {
        onSetupComplete(method);
        handleClose();
    };

    const handleSetupBack = () => {
        if (preSelectedMethod) {
            // If pre-selected, close the entire modal
            handleClose();
        } else {
            // Otherwise, go back to method selection
            setShowMethodSetup(false);
            setSelectedMethod(null);
        }
    };

    const handleClose = () => {
        setShowMethodSetup(false);
        setSelectedMethod(null);
        onClose();
    };

    // If a specific method setup is shown
    if (showMethodSetup && selectedMethod) {
        switch (selectedMethod) {
            case "AUTHENTICATOR":
                return (
                    <SettingsAuthenticatorModal
                        isOpen={isOpen}
                        onClose={handleClose}
                        onBack={handleSetupBack}
                        onSetupComplete={handleSetupComplete}
                    />
                );
            case "SMS":
                return (
                    <SettingsSMSSetupModal
                        isOpen={isOpen}
                        onClose={handleClose}
                        onSetupComplete={() => handleSetupComplete("SMS")}
                    />
                );
            case "SECURITY_QUESTION":
                return (
                    <SettingsSecurityQuestionsModal
                        isOpen={isOpen}
                        onClose={handleClose}
                        onBack={handleSetupBack}
                        onSetupComplete={() => handleSetupComplete("SECURITY_QUESTION")}
                    />
                );
            default:
                return null;
        }
    }

    // Method selection modal
    return (
        <CustomModal
            isOpen={isOpen}
            onRequestClose={handleClose}
            title={isFirstTimeSetup ? "Set up two-factor authentication" : "Switch MFA method"}
            width="500px"
        >
            <div className="space-y-4">
                <p className="text-sm text-subText mb-6">
                    {isFirstTimeSetup
                        ? "Choose a method to secure your account with two-factor authentication."
                        : "Select a new MFA method. Your current method will be replaced."}
                </p>

                <div className="rounded-xl border border-[#E3E5E8] w-full">
                    {mfaOptions.map((option, index) => (
                        <div key={option.method}>
                            <div
                                className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                                onClick={() => handleMethodSelect(option.method)}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter" || e.key === " ") handleMethodSelect(option.method);
                                }}
                                tabIndex={0}
                                data-testid={`mfa-option-${option.method.toLowerCase()}`}
                            >
                                <div className="flex items-center gap-3">
                                    {option.icon}
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-sm font-semibold text-black">{option.title}</h3>
                                            {option.recommended && (
                                                <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                                                    Recommended
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-xs text-subText mt-1">{option.subtitle}</p>
                                    </div>
                                </div>
                                <div className="text-primary">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M6 12l4-4-4-4" stroke="currentColor" strokeWidth="2" fill="none" />
                                    </svg>
                                </div>
                            </div>
                            {index < mfaOptions.length - 1 && <div className="bg-[#E3E5E8] h-[1px] w-full" />}
                        </div>
                    ))}
                </div>
            </div>
        </CustomModal>
    );
};

export default SettingsMFASetupModal;
