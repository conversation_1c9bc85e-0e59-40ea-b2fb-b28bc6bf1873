import React, { useEffect } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import CustomModal from "@/components/common/custom-modal";
import LabelInput from "@/components/common/label-input";
import { Button } from "@/components/common/buttonv3";
import { NoticeIcon } from "@/components/icons/settings";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { resetTransactionPin } from "@/redux/actions/securityActions";
import { securityActions } from "@/redux/slices/securitySlice";

export interface ResetPinModalProps {
    isOpen: boolean;
    onRequestClose: () => void;
    token?: string | null;
    onError?: () => void; // Callback to handle errors by closing modals and resetting state
    handleCloseParent: () => void;
}

const ResetPinModal: React.FC<ResetPinModalProps> = ({ isOpen, onRequestClose, token, onError, handleCloseParent }) => {
    const dispatch = useAppDispatch();
    const { resetPin } = useAppSelector((state) => state.security);

    const formik = useFormik({
        initialValues: { newPIN: "", confirmNewPIN: "" },
        validationSchema: yup.object({
            newPIN: yup
                .string()
                .required("New PIN is required")
                .matches(/^\d{6}$/, "PIN must be exactly 6 digits"),
            confirmNewPIN: yup
                .string()
                .required("Confirm New PIN is required")
                .oneOf([yup.ref("newPIN")], "PINs must match")
                .matches(/^\d{6}$/, "PIN must be exactly 6 digits"),
        }),
        onSubmit: (values) => {
            // Use the dedicated reset PIN endpoint
            dispatch(
                resetTransactionPin({
                    newPin: values.newPIN,
                    verifyNewPin: values.confirmNewPIN,
                    token: token ?? undefined,
                })
            );
        },
    });

    useEffect(() => {
        if (resetPin.success) {
            onRequestClose();
            dispatch(securityActions.clearState("resetPin"));
            formik.resetForm();
            const timer = setTimeout(() => {
                handleCloseParent();
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [resetPin.success, dispatch, formik, onRequestClose, handleCloseParent]);

    // Handle error state - if there's an error, close modals and reset state
    useEffect(() => {
        if (resetPin.error && onError) {
            // Clear the error state
            dispatch(securityActions.clearState("resetPin"));
            // Reset the form before closing
            formik.resetForm();
            // Close modals and reset state in parent component
            onError();
        }
    }, [resetPin.error, dispatch, onError, formik]);

    const handleClose = () => {
        onRequestClose();
        formik.resetForm();
    };

    // Handle PIN input - only allow numbers and max 6 digits
    const handlePinChange = (fieldName: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Remove any non-numeric characters
        const numericValue = value.replace(/\D/g, "");
        // Limit to 6 digits
        const limitedValue = numericValue.slice(0, 6);

        // Update formik with the cleaned value
        formik.setFieldValue(fieldName, limitedValue);
    };

    return (
        <CustomModal isOpen={isOpen} onRequestClose={handleClose} title="Reset transaction PIN" width="522px">
            <p className="text-center w-full text-subText text-[14px] -mt-4">
                This PIN will be required to authorize all transactions
            </p>
            <form id="resetPinForm" onSubmit={formik.handleSubmit} className="space-y-6 mt-6">
                <LabelInput
                    name="newPIN"
                    label="New PIN"
                    placeholder="Enter 6-digit PIN"
                    type="password"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    value={formik.values.newPIN}
                    onChange={handlePinChange("newPIN")}
                    onBlur={formik.handleBlur}
                    useFormik={false}
                    showError={formik.touched.newPIN && !!formik.errors.newPIN}
                    error={formik.errors.newPIN}
                />
                <LabelInput
                    name="confirmNewPIN"
                    label="Confirm New PIN"
                    placeholder="Re-enter 6-digit PIN"
                    type="password"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    value={formik.values.confirmNewPIN}
                    onChange={handlePinChange("confirmNewPIN")}
                    onBlur={formik.handleBlur}
                    useFormik={false}
                    showError={formik.touched.confirmNewPIN && !!formik.errors.confirmNewPIN}
                    error={formik.errors.confirmNewPIN}
                />
            </form>
            <div className="flex gap-1 items-center mt-8">
                <NoticeIcon />
                <p className="text-black text-base font-semibold">Important</p>
            </div>
            <div className="mt-4 text-sm text-black bg-[#F9F9FA] rounded-[12px] p-4">
                <ul className="list-disc space-y-1 pl-5">
                    <li>You'll use this PIN to authorize all transactions</li>
                    <li>Make sure it's different from your phone's PIN</li>
                    <li>Keep this PIN private, and don't share it with anyone</li>
                </ul>
            </div>
            <div className="flex w-full mt-8 justify-end gap-3 items-center">
                <Button onClick={handleClose} variant="outline">
                    Cancel
                </Button>
                <Button
                    form="resetPinForm"
                    type="submit"
                    disabled={resetPin.loading || !formik.isValid}
                    loading={resetPin.loading}
                >
                    Reset PIN
                </Button>
            </div>
        </CustomModal>
    );
};

export default ResetPinModal;
