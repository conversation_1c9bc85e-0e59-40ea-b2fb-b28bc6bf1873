import React, { useEffect, useState } from "react";
import Switch from "@/components/common/switch";
import { LockIcon1, SMSIcon, ShieldIcon } from "@/components/icons/settings";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { MFAMethod, getTeamMemberByEmail } from "@/redux/actions/settingsTwoFa";
import { clearUpdateMFAState } from "@/redux/slices/twoFaSlice";
import { sendFeedback } from "@/functions/feedback";
import SettingsMFASetupModal from "./modals/SettingsMFASetupModal";
import MFAConfirmationDialog from "./modals/MFAConfirmationDialog";

interface MFASetupState {
    showSetupModal: boolean;
    showSwitchConfirmation: boolean;
    setupMethod: MFAMethod | null;
    currentMethod: MFAMethod | null;
    newMethod: MFAMethod | null;
    isFirstTimeSetup: boolean;
}

const TwoFaSection: React.FC = () => {
    const dispatch = useAppDispatch();

    // Track the last toggle time to prevent rapid toggling
    const [lastToggleTime, setLastToggleTime] = React.useState(0);

    // MFA setup state management
    const [mfaSetupState, setMFASetupState] = useState<MFASetupState>({
        showSetupModal: false,
        showSwitchConfirmation: false,
        setupMethod: null,
        currentMethod: null,
        newMethod: null,
        isFirstTimeSetup: false,
    });

    // Track which method is currently being set up to prevent switch state changes during setup
    const [methodBeingSetup, setMethodBeingSetup] = useState<MFAMethod | null>(null);

    // Track methods that user tried to disable (to prevent visual state change)
    const [preventedDisableMethods, setPreventedDisableMethods] = useState<Set<MFAMethod>>(new Set());

    // Get 2FA settings from Redux store
    const { preferredMethod, enabledMethods, teamMember } = useAppSelector((state) => state.twoFa);
    const { success: updateSuccess } = useAppSelector((state) => state.twoFa.updateMFAMethod);

    // We don't need loading states for a seamless experience

    // We'll use the teamMember.mfaStatus to determine if any method is enabled

    // Function to get the actual switch state for a method
    // This ensures switches only show as enabled when MFA is actually set up and active
    const getSwitchState = (method: MFAMethod) => {
        // If this method is currently being set up, don't show it as enabled until completion
        if (methodBeingSetup === method) {
            return false;
        }

        const isActuallyEnabled =
            enabledMethods[method] && preferredMethod === method && (teamMember?.mfaStatus || false);

        // If user tried to disable this method but we prevented it, keep it enabled
        if (preventedDisableMethods.has(method) && isActuallyEnabled) {
            return true;
        }

        // Only show as enabled if it's the preferred method AND MFA is actually enabled
        return isActuallyEnabled;
    };

    // Fetch team member data on component mount
    useEffect(() => {
        // Use the test function to get team <NAME_EMAIL>
        // dispatch(getTestTeamMember());

        // Fetch current user's team member data (no email parameter needed)
        dispatch(getTeamMemberByEmail());
    }, [dispatch]);

    // Clear update success state after a successful update
    // We don't need to refetch since we're using optimistic updates
    useEffect(() => {
        if (updateSuccess) {
            // Clear the update success state
            dispatch(clearUpdateMFAState());
        }
    }, [updateSuccess, dispatch]);

    // Handle toggling a 2FA method
    const handleToggle = (method: MFAMethod) => {
        // Prevent rapid toggling (debounce)
        const now = Date.now();
        if (now - lastToggleTime < 500) {
            // 500ms debounce
            return;
        }
        setLastToggleTime(now);

        // Don't allow toggling if this method is currently being set up
        if (methodBeingSetup === method) {
            return;
        }

        const mfaStatus = teamMember?.mfaStatus || false;
        const currentMethod = preferredMethod;
        const isCurrentlyEnabled =
            enabledMethods[method] && preferredMethod === method && (teamMember?.mfaStatus || false);

        if (isCurrentlyEnabled) {
            // User is trying to disable current MFA method - prevent this and show feedback
            // Add this method to prevented disable methods to keep switch visually enabled
            setPreventedDisableMethods((prev) => new Set(prev).add(method));

            sendFeedback(
                "You cannot disable your active MFA method. Please set up a different MFA method first if you want to switch.",
                "neutral"
            );

            // Clear the prevented state after a short delay to allow for re-renders
            setTimeout(() => {
                setPreventedDisableMethods((prev) => {
                    const newSet = new Set(prev);
                    newSet.delete(method);
                    return newSet;
                });
            }, 100);
        } else {
            // Clear any prevented disable state for this method since user is enabling it
            setPreventedDisableMethods((prev) => {
                const newSet = new Set(prev);
                newSet.delete(method);
                return newSet;
            });

            // User is trying to enable a new MFA method
            if (mfaStatus && currentMethod) {
                // User already has MFA, switching to different method
                handleSwitchMFA(currentMethod, method);
            } else {
                // User has no MFA, setting up for first time
                handleEnableMFA(method);
            }
        }
    };

    // Handle enabling MFA for the first time
    const handleEnableMFA = (method: MFAMethod) => {
        setMethodBeingSetup(method); // Track which method is being set up
        setMFASetupState({
            showSetupModal: false,
            showSwitchConfirmation: true,
            setupMethod: null,
            currentMethod: null,
            newMethod: method,
            isFirstTimeSetup: true,
        });
    };

    // Handle switching MFA method
    const handleSwitchMFA = (currentMethod: MFAMethod, newMethod: MFAMethod) => {
        setMethodBeingSetup(newMethod); // Track which method is being set up
        setMFASetupState({
            showSetupModal: false,
            showSwitchConfirmation: true,
            setupMethod: null,
            currentMethod,
            newMethod,
            isFirstTimeSetup: false,
        });
    };

    // Handle MFA setup completion
    const handleSetupComplete = () => {
        // The individual setup modals now handle the preferred method update internally
        // following the same pattern as the signup flow

        // Clear the method being setup since it's now complete
        setMethodBeingSetup(null);

        // Close setup modal and refresh the team member data to reflect changes
        setMFASetupState((prev) => ({
            ...prev,
            showSetupModal: false,
            setupMethod: null,
        }));

        // Refresh team member data to get updated MFA status
        dispatch(getTeamMemberByEmail());
    };

    // Handle confirmation (both first-time setup and switching)
    const handleConfirmSwitch = () => {
        if (mfaSetupState.newMethod) {
            // Keep the methodBeingSetup since we're proceeding with the setup/switch
            setMFASetupState((prev) => ({
                ...prev,
                showSwitchConfirmation: false,
                showSetupModal: true,
                setupMethod: prev.newMethod,
                // Keep the isFirstTimeSetup flag as it was set
            }));
        }
    };

    // Handle closing modals
    const handleCloseSetupModal = () => {
        // Clear the method being setup since user cancelled
        setMethodBeingSetup(null);
        setMFASetupState((prev) => ({
            ...prev,
            showSetupModal: false,
            setupMethod: null,
        }));
    };

    const handleCloseSwitchConfirmation = () => {
        // Clear the method being setup since user cancelled the setup/switch
        setMethodBeingSetup(null);
        setMFASetupState((prev) => ({
            ...prev,
            showSwitchConfirmation: false,
            currentMethod: null,
            newMethod: null,
            isFirstTimeSetup: false,
        }));
    };

    // Function to determine if a switch should be disabled
    const getIsSwitchDisabled = (method: MFAMethod) => {
        // If this method is currently being set up, disable it
        if (methodBeingSetup === method) {
            return true;
        }

        // If this is the currently active MFA method, disable the switch to prevent toggling
        const isCurrentlyActive =
            enabledMethods[method] && preferredMethod === method && (teamMember?.mfaStatus || false);

        return isCurrentlyActive;
    };

    // We'll show the section even while loading, just disable the switches

    // Show team member data and MFA settings
    return (
        <div className="space-y-4" data-testid="2fa-section">
            <h2 className="text-lg font-semibold text-black" data-testid="2fa-title">
                Two-Factor Authentication (2FA)
            </h2>

            <div className="space-y-8">
                {/* Authenticator App */}
                <div className="flex items-center justify-between" data-testid="2fa-authenticator">
                    <div className="flex items-center gap-3">
                        <LockIcon1 />
                        <div>
                            <div className="flex items-center gap-2">
                                <h3 className="text-[16px] font-semibold text-black">Authenticator app</h3>
                                {preferredMethod === "AUTHENTICATOR" && (
                                    <span className="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                                        Enabled
                                    </span>
                                )}
                            </div>
                            <p className="text-sm text-subText">Use an app to get authentication codes when prompted</p>
                        </div>
                    </div>
                    <div className="relative">
                        {/* No loading spinner for a more seamless experience */}
                        <Switch
                            key={`authenticator-${enabledMethods.AUTHENTICATOR}-${preferredMethod}-${methodBeingSetup}`}
                            checked={getSwitchState("AUTHENTICATOR")}
                            name="authenticator"
                            onChange={() => handleToggle("AUTHENTICATOR")}
                            disabled={getIsSwitchDisabled("AUTHENTICATOR")} // Never disable for a more seamless experience
                        />
                    </div>
                </div>

                {/* SMS OTP */}
                <div className="flex items-center justify-between" data-testid="2fa-sms">
                    <div className="flex items-center gap-3">
                        <SMSIcon />
                        <div>
                            <div className="flex items-center gap-2">
                                <h3 className="text-[16px] font-semibold text-black">SMS OTP</h3>
                                {preferredMethod === "SMS" && (
                                    <span className="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                                        Enabled
                                    </span>
                                )}
                            </div>
                            <p className="text-sm text-subText">
                                Get authentication codes sent to your registered mobile number
                            </p>
                        </div>
                    </div>
                    <div className="relative">
                        {/* No loading spinner for a more seamless experience */}
                        <Switch
                            key={`sms-${enabledMethods.SMS}-${preferredMethod}-${methodBeingSetup}`}
                            checked={getSwitchState("SMS")}
                            name="sms"
                            onChange={() => handleToggle("SMS")}
                            disabled={getIsSwitchDisabled("SMS")} // Never disable for a more seamless experience
                        />
                    </div>
                </div>

                {/* Security Question */}
                <div data-testid="2fa-security-question" className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <ShieldIcon />
                        <div>
                            <div className="flex items-center gap-2">
                                <h3 className="text-[16px] font-semibold text-black">Security question</h3>
                                {preferredMethod === "SECURITY_QUESTION" && (
                                    <span className="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                                        Enabled
                                    </span>
                                )}
                            </div>
                            <p className="text-sm text-subText">
                                Answer a question to verify your identity when accessing your account
                            </p>
                        </div>
                    </div>
                    <div className="relative">
                        {/* No loading spinner for a more seamless experience */}
                        <Switch
                            key={`security-question-${enabledMethods.SECURITY_QUESTION}-${preferredMethod}-${methodBeingSetup}`}
                            checked={getSwitchState("SECURITY_QUESTION")}
                            name="security-question"
                            onChange={() => handleToggle("SECURITY_QUESTION")}
                            disabled={getIsSwitchDisabled("SECURITY_QUESTION")} // Never disable for a more seamless experience
                        />
                    </div>
                </div>
            </div>

            {/* MFA Setup Modal */}
            <SettingsMFASetupModal
                isOpen={mfaSetupState.showSetupModal}
                onClose={handleCloseSetupModal}
                preSelectedMethod={mfaSetupState.setupMethod}
                isFirstTimeSetup={mfaSetupState.isFirstTimeSetup}
                onSetupComplete={handleSetupComplete}
            />

            {/* MFA Confirmation Dialog - for both first-time setup and switching */}
            <MFAConfirmationDialog
                isOpen={mfaSetupState.showSwitchConfirmation}
                onClose={handleCloseSwitchConfirmation}
                onConfirm={handleConfirmSwitch}
                title={mfaSetupState.isFirstTimeSetup ? "Set up MFA?" : "Switch MFA method?"}
                subtitle={
                    mfaSetupState.isFirstTimeSetup
                        ? `You're about to set up ${mfaSetupState.newMethod?.toLowerCase().replace("_", " ")} as your MFA method. This will help secure your account.`
                        : `Are you sure you want to switch from ${mfaSetupState.currentMethod?.toLowerCase().replace("_", " ")} to ${mfaSetupState.newMethod?.toLowerCase().replace("_", " ")}? You'll need to set up the new method.`
                }
                confirmButtonText={mfaSetupState.isFirstTimeSetup ? "Set up MFA" : "Yes, switch"}
                cancelButtonText="Cancel"
            />
        </div>
    );
};

export default TwoFaSection;
