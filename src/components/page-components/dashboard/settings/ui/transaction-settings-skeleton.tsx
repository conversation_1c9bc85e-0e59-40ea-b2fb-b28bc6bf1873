import { memo } from "react";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Skeleton loader for a single transaction limit card
 */
export const TransactionLimitCardSkeleton = memo(() => (
    <div className="w-full p-4 rounded-[20px] border border-[#E3E5E8] animate-pulse">
        <div className="w-full flex items-center justify-between">
            <div className="flex items-center gap-4">
                {/* Icon placeholder */}
                <div className="bg-[#F9F0FE] rounded-full p-2">
                    <Skeleton className="h-6 w-6" />
                </div>
                {/* Title and subtitle placeholders */}
                <div className="text-left">
                    <Skeleton className="h-5 w-32 mb-2" />
                    <Skeleton className="h-4 w-48" />
                </div>
            </div>
            {/* Arrow icon placeholder */}
            <Skeleton className="h-5 w-5 rounded-full" />
        </div>
    </div>
));

TransactionLimitCardSkeleton.displayName = "TransactionLimitCardSkeleton";

/**
 * Complete skeleton loader for transaction settings
 */
export const TransactionSettingsSkeleton = memo(() => (
    <div className="p-6 sm:max-w-[650px] max-w-full sm:ml-10" data-testid="transaction-settings-skeleton">
        <div className="space-y-4">
            {/* Create 4 skeleton cards to match the typical number of transaction limit types */}
            {[1, 2, 3, 4].map((i) => (
                <TransactionLimitCardSkeleton key={i} />
            ))}
        </div>
    </div>
));

TransactionSettingsSkeleton.displayName = "TransactionSettingsSkeleton";
