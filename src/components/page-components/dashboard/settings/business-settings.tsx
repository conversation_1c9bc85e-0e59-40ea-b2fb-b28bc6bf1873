"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
    fetchBusinessInfo,
    updateBusinessInfo,
    updateBusinessInfoWithToken,
    uploadBusiness<PERSON>ogo,
    deleteBusinessLogo,
    fetchBusinessLogo,
} from "@/redux/actions/settingsActions";
import { sendFeedback } from "@/functions/feedback";
import { LogoUpload, BusinessInfoSection } from "./business";
import { AddressForm, AddressDisplay } from "./address";
import { BusinessSettingsSkeleton } from "./ui";
import { extractErrorMessage } from "./utils";
/**
 * BusinessSettings component
 *
 * Purpose: Displays and allows editing of the business information including logo upload with 10MB size limit.
 * Now includes 2FA support for secure field updates.
 *
 * Functionality: This component serves as the main container for business settings management. It fetches business
 * data from the API on mount and displays it in an organized format with sections for logo upload, business information,
 * and address details. The component provides functionality to edit individual fields through sub-components, supports
 * logo upload and deletion with proper file validation (PNG, JPG files up to 10MB), and maintains proper loading states
 * and error handling throughout all operations. It uses Redux for state management and follows API integration guidelines
 * for user feedback and error handling. Now includes 2FA verification for sensitive field updates.
 *
 * Dependencies: Redux Toolkit for state management, React hooks for local state, custom components (LogoUpload,
 * BusinessInfoSection, AddressForm/Display), settings actions from Redux, and utility functions for error handling.
 *
 * Usage: Used in the settings module to allow business users to manage their company information, upload/delete
 * business logos with 10MB limit, and update address information. The component handles all CRUD operations for
 * business data and provides appropriate user feedback for all actions.
 *
 * Features:
 * - Loading state with skeleton UI
 * - Error handling with retry functionality
 * - Fallback text for missing data
 * - Edit mode for address information
 * - Logo upload and deletion with 10MB file size limit and proper feedback
 * - 2FA verification for sensitive field updates
 */
export const BusinessSettings = () => {
    const dispatch = useAppDispatch();
    const acceptedFileTypes = [".png", ".jpg", ".jpeg"];
    const [isEditingAddress, setIsEditingAddress] = useState(false);
    const [number, setNumber] = useState("");
    const [address, setAddress] = useState("");
    const [city, setCity] = useState("");
    const [state, setState] = useState("");
    const [country, setCountry] = useState("Nigeria");
    const { countries } = useAppSelector((state) => state.countries);
    const {
        data: businessInfo,
        loading,
        error,
        updateLoading,
    } = useAppSelector((state) => state.settings.businessInfo);

    // Get logo upload/delete loading states
    const { uploadLoading, deleteLoading } = useAppSelector((state) => state.settings.businessLogo);

    const [isCountriesLoading, setIsCountriesLoading] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    // Fetch business info when component mounts
    useEffect(() => {
        dispatch(fetchBusinessInfo())
            .unwrap()
            .catch((err) => {
                setHasError(true);
                setErrorMessage(extractErrorMessage(err, "Unable to get business information"));
            });
        dispatch(fetchBusinessLogo());
    }, [dispatch]);

    // Update local error state when Redux error state changes
    useEffect(() => {
        if (error) {
            setHasError(true);
            setErrorMessage(extractErrorMessage(error, "Unable to get business information"));
        }
    }, [error]);

    // Check if countries are loading
    useEffect(() => {
        setIsCountriesLoading(!countries || countries.length === 0);
    }, [countries]);

    /**
     * Parses business address and returns address components
     * Handles different address formats (object, string, or null/undefined)
     * Maps address fields according to API structure
     *
     * @param address - The address to parse from API response
     * @returns Object containing parsed address components with default values for missing fields
     */
    const parseBusinessAddress = (address: string | object | null | undefined) => {
        // Default values for fields if they don't exist
        const defaultAddress = {
            number: "",
            street: "",
            city: "",
            state: "",
            country: "Nigeria",
        };

        // Handle null/undefined case
        if (!address) {
            return defaultAddress;
        }

        // Handle object format (from API response)
        if (typeof address === "object") {
            const addressObj = address as Record<string, string>;

            const parsedAddress = {
                number: addressObj.number || defaultAddress.number,
                street: addressObj.street || defaultAddress.street,
                city: addressObj.city || defaultAddress.city,
                state: addressObj.state || defaultAddress.state,
                country: addressObj.country || defaultAddress.country,
            };

            return parsedAddress;
        }

        // Handle string format (legacy data)
        if (typeof address === "string" && address.trim()) {
            // Parse comma-separated address
            const parts = address.split(",").map((part) => part.trim());

            // Try to extract number from the first part if it starts with a number
            let number = "";
            let street = "";

            if (parts.length > 0) {
                const firstPart = parts[0];
                const numberRegex = /^(\d+)/;
                const numberMatch = numberRegex.exec(firstPart);
                if (numberMatch) {
                    number = numberMatch[1];
                    street = firstPart.replace(/^\d+\s*/, "").trim();
                } else {
                    street = firstPart;
                }
            }

            const [_street = ""] = parts;

            return {
                ...defaultAddress,
                number,
                street: street || _street,
            };
        }

        // Fallback to default address if none of the above conditions are met
        return defaultAddress;
    };

    // Initialize address form fields when data is loaded
    useEffect(() => {
        const { number, street, city, state, country } = parseBusinessAddress(businessInfo?.address);
        setNumber(number);
        setAddress(street);
        setCity(city);
        setState(state);
        setCountry(country);
    }, [businessInfo]);

    /**
     * Maps UI field names to API field names and handles saving without MFA
     * Calls fetchBusinessInfo after successful updates to keep UI in sync
     * Follows API Integration Guideline #10 for proper user feedback
     */
    const handleSave = useCallback(
        (field: string) => (value: string) => {
            // Map UI field names to API field names
            const fieldMappings: Record<string, string> = {
                tradingName: "tradingName",
                businessEmail: "businessEmail",
                businessPhone: "phoneNumber",
            };

            const apiField = fieldMappings[field] || field;
            // Following API Integration Guideline #2: use empty string instead of null/undefined
            const updateData = { [apiField]: value || "" };

            dispatch(updateBusinessInfo(updateData))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Following API Integration Guideline #10: use sendFeedback for successful operations
                    sendFeedback(
                        `Your ${field.replace(/([A-Z])/g, " $1").toLowerCase()} has been updated successfully`,
                        "success",
                        undefined,
                        "Success"
                    );

                    // Fetch latest business information to keep UI in sync with API
                    dispatch(fetchBusinessInfo());
                    // Fetch business logo after successful update to ensure logo is not blank
                    dispatch(fetchBusinessLogo());
                });
            // Error handling is already implemented in the updateBusinessInfo thunk with sendCatchFeedback
        },
        [dispatch]
    );

    /**
     * Maps UI field names to API field names and handles saving with MFA token
     * Calls fetchBusinessInfo after successful updates to keep UI in sync
     * Follows API Integration Guideline #10 for proper user feedback
     */
    const handleSaveWithToken = useCallback(
        (field: string) => (value: string, token: string) => {
            // Map UI field names to API field names
            const fieldMappings: Record<string, string> = {
                tradingName: "tradingName",
                businessEmail: "businessEmail",
                businessPhone: "phoneNumber",
            };

            const apiField = fieldMappings[field] || field;
            // Following API Integration Guideline #2: use empty string instead of null/undefined
            const updateData = { [apiField]: value || "", token: token };

            dispatch(updateBusinessInfoWithToken(updateData))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Following API Integration Guideline #10: use sendFeedback for successful operations
                    sendFeedback(
                        `Your ${field.replace(/([A-Z])/g, " $1").toLowerCase()} has been updated successfully`,
                        "success",
                        undefined,
                        "Success"
                    );

                    // Fetch latest business information to keep UI in sync with API
                    dispatch(fetchBusinessInfo());
                    // Fetch business logo after successful update to ensure logo is not blank
                    dispatch(fetchBusinessLogo());
                });
            // Error handling is already implemented in the updateBusinessInfoWithToken thunk with sendCatchFeedback
        },
        [dispatch]
    );

    /**
     * Handles file selection for logo upload
     * Fetches latest business information after successful upload
     */
    const handleFileChange = useCallback(
        (file: File) => {
            dispatch(uploadBusinessLogo(file))
                .unwrap() // Ensure sequential execution
                .then(() => {
                    // Fetch latest business information to keep UI in sync
                    dispatch(fetchBusinessInfo());
                });
            // Error handling is already implemented in the uploadBusinessLogo thunk
        },
        [dispatch]
    );

    /**
     * Handles logo deletion
     * Fetches latest business information after successful deletion
     */
    const handleDeleteLogo = useCallback(() => {
        dispatch(deleteBusinessLogo())
            .unwrap() // Ensure sequential execution
            .then(() => {
                // Fetch latest business information to keep UI in sync
                dispatch(fetchBusinessInfo());
            });
        // Error handling is already implemented in the deleteBusinessLogo thunk
    }, [dispatch]);

    /**
     * Handles saving the address information
     * Constructs the address object according to the API documentation
     * Uses sendFeedback for success feedback and relies on sendCatchFeedback in the thunk for errors
     */
    const handleSaveAddress = useCallback(
        (formValues?: { number: string; street: string; city: string; state: string; country: string }) => {
            // Use form values if provided, otherwise fall back to state
            const values = formValues || {
                number: number,
                street: address,
                city: city,
                state: state,
                country: country,
            };

            // Validate required fields before submission
            if (!values.number && !values.street && !values.city && !values.state && !values.country) {
                // Following API Integration Guideline #10: Use proper feedback mechanisms
                sendFeedback("Please fill at least one address field", "error", undefined, "Error");
                return;
            }

            // Dispatch the update action with the address object formatted according to API requirements
            dispatch(
                updateBusinessInfo({
                    // Following API Integration Guideline #2: use empty strings instead of null/undefined
                    street: values.street || "",
                    number: values.number || "",
                    city: values.city || "",
                    state: values.state || "",
                    country: values.country || "",
                })
            )
                .unwrap()
                .then(() => {
                    setIsEditingAddress(false);
                    // Show success feedback for user-initiated action (API Integration Guideline #10)
                    sendFeedback("Your address has been updated successfully", "success", undefined, "Success");

                    // Re-fetch business info to ensure UI is in sync with API
                    dispatch(fetchBusinessInfo());
                    // Fetch business logo after successful address update to ensure logo is not blank
                    dispatch(fetchBusinessLogo());
                });
            // Error handling is already implemented in the updateBusinessInfo thunk with sendCatchFeedback
        },
        [address, city, state, country, number, dispatch]
    );

    /**
     * Handles saving the address information with MFA token
     * Constructs the address object according to the API documentation
     * Uses sendFeedback for success feedback and relies on sendCatchFeedback in the thunk for errors
     */
    const handleSaveAddressWithToken = useCallback(
        (
            token: string,
            formValues?: { number: string; street: string; city: string; state: string; country: string }
        ) => {
            // Use form values if provided, otherwise fall back to state
            const values = formValues || {
                number: number,
                street: address,
                city: city,
                state: state,
                country: country,
            };

            // Validate required fields before submission
            if (!values.number && !values.street && !values.city && !values.state && !values.country) {
                // Following API Integration Guideline #10: Use proper feedback mechanisms
                sendFeedback("Please fill at least one address field", "error", undefined, "Error");
                return;
            }

            // Dispatch the update action with the address object and token formatted according to API requirements
            dispatch(
                updateBusinessInfoWithToken({
                    // Following API Integration Guideline #2: use empty strings instead of null/undefined
                    street: values.street || "",
                    number: values.number || "",
                    city: values.city || "",
                    state: values.state || "",
                    country: values.country || "",
                    token: token,
                })
            )
                .unwrap()
                .then(() => {
                    setIsEditingAddress(false);
                    // Show success feedback for user-initiated action (API Integration Guideline #10)
                    sendFeedback("Your address has been updated successfully", "success", undefined, "Success");

                    // Re-fetch business info to ensure UI is in sync with API
                    dispatch(fetchBusinessInfo());
                    // Fetch business logo after successful address update to ensure logo is not blank
                    dispatch(fetchBusinessLogo());
                });
            // Error handling is already implemented in the updateBusinessInfoWithToken thunk with sendCatchFeedback
        },
        [address, city, state, country, number, dispatch]
    );

    /**
     * Toggles the address editing mode
     */
    const toggleAddressEdit = useCallback(() => {
        setIsEditingAddress((prev) => !prev);
    }, []);

    /**
     * Retries fetching business information and resets the error state
     */
    const handleRetry = useCallback(() => {
        setHasError(false);
        setErrorMessage("");
        dispatch(fetchBusinessInfo())
            .unwrap()
            .catch((err) => {
                setHasError(true);
                setErrorMessage(extractErrorMessage(err, "Unable to get business information"));
            });
    }, [dispatch]);

    // Loading state
    if (loading) {
        return <BusinessSettingsSkeleton />;
    }

    // Error state with retry button - uses the local hasError state which isn't cleared by middleware
    if (hasError || error) {
        return (
            <div className="bg-white rounded-[20px] border border-[#E3E5E8] p-6 shadow-sm">
                <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-black">Business information</h2>
                    <div className="text-red-500 py-4">{errorMessage || "Unable to get business information"}</div>
                    <Button onClick={handleRetry} aria-label="Retry loading business information">
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // Only render content if we have data and no error
    if (!businessInfo) {
        return <BusinessSettingsSkeleton />;
    }

    return (
        <div className="bg-white rounded-[20px] border border-[#E3E5E8] p-6 shadow-sm">
            <div className="space-y-8">
                {/* Logo Upload Section */}
                <LogoUpload
                    acceptedFileTypes={acceptedFileTypes}
                    handleFileChange={handleFileChange}
                    handleDelete={handleDeleteLogo}
                    uploadLoading={uploadLoading}
                    deleteLoading={deleteLoading}
                    logoUrl={businessInfo?.logoUrl}
                    businessName={businessInfo?.businessName}
                />

                {/* Business Information Section */}
                <BusinessInfoSection
                    handleSave={handleSave}
                    handleSaveWithToken={handleSaveWithToken}
                    businessInfo={businessInfo}
                />

                {/* Address Information Section */}
                <div>
                    {isEditingAddress ? (
                        <AddressForm
                            number={number}
                            address={address}
                            city={city}
                            state={state}
                            country={country}
                            countries={countries}
                            isCountriesLoading={isCountriesLoading}
                            toggleAddressEdit={toggleAddressEdit}
                            handleSaveAddress={handleSaveAddress}
                            handleSaveAddressWithToken={handleSaveAddressWithToken}
                            updateLoading={updateLoading}
                        />
                    ) : (
                        <AddressDisplay
                            address={businessInfo?.address ?? "No address available"}
                            toggleAddressEdit={toggleAddressEdit}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};
