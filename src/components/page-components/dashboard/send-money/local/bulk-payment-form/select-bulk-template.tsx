"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import FileAttachment from "@/components/common/file-attachment";
import { CsvFileIcon, FileIcon, LayoutIcon, UploadIcon } from "@/components/icons/bill-payment-icons";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { convertCsvToArrayOfObjects } from "@/functions/file-control";
import { downloadBulkTemplate } from "@/redux/actions/sendMoneyActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { GetBulkTemplateType } from "@/redux/types/send-money";
import { Dispatch, FC, SetStateAction, useEffect } from "react";
import { BulkRecipientType, ExtractedBulkFileData } from "../../data";
import { LoadingIcon } from "../../icons";
import SelectSavedTemplate from "./saved-templates/select-saved-template";

type Props = {
    uploadedFile: File | undefined;
    setUploadedFile: Dispatch<SetStateAction<File | undefined>>;
    isUploading: boolean;
    setIsUploading: Dispatch<SetStateAction<boolean>>;
    setIsUploadComplete: Dispatch<SetStateAction<boolean>>;
    setSelectedRecipients: Dispatch<SetStateAction<BulkRecipientType[]>>;
    setProcessedModal: Dispatch<SetStateAction<boolean>>;
    setSelectedTemplate: Dispatch<SetStateAction<GetBulkTemplateType | undefined>>;
};

const SelectBulkTemplate: FC<Props> = ({
    isUploading,
    setIsUploadComplete,
    setIsUploading,
    setUploadedFile,
    uploadedFile,
    setSelectedRecipients,
    setProcessedModal,
    setSelectedTemplate,
}) => {
    const dispatch = useAppDispatch();
    const { loading, data, success } = useAppSelector((state) => state.sendMoney.downloadBulkTemplate);
    const { banks } = useAppSelector((state) => state.recipient.getLocalBanks ?? []);

    const handleFileUpload = async (files: FileList) => {
        setSelectedTemplate(undefined); // in case user clicked on template first
        const file = files?.[0];
        if (!file) return;

        setIsUploading(true);
        setUploadedFile(files[0]);
        setIsUploadComplete(false);

        try {
            const data = (await convertCsvToArrayOfObjects(file)) as ExtractedBulkFileData[];

            const validatedData = data.map((item) => {
                let error: string | null = null;

                switch (true) {
                    case !item.ToAccount || String(item.ToAccount).length !== 10:
                        error = "Invalid Account Number";
                        break;

                    case !item.ToBankCode:
                        error = "Invalid Bank Code";
                        break;

                    case !item.Amount || isNaN(Number(item.Amount)) || item.Amount < 0:
                        error = "Invalid Amount";
                        break;

                    case !item.ToAccountName:
                        error = "Invalid Account Name";
                        break;

                    case !item.ToBankName:
                        error = "Invalid Bank Name";
                        break;

                    default:
                        break;
                }

                // Check for bank name match
                if (item.ToBankName && item.ToBankCode && banks) {
                    if (item.ToBankName !== banks.find((value) => value.bankName === item.ToBankName)?.bankName) {
                        // Bank Name does not match in list of selected banks
                        // Set bank name based on bank code
                        const correctBankDetails = banks.find((value) => value.bankCode === item.ToBankCode.toString());
                        if (correctBankDetails) {
                            item.ToBankName = correctBankDetails.bankName;
                        } else {
                            error = "Invalid bank selected";
                        }
                    }
                }

                return {
                    ...item,
                    error,
                };
            });

            setSelectedRecipients(
                validatedData.map((item) => ({
                    destinationAccount: item.ToAccount,
                    destinationAccountName: item.ToAccountName,
                    bankCode: item.ToBankCode,
                    bankName: item.ToBankName,
                    amount: item.Amount,
                    narration: item.Narration,
                    data_id: item.data_id,
                    error: item.error,
                })) as unknown as BulkRecipientType[]
            );
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
            sendCatchFeedback(error);
        }
    };

    const downloadTemplate = async () => {
        await dispatch(downloadBulkTemplate());
    };

    useEffect(() => {
        if (success && data) {
            sendFeedback("Template downloaded successfully", "success");
        }
    }, [success, data]);
    return (
        <div className="w-full">
            <div className="flex-col flex items-center w-full">
                <div className="flex flex-col w-full items-center">
                    <h3 className="font-semibold text-black text-2xl text-center mb-12" data-testid="heading">
                        Send a bulk payment
                    </h3>
                    <h5 className="mb-5 text-black font-semibold">Create a new bulk payment</h5>

                    {/* Template container */}
                    <div className="w-full border border-[#E3E5E8] rounded-[20px] py-8 px-5 flex flex-col gap-8">
                        {/* Download Template */}
                        <div className="w-full flex items-start gap-2">
                            <div>
                                <LayoutIcon className="w-full h-full" />
                            </div>
                            <div className="flex flex-col gap-5 w-full">
                                <div className="w-full flex flex-col gap-3">
                                    <p className="w-full text-black font-semibold">Download our CSV template</p>
                                    <p className="w-full text-subText text-sm">
                                        Get started by downloading our CSV template. Fill the template with the
                                        recipients' transfer details - name, and transfer amount.
                                    </p>
                                </div>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    loading={loading}
                                    type="button"
                                    onClick={downloadTemplate}
                                >
                                    Download
                                </Button>
                            </div>
                        </div>
                        <div className="w-full flex items-start gap-2">
                            <div>
                                <UploadIcon className="w-full h-full" />
                            </div>
                            <div className="flex-1 flex flex-col gap-6 w-full">
                                <div className="w-full flex flex-col gap-3">
                                    <h3 className="w-full text-black font-semibold">Upload and verify</h3>
                                    <p className="w-full text-subText text-sm">
                                        Once you're done, upload the file for review and verification.
                                    </p>
                                </div>

                                <div className="flex flex-col gap-2 w-full">
                                    <div className="w-full">
                                        <FileAttachment
                                            headerText="Attachments"
                                            acceptedTypes={["csv"]}
                                            maxSize={10}
                                            onFilesSelected={(files) => {
                                                handleFileUpload(files);
                                            }}
                                            onFileRemoved={() => setUploadedFile(undefined)}
                                            onUploadStateChange={(isComplete) => {
                                                setIsUploading(!isComplete);
                                                setIsUploadComplete(isComplete);
                                                if (!isComplete) {
                                                    setUploadedFile(undefined);
                                                }
                                            }}
                                            icon={<FileIcon className="w-5 h-5" />}
                                            progressIcon={<CsvFileIcon />}
                                            className="w-full"
                                            width="100%"
                                            descriptionText="Supported format: CSV. File size up to 10MB"
                                            initialFile={uploadedFile}
                                        />
                                    </div>
                                    {isUploading && uploadedFile && (
                                        <div className="w-full flex items-center gap-1 text-sm text-[#3A3A41] font-medium tracking-[0.02em]">
                                            <LoadingIcon />
                                            <span className="flex-1">Hold on while we verify your file</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Upload template */}
                    </div>

                    {/* Saved template container */}
                    <SelectSavedTemplate
                        setProcessedModal={setProcessedModal}
                        setSelectedRecipients={setSelectedRecipients}
                        setSelectedTemplate={setSelectedTemplate}
                    />
                </div>
            </div>
        </div>
    );
};

export default SelectBulkTemplate;
