import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import { ArrowDownIcon } from "@/components/icons/table";
import { formatNumberToNaira, getNameInitials } from "@/functions/stringManipulations";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { BulkRecipientType } from "../../../data";
import { EditDeleteIcon, EditPencilIcon } from "./icons";

interface TableMetaWithSetIsOpen extends TableMeta<BulkRecipientType> {
    editRecipient: (value: BulkRecipientType) => void;
    handleDeleteRecipient: (value: BulkRecipientType) => void;
}
export const getColumns: (type: "invalid" | "valid") => ColumnDef<BulkRecipientType>[] = (type) => {
    const columns: ColumnDef<BulkRecipientType>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
                    aria-label="Select all"
                    size="sm"
                    indeterminate={table.getIsSomePageRowsSelected()}
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(checked) => row.toggleSelected(checked)}
                    aria-label="Select row"
                    size="sm"
                />
            ),
            enableSorting: true,
            enableHiding: false,
        },
        {
            accessorKey: "data_id",
            header: "ID",
        },
        {
            accessorKey: "destinationAccountName",
            header: "Account name",
            cell: ({ row }) => {
                const destinationAccountName = row.getValue("destinationAccountName") as string;
                if (destinationAccountName) {
                    return (
                        <div className="flex items-center gap-4">
                            <div className="w-8 h-8 min-w-8 min-h-8 flex items-center justify-center rounded-full text-subText font-medium bg-[#F9F0FE] text-xs">
                                {getNameInitials(destinationAccountName)}
                            </div>
                            <p className="text-sm font-medium">{destinationAccountName}</p>
                        </div>
                    );
                } else return <div className="text-right font-semibold">{"-"}</div>;
            },
        },
        {
            accessorKey: "bankName",
            header: "Destination bank",
        },
        {
            accessorKey: "destinationAccount",
            header: "Account number",
        },
        {
            accessorKey: "amount",
            header: ({ column }) => (
                <button
                    className="flex items-center justify-end gap-1 text-right w-full"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    <ArrowDownIcon data-testid="ArrowDownIcon" />
                    <span className="text-right">Amount</span>
                </button>
            ),
            cell: ({ row }) => {
                const amount = parseFloat(row.getValue("amount"));
                if (amount) {
                    const formatted = formatNumberToNaira(amount, 2);
                    return (
                        <div className={"text-right font-semibold"}>
                            {"-"} {formatted}
                        </div>
                    );
                } else return <div className={"text-right font-semibold"}>{"-"}</div>;
            },
        },
        {
            id: "actions",
            cell: ({ row, table }) => {
                const recipient = row.original;
                const editRecipient = (table.options.meta as TableMetaWithSetIsOpen).editRecipient;
                const handleDeleteRecipient = (table.options.meta as TableMetaWithSetIsOpen).handleDeleteRecipient;

                const updatedMenuItems = [
                    {
                        label: type === "invalid" ? "Fix error" : "Edit",
                        onClick: (data: BulkRecipientType) => editRecipient(data),
                        icon: <EditPencilIcon />,
                    },
                    {
                        label: "Remove",
                        onClick: (data: BulkRecipientType) => handleDeleteRecipient(data),
                        style: {
                            color: "#D92D20",
                        },
                        icon: <EditDeleteIcon />,
                    },
                ];

                return (
                    <TableMoreAction
                        data={{ ...recipient, id: recipient.destinationAccountName }}
                        menuItems={updatedMenuItems}
                    />
                );
            },
        },
    ];

    // Only add "Comments" column when type === "invalid"
    if (type === "invalid") {
        columns.splice(5, 0, {
            accessorKey: "error",
            header: "Comments",
        });
    }
    if (type === "valid") {
        columns.splice(5, 0, {
            accessorKey: "narration",
            header: "Narration",
        });
    }

    return columns;
};
