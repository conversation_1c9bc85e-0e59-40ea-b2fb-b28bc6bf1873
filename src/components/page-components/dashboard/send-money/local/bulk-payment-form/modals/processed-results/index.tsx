/* eslint-disable @typescript-eslint/no-unused-vars */
import { But<PERSON> } from "@/components/common/buttonv3";
import Checkbox from "@/components/common/checkbox";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import SearchInput from "@/components/common/search-input";
import { useDebounce } from "@/hooks/useDebounce";
import { GetBulkTemplateType } from "@/redux/types/send-money";
import { Dispatch, FC, SetStateAction, useMemo, useState } from "react";
import { BulkRecipientType } from "../../../../data";
import DeleteTableValueControl from "../delete-table-value-control";
import { VerifiedIcon } from "../icons";
import RecipientsTable from "../recipients-table";
import AddRecipientDialog from "./add-recipient-dialog";

type Props = {
    isOpen: boolean;
    onClose: () => void;
    selectedRecipients: BulkRecipientType[] | [];
    setSelectedRecipients: Dispatch<SetStateAction<BulkRecipientType[]>>;
    goToNextStep: () => void;
    selectedTemplate: GetBulkTemplateType | undefined;
    shouldUpdateTemplate: boolean;
    setShouldUpdateTemplate: Dispatch<SetStateAction<boolean>>;
};

const ProcessedResultsModal: FC<Props> = ({
    isOpen,
    onClose,
    selectedRecipients,
    setSelectedRecipients,
    goToNextStep,
    selectedTemplate,
    setShouldUpdateTemplate,
    shouldUpdateTemplate,
}) => {
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);
    const [selectedTableValues, setSelectedTableValues] = useState<BulkRecipientType[] | []>([]);
    const [shouldResetTableSelection, setShouldResetTableSelection] = useState(false);
    const [addRecipientModal, setAddRecipientModal] = useState(false);
    const [searchInput, setSearchInput] = useState("");
    const debouncedQuery = useDebounce(searchInput, 300); // 300ms debounce
    // Search handler
    const handleSearchUpdate = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchInput(e.target.value);
    };

    const searchedRecipients = useMemo(() => {
        if (!debouncedQuery.trim()) return selectedRecipients;

        return selectedRecipients.filter((recipient: BulkRecipientType) => {
            const queryLowercase = debouncedQuery.toLowerCase();
            return (
                recipient.destinationAccount?.toString()?.includes(debouncedQuery) ||
                recipient.destinationAccountName?.toLowerCase().includes(queryLowercase) ||
                recipient.bankName?.toLowerCase().includes(queryLowercase)
            );
        });
    }, [debouncedQuery, selectedRecipients]);

    const handleConfirmExit = () => {
        onClose();
        setShowExitConfirmation(false);
    };

    const handleCancelExit = () => {
        setShowExitConfirmation(false);
    };

    const clearTableSelection = () => {
        setShouldResetTableSelection(true);
        setSelectedTableValues([]);
    };

    const deleteSelectedValues = () => {
        clearTableSelection();

        const idsToRemove = new Set(selectedTableValues.map((item) => item.data_id));
        const filteredArray = selectedRecipients.filter((item) => !idsToRemove.has(item.data_id));
        setSelectedRecipients(filteredArray);
    };

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={onClose}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            title={!selectedTemplate ? "" : `Recipients from "${selectedTemplate.filename}"`}
            showExitConfirmation={showExitConfirmation}
            disablePadding
        >
            <div className="flex flex-col h-full">
                <div className="px-[56px] py-4 max-h-full overflow-y-auto">
                    {!selectedTemplate ? (
                        <div className="mb-8">
                            <h3 className="font-semibold text-black text-xl mb-2" data-testid="heading">
                                Processed results
                            </h3>
                            <p className="flex items-center gap-1 text-subText">
                                <VerifiedIcon />
                                All payment details were successfully validated and are good to go!
                            </p>
                        </div>
                    ) : (
                        <div className="flex items-center flex-wrap justify-between mb-6">
                            <h3 className="font-semibold text-black text-xl" data-testid="heading">
                                {selectedRecipients.length} Recipients
                            </h3>

                            <div className="flex items-center gap-4">
                                <SearchInput
                                    placeholder="Search"
                                    size="md"
                                    width="w-[252px] max-w-full"
                                    value={searchInput}
                                    onChange={handleSearchUpdate}
                                />
                                <Button onClick={() => setAddRecipientModal(true)}>Add more</Button>
                            </div>
                        </div>
                    )}

                    <RecipientsTable
                        type="valid"
                        key="Valid"
                        selectedRecipients={searchedRecipients}
                        setSelectedRecipients={setSelectedRecipients}
                        setSelectedTableValues={setSelectedTableValues}
                        shouldResetTableSelection={shouldResetTableSelection}
                        setShouldResetTableSelection={setShouldResetTableSelection}
                    />
                </div>
                <div className="flex items-center justify-between w-full mt-auto gap-3 px-[56px] py-4 border-t border-t-[#E3E5E8]">
                    <div>
                        {selectedTemplate && (
                            <Checkbox
                                checked={shouldUpdateTemplate}
                                onCheckedChange={(checked) => setShouldUpdateTemplate(checked)}
                                aria-label="Update saved template"
                                label="Update saved template"
                            />
                        )}
                    </div>
                    <div className="flex items-center justify-end gap-3">
                        <Button variant="outline" onClick={onClose} data-testid="cancel-button">
                            {!selectedTemplate ? "Cancel" : "Go back"}
                        </Button>
                        <Button
                            onClick={() => {
                                goToNextStep();
                                onClose();
                            }}
                            data-testid="continue-button"
                        >
                            Continue with {selectedRecipients.length} payments
                        </Button>
                    </div>
                </div>
            </div>

            <AddRecipientDialog
                open={addRecipientModal}
                onClose={() => setAddRecipientModal(false)}
                setSelectedRecipients={setSelectedRecipients}
                selectedRecipients={selectedRecipients}
            />

            <DeleteTableValueControl
                handleClearTableSelection={clearTableSelection}
                handleDeleteSelectedTableValues={deleteSelectedValues}
                selectedTableValues={selectedTableValues}
                showAmount
            />
        </FullScreenDrawer>
    );
};

export default ProcessedResultsModal;
