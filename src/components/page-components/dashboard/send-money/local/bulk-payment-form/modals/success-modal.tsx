import { But<PERSON> } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { useAppSelector } from "@/redux/hooks";
import { GetBulkTemplateType } from "@/redux/types/send-money";
import { PATH_PROTECTED } from "@/routes/path";
import Link from "next/link";
import { Dispatch, SetStateAction, useMemo } from "react";
import { BulkRecipientType, MultiplePaymentFormType } from "../../../data";
import { DocumentIcon } from "../../../icons";
import { calculateTransactionFee } from "../../utils";

type Props<T extends MultiplePaymentFormType> = {
    open: boolean;
    selectedRecipients: BulkRecipientType[] | [];
    selectedTemplate: GetBulkTemplateType | undefined;
    onClose: () => void;
    setSelectedTemplate: Dispatch<SetStateAction<GetBulkTemplateType | undefined>>;
};
const SuccessModal = <T extends MultiplePaymentFormType>({
    open,
    onClose,
    selectedRecipients,
    selectedTemplate,
    setSelectedTemplate,
}: Props<T>) => {
    const closeModal = () => {
        onClose();
        setSelectedTemplate(undefined);
    };
    const { reference } = useAppSelector((state) => state.sendMoney.sendBulkTransfer ?? {});

    const { data } = useAppSelector((state) => state.sendMoney.getTransferApprovers);

    const requiresApproval = useMemo(() => data && data.length > 0, [data]);

    return (
        <div data-testid="success-modal">
            <FullScreenDrawer isOpen={open} onClose={closeModal}>
                <div className="flex items-center flex-col text-center py-10" data-testid="success-data">
                    <div className="w-full max-w-[420px] flex items-center flex-col ">
                        <DocumentIcon />
                        <h4 className="text-black font-bold text-2xl mb-2 mt-8">
                            {!requiresApproval ? "Payment sent!" : "Payment sent for approval"}
                        </h4>
                        <p>
                            Your bulk payment{" "}
                            {!requiresApproval
                                ? "is being processed."
                                : "will be processed when all approvals are complete."}
                        </p>

                        <div className="w-full mt-10 border border-[#E3E5E8] p-5 rounded-[20px]">
                            <div className="bg-[#F9F9FA] rounded-[12px] p-5 flex flex-col text-center w-full items-center">
                                <p className="font-bold text-[24px]">
                                    {formatNumberToNaira(
                                        selectedRecipients.reduce(
                                            (a, b) => a + Number((b as BulkRecipientType).amount || 0),
                                            0
                                        ) || 0,
                                        2
                                    )}
                                </p>
                                <p className="font-medium">
                                    {`(+ ${formatNumberToNaira(
                                        selectedRecipients.reduce(
                                            (a, b) =>
                                                a +
                                                calculateTransactionFee({
                                                    amount: (b as BulkRecipientType).amount,
                                                    destinationBank: (b as BulkRecipientType).bankCode,
                                                }),
                                            0
                                        ),
                                        2
                                    )} fees)`}
                                </p>
                            </div>
                            <div className="my-5 flex flex-col gap-3 w-full">
                                <div className="flex items-center justify-between w-full">
                                    <p className="text-sm text-subText">Recipients</p>
                                    <div className="bg-[#F9F9FA] px-2 py-1 rounded-full text-xs">
                                        {selectedRecipients.length}
                                    </div>
                                </div>
                                {selectedTemplate && (
                                    <div className="flex items-center justify-between w-full">
                                        <p className="text-sm text-subText">Template name</p>
                                        <div className="text-sm capitalize">{selectedTemplate.filename}</div>
                                    </div>
                                )}
                                <div className="flex items-center justify-between w-full gap-2 flex-wrap">
                                    <p className="text-sm text-subText">Reference</p>
                                    <div className="text-sm">{reference || "-"}</div>
                                </div>
                            </div>
                            <div className="pt-5 border-t border-t-[#E3E5E8] flex items-center gap-3 justify-end w-full">
                                {
                                    <Link href={PATH_PROTECTED.payments.outgoing.root}>
                                        <Button variant="outline" onClick={closeModal}>
                                            View payment
                                        </Button>
                                    </Link>
                                }
                                <Button onClick={closeModal}>Done</Button>
                            </div>
                        </div>
                    </div>
                </div>
            </FullScreenDrawer>
        </div>
    );
};

export default SuccessModal;
