import { Button } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import LoadingIndicator from "@/components/common/loading-indicator";
import { LocalRecipientType } from "@/components/page-components/dashboard/recipients/type";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { useTransactionFees } from "@/hooks/useTransactionFees";
import { useAppSelector } from "@/redux/hooks";
import { PATH_PROTECTED } from "@/routes/path";
import { FormikProps } from "formik";
import Link from "next/link";
import { useMemo } from "react";
import { MultiplePaymentFormType } from "../../../data";
import { DocumentIcon } from "../../../icons";

type Props<T extends MultiplePaymentFormType> = {
    formik: FormikProps<T>;
    open: boolean;
    selectedRecipients: LocalRecipientType[] | [];
    onClose: () => void;
};
const SuccessModal = <T extends MultiplePaymentFormType>({ open, onClose, formik, selectedRecipients }: Props<T>) => {
    const { fees, loading } = useTransactionFees({ amount: Number(formik.values.amount) });
    const { reference } = useAppSelector((state) => state.sendMoney.sendMultipleTransfer ?? {});
    const { data } = useAppSelector((state) => state.sendMoney.getTransferApprovers);
    const requiresApproval = useMemo(() => data && data.length > 0, [data]);

    return (
        <div data-testid="success-modal">
            <FullScreenDrawer isOpen={open} onClose={onClose}>
                <div className="flex items-center flex-col text-center py-10" data-testid="success-data">
                    <div className="w-full max-w-[420px] flex items-center flex-col ">
                        <DocumentIcon />
                        <p className="text-black font-bold text-2xl mb-1 mt-8">
                            {!requiresApproval ? "Payment successful" : "Payment sent for approval"}
                        </p>
                        <p className="mb-8">
                            Your payment{" "}
                            {!requiresApproval
                                ? "has been processed successfully."
                                : "will be processed when all approvals are complete."}
                        </p>

                        <div className="w-full mt-10 border border-[#E3E5E8] p-5 rounded-[20px]">
                            <div className="bg-[#F9F9FA] rounded-[12px] p-5 flex flex-col text-center w-full items-center">
                                <p className="font-bold text-[24px]">
                                    {formatNumberToNaira(Number(formik.values.amount || ""), 2)}
                                </p>
                                <p className="font-medium">
                                    {loading ? (
                                        <LoadingIndicator size={20} />
                                    ) : (
                                        `(+ ${formatNumberToNaira(fees * selectedRecipients.length, 2)} fees)`
                                    )}
                                </p>
                            </div>
                            <div className="my-5 flex flex-col gap-3 w-full">
                                <div className="flex items-center justify-between w-full">
                                    <p className="text-sm">Recipients</p>
                                    <div className="bg-[#F9F9FA] px-2 py-1 rounded-full text-xs">
                                        {selectedRecipients.length}
                                    </div>
                                </div>
                                <div className="flex items-center justify-between w-full gap-2 flex-wrap">
                                    <p className="text-sm">Reference</p>
                                    <div className="text-xs">{reference || "-"}</div>
                                </div>
                            </div>
                            <div className="pt-5 border-t border-t-[#E3E5E8] flex items-center gap-3 justify-end w-full">
                                <Link href={PATH_PROTECTED.payments.outgoing.root}>
                                    <Button onClick={onClose} variant="outline">
                                        View payment
                                    </Button>
                                </Link>
                                <Button onClick={onClose}>Done</Button>
                            </div>
                        </div>
                    </div>
                </div>
            </FullScreenDrawer>
        </div>
    );
};

export default SuccessModal;
