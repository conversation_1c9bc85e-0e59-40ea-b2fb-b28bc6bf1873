import { formatDateForSentence } from "@/functions/date";
import { FormikProps } from "formik";
import pluralize from "pluralize";
import { SinglePaymentFormType } from "../data";
import { CalendarIcon } from "../icons";
import { getFrequencyValue, getRecurringEndStringFromOccurrences, getRecurringFeedbackString } from "./utils";

type Props<T extends SinglePaymentFormType> = {
    formik: FormikProps<T>;
};

const TransferTypeIndicator = <T extends SinglePaymentFormType>({ formik }: Props<T>) => (
    <div>
        {formik.values.transferType === "SCHEDULED" && formik.values.scheduledDate && (
            <div
                className="mb-8 mt-5 flex items-center w-full gap-3 p-4 rounded-lg bg-[#F9F0FE]"
                data-testid="scheduledDate"
            >
                <div>
                    <CalendarIcon />
                </div>
                <p className="text-[#7707B6] text-sm">
                    This payment will be scheduled for {formatDateForSentence(new Date(formik.values.scheduledDate))}
                </p>
            </div>
        )}

        {formik.values.transferType === "RECURRING" &&
            formik.values.reoccurringFrequency &&
            formik.values.reoccurringStartDate && (
                <div className="mt-5 mb-8 flex items-center w-full gap-3 p-4 rounded-lg bg-[#F9F0FE]">
                    <div>
                        <CalendarIcon />
                    </div>
                    <p className="text-[#7707B6] text-sm">
                        {formik.values.reoccurringEndOccurrences
                            ? getRecurringEndStringFromOccurrences({
                                  occurrences: formik.values.reoccurringEndOccurrences,
                                  frequency: formik.values.reoccurringFrequency,
                              })
                            : formik.values.reoccurringEndDate &&
                              getRecurringFeedbackString({
                                  frequency: formik.values.reoccurringFrequency,
                                  startDate: formik.values.reoccurringStartDate,
                                  endDate: formik.values.reoccurringEndDate,
                              })}{" "}
                        {pluralize(
                            "payments",
                            getFrequencyValue({
                                frequency: formik.values.reoccurringFrequency,
                                startDate: formik.values.reoccurringStartDate,
                                endDate: formik.values.reoccurringEndDate,
                                endCount: formik.values.reoccurringEndOccurrences,
                            })
                        )}{" "}
                        will be created. Your first payment will process after initial approval. Future payments will be
                        automatic.
                    </p>
                </div>
            )}
    </div>
);

export default TransferTypeIndicator;
