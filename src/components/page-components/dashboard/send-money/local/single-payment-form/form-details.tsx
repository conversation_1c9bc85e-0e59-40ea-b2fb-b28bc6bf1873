"use client";

import AmountInput from "@/components/common/amount-input";
import { CheckboxLabel } from "@/components/common/checkbox-label";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import LoadingIndicator from "@/components/common/loading-indicator";
import { sendCatchFeedback } from "@/functions/feedback";
import { fetchRecipientDetails } from "@/redux/actions/recipients/local-recipient";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { recipientActions } from "@/redux/slices/recipientsSlice";
import { FormikProps } from "formik";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import AccountSelector from "../../../accounts/payments/components/account-selector";
import { SinglePaymentFormType } from "../../data";
import { VerifiedIcon } from "../../icons";
import TransferTypeSelector from "../transfer-type-selector";

type Props<T extends SinglePaymentFormType> = {
    formik: FormikProps<T>;
    openScheduleModal: () => void;
    openRecipientModal: () => void;
    openRecurringModal: () => void;
    setAccountBalance: Dispatch<SetStateAction<number | undefined>>;
    setSaveRecipient: Dispatch<SetStateAction<boolean>>;
    showSaveRecipient: boolean;
    saveRecipient: boolean;
};

const FormDetails = <T extends SinglePaymentFormType>({
    formik,
    openRecipientModal,
    openRecurringModal,
    openScheduleModal,
    setAccountBalance,
    saveRecipient,
    setSaveRecipient,
    showSaveRecipient,
}: Props<T>) => {
    const [internalRetrieval, setInternalRetrieval] = useState(false);
    const [accountVerified, setAccountVerified] = useState(false);
    const {
        details,
        loading: fetchingDetails,
        error: fetchDetailsError,
    } = useAppSelector((state) => state.recipient.getRecipientDetails ?? {});
    const dispatch = useAppDispatch();
    const { banks, loading: fetchingBanks } = useAppSelector((state) => state.recipient.getLocalBanks ?? []);

    // Trigger verification when all required fields are filled
    useEffect(() => {
        const getAccountDetails = async () => {
            try {
                await dispatch(
                    fetchRecipientDetails({
                        accountNumber: formik.values.accountNumber,
                        channelCode: "1",
                        destinationInstitutionCode: formik.values.bank,
                    })
                );
            } catch (error) {
                sendCatchFeedback(error);
            } finally {
                setInternalRetrieval(true);
            }
        };
        if (formik.values.bank && formik.values.accountNumber && formik.values.accountNumber.length === 10) {
            getAccountDetails();
        }
    }, [formik.values.bank, formik.values.accountNumber, dispatch]);

    useEffect(() => {
        if (fetchDetailsError || (details && !details.accountName)) {
            formik.setFieldValue("accountName", "");
            sendCatchFeedback(fetchDetailsError ?? "Could not retrieve account details", () => {
                dispatch(recipientActions.clearState("getRecipientDetails"));
            });
            setAccountVerified(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fetchDetailsError, details, dispatch]);

    // Update account name when details are fetched
    useEffect(() => {
        // update only when the retrieval was done from here
        if (internalRetrieval && details && details.accountName) {
            setAccountVerified(true);
            formik.setFieldValue("accountName", details.accountName);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [details, internalRetrieval]);

    return (
        <div className="w-full">
            <div className="flex-col flex items-center w-full">
                <div className="flex flex-col w-full items-center">
                    <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                        Payment information
                    </h3>
                </div>
                <form className="w-full mt-12">
                    <AccountSelector
                        labelName="Send from"
                        selectedAccount={formik.values.transferSource}
                        onChange={(account) => formik.setFieldValue("transferSource", account)}
                        updateExternalBalance={(balance) => setAccountBalance(balance)}
                    />

                    <LabelInput
                        formik={formik}
                        name="accountNumber"
                        customLabel={
                            <div className="flex items-center w-full justify-between">
                                <label
                                    htmlFor="accountNumber"
                                    className="font-primary text-xs text-black font-semibold"
                                >
                                    Account number
                                </label>
                                <button
                                    type="button"
                                    className="text-primary font-semibold text-xs"
                                    onClick={openRecipientModal}
                                >
                                    Choose recipient
                                </button>
                            </div>
                        }
                        className="my-5"
                        data-testid="form-field-accountNumber"
                    />

                    <Dropdown
                        options={banks?.map((bank) => ({ label: bank.bankName, value: bank.bankCode }))}
                        name="bank"
                        label="Bank"
                        size="sm"
                        className="mb-5"
                        formik={formik}
                        isLoading={fetchingBanks}
                        value={{
                            label: banks?.find((bank) => bank.bankCode === formik.values.bank)?.bankName ?? "",
                            value: formik.values.bank,
                        }}
                    />

                    <div className="relative mb-5">
                        <LabelInput
                            formik={formik}
                            name="accountName"
                            label="Account name"
                            placeholder={
                                fetchingDetails
                                    ? "Verifying account..."
                                    : "Complete all fields above to fetch recipient name"
                            }
                            className="mb-5"
                            disabled={true}
                            readOnly={true}
                        />

                        {fetchingDetails && (
                            <div className="absolute right-4 top-[55%]">
                                <LoadingIndicator size={16} />
                            </div>
                        )}
                        {!fetchingDetails && accountVerified && (
                            <div className="absolute right-4 top-[55%]">
                                <VerifiedIcon />
                            </div>
                        )}
                    </div>
                    {showSaveRecipient && (
                        <div className="mb-5">
                            <CheckboxLabel
                                checked={saveRecipient}
                                onChange={(checked) => setSaveRecipient(checked)}
                                label="Save recipient for future payments"
                            />
                        </div>
                    )}
                    <AmountInput<T> formik={formik} className="mb-5" label="Amount" name="amount" />

                    <TransferTypeSelector
                        formik={formik}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                    />

                    <LabelInput
                        formik={formik}
                        name="narration"
                        label="Narration"
                        className="my-5"
                        placeholder="Write something here..."
                    />
                </form>
            </div>
        </div>
    );
};

export default FormDetails;
