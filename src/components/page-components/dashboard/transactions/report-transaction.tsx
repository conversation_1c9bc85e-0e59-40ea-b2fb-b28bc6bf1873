"use client";

import Dropdown from "@/components/common/dropdown";
import LabelTextArea from "@/components/common/text-area";
import { useState } from "react";
import FileAttachment from "@/components/common/file-attachment";

const reasonValues = [
    "Amount debited but recipient was not credited",
    "Transaction was not authorized by me",
    "I got multiple debits for a single transaction",
    "Payment was sent to the wrong recipient",
    "Transaction failed but my account was debited",
    "Other reason",
];

export function ReportTransaction() {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    return (
        <div className="grid gap-6 mt-8">
            <Dropdown
                label="Select a reason"
                name="reason"
                options={reasonValues.map((item) => ({
                    label: item,
                    value: item,
                }))}
            />

            <LabelTextArea name="info" label="Tell us more" placeholder="Placeholder text...." />

            <FileAttachment
                maxSize={10}
                acceptedTypes={["pdf", "png", "jpg"]}
                onFilesSelected={(files) => {
                    setSelectedFile(files[0]);
                    console.log({ selectedFile });
                }}
                width="100%"
                headerText="Attachments"
                onFileRemoved={() => setSelectedFile(null)}
            />
        </div>
    );
}
