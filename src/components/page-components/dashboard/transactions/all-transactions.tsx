"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import { Pagination } from "@/components/common/pagination";
import PermissionGate from "@/components/common/permission-gate";
import { DataTable } from "@/components/common/table/DataTable";

import { convertCamelCaseToWords } from "@/functions/stringManipulations";
import { getAccounts, getTransactions } from "@/redux/actions/transactionActions";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { transactionActions } from "@/redux/slices/transactionSlice";
import { PATH_PROTECTED } from "@/routes/path";
import {
    ColumnFiltersState,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import { AlertCircle, X } from "lucide-react";
import dynamic from "next/dynamic";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDebounce } from "use-debounce";
import { AccountSelector } from "./account-selector";
import { columns } from "./column-data";
import { sampleTransaction } from "./data";
import { ExportTransactions } from "./export-transactions";
import { TransactionFilter } from "./transaction-filter";
import { Receipt } from "./transaction-receipts/receipt";

const TransactionDetailsError = () => <div className="text-red-500">Failed to load transaction details</div>;

const TransactionDetails = dynamic(() => import("./transaction-details").then((mod) => mod.TransactionDetails), {
    ssr: false,
});

const MultiStepTransactionFormModal = dynamic(
    () => import("./multi-step-transaction-form-modal").then((mod) => mod.MultiStepTransactionFormModal),
    { ssr: false }
);

function formatDate(dateStr: string): string {
    const decodedDateStr = decodeURIComponent(dateStr);
    const date = new Date(decodedDateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
}

interface ITransactionFilter {
    pageNo?: number | null;
    pageSize?: number | null;
    search?: string;
    startDate?: string;
    endDate?: string;
    minAmount?: string;
    maxAmount?: string;
    amount?: string;
}

const AllTransactions = () => {
    const { t } = useTranslation();
    const params = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const dispatch = useAppDispatch();

    const { list, transactions, accountGet } = useAppSelector((state) => state.transaction);

    // Temporary debug: Log all permissions
    const { userPermissions } = useAppSelector((state) => state.permissions);
    console.log("🔍 All user permissions:", userPermissions);

    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [isReportOpen, setIsReportOpen] = useState(false);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [rowSelection, setRowSelection] = useState({});
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [transactionId, setTransactionId] = useState<string | null>(null);

    const data = transactions;
    const { loading, count, isAvailable } = list;

    const [currentFilters, setCurrentFilters] = useState<ITransactionFilter>({
        pageNo: params.get("pageNo") !== null ? Number(params.get("pageNo")) : list.currentPage,
        pageSize: params.get("pageSize") !== null ? Number(params.get("pageSize")) : list.size,
        search: params.get("search") ?? "",
        startDate: params.get("startDate") ?? "",
        endDate: params.get("endDate") ?? "",
        minAmount: params.get("minAmount") ?? "",
        maxAmount: params.get("maxAmount") ?? "",
        amount: params.get("amount") ?? "",
    });

    const [tempFilters, setTempFilters] = useState<ITransactionFilter>({ ...currentFilters });
    const [debouncedSearch] = useDebounce(currentFilters.search, 300);

    const createQueryString = useCallback((filters: ITransactionFilter) => {
        const newParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                newParams.set(key, value);
            }
        });
        return newParams.toString();
    }, []);

    const updateFilters = useCallback(
        async (newFilters: ITransactionFilter) => {
            setCurrentFilters(newFilters);
            const queryString = createQueryString(newFilters);
            router.push(pathname + (queryString ? "?" + queryString : ""));
        },
        [createQueryString, pathname, router]
    );

    const onSearch = useCallback(
        (value: string) => {
            updateFilters({ ...currentFilters, search: value });
        },
        [currentFilters, updateFilters]
    );

    const onChangePage = useCallback(
        async (newPage: number) => {
            await updateFilters({ ...currentFilters, pageNo: newPage });
            dispatch(transactionActions.onPageChange(newPage));
        },
        [currentFilters, updateFilters, dispatch]
    );

    const onChangeRowsPerPage = useCallback(
        async (value: number) => {
            await updateFilters({ ...currentFilters, pageSize: value });
            dispatch(transactionActions.onChangeRowsSize(value));
        },
        [currentFilters, updateFilters, dispatch]
    );

    const onGetTransactions = useCallback(async () => {
        const { startDate, endDate, minAmount, maxAmount } = tempFilters;
        const { pageNo, pageSize } = currentFilters;
        if (!accountGet.account?.accountNumber) return;
        dispatch(
            getTransactions({
                accountNumber: accountGet.account?.accountNumber as string,
                params: {
                    pageNo: pageNo ? (pageNo > 0 ? pageNo - 1 : 0) : list.currentPage - 1,
                    pageSize: pageSize ?? undefined,
                    minAmount: minAmount ?? undefined,
                    maxAmount: maxAmount ?? undefined,
                    startDate: startDate ? formatDate(startDate) : undefined,
                    endDate: endDate ? formatDate(endDate) : undefined,
                },
            })
        );
    }, [tempFilters, currentFilters, accountGet.account?.accountNumber, dispatch, list.currentPage]);

    const onClearAll = useCallback(() => {
        const emptyFilters: ITransactionFilter = {
            search: "",
            startDate: "",
            endDate: "",
            minAmount: "",
            maxAmount: "",
            amount: "",
        };
        setTempFilters(emptyFilters);
        setCurrentFilters(emptyFilters);
        updateFilters({ pageNo: list.currentPage, pageSize: list.size, ...emptyFilters }).then(onGetTransactions);
    }, [list.currentPage, list.size, onGetTransactions, updateFilters]);

    const onApplyFilter = useCallback(() => {
        setCurrentFilters(tempFilters);
        updateFilters({ ...currentFilters, ...tempFilters }).then(onGetTransactions);
    }, [tempFilters, updateFilters, onGetTransactions, currentFilters]);

    const handleSearch = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            onSearch(event.target.value);
        },
        [onSearch]
    );

    const handleCloseDetails = () => {
        setIsOpen(false);
        setTransactionId(null);
    };

    useEffect(() => {
        if (debouncedSearch !== undefined) {
            // Fetch transactions with debounced search
        }
    }, [debouncedSearch]);

    useEffect(() => {
        if (isAvailable || accountGet.account?.accountNumber) {
            onGetTransactions();
        }
    }, [
        currentFilters.pageNo,
        currentFilters.pageSize,
        accountGet.account?.accountNumber,
        isAvailable,
        onGetTransactions,
    ]);

    useEffect(() => {
        dispatch(getAccounts());
    }, [dispatch]);

    const downloadTransactionReceipt = async (id: string) => {
        await dispatch(
            downloadReceipt({
                transactionId: id,
                isUserInitiated: true,
            })
        );
    };
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            rowSelection,
            columnFilters,
        },
        meta: {
            setIsOpen: (value: boolean) => setIsOpen(value),
            setIsReportOpen: (value: boolean) => setIsReportOpen(value),
            setTransactionId: (value: string) => setTransactionId(value),
            handleReportDownload: downloadTransactionReceipt,
        },
    });

    return (
        <>
            <div>
                <div className="flex items-center bg-white flex-wrap gap-[26px] pt-8 pb-6">
                    <h1 className="font-bold text-xl text-black">{t("Transactions")}</h1>
                    {!accountGet.loading ? (
                        <AccountSelector
                            accounts={accountGet.accounts}
                            onAccountChange={(accountId) => {
                                dispatch(transactionActions.setCurrentAccount(accountId));
                            }}
                        />
                    ) : (
                        <div className="flex items-center gap-2 text-gray-600">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                            <span className="text-sm">Loading accounts...</span>
                        </div>
                    )}
                </div>
            </div>

            <div className="flex items-center justify-between overscroll-x-auto">
                {!accountGet.loading && accountGet.accounts.length === 0 ? (
                    <div className="w-full opacity-60 pointer-events-none">
                        <TransactionFilter
                            currentFilters={currentFilters}
                            tempFilters={tempFilters}
                            setTempFilters={setTempFilters}
                            onApplyFilter={onApplyFilter}
                            onClearAll={onClearAll}
                            onSearch={handleSearch}
                        />
                    </div>
                ) : (
                    <>
                        <TransactionFilter
                            currentFilters={currentFilters}
                            tempFilters={tempFilters}
                            setTempFilters={setTempFilters}
                            onApplyFilter={onApplyFilter}
                            onClearAll={onClearAll}
                            onSearch={handleSearch}
                        />

                        {/* <PermissionGate
                            permission="View transaction details"
                            fallback={
                                <div className="text-xs text-gray-500 p-2 border border-dashed">
                                    Debug: Missing "View transaction details" permission
                                </div>
                            }
                        > */}
                            <ExportTransactions
                                accounts={accountGet.accounts}
                                columns={columns}
                                data={data}
                                selectedRows={
                                    Object.keys(rowSelection).length > 0
                                        ? Object.keys(rowSelection).map((index) => data[parseInt(index)])
                                        : []
                                }
                            />
                        {/* </PermissionGate> */}
                    </>
                )}
            </div>

            <CurrentFilterItems currentFilters={currentFilters} onClearAll={onClearAll} />

            <div className="!mt-8 flex-1 flex flex-col justify-between">
                {!accountGet.loading && accountGet.accounts.length === 0 ? (
                    <div className="bg-white rounded-lg p-8 text-center">
                        <div className="flex flex-col items-center justify-center gap-4">
                            <div className="bg-amber-50 p-3 rounded-full">
                                <AlertCircle size={24} className="text-amber-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">No Accounts Available</h3>
                            <p className="text-gray-600 max-w-md">
                                You don't have any accounts set up yet. Transaction data will be displayed once you have
                                at least one account.
                            </p>
                        </div>
                    </div>
                ) : (
                    <>
                        <DataTable columns={columns} table={table} loading={loading} />

                        {data.length > 0 ? (
                            <div className="absolute inset-0 top-auto bottom-5">
                                <Pagination
                                    totalItems={count}
                                    onPageChange={onChangePage}
                                    onItemsPerPageChange={onChangeRowsPerPage}
                                    initialItemsPerPage={currentFilters.pageSize ?? list.size}
                                    initialPage={list.currentPage}
                                />
                            </div>
                        ) : null}
                    </>
                )}
            </div>

            <TransactionDetails
                transactionId={transactionId as string}
                handleCloseDetails={handleCloseDetails}
                isOpen={isOpen}
                handleOpenReport={() => setIsReportOpen(true)}
            />

            <MultiStepTransactionFormModal
                isOpen={isReportOpen}
                closeModal={() => setIsReportOpen(false)}
                onComplete={() => {}}
            />

            <div className="hidden inset-0 bg-white">
                <div className="container mx-auto p-4 space-y-4">
                    <div className="flex justify-end space-x-2 print:hidden">
                        <Button variant="outline" onClick={() => window.print()}>
                            Print Receipt
                        </Button>
                    </div>

                    <Receipt variant="modern" details={sampleTransaction} />
                </div>
            </div>
        </>
    );
};

interface CurrentFilterItemsProps {
    currentFilters: ITransactionFilter;
    onClearAll: () => void;
}

export const CurrentFilterItems: React.FC<CurrentFilterItemsProps> = ({ currentFilters, onClearAll }) => {
    const { pageNo, pageSize, ...others } = currentFilters;
    const hasFilters = Object.entries(others).some(([key, value]) => key !== "search" && value);

    if (!hasFilters) {
        return null;
    }

    return (
        <div className="flex items-center gap-[26px] !mt-6">
            <div className="flex flex-wrap gap-2">
                {Object.entries(others).map(([key, value]) => {
                    if (key !== "search" && value) {
                        let displayText = `${convertCamelCaseToWords(key)}: ${value}`;

                        if (key === "startDate" || key === "endDate") {
                            displayText = `${key === "startDate" ? "From" : "To"}: ${formatDate(value)}`;
                        }

                        return <FilterItem key={key} text={displayText} />;
                    }
                    return null;
                })}
            </div>
            <Button variant="text-destructive" leftIcon={<X color="#D92D20" size={16} />} onClick={onClearAll}>
                Clear Filter
            </Button>
        </div>
    );
};

const FilterItem = ({ text }: { text: string }) => (
    <span className="flex items-center rounded-full py-[9px] px-3 border border-[#DBDBE1] bg-[#F9F9FA] text-sm leading-[18px] font-medium text-subText h-8 w-max">
        {text}
    </span>
);

// Use the new dynamic permission HOC
import { withDynamicPermissionCheck } from "@/components/hoc/withDynamicPermissionCheck";

// Create protected component with dynamic permissions
const ProtectedPage = withDynamicPermissionCheck(AllTransactions, {
    // Use dynamic permission checker that automatically updates when backend permissions change
    permissions: (P) => [P.TRANSACTIONS.VIEW_ALL],
    // User needs any of these permissions (not all)
    requireAll: false,
    // Where to redirect if user doesn't have permissions
    redirectTo: PATH_PROTECTED.root,
    // Fallback permissions for safety (optional)
    fallbackPermissions: ["View all transactions (CIB + external channels)"],
    // Show loading state while permissions are being resolved
    showLoadingState: true,
});

export default ProtectedPage;
