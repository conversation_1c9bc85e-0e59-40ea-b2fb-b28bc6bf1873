"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/common/Avatar";
import { Button } from "@/components/common/buttonv3";
import SideDrawer from "@/components/common/drawer";
import TabSwitch from "@/components/common/tab-switch";
import { formatDate } from "@/functions/date";
import { formatNumberToNaira, getNameInitials } from "@/functions/stringManipulations";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { transactionActions } from "@/redux/slices/transactionSlice";
import { ITransaction } from "@/redux/types/transactions";
import { ITransactionType } from "@/types";
import { X } from "lucide-react";
import { useEffect, useMemo } from "react";
import TabAttachments from "./tab-attachments";
import TabDetails from "./tab-details";
import TabPeople from "./tab-People";

interface TransactionDetailsProps {
    transactionId: string;
    isOpen: boolean;
    handleCloseDetails: () => void;
    handleOpenReport: () => void;
    selectedTransaction?: ITransaction;
}

export function TransactionDetails({
    handleCloseDetails,
    isOpen,
    // handleOpenReport,
    transactionId,
    selectedTransaction,
}: TransactionDetailsProps) {
    const dispatch = useAppDispatch();
    const { get, transaction } = useAppSelector((state) => state.transaction);
    const { downloadReceiptLoading } = useAppSelector((state) => state.transfer);
    const { loading, error } = get;
    const transactionToUse = useMemo(() => selectedTransaction ?? transaction, [selectedTransaction, transaction]);

    useEffect(() => {
        if (transactionId) {
            dispatch(transactionActions.getTransaction(transactionId));
        }
        // eslint-disable-next-line
    }, [transactionId]);

    const handleReportDownload = async () => {
        await dispatch(
            downloadReceipt({
                transactionId,
                isUserInitiated: true,
            })
        );
    };

    return (
        <div>
            <SideDrawer isOpen={isOpen} className="!max-w-[488px]">
                <div className="mt-[19px] px-6">
                    <button onClick={handleCloseDetails} className="ml-auto block" data-testid="close-btn">
                        <X color="#90909D" />
                    </button>
                </div>
                {transactionToUse && !loading && !error ? (
                    <div className="flex flex-col justify-between h-full">
                        <div className="px-6">
                            <div className="border-b border-[#E3E5E8] pb-[19px]">
                                <div className="flex justify-between mt-[36px]">
                                    <div className="flex gap-[12px] items-center">
                                        <div>
                                            <Avatar className="w-[64px] h-[64px]">
                                                <AvatarImage src="" alt="" />
                                                <AvatarFallback className="text-[20px] leading-[26px] font-medium text-subText">
                                                    {getNameInitials(transactionToUse.counterpartyType)}
                                                </AvatarFallback>
                                            </Avatar>
                                        </div>
                                        <div className="flex flex-col gap-[12px]">
                                            <h4 className="text-[18px] leading-6 font-semibold text-black">
                                                {transactionToUse.counterpartyType}
                                            </h4>
                                            <p className="text-sm leading-[18px] font-normal text-subText">
                                                {transactionToUse.narration}
                                            </p>
                                            <p className="text-sm leading-[18px] font-normal text-subText">
                                                {formatDate(transactionToUse.createdDate)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-[12px] items-end">
                                        <h4
                                            className={`text-[18px] leading-6 font-semibold ${transaction?.transactionType === "Credit" ? "text-[var(--success)]" : "text-black"}`}
                                        >
                                            {transaction?.transactionType === "Credit" ? "+" : "-"}{" "}
                                            {formatNumberToNaira(transactionToUse.amount)}
                                        </h4>
                                        <TransactionBadge transactionType={transactionToUse?.transactionType} />
                                    </div>
                                </div>
                            </div>

                            {transaction?.transactionType === "Debit" ? (
                                <div>
                                    <TabSwitch
                                        tabs={["Details", "People involved", "Attachments"]}
                                        panels={[
                                            <TabDetails key="details" transaction={transaction} />,
                                            <TabPeople key="people involved" amount={transactionToUse.amount} />,
                                            <TabAttachments key="attachments" />,
                                        ]}
                                        tabSpacing="h-[32px]"
                                    />
                                </div>
                            ) : (
                                <div className="py-8">
                                    <h4 className="text-sm leading-[18px] font-medium mb-8">Details</h4>
                                    <TabDetails key="details" transaction={transactionToUse} />
                                </div>
                            )}
                        </div>

                        <div className="flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-4 px-6">
                            {/* <Button type="submit" variant="outline" onClick={handleOpenReport}>
                                Report transaction
                            </Button> */}
                            <Button onClick={handleReportDownload} loading={downloadReceiptLoading}>
                                Download receipt
                            </Button>
                        </div>
                    </div>
                ) : null}
            </SideDrawer>
        </div>
    );
}

export const TransactionBadge = ({ transactionType }: { transactionType: ITransactionType }) => {
    const getTransactionTypeColor = (status: ITransactionType): { color: string; bgColor: string } => {
        let color: string;
        let bgColor: string;
        switch (status) {
            case "Debit":
                bgColor = "#FEF3F2";
                color = "#D92D20";
                break;
            case "Credit":
                bgColor = "#ECFDF3";
                color = "#039855";
                break;
            default:
                color = "";
                bgColor = "";
                break;
        }

        return { color, bgColor };
    };

    return (
        <div
            className="rounded-full py-[6px] px-[10px] max-w-max capitalize text-[14px] leading-[18px] font-medium leading-4]"
            style={{
                backgroundColor: getTransactionTypeColor(transactionType).bgColor,
                color: getTransactionTypeColor(transactionType).color,
            }}
        >
            {transactionType}
        </div>
    );
};
