"use client";
import React, { useEffect, useState, useRef, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/common/buttonv3";
import { PlusIcon } from "@/components/icons/team";
import { RolesFilter } from "./roles-filter";
import { DataTable } from "@/components/common/table/DataTable";
import { getCoreRowModel, TableMeta, useReactTable } from "@tanstack/react-table";
import RolePermissionContent from "./role-permission-content";
import { columns, FullRolesColumnData } from "./roles-column-data";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getAllPermissions, getAllRoles } from "@/redux/actions/rolesActions";
import { getTeamMembers } from "@/redux/actions/teamMembersActions";
import { RoleData } from "@/redux/types/roles";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import CreateEditCustomRoleModal from "./modals/permission-adapter";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { usePermissions } from "@/contexts/PermissionContext";
import { PermissionFallback } from "@/components/common/permission-fallback";
import { withDynamicPermissionCheck } from "@/components/hoc/withDynamicPermissionCheck";
import { PATH_PROTECTED } from "@/routes/path";

interface IRolesTableMeta extends TableMeta<FullRolesColumnData> {
    setIsOpen: (value: boolean) => void;
    setRoleToView: (value: RoleData) => void;
    setIsViewPermissionsSideDrawerOpen: (value: boolean) => void;
    isViewPermissionsSideDrawerOpen: boolean;
}

const RolesComponent = () => {
    const dispatch = useAppDispatch();
    // Selector with memoized data
    const { error, success, loading, data: tableData = [] } = useAppSelector((state) => state.roles.getAllRoles);

    const {
        data: permissionData,
        success: permissionSuccess,
        loading: permissionLoading,
    } = useAppSelector((state) => state.roles.getAllPermissions);

    // Use dynamic permissions from context
    const { systemPermissions, isLoadingSystemPermissions } = usePermissions();

    const { t } = useTranslation();
    const [isCreateCustomRoleModalOpen, setIsCreateCustomRoleModalOpen] = useState(false);
    const [rowSelection, setRowSelection] = useState({});
    const [isEditMode, setIsEditMode] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isViewPermissionsSideDrawerOpen, setIsViewPermissionsSideDrawerOpen] = useState(false);
    const [selectedRole, setSelectedRole] = useState<RoleData>({} as RoleData);

    // Search functionality
    const router = useRouter();
    const pathname = usePathname();
    const params = useSearchParams();
    const [currentFilters, setCurrentFilters] = useState({
        search: params.get("search") ?? "",
    });

    const emptyTabledescription = currentFilters.search
        ? `No role found matching "${currentFilters.search}". Try a different search term.`
        : "You have no roles yet. Create a new role.";
    const emptyTabletitle = currentFilters.search ? "No search results" : "No roles yet";

    // Create query string for URL
    const createQueryString = useCallback((searchTerm: string) => {
        const newParams = new URLSearchParams();
        if (searchTerm) {
            newParams.set("search", searchTerm);
        }
        return newParams.toString();
    }, []);

    // Update filters and URL
    const updateFilters = useCallback(
        (newSearch: string) => {
            setCurrentFilters({ search: newSearch });
            const queryString = createQueryString(newSearch);
            router.push(pathname + (queryString ? "?" + queryString : ""));
        },
        [createQueryString, pathname, router]
    );

    // Handle search input change
    const handleSearch = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const searchTerm = event.target.value;
            updateFilters(searchTerm);
        },
        [updateFilters]
    );

    const handleCreateCustomRole = () => {
        // Check if either dynamic permissions are available or Redux permissions are ready
        const permissionsReady =
            (systemPermissions.length > 0 && !isLoadingSystemPermissions) || (permissionSuccess && !permissionLoading);

        if (permissionsReady) {
            setIsCreateCustomRoleModalOpen(!isCreateCustomRoleModalOpen);
        }
    };

    const handleCloseViewRolesSideDrawer = () => {
        setIsViewPermissionsSideDrawerOpen(false);
    };
    // Filter the table data based on search term
    const filteredData = useMemo(() => {
        if (!currentFilters.search || !Array.isArray(tableData)) {
            return Array.isArray(tableData) ? (tableData as FullRolesColumnData[]) : [];
        }

        const searchTerm = currentFilters.search.toLowerCase();
        return (tableData as FullRolesColumnData[])?.filter(
            (role) => role?.name?.toLowerCase()?.includes(searchTerm) ?? false
        ); // Added null check for role?.name
    }, [tableData, currentFilters.search]);

    const table = useReactTable<FullRolesColumnData>({
        data: filteredData,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onRowSelectionChange: setRowSelection,
        state: {
            rowSelection,
        },
        meta: {
            setIsOpen,
            setRoleToView: setSelectedRole,
            setIsViewPermissionsSideDrawerOpen,
            isViewPermissionsSideDrawerOpen,
            setIsEditMode,
            setIsCreateCustomRoleModalOpen,
        } as unknown as IRolesTableMeta,
    });

    // Track data fetching state
    const dataFetchedRef = useRef(false);
    const shouldShowFeedbackRef = useRef(false);

    // Function to explicitly refresh data when needed
    const refreshRoles = () => {
        shouldShowFeedbackRef.current = true; // Show feedback on manual refresh
        dispatch(getAllRoles());
    };

    // Fetch data only on initial mount
    useEffect(() => {
        // Only fetch roles data on initial mount
        if (!dataFetchedRef.current) {
            dispatch(getAllRoles());
            // Show feedback on initial load
            shouldShowFeedbackRef.current = false;
            // Mark as fetched to prevent future fetches
            dataFetchedRef.current = true;
        }

        // Always fetch permissions and team members as they're needed for the UI
        dispatch(getAllPermissions());
        dispatch(getTeamMembers()); // Fetch team members to count them by role
    }, [dispatch]);

    // Handle success and error states
    const prevSuccessRef = useRef(success);
    const prevLoadingRef = useRef(loading);

    useEffect(() => {
        // Only run this effect when loading transitions from true to false (request completed)
        const loadingComplete = prevLoadingRef.current && !loading;

        if (loadingComplete) {
            if (success && !prevSuccessRef.current) {
                // Only show feedback when explicitly requested
                if (shouldShowFeedbackRef.current) {
                    sendFeedback("Roles fetched successfully", "success");
                    shouldShowFeedbackRef.current = false;
                }
            } else if (error) {
                sendCatchFeedback(error);
                shouldShowFeedbackRef.current = false;
            }
        }

        // Update refs for next render
        prevSuccessRef.current = success;
        prevLoadingRef.current = loading;
    }, [success, loading, error]);

    return (
        <section data-testid="Roles-component">
            <h1 className="font-bold text-xl text-[#2E335B] mb-11">{t("Roles")}</h1>

            <div className="flex justify-between items-center mb-8">
                <RolesFilter value={currentFilters.search} onSearch={handleSearch} />
                <PermissionFallback>
                    <Button
                        size="medium"
                        variant="primary"
                        onClick={handleCreateCustomRole}
                        disabled={isLoadingSystemPermissions || (!systemPermissions.length && permissionLoading)}
                    >
                        <PlusIcon />
                        {isLoadingSystemPermissions || permissionLoading ? "Loading..." : "Create custom role"}
                    </Button>
                </PermissionFallback>
            </div>

            <main className="w-full mt-6">
                <DataTable
                    columns={columns}
                    table={table}
                    loading={loading}
                    emptyTabledescription={emptyTabledescription}
                    emptyTabletitle={emptyTabletitle}
                />
            </main>

            {isCreateCustomRoleModalOpen && (
                <CreateEditCustomRoleModal
                    open={isCreateCustomRoleModalOpen}
                    onClose={() => {
                        // Reset the selected role and edit mode when the modal is closed
                        setSelectedRole({} as RoleData);
                        setIsEditMode(false);
                        handleCreateCustomRole();
                    }}
                    permissionsData={permissionData || []}
                    roleToEdit={selectedRole}
                    isEditMode={isEditMode}
                    refreshRoles={refreshRoles}
                />
            )}

            {/* Render the sidebar only when it's open and a role is selected */}
            {isViewPermissionsSideDrawerOpen && selectedRole && (
                <RolePermissionContent
                    data-testid="role-permission-content"
                    handleCloseDrawer={handleCloseViewRolesSideDrawer}
                    roleData={selectedRole}
                    role={selectedRole.name}
                    isOpen={isViewPermissionsSideDrawerOpen}
                />
            )}
        </section>
    );
};

// export default RolesComponent;

// Create protected component with dynamic permissions
const ProtectedPage = withDynamicPermissionCheck(RolesComponent, {
    // Use dynamic permission checker that automatically updates when backend permissions change
    permissions: (P) => [P.TEAM_MANAGEMENT.VIEW_ROLES],
    // User needs any of these permissions (not all)
    requireAll: false,
    // Where to redirect if user doesn't have permissions
    redirectTo: PATH_PROTECTED.root,
    // Fallback permissions for safety (optional)
    fallbackPermissions: ["View and edit roles"],
    // Show loading state while permissions are being resolved
    showLoadingState: true,
});

export default ProtectedPage;
