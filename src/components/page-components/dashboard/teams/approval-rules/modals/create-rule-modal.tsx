"use client";

import React, { use<PERSON>allback, useEffect, useState } from "react";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { Formik, Form, type FormikProps, type FormikHelpers } from "formik";
// Yup is imported through the validation utilities
import { getRuleDetailsValidationSchema, getCompleteRuleValidationSchema } from "../validation/approval-rules";
import LabelInput from "@/components/common/label-input";
import { AnimatePresence, motion } from "framer-motion";
import { ConditionRow } from "./condition-row";
import { ApprovalChain, IApprovalChainFormValues } from "./approval-chain";
import Stepper from "@/components/common/stepper";
import ApprovalReviewSideDrawer from "./approval-review-side-drawer";
import { IFinalFormDataValues, IApprovalRuleFormValues } from "../types/approval-rule-types";
import { SuccessResponse } from "@/redux/types/approvalRule";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getTeamMembers } from "@/redux/actions/teamMembersActions";
import { getAllRoles } from "@/redux/actions/rolesActions";

// Import feedback functions if needed
// import { sendCatchFeedback } from "@/functions/feedback";

type CreateRuleModalProps = {
    open: boolean;
    onClose: () => void;
    ruleToEdit?: SuccessResponse | null;
    isRuleEditClicked?: boolean;
    setIsRuleEditClicked?: (value: boolean) => void;
    setRuleToEdit?: (ruleData: SuccessResponse | null) => void;
};

// Types for approval rule form

// Types for approval chain form are imported from approval-chain.tsx

// Type for dynamic approval fields
type DynamicApprovalFields = {
    [key: `approval${number}`]: string[];
};

// Use the imported validation schema
const validationSchema = getRuleDetailsValidationSchema();

const CreateRuleModal: React.FC<CreateRuleModalProps> = ({
    onClose,
    open,
    ruleToEdit,
    isRuleEditClicked,
    setIsRuleEditClicked,
    setRuleToEdit,
}: Readonly<CreateRuleModalProps>) => {
    const dispatch = useAppDispatch();

    // Fetch team members and roles data
    const { data: teamMembersData, loading: teamMembersLoading } = useAppSelector(
        (state) => state.teamMembers.getTeamMembers
    );
    const { data: rolesData, loading: rolesLoading } = useAppSelector((state) => state.roles.getAllRoles);

    // Fetch team members and roles data when component mounts
    useEffect(() => {
        // Fetch data when the modal is opened
        if (open) {
            dispatch(getTeamMembers());
            dispatch(getAllRoles());
        }
    }, [dispatch, open]);

    const [additionalConditions, setAdditionalConditions] = useState<Array<{ id: string }>>([]);
    const [currentStep, setCurrentStep] = useState<"details" | "approvals">("details");
    const [detailsFormValues, setDetailsFormValues] = useState<IApprovalRuleFormValues | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    const [finalFormData, setFinalFormData] = useState<IFinalFormDataValues>({
        ruleName: "",
        ruleType: "Transfers",
        conditions: [{ triggerIf: "", conditionIf: "", valueIf: "" }],
        additionalConditions: [],
        numberOfApprovals: "1 approval", // Default to 1 approval to match approval-chain component
        finalApproval: [""],
        allowParallelApprovals: false, // Default based on approval-chain component
        notifyAllApprovers: false, // Default based on approval-chain component
    });

    // Helper function to map applicableTo to ruleType
    const mapApplicableToToRuleType = (applicableTo?: string): "Transfers" | "Bills" | "All payments" => {
        if (!applicableTo) return "All payments";

        // Convert to lowercase for case-insensitive comparison
        const type = applicableTo.toLowerCase();

        if (type === "transfers") return "Transfers";
        if (type === "bills") return "Bills";
        return "All payments";
    };

    const initialValues: IApprovalRuleFormValues = {
        ruleName: ruleToEdit?.name ?? "",
        ruleType: mapApplicableToToRuleType(ruleToEdit?.applicableTo) ?? "All payments",
        conditions: [
            {
                triggerIf: ruleToEdit?.trigger
                    ? ruleToEdit.trigger[0] + ruleToEdit.trigger.slice(1).toLocaleLowerCase()
                    : "",
                conditionIf: ruleToEdit?.minAmount?.toString() ?? "",
                valueIf: ruleToEdit?.maxAmount?.toString() ?? "",
            },
        ],
        additionalConditions: [],
    };

    const handleDetailsSubmit = (
        values: IApprovalRuleFormValues,
        { setSubmitting }: FormikHelpers<IApprovalRuleFormValues>
    ) => {
        // Filter out any empty additional conditions
        const filteredAdditionalConditions = values.additionalConditions.filter(
            (condition) => condition.trigger && condition.condition && condition.value
        );

        const finalValues = {
            ...values,
            additionalConditions: filteredAdditionalConditions,
        };

        setDetailsFormValues(finalValues);

        // Update the finalFormData with the rule details
        setFinalFormData((prevData: IFinalFormDataValues) => ({
            ...prevData,
            ruleName: values.ruleName,
            ruleType: values.ruleType,
            conditions: values.conditions,
            additionalConditions: filteredAdditionalConditions,
        }));
        setCurrentStep("approvals");
        setSubmitting(false);
    };

    const handleApprovalChainSubmit = (values: IApprovalChainFormValues) => {
        // Extract the number of approvals to determine which fields are needed
        const numApprovals = parseInt(values.numberOfApprovals.split(" ")[0], 10) || 1;

        // Start with a clean base form data structure
        const combinedFormData: IFinalFormDataValues = {
            // Keep the rule details from finalFormData
            ruleName: finalFormData.ruleName,
            ruleType: finalFormData.ruleType,
            conditions: finalFormData.conditions,
            additionalConditions: finalFormData.additionalConditions,
            // Update with approval chain values
            numberOfApprovals: values.numberOfApprovals,
            finalApproval: values.finalApproval,
            allowParallelApprovals: values.allowParallelApprovals,
            notifyAllApprovers: values.notifyAllApprovers,
        };

        // Only add dynamic approval fields that are actually needed for this number of approvals
        for (let i = 1; i < numApprovals; i++) {
            const fieldName = `approval${i}`;
            if (values[fieldName]) {
                (combinedFormData as IFinalFormDataValues & DynamicApprovalFields)[fieldName as `approval${number}`] =
                    values[fieldName] as string[];
            }
        }

        // Update our state with combined data
        setFinalFormData(combinedFormData);

        // Validate that the form is completely filled before opening the side drawer
        const isFormComplete = validateCompleteForm(combinedFormData);

        if (isFormComplete) {
            // Open the review side drawer only if the form is complete
            setIsOpen(true);
        } else {
            // Show feedback to the user that the form is incomplete
            // You can use your sendFeedback function here
            // sendFeedback("Please complete all required fields before proceeding", "warning");
            console.warn("Form is incomplete. Please fill all required fields.");
        }
    };

    // Function to validate that the form is completely filled using our shared validation schema
    const validateCompleteForm = (formData: IFinalFormDataValues): boolean => {
        try {
            // Extract the number from the string (e.g., "3 approvals" -> 3)
            const numApprovals = parseInt(formData.numberOfApprovals.split(" ")[0], 10) || 0;

            // Get the validation schema for the complete form
            const validationSchema = getCompleteRuleValidationSchema(numApprovals);

            // Validate the form data against the schema
            validationSchema.validateSync(formData, { abortEarly: false });

            return true;
        } catch (error) {
            // Log validation errors for debugging
            console.warn("Form validation failed:", error);
            return false;
        }
    };

    const addAnotherCondition = useCallback((formik: FormikProps<IApprovalRuleFormValues>) => {
        const newCondition = { id: `condition-${Date.now()}` };
        setAdditionalConditions((prev) => [...prev, newCondition]);

        // Update formik values
        const newConditions = [...formik.values.additionalConditions];
        newConditions.push({
            trigger: "",
            condition: "",
            value: "",
        });
        formik.setFieldValue("additionalConditions", newConditions);
    }, []);

    const removeCondition = useCallback((index: number, formik: FormikProps<IApprovalRuleFormValues>) => {
        // Remove from state
        setAdditionalConditions((prev) => {
            const newConditions = [...prev];
            newConditions.splice(index, 1);
            return newConditions;
        });

        // Remove from formik values
        const newConditions = [...formik.values.additionalConditions];
        newConditions.splice(index, 1);
        formik.setFieldValue("additionalConditions", newConditions);
    }, []);

    const handleBack = () => {
        setCurrentStep("details");
    };

    const handleCloseDrawer = () => {
        setIsOpen(false);
    };

    const steps = [
        {
            id: "details",
            label: "Approval details",
        },
        {
            id: "approve",
            label: "Approval chain",
        },
    ];

    // Function to update the form data from the side drawer
    const updateFormData = (values: IFinalFormDataValues) => {
        setFinalFormData(values);
        // This would typically be where you'd make an API call to save the data
    };

    // Function to handle changes in the approval chain form - updates form data in real-time
    const handleApprovalChainChange = useCallback(
        (values: IApprovalChainFormValues) => {
            // Always update the form data so the side drawer can reflect changes in real-time
            // Use a function to update state to ensure we're working with the latest state
            setFinalFormData((prevData) => {
                // Extract the number of approvals to determine which fields are needed
                const numApprovals = parseInt(values.numberOfApprovals.split(" ")[0], 10) || 1;

                // Start with a clean base form data structure, keeping rule details
                const updatedFormData: IFinalFormDataValues = {
                    // Keep the rule details from previous data
                    ruleName: prevData.ruleName,
                    ruleType: prevData.ruleType,
                    conditions: prevData.conditions,
                    additionalConditions: prevData.additionalConditions,
                    // Update with approval chain values
                    numberOfApprovals: values.numberOfApprovals,
                    finalApproval: values.finalApproval,
                    allowParallelApprovals: values.allowParallelApprovals,
                    notifyAllApprovers: values.notifyAllApprovers,
                };

                // Only add dynamic approval fields that are actually needed for this number of approvals
                for (let i = 1; i < numApprovals; i++) {
                    const fieldName = `approval${i}`;
                    if (values[fieldName]) {
                        (updatedFormData as IFinalFormDataValues & DynamicApprovalFields)[
                            fieldName as `approval${number}`
                        ] = values[fieldName] as string[];
                    }
                }

                return updatedFormData;
            });
        },
        [] // No dependencies needed since we always want to update
    );

    const resetForm = () => {
        // Close the side drawer if it's open
        setIsOpen(false);

        setDetailsFormValues(null);
        setAdditionalConditions([]);

        // Create a base form data object
        const baseFormData: IFinalFormDataValues = {
            ruleName: "",
            ruleType: "Transfers",
            conditions: [{ triggerIf: "", conditionIf: "", valueIf: "" }],
            additionalConditions: [],
            numberOfApprovals: "1 approval", // Default to 1 approval to match approval-chain component
            finalApproval: [""],
            allowParallelApprovals: false, // Default based on approval-chain component
            notifyAllApprovers: false, // Default based on approval-chain component
        };

        // For 1 approval, we only need finalApproval field, no intermediate approval fields
        setFinalFormData(baseFormData);
        setCurrentStep("details");
    };

    // Handle modal close with reset
    const handleModalClose = () => {
        if (setIsRuleEditClicked) {
            setIsRuleEditClicked(false);
        }
        if (setRuleToEdit) {
            setRuleToEdit(null);
        }

        resetForm();
        onClose();
    };

    useEffect(() => {
        if (!isRuleEditClicked) {
            resetForm();
        }
    }, [isRuleEditClicked]);

    // Effect to close the side drawer when the form is reset due to external factors
    useEffect(() => {
        // If the form is reset to initial state and the drawer is open, close it
        if (
            isOpen &&
            (!finalFormData.ruleName || finalFormData.ruleName === "") &&
            finalFormData.ruleType === "Transfers" &&
            (!finalFormData.conditions?.[0]?.triggerIf || finalFormData.conditions?.[0]?.triggerIf === "")
        ) {
            setIsOpen(false);
        }
    }, [finalFormData, isOpen]);

    return (
        <>
            <FullScreenDrawer
                className="h-screen relative"
                isOpen={open}
                onClose={handleModalClose}
                showSupport
                title={isRuleEditClicked ? "Edit rule" : "Create rule"}
            >
                <nav
                    className="absolute left-0 top-[64px] bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white font-primary"
                    aria-label="Payment progress"
                >
                    <Stepper steps={steps} currentStep={currentStep === "details" ? 1 : 2} />
                </nav>
                <div className="w-full max-w-[680px] mx-auto flex flex-col h-full relative font-primary overflow-y-auto">
                    <AnimatePresence mode="wait">
                        {currentStep === "details" ? (
                            <motion.div
                                key="details"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.3 }}
                            >
                                <Formik
                                    initialValues={detailsFormValues || initialValues}
                                    validationSchema={validationSchema}
                                    onSubmit={handleDetailsSubmit}
                                    enableReinitialize={true} // Important for edit mode
                                >
                                    {(formik: FormikProps<IApprovalRuleFormValues>) => (
                                        <Form>
                                            <div className="max-w-[650px] mx-auto font-primary">
                                                <h2 className="text-[24px] font-[600] text-black text-center leading-[30px] mb-8">
                                                    What's this approval rule for?
                                                </h2>

                                                <div className="mb-6">
                                                    <p className="text-xs font-semibold mb-2.5">
                                                        Give this rule a name
                                                    </p>
                                                    <LabelInput
                                                        formik={formik}
                                                        className="w-full"
                                                        name="ruleName"
                                                        type="text"
                                                        placeholder="e.g., High value transfers"
                                                        useFormik={true}
                                                        required
                                                    />
                                                </div>

                                                <div className="mb-12">
                                                    <p className="text-[14px] leading-[18px] font-semibold mb-4">
                                                        Apply rule to
                                                    </p>
                                                    <div className="flex gap-2">
                                                        {["Transfers", "Bills", "All payments"].map((type) => (
                                                            <motion.button
                                                                key={type}
                                                                type="button"
                                                                onClick={() => formik.setFieldValue("ruleType", type)}
                                                                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                                                                    formik.values.ruleType === type
                                                                        ? "bg-[#5C068C] text-white"
                                                                        : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                                                                }`}
                                                                whileHover={{ scale: 1.03 }}
                                                                whileTap={{ scale: 0.97 }}
                                                            >
                                                                {type}
                                                            </motion.button>
                                                        ))}
                                                    </div>
                                                </div>

                                                <div className="mb-8">
                                                    <h3 className="text-[14px] leading-[18px] font-semibold mb-8">
                                                        Customize your approval rule
                                                    </h3>

                                                    {/* First condition (always present) */}
                                                    <ConditionRow
                                                        index={0}
                                                        formik={formik}
                                                        onRemove={() => {}}
                                                        isFirst={true}
                                                    />

                                                    {/* Additional conditions */}
                                                    <AnimatePresence>
                                                        {additionalConditions.map((condition, index) => (
                                                            <ConditionRow
                                                                key={condition.id}
                                                                index={index}
                                                                formik={formik}
                                                                onRemove={(idx) => removeCondition(idx, formik)}
                                                            />
                                                        ))}
                                                    </AnimatePresence>

                                                    {/* Add another condition button (currently hidden) */}
                                                    <motion.button
                                                        type="button"
                                                        className="items-center gap-2 text-[#5C068C] font-medium hidden text-sm mt-4"
                                                        onClick={() => addAnotherCondition(formik)}
                                                        whileHover={{ scale: 1.03 }}
                                                        whileTap={{ scale: 0.97 }}
                                                    >
                                                        <svg
                                                            width="16"
                                                            height="16"
                                                            viewBox="0 0 16 16"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8 4V12"
                                                                stroke="#5C068C"
                                                                strokeWidth="2"
                                                                strokeLinecap="round"
                                                            />
                                                            <path
                                                                d="M4 8H12"
                                                                stroke="#5C068C"
                                                                strokeWidth="2"
                                                                strokeLinecap="round"
                                                            />
                                                        </svg>
                                                        Add another condition
                                                    </motion.button>
                                                </div>

                                                <div className="flex items-center gap-3 justify-end mt-8">
                                                    <motion.button
                                                        type="button"
                                                        onClick={handleModalClose}
                                                        className="border border-[#DBDBE1] px-[14px] py-2 rounded-[8px] text-[14px] text-[#151519] leading-[18px] font-[600]"
                                                        whileHover={{ backgroundColor: "#f5f5f5" }}
                                                        whileTap={{ scale: 0.97 }}
                                                    >
                                                        Cancel
                                                    </motion.button>
                                                    <motion.button
                                                        type="submit"
                                                        disabled={!formik.isValid || formik.isSubmitting}
                                                        className={`border border-[#5C068C] bg-[#5C068C] text-[14px] text-white leading-[18px] font-[600] px-[14px] py-2 rounded-[8px] ${
                                                            !formik.isValid || formik.isSubmitting
                                                                ? "opacity-50 cursor-not-allowed bg-[#F7F7F8] border-[#F7F7F8] text-[#9D9DAC]"
                                                                : ""
                                                        }`}
                                                        whileHover={
                                                            !formik.isValid || formik.isSubmitting
                                                                ? {}
                                                                : { backgroundColor: "#4A0572" }
                                                        }
                                                        whileTap={
                                                            !formik.isValid || formik.isSubmitting
                                                                ? {}
                                                                : { scale: 0.97 }
                                                        }
                                                    >
                                                        Continue
                                                    </motion.button>
                                                </div>
                                            </div>
                                        </Form>
                                    )}
                                </Formik>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="approvals"
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <ApprovalChain
                                    onBack={handleBack}
                                    onSubmit={handleApprovalChainSubmit}
                                    onChange={handleApprovalChainChange} // Add onChange handler to detect form changes
                                    // onCancel={handleModalClose}
                                    ruleToEdit={ruleToEdit}
                                    isRuleEditClicked={isRuleEditClicked}
                                    setRuleToEdit={setRuleToEdit}
                                    // Pass team members and roles data
                                    teamMembersData={teamMembersData || undefined}
                                    teamMembersLoading={teamMembersLoading}
                                    rolesData={rolesData || undefined}
                                    rolesLoading={rolesLoading}
                                />
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </FullScreenDrawer>

            <ApprovalReviewSideDrawer
                isOpen={isOpen}
                onClose={handleCloseDrawer}
                formData={finalFormData}
                updateFormData={updateFormData}
                resetForm={resetForm}
                isEditMode={isRuleEditClicked}
                ruleId={ruleToEdit?.id}
                setIsRuleEditClicked={setIsRuleEditClicked}
                setRuleToEdit={setRuleToEdit}
                handleModalClose={handleModalClose}
            />
        </>
    );
};

export default CreateRuleModal;
