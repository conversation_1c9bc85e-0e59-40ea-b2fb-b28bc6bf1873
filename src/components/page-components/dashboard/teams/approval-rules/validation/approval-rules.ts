import * as Yup from "yup";

// Helper function to get ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
const getOrdinalSuffix = (num: number): string => {
    const j = num % 10;
    const k = num % 100;

    if (j === 1 && k !== 11) {
        return num + "st";
    }
    if (j === 2 && k !== 12) {
        return num + "nd";
    }
    if (j === 3 && k !== 13) {
        return num + "rd";
    }
    return num + "th";
};

/**
 * Generates a validation schema for the approval chain form
 * @param numApprovals The number of approvals required
 * @returns A Yup validation schema for the approval chain form
 */
export const getApprovalChainValidationSchema = (numApprovals: number) => {
    // Create a schema builder object
    const schemaBuilder: Record<string, Yup.AnySchema> = {
        numberOfApprovals: Yup.string().required("Number of approvals is required"),
    };

    // Add validation for each approval level based on the number of approvals
    for (let i = 1; i < numApprovals; i++) {
        const fieldName = `approval${i}`;
        let label: string;

        // Use proper ordinal labels for better user experience
        if (i === 1) {
            label = "First approval";
        } else if (i === 2) {
            label = "Second approval";
        } else if (i === 3) {
            label = "Third approval";
        } else {
            label = `${getOrdinalSuffix(i)} approval`;
        }

        schemaBuilder[fieldName] = Yup.array()
            .of(Yup.string())
            .min(1, "At least one approver is required")
            .required(`${label} is required`);
    }

    // Final approval is always required
    schemaBuilder.finalApproval = Yup.array()
        .of(Yup.string())
        .min(1, "At least one approver is required")
        .required("Final approval is required");

    return Yup.object(schemaBuilder);
};

/**
 * Generates a validation schema for the rule details form
 * @returns A Yup validation schema for the rule details form
 */
export const getRuleDetailsValidationSchema = () =>
    Yup.object({
        ruleName: Yup.string().required("Rule name is required"),
        ruleType: Yup.string().required("Rule type is required"),
        conditions: Yup.array().of(
            Yup.object({
                triggerIf: Yup.string().required("Trigger is required"),
                conditionIf: Yup.string().required("Condition is required"),
                valueIf: Yup.string().required("Value is required"),
            })
        ),
    });

/**
 * Generates a validation schema for the complete rule form
 * @param numApprovals The number of approvals required
 * @returns A Yup validation schema for the complete rule form
 */
export const getCompleteRuleValidationSchema = (numApprovals: number) => {
    const ruleDetailsSchema = getRuleDetailsValidationSchema();
    const approvalChainSchema = getApprovalChainValidationSchema(numApprovals);

    // Combine the schemas
    return Yup.object().shape({
        ...ruleDetailsSchema.fields,
        ...approvalChainSchema.fields,
    });
};
