// Combined interface as requested - flattened structure
export interface IFinalFormDataValues {
    // From IApprovalRuleFormValues
    ruleName: string;
    ruleType: "Transfers" | "Bills" | "All payments";
    conditions: Array<{
        triggerIf: string;
        conditionIf: string;
        valueIf: string;
    }>;
    additionalConditions: Array<{
        trigger: string;
        condition: string;
        value: string;
    }>;

    // From IApprovalChainFormValues
    numberOfApprovals: string;
    finalApproval: string[];
    allowParallelApprovals: boolean;
    notifyAllApprovers: boolean;

    // Dynamic approval levels (approval1, approval2, etc.)
    [key: string]:
        | string
        | string[]
        | boolean
        | Array<{
              trigger?: string;
              condition?: string;
              value?: string;
              triggerIf?: string;
              conditionIf?: string;
              valueIf?: string;
          }>
        | undefined;
}

// Individual interfaces
export interface IApprovalRuleFormValues {
    ruleName: string;
    ruleType: "Transfers" | "Bills" | "All payments";
    conditions: Array<{
        triggerIf: string;
        conditionIf: string;
        valueIf: string;
    }>;
    additionalConditions: Array<{
        trigger: string;
        condition: string;
        value: string;
    }>;
}
