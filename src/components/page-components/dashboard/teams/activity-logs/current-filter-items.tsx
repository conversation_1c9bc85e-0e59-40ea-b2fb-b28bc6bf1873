import React from "react";
import { Button } from "@/components/common/buttonv3";
import { X } from "lucide-react";
import Badge from "@/components/common/badge";
import { IActivityLogFilter } from "./types";

interface CurrentFilterItemsProps {
    currentFilters: IActivityLogFilter;
    onClearAll: () => void;
    roleMap?: Record<number, string>;
}

export function CurrentFilterItems({ currentFilters, onClearAll, roleMap }: Readonly<CurrentFilterItemsProps>) {
    // Only check for user-applied filters, exclude pagination and sorting
    const hasActiveFilters = () =>
        !!currentFilters.search ||
        !!currentFilters.startDate ||
        !!currentFilters.endDate ||
        (currentFilters.roleId !== null && currentFilters.roleId !== undefined) ||
        !!currentFilters.actionTypes;

    const getDateFilterText = () => {
        if (currentFilters.startDate && currentFilters.endDate) {
            const startDate = new Date(currentFilters.startDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
            });
            const endDate = new Date(currentFilters.endDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
            });
            return `${startDate} - ${endDate}`;
        }
        if (currentFilters.startDate) {
            return `From ${new Date(currentFilters.startDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
            })}`;
        }
        if (currentFilters.endDate) {
            return `Until ${new Date(currentFilters.endDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
            })}`;
        }
        return "Date range";
    };

    const getRoleFilterText = () => {
        if (currentFilters.roleId === 0) {
            return "Role: Super Admin";
        }
        if (roleMap && currentFilters.roleId && roleMap[currentFilters.roleId]) {
            return `Role: ${roleMap[currentFilters.roleId]}`;
        }
        return `Role: ${currentFilters.roleId}`;
    };

    if (!hasActiveFilters()) {
        return null;
    }

    return (
        <div className="flex flex-wrap gap-3 mt-6">
            {/* Search filter */}
            {currentFilters.search && <Badge text={`Search: "${currentFilters.search}"`} color="neutral" size="xl" />}

            {/* Date filter */}
            {(currentFilters.startDate || currentFilters.endDate) && (
                <Badge text={getDateFilterText()} color="neutral" size="xl" />
            )}

            {/* Role filter */}
            {currentFilters.roleId !== null && currentFilters.roleId !== undefined && (
                <Badge text={getRoleFilterText()} color="neutral" size="xl" />
            )}

            {/* Action type filter */}
            {currentFilters.actionTypes && (
                <Badge text={`Action: ${currentFilters.actionTypes}`} color="neutral" size="xl" />
            )}

            {/* Clear all button */}
            <Button size="sm" variant="text-destructive" leftIcon={<X size={14} />} onClick={onClearAll}>
                Clear all
            </Button>
        </div>
    );
}
