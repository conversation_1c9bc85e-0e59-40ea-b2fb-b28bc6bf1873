"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import Checkbox from "@/components/common/checkbox";
import CloseX from "@/components/common/close-x";
import DatePicker from "@/components/common/date-picker";
import SideDrawer from "@/components/common/drawer";
import { Label } from "@/components/common/label";
import { RadioGroup, RadioGroupItem } from "@/components/common/radio-group";
import { ExportIcon } from "@/components/icons/transaction";
import { sendCatchFeedback } from "@/functions/feedback";
import { AppDispatch, RootState } from "@/redux";
import { exportActivityLogs } from "@/redux/actions/activityLogsActions";
import { clearExportSuccess } from "@/redux/slices/activityLogsSlice";
import {
    ActivityLogsData,
    DateRangeValues,
    ExportActivityLogsPayload,
    PerformedByObject,
} from "@/redux/types/activityLogs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface IExportActivityLogProps {
    currentFilters?: {
        search?: string;
        startDate?: string;
        endDate?: string;
        source?: string;
        actionType?: string;
    };
    onExportSuccess?: () => void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedRows?: ActivityLogsData[];
}

export function ExportActivityLog({
    currentFilters,
    onExportSuccess,
    selectedRows = [],
}: Readonly<IExportActivityLogProps>) {
    const dispatch = useDispatch<AppDispatch>();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [fileExportType, setfileExportType] = useState<"pdf" | "csv">("csv");
    const [dateRangeType, setDateRangeType] = useState<DateRangeValues>("LAST_3_MONTHS"); // Default to 3 months
    const [startDate, setStartDate] = useState<Date>(new Date());
    const [endDate, setEndDate] = useState<Date>(new Date());

    // Redux state
    const { exportActivityLogs: exportState } = useSelector((state: RootState) => state.activityLogs);
    const { loading: exportLoading, success: exportSuccess } = exportState;

    // Update date range based on selection
    useEffect(() => {
        const now = new Date();
        const today = new Date(now);

        switch (dateRangeType) {
            case "LAST_30_DAYS":
                {
                    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    setStartDate(thirtyDaysAgo);
                    setEndDate(today);
                }
                break;
            case "LAST_3_MONTHS": // Last 3 months
                {
                    const threeMonthsAgo = new Date(now);
                    threeMonthsAgo.setMonth(now.getMonth() - 3);
                    setStartDate(threeMonthsAgo);
                    setEndDate(today);
                }
                break;
            case "LAST_6_MONTHS": // Last 6 months
                {
                    const sixMonthsAgo = new Date(now);
                    sixMonthsAgo.setMonth(now.getMonth() - 6);
                    setStartDate(sixMonthsAgo);
                    setEndDate(today);
                }
                break;

            // For custom, don't update the dates automatically
            case "CUSTOM":
                break;
        }
    }, [dateRangeType]);

    // Handle export success - reset form and call parent callback
    useEffect(() => {
        if (exportSuccess) {
            // Reset export form to defaults
            setfileExportType("csv");
            setDateRangeType("LAST_3_MONTHS");
            setStartDate(new Date());
            setEndDate(new Date());
            setIsOpen(false);

            // Call parent callback to reset main filters
            if (onExportSuccess) {
                onExportSuccess();
            }

            // Clear the success state
            dispatch(clearExportSuccess());
        }
    }, [exportSuccess, onExportSuccess, dispatch]);

    const handleCloseFilter = () => {
        setIsOpen(false);
    };

    const handleExport = () => {
        try {
            // Use the current startDate and endDate from state
            const exportStartDate = startDate.toISOString().split("T")[0];
            const exportEndDate = endDate.toISOString().split("T")[0];

            const exportPayload: ExportActivityLogsPayload = {
                format: fileExportType,
                startDate: exportStartDate,
                endDate: exportEndDate,
                // Include current filters from the activity logs page
                searchKeyword: currentFilters?.search,
                actionTypes: currentFilters?.actionType,
                // Note: roleId would need to be mapped from source if it's a role name
                selectedRows: selectedRows
                    ? selectedRows.map((item) => ({
                          ...item,
                          performedBy: (item.performedBy as PerformedByObject)?.name,
                      }))
                    : undefined,
                dateRange: dateRangeType,
            };

            dispatch(exportActivityLogs(exportPayload));
        } catch (error) {
            sendCatchFeedback(error);
        }
    };

    return (
        <div>
            <div>
                <Button
                    variant="outline"
                    size="sm"
                    rightIcon={<ExportIcon data-testid="export-icon" />}
                    onClick={() => setIsOpen(true)}
                    data-testid="button"
                >
                    Export
                </Button>
            </div>

            <SideDrawer isOpen={isOpen} data-testid="side-drawer">
                <div className="flex flex-col justify-between h-full">
                    <div>
                        <div className="border-b border-[#E3E5E8] py-[19px] px-4">
                            <div className="flex justify-between items-center">
                                <h2 className="text-[20px] leading-[26px] font-semibold text-black">
                                    Export activity log
                                </h2>
                                <CloseX onClick={handleCloseFilter} data-testid="close-button" color="#90909D" />
                            </div>
                            <p className="text-sm leading-[18px] font-normal text-subText mt-[12px]">
                                Download and analyze your activity log.
                            </p>
                        </div>
                        <div className="grid gap-[40px] py-8 px-6 text-[14px] leading-[18px]">
                            <div>
                                <h4 className="font-semibold mb-[20px]">Date range</h4>
                                <RadioGroup
                                    value={dateRangeType}
                                    onValueChange={(value) => setDateRangeType(value as DateRangeValues)}
                                    className="flex flex-col gap-4"
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="LAST_30_DAYS" id="r1" />
                                        <Label htmlFor="r1">Last 30 days</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="LAST_3_MONTHS" id="r2" />
                                        <Label htmlFor="r2">Last 3 months</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="LAST_6_MONTHS" id="r3" />
                                        <Label htmlFor="r3">Last 6 months</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="CUSTOM" id="r5" />
                                        <Label htmlFor="r5">Custom date</Label>
                                    </div>
                                </RadioGroup>

                                {/* Custom Date Range Picker */}
                                {dateRangeType === "CUSTOM" && (
                                    <div className="mt-4 flex flex-col gap-[20px] md:flex-row">
                                        <DatePicker
                                            label="Start date"
                                            value={startDate}
                                            onChange={(value) => {
                                                if (value) {
                                                    setStartDate(value);
                                                }
                                            }}
                                        />
                                        <DatePicker
                                            label="End date"
                                            value={endDate}
                                            onChange={(value) => {
                                                if (value) {
                                                    setEndDate(value);
                                                }
                                            }}
                                        />
                                    </div>
                                )}
                            </div>

                            <div>
                                <h4 className="font-semibold">Choose your export format</h4>
                                <div className="font-medium mt-[20px] flex flex-col gap-4">
                                    <Checkbox
                                        label="CSV (For spreadsheet software)"
                                        id="csv"
                                        checked={fileExportType === "csv"}
                                        onCheckedChange={() => {
                                            setfileExportType("csv");
                                        }}
                                        size={"sm"}
                                        data-testid="checkbox-csv"
                                    />
                                    <Checkbox
                                        label="PDF (For printing and sharing)"
                                        id="pdf"
                                        checked={fileExportType === "pdf"}
                                        onCheckedChange={() => {
                                            setfileExportType("pdf");
                                        }}
                                        size={"sm"}
                                        data-testid="checkbox-pdf"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-4 px-6">
                        <Button type="button" variant="outline" onClick={handleCloseFilter}>
                            Cancel
                        </Button>
                        <Button type="button" onClick={handleExport} loading={exportLoading}>
                            Export
                        </Button>
                    </div>
                </div>
            </SideDrawer>
        </div>
    );
}
