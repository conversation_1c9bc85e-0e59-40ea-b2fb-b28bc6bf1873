/**
 * Utility functions for handling date operations in activity logs
 */

export interface DateRange {
    startDate: string;
    endDate: string;
}

/**
 * Converts date filter options to actual date ranges
 * @param option The selected date option
 * @returns Object with startDate and endDate strings in YYYY-MM-DD format
 */
export const getDateRangeFromOption = (option: string): DateRange | null => {
    const today = new Date();
    const formatDate = (date: Date): string => date.toISOString().split("T")[0];

    switch (option) {
        case "Today": {
            return {
                startDate: formatDate(today),
                endDate: formatDate(today),
            };
        }

        case "Yesterday": {
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            return {
                startDate: formatDate(yesterday),
                endDate: formatDate(yesterday),
            };
        }

        case "Last 7 days": {
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 7);
            return {
                startDate: formatDate(sevenDaysAgo),
                endDate: formatDate(today),
            };
        }

        case "Last 30 days": {
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            return {
                startDate: formatDate(thirtyDaysAgo),
                endDate: formatDate(today),
            };
        }

        case "Custom date":
        default:
            return null; // For custom date, let user pick manually
    }
};

/**
 * Determines the appropriate date filter label based on current start and end dates
 * @param startDate Start date string
 * @param endDate End date string
 * @returns The appropriate label for the date filter
 */
export const getDateFilterLabel = (startDate?: string, endDate?: string): string => {
    if (!startDate || !endDate) {
        return "Custom date";
    }

    const today = new Date().toISOString().split("T")[0];
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split("T")[0];

    // Check for Today
    if (startDate === today && endDate === today) {
        return "Today";
    }

    // Check for Yesterday
    if (startDate === yesterdayStr && endDate === yesterdayStr) {
        return "Yesterday";
    }

    // Check for Last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const sevenDaysAgoStr = sevenDaysAgo.toISOString().split("T")[0];

    if (startDate === sevenDaysAgoStr && endDate === today) {
        return "Last 7 days";
    }

    // Check for Last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split("T")[0];

    if (startDate === thirtyDaysAgoStr && endDate === today) {
        return "Last 30 days";
    }

    return "Custom date";
};
