/**
 * Utility functions for handling URL parameters in the activity logs module
 */
import { IActivityLogFilter, IActivityLogTempFilter, emptyActivityLogFilter } from "../types";
import { ReadonlyURLSearchParams } from "next/navigation";

/**
 * Extracts filter state from URL search parameters
 * @param searchParams Current URL search parameters
 * @returns Filter state object
 */
export const getActivityLogFiltersFromUrlParams = (searchParams: ReadonlyURLSearchParams): IActivityLogFilter => {
    const params = new URLSearchParams(searchParams.toString());
    const newFilters = { ...emptyActivityLogFilter };

    // Get parameters from URL
    const search = params.get("search") ?? "";
    const startDate = params.get("startDate") ?? "";
    const endDate = params.get("endDate") ?? "";
    const roleId = params.get("roleId");
    const actionTypes = params.get("actionTypes") ?? "";
    const page = params.get("page");
    const size = params.get("size");
    const sortBy = params.get("sortBy") ?? "timestamp";
    const sortDirection = params.get("sortDirection") ?? "DESC";

    // Update filters with URL parameters
    Object.assign(newFilters, {
        search,
        startDate,
        endDate,
        roleId: roleId ? Number(roleId) : null,
        actionTypes,
        page: page ? Number(page) : 0,
        size: size ? Number(size) : 10,
        sortBy,
        sortDirection: (sortDirection as "ASC" | "DESC") || "DESC",
    });

    return newFilters;
};

/**
 * Updates URL search parameters with the current filter state
 * @param searchParams Current URL search parameters
 * @param filters Current filter state
 * @returns Updated URL search parameters
 */
export const updateUrlParamsFromActivityLogFilters = (
    searchParams: ReadonlyURLSearchParams,
    filters: IActivityLogFilter
): URLSearchParams => {
    const params = new URLSearchParams(searchParams.toString());

    // Clear existing activity log parameters
    const activityLogParams = [
        "search",
        "startDate",
        "endDate",
        "roleId",
        "actionTypes",
        "page",
        "size",
        "sortBy",
        "sortDirection",
    ];

    activityLogParams.forEach((param) => {
        params.delete(param);
    });

    // Add new parameters if they have values
    if (filters.search) params.set("search", filters.search);
    if (filters.startDate) params.set("startDate", filters.startDate);
    if (filters.endDate) params.set("endDate", filters.endDate);
    if (filters.roleId !== null && filters.roleId !== undefined) {
        params.set("roleId", filters.roleId.toString());
    }
    if (filters.actionTypes) params.set("actionTypes", filters.actionTypes);

    // Always set pagination and sorting params
    params.set("page", (filters.page ?? 0).toString());
    params.set("size", (filters.size ?? 10).toString());
    params.set("sortBy", filters.sortBy ?? "timestamp");
    params.set("sortDirection", filters.sortDirection ?? "DESC");

    return params;
};

/**
 * Converts UI temp filters to API filter format
 * @param tempFilters Temporary filters from UI
 * @param currentFilters Current applied filters for pagination/sorting
 * @param roleMap Optional role mapping for converting role names to IDs
 * @returns API-compatible filter object
 */
export const mapTempFiltersToApiFilters = (
    tempFilters: IActivityLogTempFilter,
    currentFilters: IActivityLogFilter,
    roleMap?: Record<number, string>
): IActivityLogFilter => {
    const apiFilters: IActivityLogFilter = {
        ...currentFilters,
        search: tempFilters.search ?? "",
        startDate: tempFilters.startDate ?? "",
        endDate: tempFilters.endDate ?? "",
        actionTypes: tempFilters.actionType ?? "",
    };

    // Convert role name to roleId if source is provided
    if (tempFilters.source) {
        if (tempFilters.source === "Super Admin") {
            apiFilters.roleId = 0; // Super Admin has roleId 0
        } else if (roleMap && typeof roleMap === "object" && Object.keys(roleMap).length > 0) {
            // Find roleId by role name
            const roleEntry = Object.entries(roleMap).find(([_, name]) => name === tempFilters.source);
            apiFilters.roleId = roleEntry ? Number(roleEntry[0]) : null;
        } else {
            apiFilters.roleId = null;
        }
    } else {
        apiFilters.roleId = null;
    }

    return apiFilters;
};

/**
 * Converts API filters to UI temp filters format
 * @param apiFilters API filter object
 * @param roleMap Optional role mapping for converting role IDs to names
 * @returns UI-compatible temp filter object
 */
export const mapApiFiltersToTempFilters = (
    apiFilters: IActivityLogFilter,
    roleMap?: Record<number, string>
): IActivityLogTempFilter => {
    let sourceName = "";

    if (apiFilters.roleId !== null && apiFilters.roleId !== undefined) {
        if (apiFilters.roleId === 0) {
            sourceName = "Super Admin";
        } else if (roleMap && typeof roleMap === "object" && roleMap[apiFilters.roleId]) {
            sourceName = roleMap[apiFilters.roleId];
        }
    }

    return {
        search: apiFilters.search ?? "",
        startDate: apiFilters.startDate ?? "",
        endDate: apiFilters.endDate ?? "",
        actionType: apiFilters.actionTypes ?? "",
        source: sourceName,
        dateFilterType: "", // This will be determined by the date values in the UI
    };
};
