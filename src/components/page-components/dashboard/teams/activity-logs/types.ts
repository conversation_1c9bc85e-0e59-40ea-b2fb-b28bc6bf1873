export interface IActivityLogFilter {
    search?: string;
    startDate?: string;
    endDate?: string;
    roleId?: number | null;
    actionTypes?: string;
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: "ASC" | "DESC";
}

export interface IActivityLogTempFilter {
    search?: string;
    startDate?: string;
    endDate?: string;
    source?: string; // Role name for UI display
    actionType?: string; // Single action type for UI
    dateFilterType?: string;
}

export const emptyActivityLogFilter: IActivityLogFilter = {
    search: "",
    startDate: "",
    endDate: "",
    roleId: null,
    actionTypes: "",
    page: 0,
    size: 10,
    sortBy: "timestamp",
    sortDirection: "DESC",
};

export const emptyActivityLogTempFilter: IActivityLogTempFilter = {
    search: "",
    startDate: "",
    endDate: "",
    source: "",
    actionType: "",
    dateFilterType: "",
};

// Note: Role mappings are now dynamic and fetched from the API
// The roleMap is created in the component from the actual roles data
