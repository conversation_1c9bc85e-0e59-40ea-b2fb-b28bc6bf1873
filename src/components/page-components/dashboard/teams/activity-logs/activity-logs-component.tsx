"use client";
import { Pagination } from "@/components/common/pagination";
import { DataTable } from "@/components/common/table/DataTable";
import type { AppDispatch, RootState } from "@/redux";
import { getActivityLogs } from "@/redux/actions/activityLogsActions";
import { getAllRoles } from "@/redux/actions/rolesActions";
import type { ActivityLogsFilters } from "@/redux/types/activityLogs";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import isEqual from "lodash/isEqual";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { ActivityLogFilter } from "./activity-log-filter";
import { type IActivityLogsTableMeta, columns } from "./column-data";
import { CurrentFilterItems } from "./current-filter-items";
import { useActivityLogFilters } from "./hooks/useActivityLogFilters";
import { ExportActivityLog } from "./modals/export-log-side-drawer";

// Static configurations
const emptyTableConfig = {
    description: "No activity logs found. Activity will appear here as team members perform actions.",
    title: "No activity logs yet",
};

const ActivityLogsComponent = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();
    const [rowSelection, setRowSelection] = useState({});

    // Use ref to track if initial load is complete
    const initialLoadRef = useRef(false);
    const prevFiltersRef = useRef<ActivityLogsFilters | null>(null);

    // Redux state
    const { getActivityLogs: activityLogsState } = useSelector((state: RootState) => state.activityLogs);
    const { data: activityLogsResponse, loading } = activityLogsState;
    const { data: rolesData, loading: rolesLoading } = useSelector((state: RootState) => state.roles.getAllRoles);

    // Stable role mapping - only recreate when rolesData actually changes
    const roleMap = useMemo(() => {
        const map: Record<number, string> = {};
        if (rolesData?.length) {
            rolesData.forEach((role) => {
                map[role.id] = role.name;
            });
        }
        return map;
    }, [rolesData]);

    // Use the hook with stable roleMap
    const {
        tempFilters,
        currentFilters,
        updateTempFilter,
        applyFilters,
        clearAllFilters,
        handleSearch,
        handlePageChange,
        handleSizeChange,
    } = useActivityLogFilters(roleMap);

    // ✅ Fixed: Stable mapFiltersToAPI function
    const mapFiltersToAPI = useCallback((filters: typeof currentFilters): ActivityLogsFilters => {
        const apiFilters: ActivityLogsFilters = {
            page: filters.page !== undefined ? Number(filters.page) : 0,
            size: filters.size !== undefined ? Number(filters.size) : 10,
        };

        if (filters.search) apiFilters.searchKeyword = String(filters.search);
        if (filters.startDate) apiFilters.startDate = String(filters.startDate);
        if (filters.endDate) apiFilters.endDate = String(filters.endDate);
        if (filters.actionTypes) apiFilters.actionTypes = String(filters.actionTypes);
        if (filters.roleId) apiFilters.roleId = Number(filters.roleId);

        return apiFilters;
    }, []); // Empty dependency array - this function is pure

    // Stable data transformation
    const transformedData = useMemo(
        () =>
            activityLogsResponse?.content?.map((log) => ({
                id: log.id,
                performedBy:
                    typeof log.performedBy === "object"
                        ? {
                              id: log.performedBy.id,
                              name: log.performedBy.name,
                              role: log.performedBy.role,
                          }
                        : {
                              id: 0,
                              name: log.performedBy,
                              role: "",
                          },
                action: log.action,
                actionType: log.actionType,
                timestamp: log.timestamp,
                location: log.location,
                ipAddress: log.ipAddress,
                details: log.details,
                role: log.role,
            })) || [],
        [activityLogsResponse?.content]
    );

    // ✅ Fixed: Fetch roles only once on mount
    useEffect(() => {
        if (!rolesData && !rolesLoading) {
            dispatch(getAllRoles());
        }
    }, [dispatch, rolesData, rolesLoading]);

    // ✅ Fixed: Optimized data fetching with proper dependency management
    useEffect(() => {
        // Skip if roles are still loading on initial mount
        if (!initialLoadRef.current && rolesLoading) {
            return;
        }

        const apiFilters = mapFiltersToAPI(currentFilters);

        // Only fetch if filters actually changed
        if (!isEqual(apiFilters, prevFiltersRef.current)) {
            dispatch(getActivityLogs(apiFilters));
            prevFiltersRef.current = apiFilters;
            initialLoadRef.current = true;
        }
    }, [dispatch, currentFilters, mapFiltersToAPI, rolesLoading]);

    // Stable table meta
    const tableMeta = useMemo(
        (): IActivityLogsTableMeta => ({
            setSelectedLog: () => {}, // No-op function since selectedRule functionality was removed
        }),
        []
    );

    // Stable table configuration
    const table = useReactTable({
        data: transformedData,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onRowSelectionChange: setRowSelection,
        state: { rowSelection },
        meta: tableMeta,
    });

    // ✅ Optimized: Stable callback functions
    const handleApplyFilters = useCallback(() => {
        applyFilters?.();
    }, [applyFilters]);

    const handleClearFilters = useCallback(() => {
        clearAllFilters?.();
    }, [clearAllFilters]);

    const handleExportSuccess = useCallback(() => {
        clearAllFilters?.();
    }, [clearAllFilters]);

    // Stable pagination data
    const paginationData = useMemo(() => {
        if (!activityLogsResponse) {
            return {
                totalItems: 0,
                totalPages: 1,
                currentPage: 1,
                hasNext: false,
                hasPrevious: false,
                currentSize: currentFilters.size ?? 10,
            };
        }

        return {
            totalItems: activityLogsResponse.totalElements,
            totalPages: activityLogsResponse.totalPages,
            currentPage: (activityLogsResponse.currentPage ?? 0) + 1,
            hasNext: !activityLogsResponse.last,
            hasPrevious: !activityLogsResponse.first,
            currentSize: activityLogsResponse.size,
        };
    }, [activityLogsResponse, currentFilters.size]);

    return (
        <section data-testid="activity-logs-component">
            <h1 className="font-bold text-xl text-[#2E335B]">{t("Activity log")}</h1>

            <div className="flex justify-between items-center mb-8 mt-8">
                <ActivityLogFilter
                    filters={tempFilters}
                    onSearch={handleSearch}
                    onApplyFilter={handleApplyFilters}
                    onClearAll={handleClearFilters}
                    updateTempFilter={updateTempFilter}
                    rolesData={rolesData}
                    activityLogsLoading={loading}
                />

                <ExportActivityLog
                    currentFilters={tempFilters}
                    onExportSuccess={handleExportSuccess}
                    selectedRows={
                        Object.keys(rowSelection).length > 0
                            ? Object.keys(rowSelection).map((index) => transformedData[parseInt(index)])
                            : []
                    }
                />
            </div>

            <CurrentFilterItems currentFilters={currentFilters} onClearAll={clearAllFilters} roleMap={roleMap} />

            <main className="w-full mt-6">
                <DataTable
                    columns={columns}
                    table={table}
                    loading={loading || rolesLoading}
                    emptyTabledescription={emptyTableConfig.description}
                    emptyTabletitle={emptyTableConfig.title}
                />

                {transformedData.length > 0 && (
                    <div className="mt-6">
                        <Pagination
                            totalItems={paginationData.totalItems}
                            currentPage={paginationData.currentPage}
                            currentItemsPerPage={paginationData.currentSize}
                            totalPages={paginationData.totalPages}
                            hasNext={paginationData.hasNext}
                            hasPrevious={paginationData.hasPrevious}
                            onPageChange={handlePageChange}
                            onItemsPerPageChange={handleSizeChange}
                            itemsPerPageOptions={[10, 20, 50, 100]}
                            className="px-4"
                        />
                    </div>
                )}
            </main>
        </section>
    );
};

export default React.memo(ActivityLogsComponent);
