"use client";

import React, { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { type IActivityLogFilter, type IActivityLogTempFilter, emptyActivityLogTempFilter } from "../types";
import {
    getActivityLogFiltersFromUrlParams,
    mapTempFiltersToApiFilters,
    mapApiFiltersToTempFilters,
} from "../utils/urlParamUtils";
import isEqual from "lodash/isEqual";

export function useActivityLogFilters(roleMap?: Record<number, string>) {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const isInitializedRef = useRef(false);
    const prevCurrentFiltersRef = useRef<IActivityLogFilter | null>(null);

    const [tempFilters, setTempFilters] = useState<IActivityLogTempFilter>(emptyActivityLogTempFilter);

    // Memoize current filters to prevent unnecessary re-computations
    const currentFilters = useMemo(() => {
        const filters = getActivityLogFiltersFromUrlParams(searchParams);

        // Only update if filters actually changed
        if (!isEqual(filters, prevCurrentFiltersRef.current)) {
            prevCurrentFiltersRef.current = filters;
            return filters;
        }

        return prevCurrentFiltersRef.current || filters;
    }, [searchParams]);

    // Memoize the createQueryString function
    const createQueryString = useCallback((filters: IActivityLogFilter) => {
        const params = new URLSearchParams();

        // Add all non-empty filter values to params
        Object.entries(filters).forEach(([key, value]) => {
            if (
                value !== null &&
                value !== undefined &&
                value !== "" &&
                !(Array.isArray(value) && value.length === 0)
            ) {
                if (Array.isArray(value)) {
                    value.forEach((v) => params.append(key, String(v)));
                } else {
                    params.set(key, String(value));
                }
            }
        });

        return params.toString();
    }, []);

    // Initialize temp filters and set default URL params on first load
    useEffect(() => {
        if (!isInitializedRef.current) {
            setTempFilters(mapApiFiltersToTempFilters(currentFilters, roleMap));

            // If URL has no parameters, set the default filters in URL
            if (searchParams.toString() === "") {
                const queryString = createQueryString(currentFilters);
                router.replace(`${pathname}?${queryString}`, { scroll: false });
            }

            isInitializedRef.current = true;
        }
    }, [currentFilters, pathname, roleMap, router, searchParams]); // Removed createQueryString to prevent infinite loop

    // Update temp filters when roleMap changes (but not on every render)
    useEffect(() => {
        if (isInitializedRef.current && roleMap) {
            const newTempFilters = mapApiFiltersToTempFilters(currentFilters, roleMap);
            setTempFilters((prev) =>
                // Only update if actually different
                isEqual(prev, newTempFilters) ? prev : newTempFilters
            );
        }
    }, [currentFilters, roleMap]); // Only depend on roleMap, not currentFilters

    const debouncedSearch = useCallback(
        (searchValue: string) => {
            if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);

            searchTimeoutRef.current = setTimeout(() => {
                const newFilters = {
                    ...currentFilters,
                    search: searchValue,
                    page: 0,
                };
                const queryString = createQueryString(newFilters);
                router.push(`${pathname}?${queryString}`, { scroll: false });
            }, 1000);
        },
        [currentFilters, createQueryString, pathname, router]
    );

    const updateTempFilter = useCallback((updates: Partial<IActivityLogTempFilter>) => {
        setTempFilters((prev) => {
            const newFilters = { ...prev, ...updates };
            // Only update if actually different
            return isEqual(prev, newFilters) ? prev : newFilters;
        });
    }, []);

    const applyFilters = useCallback(() => {
        try {
            if (!tempFilters) {
                return;
            }

            const apiFilters = mapTempFiltersToApiFilters(tempFilters, currentFilters, roleMap);
            const queryString = createQueryString(apiFilters);
            router.push(`${pathname}?${queryString}`, { scroll: false });
        } catch (error) {
            console.error("Error in applyFilters:", error);
        }
    }, [tempFilters, currentFilters, roleMap, createQueryString, pathname, router]);

    const clearAllFilters = useCallback(() => {
        setTempFilters({ ...emptyActivityLogTempFilter });
        router.push(pathname, { scroll: false });
    }, [pathname, router]);

    const handleSearch = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const searchValue = event.target.value;
            updateTempFilter({ search: searchValue });

            if (searchValue.length >= 3 || searchValue.length === 0) {
                debouncedSearch(searchValue);
            }
        },
        [debouncedSearch, updateTempFilter]
    );

    // Pagination handlers
    const handlePageChange = useCallback(
        (newPage: number) => {
            const apiFilters = {
                ...currentFilters,
                page: newPage - 1, // API uses 0-based indexing, UI uses 1-based
            };
            const queryString = createQueryString(apiFilters);
            router.push(`${pathname}?${queryString}`, { scroll: false });
        },
        [currentFilters, createQueryString, pathname, router]
    );

    const handleSizeChange = useCallback(
        (newSize: number) => {
            const apiFilters = {
                ...currentFilters,
                size: newSize,
                page: 0, // Reset to first page when changing size
            };
            const queryString = createQueryString(apiFilters);
            router.push(`${pathname}?${queryString}`, { scroll: false });
        },
        [currentFilters, createQueryString, pathname, router]
    );

    useEffect(
        () => () => {
            if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);
        },
        []
    );

    // Memoize the return object to prevent unnecessary re-renders of consuming components
    return useMemo(
        () => ({
            currentFilters,
            tempFilters,
            updateTempFilter,
            applyFilters,
            clearAllFilters,
            handleSearch,
            handlePageChange,
            handleSizeChange,
        }),
        [
            currentFilters,
            tempFilters,
            updateTempFilter,
            applyFilters,
            clearAllFilters,
            handleSearch,
            handlePageChange,
            handleSizeChange,
        ]
    );
}
