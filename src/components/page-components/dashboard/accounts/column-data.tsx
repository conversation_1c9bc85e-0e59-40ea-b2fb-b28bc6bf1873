"use client";

// LIBRARY IMPORTS
import * as Dialog from "@radix-ui/react-dialog";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import React, { useState } from "react";

// PROJECT IMPORTS
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
// import Button from "@/components/common/button";
import { Button } from "@/components/common/buttonv3";
import Checkbox from "@/components/common/checkbox";
import { NGFlag, UKFlag, USFlag } from "@/components/common/country-code/flags";
import TableMoreAction from "@/components/common/table/table-more-action";
import { ArrowDownIcon } from "@/components/icons/table";
import { formatDate } from "@/functions/date";
import { renderFormatAmount } from "@/functions/facilities";
import { DashboardStatisticsType, DashboardTransactionType } from "@/redux/types/dashboard";
import { ArrowDownTrayIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";
import DownloadStatementModal from "./download-statement-modal";
import { DownloadIcon, TransferIcon } from "./icons";

interface TableMetaWithTransfer extends TableMeta<DashboardStatisticsType> {
    openTransferModal: () => void;
    openStatementModal: () => void;
}
export const columns: ColumnDef<DashboardStatisticsType>[] = [
    {
        accessorKey: "accountName",
        header: "Accounts",
        cell: ({ row }) => {
            const item = row.original;
            const currencyCode = item.currencyCode?.toUpperCase();
            let flag = <NGFlag />;

            switch (currencyCode) {
                case "NGN":
                    flag = <NGFlag />;
                    break;
                case "USD":
                    flag = <USFlag />;
                    break;
                case "GBP":
                    flag = <UKFlag />;
                    break;
                default:
                    flag = <NGFlag />;
                    break;
            }

            return (
                <div className="flex gap-2 items-center">
                    <div className="h-8 w-8 overflow-hidden rounded-full">{flag}</div>
                    <div className="text-sm leading-[18px]">
                        <p className="text-black font-medium">{item?.accountName ?? "-"}</p>
                        <p className="text-subText">
                            {currencyCode} - {item?.accountNumber ?? "-"}
                        </p>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "balance",
        header: () => <span className="flex justify-end gap-1">Balance</span>,
        cell: ({ row }) => {
            const item = row.original;
            return (
                <div className="justify-end flex font-semibold text-sm">{renderFormatAmount(item?.balance ?? 0)}</div>
            );
        },
    },
    {
        accessorKey: "moneyIn",
        header: () => <span className="flex justify-end gap-1 text-right">Money in this month</span>,
        cell: ({ row }) => {
            const item = row.original;
            return (
                <div className="flex justify-end font-semibold text-success text-sm">
                    {renderFormatAmount(item?.totalInflow ?? 0)}
                </div>
            );
        },
    },
    {
        accessorKey: "moneyOut",
        header: () => <span className="flex justify-end gap-1 text-right">Money out this month</span>,
        cell: ({ row }) => {
            const item = row.original;
            return (
                <div className="justify-end flex font-semibold text-sm">
                    -{renderFormatAmount(item?.totalOutflow ?? 0)}
                </div>
            );
        },
    },
    {
        id: "actions",
        cell: ({ row, table }) => {
            const data = row.original;
            const openTransferModal = (table.options.meta as TableMetaWithTransfer).openTransferModal;
            const openStatementModal = (table.options.meta as TableMetaWithTransfer).openStatementModal;

            const accountActionList = [
                {
                    label: "Download statement",
                    onClick: () => openStatementModal(),
                    icon: <DownloadIcon />,
                },
                {
                    label: "Transfer funds",
                    onClick: () => openTransferModal(),
                    icon: <TransferIcon />,
                },
            ];
            return <TableMoreAction menuItems={accountActionList} data={{ ...data, id: data.accountNumber }} />;
        },
    },
];

export const DownloadStatementDialog: React.FC = () => {
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    return (
        <div>
            <button
                type="button"
                onClick={handleOpen}
                className="duration-300 hover:text-purple-600 flex items-center gap-2"
            >
                <ArrowDownTrayIcon className="w-5" />
                Get statement
            </button>
            <DownloadStatementModal open={open} onClose={handleClose} />
        </div>
    );
};

export const TransferFundsDialog: React.FC = () => {
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    const [fromAccountOption, setFromAccountOption] = useState<{ label: string; value: string }>({
        label: "Main account - **********",
        value: "Main account - **********",
    });
    const [toAccountOption, setToAccountOption] = useState<{ label: string; value: string }>({
        label: "Expense account - *********",
        value: "Expense account - *********",
    });

    const accountFilterValues = ["Main account - **********", "Expense account - *********", "Account Placeholder"];
    return (
        <form>
            <button type="button" onClick={handleOpen} className="hover:text-purple-600">
                Transfer Funds
            </button>
            <Dialog.Root open={open} onOpenChange={setOpen}>
                <Dialog.Overlay style={overlayStyle} />
                <Dialog.Content style={contentStyle} className="w-[522px] bg-white rounded-[8px] p-[20px] fixed ">
                    <Dialog.Title className="text-center w-[462px] text-[20px] text-[#151519]">
                        Transfer funds to another account
                    </Dialog.Title>
                    <Dialog.Description className="py-[10px] text-[14px]">
                        <div className="flex flex-col justify-between h-full w-full">
                            <div className="grid gap-[20px] py-8 px-6">
                                <div>
                                    <Dropdown
                                        label="Transfer from"
                                        name="transferFrom"
                                        value={fromAccountOption}
                                        options={accountFilterValues.map((item) => ({
                                            label: item,
                                            value: item,
                                        }))}
                                        useFormik={false}
                                        onChange={(value) => {
                                            setFromAccountOption(value as { label: string; value: string });
                                        }}
                                    />
                                </div>
                                <div>
                                    <Dropdown
                                        label="Destination account"
                                        name="destination"
                                        value={toAccountOption}
                                        options={accountFilterValues.map((item) => ({
                                            label: item,
                                            value: item,
                                        }))}
                                        useFormik={false}
                                        onChange={(value) => {
                                            setToAccountOption(value as { label: string; value: string });
                                        }}
                                    />
                                </div>
                                <div>
                                    <LabelInput name="amount" label="Amount" value="₦300,000.00" data-testid="amount" />
                                </div>
                                <div>
                                    <LabelInput
                                        name="narration"
                                        label="Narration"
                                        placeholder="Write something here"
                                        data-testid="narration"
                                    />
                                </div>
                            </div>
                        </div>
                    </Dialog.Description>
                    <div className="flex justify-end items-center gap-3 border-[#E3E5E8] py-4 px-6">
                        <Button type="submit" color="outline" onClick={handleClose}>
                            Cancel
                        </Button>
                        <Button type="submit" onClick={handleClose}>
                            Transfer funds
                        </Button>
                    </div>
                </Dialog.Content>
            </Dialog.Root>
        </form>
    );
};

const overlayStyle: React.CSSProperties = {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    position: "fixed",
    inset: 0,
    zIndex: 90,
};
const contentStyle: React.CSSProperties = {
    boxShadow: "0 10px 15px rgba(0, 0, 0, 0.1)",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    zIndex: 100,
};

interface TableMetaWithSetIsOpen extends TableMeta<DashboardTransactionType> {
    setIsOpen: (value: boolean) => void;
    setIsReportOpen: (value: boolean) => void;
    setTransactionId: (value: string) => void;
    handleReportDownload: (value: string) => void;
}
export const recentTransactionColumns: ColumnDef<DashboardTransactionType>[] = [
    {
        enableSorting: false,
        enableHiding: false,
        id: "select",

        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(checked) => row.toggleSelected(!checked)}
                aria-label="Select row"
                size="sm"
            />
        ),
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={(checked) => table.toggleAllPageRowsSelected(!checked)}
                aria-label="Select all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
    },
    {
        accessorKey: "createdDate",
        header: ({ column }) => (
            <button
                className="flex items-center gap-1"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <span>Date</span>
                <ArrowDownIcon />
            </button>
        ),
        cell: ({ row }) => {
            const payment = row.original;

            return (
                <div className="flex flex-col">
                    <span className="text-black font-semibold">{formatDate(payment.createdDate)}</span>
                    <span className="text-xs text-subText">{format(new Date(payment.createdDate), "h:mm a")}</span>
                </div>
            );
        },
    },

    {
        header: "Counterparty",
        accessorKey: "counterpartyType",
    },

    {
        header: ({ column }) => (
            <button
                className="flex items-center justify-end gap-1 text-right w-full"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <ArrowDownIcon />
                <span className="text-right">Amount</span>
            </button>
        ),
        accessorKey: "amount",
        cell: ({ row }) => {
            const payment = row.original;
            const formatted = new Intl.NumberFormat("en-NG", {
                style: "currency",
                currency: "NGN",
            }).format(parseFloat(row.getValue("amount")));

            return (
                <div
                    className={`text-right font-semibold ${payment.transactionType === "Credit" ? "text-success" : ""}`}
                >
                    {payment.transactionType === "Credit" ? "+" : "-"} {formatted}
                </div>
            );
        },
    },
    {
        id: "actions",
        cell: ({ row, table }) => {
            const payment = row.original;
            const setIsReportOpen = (table.options.meta as TableMetaWithSetIsOpen).setIsReportOpen;
            const setTransactionId = (table.options.meta as TableMetaWithSetIsOpen).setTransactionId;
            const handleReportDownload = (table.options.meta as TableMetaWithSetIsOpen).handleReportDownload;

            const setIsOpen = (table.options.meta as TableMetaWithSetIsOpen).setIsOpen;

            const menuItems = [
                {
                    label: "View",
                    onClick: (data: DashboardTransactionType) => {
                        if (setIsOpen) {
                            setTransactionId(data.transactionId);
                            setIsOpen(true);
                        }
                    },
                },
                // {
                //     label: "Report",
                //     onClick: (data: DashboardTransactionType) => {
                //         if (setIsReportOpen) {
                //             setIsReportOpen(true);
                //         }
                //     },
                // },
                {
                    label: "Download receipt",
                    onClick: (data: DashboardTransactionType) => {
                        // action here
                        handleReportDownload(data.transactionId);
                    },
                },
            ];

            return <TableMoreAction data={{ ...payment, id: payment.transactionId }} menuItems={menuItems} />;
        },
    },
];
