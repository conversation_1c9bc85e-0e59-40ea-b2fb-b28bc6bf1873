"use client";

// LIBRARY IMPORTS
import Link from "next/link";

// PROJECT IMPORTS
import { closeTransferFundDialog, openAddTransferFundsDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";

import LoadingIndicator from "@/components/common/loading-indicator";
import { CopyIcon } from "@/components/icons/auth";
import { copyToClipboard } from "@/functions/stringManipulations";
import { closeTransferSuccessModal } from "@/redux/slices/accountSlice";
import { PATH_DASHBOARD } from "@/routes/path";
import { ArrowUturnRightIcon } from "@heroicons/react/24/outline";
import AccountChart from "./account-chart";
import AccountSwitcher from "./account-switcher";
import { DownloadStatementDialog } from "./column-data";
import AddTransferFunds from "./payments/add-transfer-funds";
import AddTransferFundsReview from "./payments/add-transfer-funds-review";
import TransferSuccessModal from "./payments/transfer-success-modal";
import RecentTransaction from "./recent-transaction";

const AccountOverview = () => {
    const dispatch = useAppDispatch();
    const isAddTransferFundsOpen = useAppSelector((state) => state.uiDialog?.transferFunds?.isAddTransferFundsOpen);
    const isReviewTransferFundsOpen = useAppSelector(
        (state) => state.uiDialog?.transferFunds?.isReviewTransferFundsOpen
    );
    const { selectedAccount, loadingStatus } = useAppSelector((state) => state.account);
    const { successModalOpen } = useAppSelector((state) => state.account.transferFundsAccount);

    return (
        <div className="flex">
            <div className="flex-1 p-6">
                <AccountSwitcher />
                <div className="mb-12 grid grid-cols-1 lg:grid-cols-3 gap-5 mt-[40px]">
                    <div className="lg:col-span-2">
                        <AccountChart />
                    </div>
                    <div className="w-full h-full flex flex-col">
                        <div className="bg-[#F9F9FA] py-[30px] rounded-lg flex flex-col h-full">
                            {loadingStatus === "loading" ? (
                                <LoadingIndicator />
                            ) : selectedAccount ? (
                                <div className="flex flex-col gap-7 px-4 mb-5">
                                    {[
                                        {
                                            label: "Bank name",
                                            value: "First city monument bank",
                                            showCopy: true,
                                        },
                                        {
                                            label: "Account number",
                                            value: selectedAccount.accountNumber,
                                            showCopy: true,
                                        },
                                        { label: "Account name", value: selectedAccount.accountName },
                                        { label: "Alias", value: selectedAccount.preferredName },
                                        { label: "Account type", value: selectedAccount.schemeType },
                                    ].map(({ label, value, showCopy }, i) => (
                                        <div key={label} className="flex justify-between items-center gap-2">
                                            <div className="text-subText text-sm font-medium">{label}</div>
                                            <div className="text-right flex items-center gap-[10px] text-black text-sm font-medium">
                                                {value}
                                                {showCopy && (
                                                    <button
                                                        aria-label="Copy authenticator code"
                                                        onClick={async () => await copyToClipboard(value)}
                                                        data-testid="copy-authenticator"
                                                    >
                                                        <CopyIcon />
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-xs text-subText font-medium px-5 italic">
                                    Select an account to view details
                                </p>
                            )}

                            <div className="flex items-center gap-9 mt-auto border-t border-t-[#E3E5E8] pt-[24.5px] justify-center flex-wrap">
                                <div className="rounded-lg justify-center items-center gap-2.5 inline-flex">
                                    <div className="justify-center items-center gap-2 inline-flex">
                                        <div className="text-center text-[#151518] text-sm font-semibold font-['SF Pro Display'] leading-[18px] tracking-tight">
                                            <button
                                                onClick={() => dispatch(openAddTransferFundsDialog())}
                                                className="flex items-center gap-2 duration-300 hover:text-purple-600"
                                            >
                                                <ArrowUturnRightIcon className="w-5" />
                                                Transfer funds
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="rounded-lg flex-col justify-center items-center gap-2.5 inline-flex">
                                    <div className="justify-center items-center gap-2 inline-flex">
                                        <div className="text-center text-[#151518] text-sm font-semibold font-['SF Pro Display'] leading-[18px] tracking-tight">
                                            <DownloadStatementDialog />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="text-[#151518] text-lg font-semibold font-['SF Pro Display'] ">Recent transactions</div>
                <div className="text-right text-[#5c068c] text-base font-normal font-['SF Pro Display']">
                    <Link href={PATH_DASHBOARD.transactions.root}>See all transactions</Link>
                </div>
                <RecentTransaction />
            </div>

            <AddTransferFunds isOpen={isAddTransferFundsOpen} onClose={() => dispatch(closeTransferFundDialog())} />
            <AddTransferFundsReview
                onBack={() => dispatch(openAddTransferFundsDialog())}
                isOpen={isReviewTransferFundsOpen}
                onClose={() => dispatch(closeTransferFundDialog())}
            />
            <TransferSuccessModal onClose={() => dispatch(closeTransferSuccessModal())} isOpen={successModalOpen} />
        </div>
    );
};

export default AccountOverview;
