"use client";

import { DataTable } from "@/components/common/table/DataTable";
import { getRecentTransactions } from "@/redux/actions/dashboardActions";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { ITransaction } from "@/redux/types/transactions";
import {
    ColumnFiltersState,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { MultiStepTransactionFormModal } from "../transactions/multi-step-transaction-form-modal";
import { TransactionDetails } from "../transactions/transaction-details";
import { recentTransactionColumns } from "./column-data";

const RecentTransaction = () => {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [rowSelection, setRowSelection] = React.useState({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const { selectedAccount } = useAppSelector((state) => state.account);
    const dispatch = useAppDispatch();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const { data, loading } = useAppSelector((state) => state.dashboard.getRecentTransactions);
    const [isReportOpen, setIsReportOpen] = useState(false);
    const [transactionId, setTransactionId] = useState<string | null>(null);

    const handleReportDownload = async (id: string) => {
        await dispatch(
            downloadReceipt({
                transactionId: id,
                isUserInitiated: true,
            })
        );
    };
    const handleCloseDetails = () => {
        setIsOpen(false);
        setTransactionId(null);
    };

    useEffect(() => {
        const getData = async () => {
            await dispatch(
                getRecentTransactions({
                    accountNumber: selectedAccount?.accountNumber,
                })
            );
        };

        if (selectedAccount) getData();
    }, [dispatch, selectedAccount]);

    const table = useReactTable({
        data,
        columns: recentTransactionColumns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            rowSelection,
            columnFilters,
        },
        meta: {
            setIsOpen: (value: boolean) => setIsOpen(value),
            setIsReportOpen: (value: boolean) => setIsReportOpen(value),
            setTransactionId: (value: string) => setTransactionId(value),
            handleReportDownload,
        },
    });

    return (
        <>
            <div className="!mt-4 flex-1 flex flex-col justify-between">
                <DataTable columns={recentTransactionColumns} table={table} loading={loading} />
            </div>
            <TransactionDetails
                transactionId={transactionId ?? ""}
                handleCloseDetails={handleCloseDetails}
                isOpen={isOpen}
                handleOpenReport={() => setIsReportOpen(true)}
                selectedTransaction={
                    data?.find((transaction) => transaction.transactionId === transactionId) as ITransaction
                }
            />

            <MultiStepTransactionFormModal
                isOpen={isReportOpen}
                closeModal={() => setIsReportOpen(false)}
                onComplete={() => {}}
            />
        </>
    );
};

export default RecentTransaction;
