"use client";

import OTPContainer from "@/components/page-components/auth/otp-container";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useOtp } from "@/hooks/useOtp";
import { useEffect, useState } from "react";

type SmsVerificationProps = {
    phoneNumber: string;
    onVerified: (token: string) => void;
};

const SmsVerification = ({ phoneNumber, onVerified }: SmsVerificationProps) => {
    const [otpValue, setOtpValue] = useState("");

    const {
        validateOtp,
        resendOtp,
        sendOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,
        clearOtpState,
        validateOtpToken,
    } = useOtp();

    const handleResendOTP = async () => {
        await resendOtp(String(phoneNumber));
    };

    const submitForm = async () => {
        await validateOtp({
            receiver: String(phoneNumber),
            otp: otpValue,
        });
    };

    // Send OTP on mount
    useEffect(() => {
        clearOtpState("validate");
        clearOtpState("resend");
        sendOtp({
            receiver: String(phoneNumber),
            receiverType: "SMS",
        });
    }, [phoneNumber]);

    // Handle verification success
    useEffect(() => {
        if (validateOtpSuccess && validateOtpToken) {
            onVerified(validateOtpToken);
            sendFeedback("OTP verified successfully", "success");
            clearOtpState("validate");
        }

        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success");
            clearOtpState("resend");
        }
    }, [validateOtpSuccess, resendOtpSuccess, validateOtpToken]);

    // Handle errors
    useEffect(() => {
        if (resendOtpError) {
            sendCatchFeedback(resendOtpError, () => clearOtpState("resend"));
        }

        if (validateOtpError) {
            sendCatchFeedback(validateOtpError, () => clearOtpState("validate"));
            setOtpValue("");
        }
    }, [validateOtpError, resendOtpError]);

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            onResend={handleResendOTP}
            loading={validateOtpLoading}
            resendLoading={resendOtpLoading}
            variant="autoVerify"
            title="Check your mobile for OTP"
            type="phone"
            subtitle="Enter the SMS OTP sent to your mobile"
            resendText="Send New Code"
        />
    );
};

export default SmsVerification;
