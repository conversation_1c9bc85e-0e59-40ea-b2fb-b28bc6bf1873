"use client";
// PROJECT IMPORTS
import OTPContainer from "@/components/page-components/auth/otp-container";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { validateSettingsAuthenticator } from "@/redux/actions/settingsMfaActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { useEffect, useState } from "react";

type TwoFaFormProps = {
    username: string;
    onVerified: (token: string) => void;
};

const TwoFaForm = ({ username, onVerified }: TwoFaFormProps) => {
    const { error, success, loading, token } = useAppSelector((state) => state.settingsMfa.validateAuthenticator || {});

    const dispatch = useAppDispatch();

    const [otpValue, setOtpValue] = useState("");

    const submitForm = async () => {
        // Set current login step
        // dispatch(signinActions.setLoginStep(LoginStep.MFA_VERIFICATION));

        await dispatch(
            validateSettingsAuthenticator({
                username,
                otp: otpValue,
            })
        );
    };

    useEffect(() => {
        if (error) {
            sendCatchFeedback(error, () => dispatch(signinActions.clearLoginValidationError()));
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [error]);

    useEffect(() => {
        if (success && token) {
            // Get username from session storage

            onVerified(token);
            sendFeedback("Authenticator verified successfully", "success");
            dispatch(signinActions.clearLoginValidationError());
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [success, token]);

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            loading={loading}
            variant="autoVerify"
            title="Check your authenticator app"
            type="email"
            subtitle="Enter your OTP from your authenticator app"
            initialTimerSeconds={5}
        />
    );
};

export default TwoFaForm;
