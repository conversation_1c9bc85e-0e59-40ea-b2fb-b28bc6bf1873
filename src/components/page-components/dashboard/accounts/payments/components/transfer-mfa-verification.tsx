"use client";

// PROJECT IMPORTS

import FullScreenDrawer from "@/components/common/full-screen-drawer";
import TwoFaForm from "./user-authenticator";
import SecurityQuestion from "./user-security-questions";
import SmsVerification from "./user-sms";

interface TransferMfaVerificationProps {
    userMfaType?: string | null;
    onClose: () => void;
    isOpen: boolean;
    onSuccess?: () => void;
    onVerified?: (token: string) => void;
    email: string;
    phoneNumber: string;
}

const TransferMfaVerification = ({
    userMfaType,
    onClose,
    isOpen,
    onSuccess,
    onVerified,
    email,
    phoneNumber,
}: TransferMfaVerificationProps) => {
    if (!userMfaType && !email && !phoneNumber) {
        return null;
    }

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Verify MFA">
            {userMfaType === "AUTHENTICATOR" && <TwoFaForm username={email} onVerified={onVerified || (() => {})} />}
            {userMfaType === "SECURITY_QUESTION" && (
                <SecurityQuestion email={email} onVerified={onVerified || (() => {})} />
            )}
            {userMfaType === "SMS" && (
                <SmsVerification phoneNumber={phoneNumber} onVerified={onVerified || (() => {})} />
            )}
        </FullScreenDrawer>
    );
};

export default TransferMfaVerification;
