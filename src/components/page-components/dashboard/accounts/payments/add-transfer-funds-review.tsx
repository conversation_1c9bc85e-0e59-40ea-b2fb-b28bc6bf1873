// LIBRARY IMPORTS
import { FC, useEffect, useState } from "react";

// PROJECT IMPORTS
import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { BankNoteIcon } from "@/components/icons/bill-payment-icons";
import { maskNumber } from "@/functions/facilities";
import { sendFeedback } from "@/functions/feedback";
import { transferFundsAccount } from "@/redux/actions/accountActions";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { openReviewTransferFundsDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { clearTransferFundsSuccess, openTransferSuccessModal, resetAccount } from "@/redux/slices/accountSlice";
import { clearVerifyPin, openVerifyPinModal } from "@/redux/slices/securitySlice";
import MultiPartyApproval from "../../bill-payments/common/multi-party-approval";
import TransferMfaVerification from "./components/transfer-mfa-verification";

interface TransferInfoProps {
    isOpen: boolean;
    onBack: () => void;
    onClose: () => void;
}

const AddTransferFundsReview: FC<TransferInfoProps> = ({ isOpen, onBack, onClose }) => {
    const dispatch = useAppDispatch();
    const transferFunds = useAppSelector((state) => state.account?.transferFunds);
    const { loading, success } = useAppSelector((state) => state.account.transferFundsAccount);
    const { success: pinSuccess, pin } = useAppSelector((state) => state.security.verifyPin);
    const { success: teamMemberSuccess, loading: teamMemberLoading } = useAppSelector(
        (state) => state.transferMfaSlice.getTeamMemberDetails
    );
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);

    const [verificationState, setVerificationState] = useState({
        pinVerified: false,
        mfaVerified: false,
        pin: "",
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [initiateTransferFlowState, setInitiateTransferFlowState] = useState(false);

    const [showApprovals] = useState(true);

    const transferFundsSteps = [
        { id: "1", label: "Transfer details" },
        { id: "4", label: "Review" },
    ];

    const accountReview = [
        {
            label: transferFunds?.fromAccount?.accountName,
            accountName: transferFunds?.fromAccount?.accountName,
            accountNumber: transferFunds?.fromAccount?.accountNumber,
        },
        {
            label: transferFunds?.destinationAccount?.accountName,
            accountName: transferFunds?.destinationAccount?.accountName,
            accountNumber: transferFunds?.destinationAccount?.accountNumber,
        },
    ];

    const onSubmit = (token: string) => {
        const { fromAccount, destinationAccount, amount, narration } = transferFunds;
        dispatch(
            transferFundsAccount({
                sourceAccount: fromAccount?.accountNumber ?? "",
                sourceAccountName: fromAccount?.accountName ?? "",
                sourceAccountCurrency: "USD",
                destinationAccount: destinationAccount?.accountNumber ?? "",
                destinationAccountName: destinationAccount?.accountName ?? "",
                destinationAccountCurrency: "EUR",
                amount,
                bankCode: "214",
                bankName: "FCMB",
                requestType: "INSTANT",
                needsApproval: false,
                fxRequest: false,
                frequencyType: "DAILY",
                transactionPin: verificationState.pin,
                token,
                narration,
            })
        );
    };

    const initiateTransferFlow = () => {
        dispatch(getTeamMemberDetails());
        setInitiateTransferFlowState(true);
    };

    // Handle team member details response
    useEffect(() => {
        if (teamMemberSuccess && teamMember && initiateTransferFlowState) {
            // MFA is setup
            if (teamMember.mfaStatus) {
                // Always require PIN verification
                dispatch(openVerifyPinModal());
            } else {
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
    }, [teamMemberSuccess, teamMember, dispatch, initiateTransferFlowState]);

    // Handle PIN verification success
    useEffect(() => {
        if (pinSuccess && pin && initiateTransferFlowState) {
            setVerificationState({
                pinVerified: true,
                mfaVerified: false,
                pin: pin,
            });

            // Only require MFA if user has it enabled
            if (teamMember?.mfaStatus) {
                dispatch(openReviewTransferFundsDialog());
            } else {
                // if not enabled
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
    }, [pinSuccess, pin, dispatch, teamMember, initiateTransferFlowState]);

    // Handle MFA verification completion or skip
    const handleMfaVerified = (token: string) => {
        setIsSubmitting(true);
        onSubmit(token);
    };

    const handleMfaVerificationDrawer = () => {
        setVerificationState((prev) => ({
            ...prev,
            pinVerified: false,
            pin: "",
        }));
        setInitiateTransferFlowState(false);
    };

    useEffect(() => {
        if (success) {
            setIsSubmitting(false);
            setVerificationState({
                pinVerified: false,
                mfaVerified: true,
                pin: "",
            });
            onClose();
            dispatch(resetAccount());
            dispatch(openTransferSuccessModal());
            dispatch(clearTransferFundsSuccess());
            dispatch(clearVerifyPin());
            setInitiateTransferFlowState(false);
            setInitiateTransferFlowState(false);
        }
    }, [dispatch, success]);

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Transfer funds">
            <div className="flex-1 flex min-h-fit relative overflow-auto">
                <nav
                    className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment steps"
                >
                    <Stepper steps={transferFundsSteps} currentStep={2} />
                </nav>
                <main
                    className="flex-1 flex justify-center overflow-y-auto overflow-x-auto relative pl-[240px] pr-[435px] min-[1450px]:pr-[480px]"
                    role="main"
                >
                    <div className="w-[495px] min-[1450px]:w-[515px] flex flex-col py-8 min-h-min">
                        <div className="w-[515px] h-[531px] flex-col justify-start items-center gap-12 inline-flex">
                            <div className="self-stretch text-center text-[#151518] text-2xl font-semibold font-['SF Pro Display'] leading-[30px] tracking-tight">
                                Review transfer details
                            </div>
                            <div className="self-stretch h-[466px] flex-col justify-start items-start gap-8 flex">
                                {accountReview.map((item, index) => (
                                    <div
                                        key={index}
                                        className="self-stretch h-[103px] flex-col justify-start items-start gap-5 flex"
                                    >
                                        <div className="self-stretch text-[#151518] text-base font-semibold font-['SF Pro Display'] leading-tight tracking-tight">
                                            {index === 0 ? "Transfer from" : "Destination account"}
                                        </div>
                                        <div className="self-stretch p-4 bg-white rounded-xl border border-[#e3e5e7] justify-start items-center gap-10 inline-flex">
                                            <div className="grow shrink basis-0 h-10 justify-start items-center gap-4 flex">
                                                <BankNoteIcon className="w-10 h-10 relative  overflow-hidden" />
                                                <div className="w-[137px] flex-col justify-start items-start gap-3 inline-flex">
                                                    <div className="self-stretch text-[#151518] text-sm font-medium font-['SF Pro Display'] leading-[18px] tracking-tight">
                                                        {item.accountName}
                                                    </div>
                                                    <div className="self-stretch text-[#393941] text-sm font-normal font-['SF Pro Display'] leading-[18px] tracking-tight">
                                                        {maskNumber(item?.accountNumber ?? "")}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="px-3 py-[11px] bg-[#f9f0fe] rounded-full justify-start items-center flex">
                                                <button
                                                    onClick={onBack}
                                                    type="button"
                                                    className="text-center text-[#5c068c] text-sm font-medium font-['SF Pro Display'] leading-[18px] tracking-tight"
                                                >
                                                    Change
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <div className="self-stretch h-[196px] flex-col justify-start items-end gap-8 flex">
                                    <div className="self-stretch h-[124px] flex-col justify-start items-start gap-5 flex">
                                        <div className="self-stretch justify-between items-center inline-flex">
                                            <div className="grow shrink basis-0 text-[#151518] text-base font-semibold font-['SF Pro Display'] leading-tight tracking-tight">
                                                Payment information
                                            </div>
                                            <button
                                                onClick={onBack}
                                                type="button"
                                                className="grow shrink basis-0 text-right text-[#5c068c] text-base font-semibold font-['SF Pro Display'] leading-tight tracking-tight"
                                            >
                                                Edit
                                            </button>
                                        </div>
                                        <div className="self-stretch p-5 bg-white rounded-xl border border-[#e3e5e7] flex-col justify-start items-start gap-8 flex">
                                            <div className="self-stretch justify-between items-center inline-flex">
                                                <div className="grow shrink basis-0 text-[#393941] text-sm font-normal font-['SF Pro Display'] leading-[18px] tracking-tight">
                                                    Amount
                                                </div>
                                                <div className="grow shrink basis-0 text-right text-[#151518] text-base font-medium font-['SF Pro Display'] leading-tight tracking-tight">
                                                    ₦{transferFunds?.amount}
                                                </div>
                                            </div>
                                            <div className="self-stretch justify-between items-center inline-flex">
                                                <div className="grow shrink basis-0 text-[#393941] text-sm font-normal font-['SF Pro Display'] leading-[18px] tracking-tight">
                                                    Narration
                                                </div>
                                                <div className="grow shrink basis-0 text-right text-[#151518] text-sm font-medium font-['SF Pro Display'] leading-[18px] tracking-tight">
                                                    {transferFunds?.narration ? transferFunds?.narration : "-"}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex justify-end gap-3 mt-8">
                                        <Button onClick={onBack} variant="outline" size="medium">
                                            Previous
                                        </Button>
                                        <Button
                                            loading={loading || teamMemberLoading}
                                            onClick={initiateTransferFlow}
                                            variant="primary"
                                            size="medium"
                                        >
                                            Transfer
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <MultiPartyApproval isOpen={showApprovals} amount={transferFunds.amount} />

                {initiateTransferFlowState &&
                    verificationState.pinVerified &&
                    teamMember?.mfaStatus &&
                    !verificationState.mfaVerified && (
                        <TransferMfaVerification
                            userMfaType={teamMember?.preferredMfaMethod}
                            onClose={handleMfaVerificationDrawer}
                            isOpen={true}
                            onVerified={handleMfaVerified}
                            email={teamMember?.email}
                            phoneNumber={teamMember?.phoneNumber}
                        />
                    )}
            </div>
        </FullScreenDrawer>
    );
};

export default AddTransferFundsReview;
