// LIBRARY IMPORTS
import { FC, useEffect, useState } from "react";

// PROJECT IMPORTS
import { But<PERSON> } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import LabelInput from "@/components/common/label-input";
import Stepper from "@/components/common/stepper";
import { sendFeedback } from "@/functions/feedback";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { openReviewTransferFundsDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { handleTransferFunds } from "@/redux/slices/accountSlice";
import AccountSelector from "./components/account-selector";

interface TransferInfoProps {
    isOpen: boolean;
    onClose: () => void;
}

const AddTransferFunds: FC<TransferInfoProps> = ({ isOpen, onClose }) => {
    const dispatch = useAppDispatch();
    const [accountBalance, setAccountBalance] = useState<number | undefined>(undefined);
    const { selectedAccount } = useAppSelector((state) => state.account);
    const [isAmountFocused, setIsAmountFocused] = useState(false);

    const transferFundsSteps = [
        { id: "1", label: "Transfer details" },
        { id: "4", label: "Review" },
    ];
    const { fromAccount, destinationAccount, amount, narration } = useAppSelector(
        (state) => state.account?.transferFunds
    );

    const displayAmountValue = () => {
        if (isAmountFocused) return amount || "";
        if (!amount || isNaN(Number(amount))) return "";
        return formatNumberToNaira(Number(amount), 2);
    };

    const handleAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        let value = event.target.value;

        // Remove any character that's not digit or dot
        value = value.replace(/[^0-9.]/g, "");

        // Prevent more than one decimal point
        const parts = value.split(".");
        if (parts.length > 2) return;

        // If decimal part exists, limit to max 2 digits
        if (parts[1]?.length > 2) {
            value = parts[0] + "." + parts[1].slice(0, 2);
        }

        dispatch(handleTransferFunds({ labelName: "amount", amount: value }));
    };

    const handleAmountBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsAmountFocused(false);

        const val = amount?.toString();
        if (val !== "" && val !== null && !isNaN(Number(val))) {
            // If value is zero or zero with decimals, clear it
            if (Number(val) === 0) {
                dispatch(handleTransferFunds({ labelName: "amount", amount: "" }));
            } else {
                dispatch(handleTransferFunds({ labelName: "amount", amount: Number(val) }));
            }
        }
    };

    const handleNarrationChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        dispatch(handleTransferFunds({ labelName: "narration", narration: value }));
    };

    const handleContinueBtn = () => {
        if (!fromAccount?.accountNumber) {
            return sendFeedback("Select source account", "error");
        }
        if (!destinationAccount?.accountNumber) {
            return sendFeedback("Select destination account", "error");
        }
        if (fromAccount?.accountNumber === destinationAccount?.accountNumber)
            //cannot send money to the same account
            return sendFeedback("Account cannot be the same.", "error");
        if (fromAccount?.currencyCode !== destinationAccount?.currencyCode)
            //cannot send money to different currency
            return sendFeedback("Please select accounts that have the same currency", "error");
        if (!amount) return sendFeedback("Amount is required", "error");

        dispatch(openReviewTransferFundsDialog());
    };

    useEffect(() => {
        if (selectedAccount) {
            dispatch(
                handleTransferFunds({
                    accountName: selectedAccount.accountName,
                    accountNumber: selectedAccount.accountNumber,
                    labelName: "Transfer from",
                })
            );
        }
    }, [dispatch, selectedAccount]);

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Transfer funds">
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={transferFundsSteps} currentStep={1} />
                </nav>
                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[470px]">
                        <h2
                            className="text-center text-[#151518] text-2xl font-semibold leading-[30px] tracking-tight mb-10"
                            data-testid="payment-info-title"
                        >
                            Payment information
                        </h2>
                        <div className="min-h-[516px] flex-col justify-start items-start gap-8 inline-flex w-full overflow-y-auto">
                            <form className="self-stretch flex-col justify-end items-end gap-8 flex">
                                <div className="w-full space-y-7">
                                    <AccountSelector
                                        labelName={"Transfer from"}
                                        selectedAccount={fromAccount}
                                        updateExternalBalance={(balance) => setAccountBalance(balance)}
                                        onChange={(selected) =>
                                            dispatch(
                                                handleTransferFunds({
                                                    accountName: selected.accountName,
                                                    accountNumber: selected.accountNumber,
                                                    labelName: "Transfer from",
                                                    currencyCode: selected.currencyCode,
                                                })
                                            )
                                        }
                                    />
                                    <AccountSelector
                                        labelName={"Destination account"}
                                        selectedAccount={destinationAccount}
                                        onChange={(selected) =>
                                            dispatch(
                                                handleTransferFunds({
                                                    accountName: selected.accountName,
                                                    accountNumber: selected.accountNumber,
                                                    labelName: "Destination account",
                                                    currencyCode: selected.currencyCode,
                                                })
                                            )
                                        }
                                    />
                                    <LabelInput
                                        required={true}
                                        useFormik={false}
                                        name="amount"
                                        label="Amount"
                                        value={displayAmountValue()}
                                        onBlur={handleAmountBlur}
                                        onFocus={() => setIsAmountFocused(true)}
                                        data-testid="amount"
                                        onChange={handleAmountChange}
                                        showError={accountBalance ? amount > accountBalance : false}
                                        error={
                                            accountBalance
                                                ? `Total amount exceeds your account balance of ${formatNumberToNaira(accountBalance, 2)}`
                                                : ""
                                        }
                                    />
                                    <LabelInput
                                        useFormik={false}
                                        value={narration}
                                        name="narration"
                                        label="Narration"
                                        placeholder="Write something here"
                                        data-testid="narration"
                                        onChange={handleNarrationChange}
                                    />
                                </div>
                                <div className="justify-end items-center gap-3 inline-flex">
                                    <Button type="button" variant="outline" size="medium" onClick={onClose}>
                                        Previous
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="primary"
                                        size="medium"
                                        onClick={handleContinueBtn}
                                        disabled={(accountBalance ? amount > accountBalance : false) || !narration}
                                    >
                                        Continue
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default AddTransferFunds;
