"use client";
// LIBRARY IMPORTS
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useMemo } from "react";

// PROJECT IMPORTS
import { But<PERSON> } from "@/components/common/buttonv3";
import CloseX from "@/components/common/close-x";
import SideDrawer from "@/components/common/drawer";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import { closeRequestAccountDialog } from "@/redux/features/uiDialogSlice";
import { useFormik } from "formik";

const requestTypeFilter = ["I want a new account", "I want to add an existing account"];
const accountTypeFilterValues = ["Domiciliary account", "Current", "Saving"];
const currencyFilterValues = ["NGN", "USD"];
const yesOrNoFilterValues = ["Yes", "No"];
const accountNameFilterValues = ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"];

const AccountRequestDialog = () => {
    const dispatch = useAppDispatch();
    const { isOpen } = useAppSelector((state) => state.uiDialog);
    const handleClose = () => dispatch(closeRequestAccountDialog());

    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                requestType: "",
                accountType: "",
                accountName: "",
                currency: "",
                useExistingDoc: "",
                narration: "",
                accountNumber: "",
            }),

            []
        ),
        onSubmit: () => {
            submitValues();
        },
    });

    const submitValues = async () => {
        handleClose();
    };

    return (
        <SideDrawer isOpen={isOpen}>
            <form onSubmit={formik.handleSubmit}>
                <div className="flex flex-col h-full">
                    {/* Header */}

                    <div>
                        <div className="py-[19px] px-4">
                            <div className="flex justify-between items-center">
                                <h2 className="flex text-xl font-semibold text-black px-24">Request an account</h2>
                                <CloseX onClick={() => dispatch(closeRequestAccountDialog())} color="#90909D" />
                            </div>
                            <p className="text-[14px] text-center leading-[18px] font-normal text-subText mt-[12px]">
                                Request a new account or add an existing account to your Corporate Internet Banking
                                profile.
                            </p>
                        </div>
                    </div>

                    {/* Sections */}
                    <div className="flex flex-col justify-between h-full w-full">
                        <div className="grid gap-[20px] py-8 px-6">
                            <div>
                                <Dropdown
                                    label="What would you like to request for?"
                                    name="requestType"
                                    formik={formik}
                                    options={requestTypeFilter.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                />
                            </div>

                            <div>
                                <Dropdown
                                    label="Account Type"
                                    name="accountType"
                                    formik={formik}
                                    options={accountTypeFilterValues.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                />
                            </div>

                            <div>
                                <Dropdown
                                    label="Account name"
                                    name="accountName"
                                    formik={formik}
                                    placeholder={"Select account"}
                                    options={accountNameFilterValues.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                />
                            </div>

                            <div>
                                <Dropdown
                                    label="Currency"
                                    name="currencyCode"
                                    formik={formik}
                                    options={currencyFilterValues.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                />
                            </div>
                            <div>
                                <Dropdown
                                    label="Would you like to use your existing documents??"
                                    name="useExistingDoc"
                                    options={yesOrNoFilterValues.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                />
                            </div>
                            <div>
                                <LabelInput
                                    label="Narration"
                                    name="narration"
                                    formik={formik}
                                    placeholder="Write something here"
                                    data-testid="narration"
                                />
                            </div>
                            <div className="flex justify-end items-center gap-3 border-[#E3E5E8] py-4 px-6">
                                <Button type="submit" variant="outline" onClick={handleClose}>
                                    Cancel
                                </Button>
                                <Button type="submit">Submit</Button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </SideDrawer>
    );
};

export default AccountRequestDialog;
