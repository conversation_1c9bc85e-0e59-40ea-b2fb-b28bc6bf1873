// LIBRARY IMPORTS
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { ChevronDown, Landmark } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/common/dropdown-menu";
import React, { useEffect, useRef } from "react";

// PROJECT IMPORTS
import { EuroFlag, NGFlag, UKFlag, USFlag } from "@/components/common/country-code/flags";
import { renderFormatAmount } from "@/functions/facilities";
import { getAccounts } from "@/redux/actions/accountActions";
import { getAccountDetails } from "@/redux/actions/dashboardActions";
import { onSwitchAccount } from "@/redux/slices/accountSlice";
import { Button } from "@/components/common/buttonv3";

const AccountSwitcher: React.FC = () => {
    const { accounts } = useAppSelector((state) => state.account);
    const selectedAccount = useAppSelector((state) => state.account?.selectedAccount);
    const isFirstRender = useRef(true);
    const { data, loading } = useAppSelector((state) => state.dashboard.getAccountDetails);
    const dispatch = useAppDispatch();

    const renderCurrencyFlag = (currencyParam: string) => {
        switch (currencyParam?.toLowerCase()) {
            case "ngn":
                return <NGFlag />;
            case "usd":
                return <USFlag />;
            case "gbp":
                return <UKFlag />;
            case "eur":
                return <EuroFlag />;
            default:
                return <NGFlag />;
        }
    };

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            dispatch(getAccounts());
        }
    }, [dispatch]);

    useEffect(() => {
        if (selectedAccount) {
            dispatch(getAccountDetails({ accountNumber: selectedAccount.accountNumber }));
        }
    }, [dispatch, selectedAccount]);

    return (
        <div className="w-full max-w-sm">
            {/* Account Selector */}
            <div className="mb-4">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="outline"
                            className="w-full justify-between p-0 h-auto font-normal hover:bg-transparent border-none outline-none !ring-0 !ring-offset-0 pl-0"
                        >
                            <div className="flex items-center gap-2 text-gray-600">
                                <Landmark className="w-4 h-4 text-primary" />
                                <span className="text-sm">
                                    {selectedAccount?.accountName || "Select account"} |{" "}
                                    {selectedAccount?.accountNumber || ""}
                                </span>
                            </div>
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                        </Button>
                    </DropdownMenuTrigger>

                    <DropdownMenuContent className="w-80 p-2 bg-white rounded-[14px]" align="start">
                        <div className="space-y-1">
                            {accounts && accounts.length > 0 ? (
                                accounts.map((account) => (
                                    <DropdownMenuItem
                                        key={account.accountNumber}
                                        className={`p-3 cursor-pointer rounded-lg ${
                                            selectedAccount?.accountNumber === account.accountNumber
                                                ? "bg-gray-100"
                                                : "hover:bg-primary-ghost"
                                        }`}
                                        onClick={() => {
                                            dispatch(onSwitchAccount(account));
                                        }}
                                    >
                                        <div className="flex items-center gap-3 w-full">
                                            <div className="flex-shrink-0 h-6 w-6">
                                                {renderCurrencyFlag(account.currencyCode)}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="font-medium text-sm text-black  truncate">
                                                    {account.accountName}
                                                </p>
                                                <p className="text-xs text-sub-text uppercase">
                                                    {account.currencyCode} - {account.accountNumber}
                                                </p>
                                            </div>
                                        </div>
                                    </DropdownMenuItem>
                                ))
                            ) : (
                                <div className="p-3 text-sm text-gray-500">No accounts found</div>
                            )}
                        </div>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Balance Display */}
            <div className="space-y-2">
                <div className="text-4xl font-semibold text-black tracking-wide">
                    {loading ? (
                        <div data-testid="loading-skeleton" className="animate-pulse bg-gray-200 h-10 w-48 rounded" />
                    ) : (
                        renderFormatAmount(data?.balance ?? 0, selectedAccount?.currencyCode || "NGN")
                    )}
                </div>
            </div>
        </div>
    );
};

export default AccountSwitcher;
