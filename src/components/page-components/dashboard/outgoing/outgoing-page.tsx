/**
 * Purpose: Serves as the main orchestrator component for the outgoing payments module.
 *
 * Functionality: This component manages the display and interaction of instant, scheduled, and recurring
 * transfer data across three main tabs. It coordinates data fetching from the Redux store using appropriate
 * thunks for each transfer type and implements comprehensive client-side filtering functionality. The component
 * handles pagination, search, and filter state management while determining appropriate UI states (loading,
 * error, empty, data) based on current data status. Since backend APIs don't support filtering, all filtering
 * operations are performed client-side to provide immediate, responsive user interactions without additional
 * API calls. The component excludes currentFilters from useEffect dependencies to prevent unnecessary API
 * calls when search or filter changes occur, optimizing performance for client-side filtering.
 *
 * Dependencies: Requires React hooks for state management, Redux hooks for transfer data access, TabSwitch
 * for navigation, FilterSection for search/filtering UI, OutgoingContent for rendering states, transfer
 * actions for API calls, and filter utilities for client-side data processing.
 *
 * Usage: Rendered as the main page for the outgoing payments section, coordinating between multiple child
 * components and managing overall module state. Includes comprehensive null checks to prevent runtime errors
 * when processing transfer data and ensures graceful handling of undefined or malformed API responses through
 * consistent error handling and fallback mechanisms.
 */

"use client";

import React, { useState, useRef, useEffect } from "react";
import TabSwitch from "@/components/common/tab-switch";
import { TransferTab } from "./types";
import { TRANSFER_TABS } from "./utils/constants";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { openSendMoneyDialog } from "@/redux/features/sendMoneyDialog";
import { useOutgoingFilters } from "./hooks/useOutgoingFilters";
import {
    fetchTransfers,
    fetchScheduledTransfers,
    fetchRecurringTransfers,
    OutgoingRequestParams,
} from "@/redux/actions/transferActions";
import { FilterSection } from "./filters/filter-section";
import { filterOutgoing } from "./utils/filterUtils";
import OutgoingContent from "./list/outgoing-content";

export default function OutgoingPage() {
    // State and hooks
    const [activeTab, setActiveTab] = useState<TransferTab>("instant");
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const amountDropdownRef = useRef<HTMLDivElement>(null);

    // Redux hooks
    const dispatch = useAppDispatch();
    const { transfers } = useAppSelector((state) => state.transfer);
    const { scheduledTransfers } = useAppSelector((state) => state.transfer);
    const { recurringTransfers } = useAppSelector((state) => state.transfer);

    // Filter hooks - now passing activeTab
    const {
        currentFilters,
        tempFilters,
        searchInput,
        dateFilterLabel,
        isFilterOpen,
        isAmountDropdownOpen,
        amountFilterOption,
        tempAmountValues,
        setTempFilters,
        setIsFilterOpen,
        setIsAmountDropdownOpen,
        setAmountFilterOption,
        handleAmountValueChange,
        applyAmountFilter,
        applyCustomDateFilter,
        handleDateFilterSelect,
        onApplyFilter,
        onClearAll,
        onSearch,
    } = useOutgoingFilters(activeTab);

    // Handlers
    const handleSendMoney = () => {
        dispatch(openSendMoneyDialog());
    };

    const handleTabChange = (tab: string) => {
        setActiveTab(tab as TransferTab);
        setCurrentPage(1);
    };

    // Fetch transfers data when component mounts or when tab changes
    useEffect(() => {
        const params: OutgoingRequestParams = {
            pageNo: Math.max(0, currentPage - 1), // API is 0-indexed
            pageSize: itemsPerPage,
            isUserInitiated: false,
        };

        // Never send filter parameters to API since backend doesn't support filtering
        // All filtering is performed client-side for immediate, responsive user experience

        if (activeTab === "instant") {
            dispatch(
                fetchTransfers({
                    ...params,
                    paymentType: "INSTANT",
                })
            );
        } else if (activeTab === "scheduled") {
            dispatch(
                fetchScheduledTransfers({
                    ...params,
                    paymentType: "SCHEDULED",
                })
            );
        } else if (activeTab === "recurring") {
            dispatch(
                fetchRecurringTransfers({
                    ...params,
                    paymentType: "RECURRING",
                })
            );
        }
    }, [
        dispatch,
        activeTab,
        currentPage,
        itemsPerPage,
        // currentFilters is excluded from dependencies because all filtering is client-side
        // This prevents unnecessary API calls when search/filter changes occur
        // eslint-disable-next-line react-hooks/exhaustive-deps
    ]);

    // Get the appropriate transfers data based on the active tab
    const getTransfersData = () => {
        switch (activeTab) {
            case "instant":
                return transfers || [];
            case "scheduled":
                return scheduledTransfers || [];
            case "recurring":
                return recurringTransfers || [];
            default:
                return [];
        }
    };

    // Apply client-side filtering since backend APIs don't support filtering
    // This provides immediate, responsive filtering without additional API calls
    const filteredTransfers =
        filterOutgoing(getTransfersData(), {
            ...(currentFilters || {}),
            search: currentFilters?.search || "",
        }) || [];

    // Check if any filters are currently applied
    const areFiltersApplied = Boolean(
        currentFilters &&
            ((currentFilters.search && currentFilters.search.trim() !== "") ||
                (currentFilters.startDate && currentFilters.startDate !== "") ||
                (currentFilters.endDate && currentFilters.endDate !== "") ||
                (currentFilters.minAmount && currentFilters.minAmount !== "") ||
                (currentFilters.maxAmount && currentFilters.maxAmount !== "") ||
                (currentFilters.amount && currentFilters.amount !== "") ||
                (currentFilters.dateFilterType && currentFilters.dateFilterType !== ""))
    );

    // Determine the current UI state based on loading status, errors, and data
    const renderContent = () => (
        // Always pass filtered transfers and filter status to OutgoingContent
        <OutgoingContent
            activeTab={activeTab}
            filteredTransfers={filteredTransfers}
            areFiltersApplied={areFiltersApplied}
        />
    );
    return (
        <div className="flex flex-col px-8">
            {/* Page header */}
            <div className="flex items-center mb-9">
                <h1 className="text-black [text-edge:cap] [leading-trim:both] text-xl font-semibold leading-[26px] tracking-[0.3px]">
                    Outgoing payments
                </h1>
            </div>

            {/* Main content with consistent spacing */}
            <div className="flex flex-col">
                {/* Tab switch */}
                <TabSwitch
                    tabs={TRANSFER_TABS}
                    activeTab={activeTab}
                    onChange={handleTabChange}
                    tabSpacing="h-[24px]"
                />

                {/* Filter section */}
                <div className="mb-6">
                    <FilterSection
                        searchQuery={searchInput} // Use immediate search input for responsive UI
                        currentFilters={currentFilters}
                        tempFilters={tempFilters}
                        dateFilterLabel={dateFilterLabel}
                        isFilterOpen={isFilterOpen}
                        isAmountDropdownOpen={isAmountDropdownOpen}
                        amountFilterOption={amountFilterOption}
                        tempAmountValues={tempAmountValues}
                        amountDropdownRef={amountDropdownRef}
                        onSearch={onSearch}
                        onApplyFilter={onApplyFilter}
                        onClearAll={onClearAll}
                        handleSendMoney={handleSendMoney}
                        handleDateFilterSelect={handleDateFilterSelect}
                        setTempFilters={setTempFilters}
                        setIsFilterOpen={setIsFilterOpen}
                        setIsAmountDropdownOpen={setIsAmountDropdownOpen}
                        setAmountFilterOption={setAmountFilterOption}
                        handleAmountValueChange={handleAmountValueChange}
                        applyAmountFilter={applyAmountFilter}
                        applyCustomDateFilter={applyCustomDateFilter}
                    />
                </div>

                {/* Content */}
                {renderContent()}
            </div>
        </div>
    );
}
