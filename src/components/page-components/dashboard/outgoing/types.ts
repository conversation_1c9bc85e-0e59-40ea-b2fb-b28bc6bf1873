/**
 * Type definitions for transfers module
 */

// Enum for UI content states
export enum OutgoingContentState {
    LOADING = "loading",
    ERROR = "error",
    EMPTY = "empty",
    FILTERED_EMPTY = "filtered_empty",
    DATA = "data",
}

export type OptionProp = {
    label: string;
    value: string;
};

export interface ITransfer {
    id: string;
    paymentRequestId: number;
    transferScheduledId?: number;
    transactionRef?: string; // Transaction reference from API response
    date?: string | null;
    scheduledDate?: string | null;
    counterparty: string;
    accountNumber: string;
    narration: string;
    status: string;
    amount: number;
    transferType: "Intra-bank" | "Inter-bank" | "International";
    frequency?: string;
    bank?: string;

    // Fields from API for recurring payments
    firstPaymentDate?: string | null;
    lastPaymentDate?: string | null;
    nextPaymentDate?: string | null;
    frequencyType?: string;
    frequencyValue?: number;
    numberOfOccurrence?: number;
    numberOfCompletedOccurrence?: number;
    totalTransfers?: number; // Number of transfers in a bulk transfer, 1 for single transfers
    bulkTransactionRef?: string | null; // Reference ID for bulk transfer operations from API response

    // Original API status field - used for all transfer types
    transferStatus?: string; // Status for all transfers (instant, scheduled, and recurring)

    // For bulk transfers, contains individual recipient transfers
    recipients?: ITransfer[];

    // For instance breakdowns
    instanceRecipients?: ITransfer[]; // Recipients specific to a particular instance
    isInstanceBreakdown?: boolean; // Flag to identify when viewing an instance breakdown
    recipientAmounts?: number[]; // Array of amounts for each recipient in a bulk transfer
}

export interface ITransferFilter {
    search: string;
    startDate: string;
    endDate: string;
    minAmount: string;
    maxAmount: string;
    amount: string;
    dateFilterType: string; // Stores whether the date is a custom date or one of the predefined options
    // Add pagination properties similar to ITransactionFilter
    size?: number | null;
    page?: number | null;
}

// Interface for tab-specific filters
export interface TabSpecificFilters {
    instant: ITransferFilter;
    scheduled: ITransferFilter;
    recurring: ITransferFilter;
}

export type TransferTab = "instant" | "scheduled" | "recurring";

// Table metadata interface for TanStack Table
export interface TableMetaWithActions {
    setIsDetailsOpen: (value: boolean) => void;
    setSelectedTransfer: (transfer: ITransfer) => void;
}

// Define the state structure for the outgoing Redux slice
export interface OutgoingState {
    // Regular outgoing payments
    transfers: ITransfer[];
    transfersLoading: boolean;
    transfersError: string | null;
    transfersTotalElements: number;
    transfersTotalPages: number;
    transfersCurrentPage: number;
    transfersHasNext: boolean;
    transfersHasPrevious: boolean;

    // Scheduled outgoing payments
    scheduledTransfers: ITransfer[];
    scheduledTransfersLoading: boolean;
    scheduledTransfersError: string | null;
    scheduledTransfersTotalElements: number;
    scheduledTransfersTotalPages: number;
    scheduledTransfersCurrentPage: number;
    scheduledTransfersHasNext: boolean;
    scheduledTransfersHasPrevious: boolean;

    // Recurring outgoing payments
    recurringTransfers: ITransfer[];
    recurringTransfersLoading: boolean;
    recurringTransfersError: string | null;
    recurringTransfersTotalElements: number;
    recurringTransfersTotalPages: number;
    recurringTransfersCurrentPage: number;
    recurringTransfersHasNext: boolean;
    recurringTransfersHasPrevious: boolean;

    // Payment approvals
    paymentApprovals: PaymentApprovalItem[];
    paymentApprovalsLoading: boolean;
    paymentApprovalsError: string | null;
    paymentApprovalsTotalElements: number;
    paymentApprovalsTotalPages: number;

    // Update transfer status
    updateTransferStatusLoading: boolean;
    updateTransferStatusError: string | null;

    // Download receipt
    downloadReceiptLoading: boolean;
    downloadReceiptError: string | null;
}

// This interface is needed for the OutgoingState
export interface PaymentApprovalItem {
    id: number;
    paymentRequestId: number;
    userId: number;
    userName: string;
    userRole: string;
    status: "APPROVE" | "DECLINE" | "CREATOR";
}
