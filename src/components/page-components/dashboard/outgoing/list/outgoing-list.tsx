"use client";

import React, { useState, useMemo, use<PERSON><PERSON>back, useEffect } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from "next/navigation";
import { ITransfer, TransferTab } from "../types";
import { Pagination } from "@/components/common/pagination";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { downloadReceipt } from "@/redux/actions/transferActions";
import dynamic from "next/dynamic";
import { sendCatchFeedback } from "@/functions/feedback";
import { DataTable } from "@/components/common/table/DataTable";
import {
    ColumnFiltersState,
    SortingState,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { getOutgoingColumns, TableMetaWithDownload } from "./outgoing-column-data";

// Dynamically import the details component for better performance
const OutgoingDetails = dynamic(() => import("../details/outgoing-details"), {
    ssr: false,
});

// Define the pagination filter interface for URL state
interface ITransferFilter {
    size?: number | null;
    page?: number | null;
}

/**
 * Props interface for the OutgoingList component
 */
interface OutgoingListProps {
    /** List of transfers to display */
    transfers: ITransfer[];
    /** Currently active tab (instant, scheduled, recurring) */
    activeTab?: TransferTab;
    /** Whether the list is in a loading state */
    isLoading?: boolean;
    /** Callback for page change */
    onPageChange?: (page: number) => void;
    /** Callback for items per page change */
    onItemsPerPageChange?: (size: number) => void;
}

/**
 * OutgoingList component displays a table of transfers with sorting, pagination,
 * and actions like viewing details, downloading receipts, etc.
 *
 * Features:
 * - Sortable columns (date and amount)
 * - Bulk selection
 * - Pagination
 * - Transfer details modal
 * - Download receipt functionality
 * - Status badges
 */
export default function OutgoingList({
    transfers,
    activeTab = "instant",
    isLoading = false,
    onPageChange,
    onItemsPerPageChange,
}: Readonly<OutgoingListProps>) {
    // Next.js router hooks
    const params = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const dispatch = useAppDispatch();

    // Local component state
    const [sorting, setSorting] = useState<SortingState>([]);
    const [rowSelection, setRowSelection] = useState({});
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [selectedTransfer, setSelectedTransfer] = useState<ITransfer | null>(null);
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);

    // URL-based pagination state
    const [currentFilters, setCurrentFilters] = useState<ITransferFilter>({
        size: Number(params.get("size")) || 10,
        page: Number(params.get("page")) || 1,
    });

    // Redux state
    const { downloadReceiptLoading } = useAppSelector((state) => state.transfer);
    const {
        transfersTotalElements,
        transfersTotalPages,
        transfersCurrentPage,
        transfersHasNext,
        transfersHasPrevious,
        scheduledTransfersTotalElements,
        scheduledTransfersTotalPages,
        scheduledTransfersCurrentPage,
        scheduledTransfersHasNext,
        scheduledTransfersHasPrevious,
        recurringTransfersTotalElements,
        recurringTransfersTotalPages,
        recurringTransfersCurrentPage,
        recurringTransfersHasNext,
        recurringTransfersHasPrevious,
    } = useAppSelector((state) => state.transfer);

    /**
     * Creates a query string from filter parameters
     */
    const createQueryString = useCallback((filters: ITransferFilter) => {
        const newParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                newParams.set(key, value.toString());
            }
        });
        return newParams.toString();
    }, []);

    /**
     * Updates filters and URL parameters
     */
    const updateFilters = useCallback(
        async (newFilters: ITransferFilter) => {
            // Check if filters are different to prevent unnecessary updates
            if (newFilters.page === currentFilters.page && newFilters.size === currentFilters.size) {
                return;
            }

            setCurrentFilters(newFilters);
            const queryString = createQueryString(newFilters);
            router.push(pathname + (queryString ? "?" + queryString : ""), {
                scroll: false, // Prevent scrolling to top on URL update
            });

            // Call parent callbacks if provided
            if (newFilters.page !== currentFilters.page && onPageChange) {
                onPageChange(newFilters.page ?? 1);
            }

            if (newFilters.size !== currentFilters.size && onItemsPerPageChange) {
                onItemsPerPageChange(newFilters.size ?? 10);
            }
        },
        [createQueryString, pathname, router, currentFilters, onPageChange, onItemsPerPageChange]
    );

    /**
     * Handles page changes in pagination
     */
    async function onChangePage(newPage: number) {
        await updateFilters({
            ...currentFilters,
            page: newPage,
        });
    }

    /**
     * Handles items per page changes in pagination
     */
    async function onChangeRowsPerPage(value: number) {
        await updateFilters({
            ...currentFilters,
            size: value,
            page: 1, // Reset to first page when changing items per page
        });
    }

    // Sync URL with component state when params change
    useEffect(() => {
        const newSize = Number(params.get("size")) || 10;
        const newPage = Number(params.get("page")) || 1;

        if (newSize !== currentFilters.size || newPage !== currentFilters.page) {
            setCurrentFilters({
                size: newSize,
                page: newPage,
            });
        }
    }, [params, currentFilters.page, currentFilters.size]);

    /**
     * Handles closing the transfer details modal
     */
    const handleCloseDetails = useCallback(() => {
        setIsDetailsOpen(false);
    }, []);

    /**
     * Handles row click to open transfer details
     */
    const handleRowClick = useCallback((transfer: ITransfer) => {
        setSelectedTransfer(transfer);
        setIsDetailsOpen(true);
    }, []);

    /**
     * Handles downloading a transfer receipt
     */
    const handleDownloadReceipt = useCallback(
        (transfer: ITransfer) => {
            const transactionId = transfer.transferScheduledId;
            if (!transactionId) {
                sendCatchFeedback(new Error("No valid transaction ID found for this transfer"));
                return;
            }
            dispatch(
                downloadReceipt({
                    transactionId,
                    isUserInitiated: true,
                })
            );
        },
        [dispatch]
    );

    /**
     * Gets the total number of items based on the active tab
     */
    const getTotalItems = useCallback(() => {
        switch (activeTab) {
            case "instant":
                return transfersTotalElements ?? 0;
            case "scheduled":
                return scheduledTransfersTotalElements ?? 0;
            case "recurring":
                return recurringTransfersTotalElements ?? 0;
            default:
                return 0;
        }
    }, [activeTab, transfersTotalElements, scheduledTransfersTotalElements, recurringTransfersTotalElements]);

    /**
     * Gets the total number of pages based on the active tab
     */
    const getTotalPages = useCallback(() => {
        switch (activeTab) {
            case "instant":
                return transfersTotalPages || 1;
            case "scheduled":
                return scheduledTransfersTotalPages || 1;
            case "recurring":
                return recurringTransfersTotalPages || 1;
            default:
                return 1;
        }
    }, [activeTab, transfersTotalPages, scheduledTransfersTotalPages, recurringTransfersTotalPages]);

    /**
     * Gets the current page from the API response
     */
    const getCurrentApiPage = useCallback(() => {
        // The API returns 0-indexed page number but our UI is 1-indexed
        switch (activeTab) {
            case "instant":
                return (transfersCurrentPage ?? -1) + 1;
            case "scheduled":
                return (scheduledTransfersCurrentPage ?? -1) + 1;
            case "recurring":
                return (recurringTransfersCurrentPage ?? -1) + 1;
            default:
                return currentFilters.page ?? 1;
        }
    }, [
        activeTab,
        transfersCurrentPage,
        scheduledTransfersCurrentPage,
        recurringTransfersCurrentPage,
        currentFilters.page,
    ]);

    /**
     * Checks if there is a next page available
     */
    const hasNextPage = useCallback(() => {
        switch (activeTab) {
            case "instant":
                return transfersHasNext ?? getCurrentApiPage() < getTotalPages();
            case "scheduled":
                return scheduledTransfersHasNext ?? getCurrentApiPage() < getTotalPages();
            case "recurring":
                return recurringTransfersHasNext ?? getCurrentApiPage() < getTotalPages();
            default:
                return false;
        }
    }, [
        activeTab,
        transfersHasNext,
        scheduledTransfersHasNext,
        recurringTransfersHasNext,
        getCurrentApiPage,
        getTotalPages,
    ]);

    /**
     * Checks if there is a previous page available
     */
    const hasPreviousPage = useCallback(() => {
        switch (activeTab) {
            case "instant":
                return transfersHasPrevious ?? getCurrentApiPage() > 1;
            case "scheduled":
                return scheduledTransfersHasPrevious ?? getCurrentApiPage() > 1;
            case "recurring":
                return recurringTransfersHasPrevious ?? getCurrentApiPage() > 1;
            default:
                return false;
        }
    }, [
        activeTab,
        transfersHasPrevious,
        scheduledTransfersHasPrevious,
        recurringTransfersHasPrevious,
        getCurrentApiPage,
    ]);

    // Create memoized columns based on the active tab
    const columns = useMemo(() => getOutgoingColumns(activeTab), [activeTab]);

    // Set up the table instance with memoization
    const table = useReactTable({
        data: transfers,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            rowSelection,
            columnFilters,
        },
        meta: {
            setIsDetailsOpen: (value: boolean) => setIsDetailsOpen(value),
            setSelectedTransfer: (transfer: ITransfer) => setSelectedTransfer(transfer),
            handleDownloadReceipt,
            downloadReceiptLoading,
        } as TableMetaWithDownload,
    });

    // Extract empty state messages from EmptyStateComponent based on the active tab
    const getEmptyStateTitle = () => {
        switch (activeTab) {
            case "instant":
                return "No payments yet";
            case "scheduled":
                return "No scheduled payments";
            case "recurring":
                return "No active recurring payments";
            default:
                return "No payments found";
        }
    };

    const getEmptyStateDescription = () => {
        switch (activeTab) {
            case "instant":
                return "Ready for your first payment? Click 'send money' to make your first payment.";
            case "scheduled":
                return "Payments scheduled for future dates will show up here.";
            case "recurring":
                return "Your regular automated payments will show up here.";
            default:
                return "Start by creating a new transfer.";
        }
    };

    // Always render DataTable instead of conditionally showing EmptyStateComponent
    // This ensures table headers are displayed even when there's no data, consistent with all-transactions.tsx
    return (
        <div className="space-y-4 pb-12 flex flex-col items-center w-full">
            <div className="w-full">
                {/* The overflow-x-auto enables horizontal scrolling when table width exceeds viewport */}
                {/* The min-w-[1500px] ensures table maintains column widths on smaller screens */}
                <div className="w-full overflow-x-auto">
                    <div className="min-w-[1500px]">
                        <DataTable
                            table={table}
                            columns={columns}
                            loading={isLoading}
                            emptyTabletitle={getEmptyStateTitle()}
                            emptyTabledescription={getEmptyStateDescription()}
                            onRowClick={handleRowClick}
                        />
                    </div>
                </div>
            </div>

            {isDetailsOpen && selectedTransfer && (
                <OutgoingDetails
                    isOpen={isDetailsOpen}
                    handleCloseDetails={handleCloseDetails}
                    transfer={selectedTransfer}
                    activeTab={activeTab}
                />
            )}

            {transfers.length > 0 && (
                <div className="fixed bottom-0 left-[256px] right-0 pb-6">
                    <Pagination
                        initialPage={currentFilters.page ?? 1}
                        totalItems={getTotalItems()}
                        initialItemsPerPage={currentFilters.size ?? 10}
                        currentItemsPerPage={currentFilters.size ?? 10}
                        onPageChange={onChangePage}
                        onItemsPerPageChange={onChangeRowsPerPage}
                        itemsPerPageOptions={[3, 10, 20, 50, 100]}
                        className="px-8"
                        totalPages={getTotalPages()}
                        hasNext={hasNextPage()}
                        hasPrevious={hasPreviousPage()}
                        currentPage={getCurrentApiPage()}
                    />
                </div>
            )}
        </div>
    );
}
