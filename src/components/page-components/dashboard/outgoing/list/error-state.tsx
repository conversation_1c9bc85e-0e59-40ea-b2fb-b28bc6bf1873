import React from "react";
import { But<PERSON> } from "@/components/common/buttonv3";
import { WarningIcon, ErrorCircleBackgroundIcon } from "@/components/icons/outgoing";
import { useAppDispatch } from "@/redux/hooks";
import { openSupportDialog } from "@/redux/features/supportDialog";

interface ErrorStateProps {
    onRetry: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({ onRetry }) => {
    const dispatch = useAppDispatch();

    const handleGetSupport = () => {
        dispatch(openSupportDialog());
    };

    return (
        <div className="w-full flex justify-center items-center py-16" data-testid="error-state">
            <div className="flex flex-col items-center justify-center text-center">
                <div className="relative mb-4">
                    <ErrorCircleBackgroundIcon className="text-[#F7F7F8] w-[120px] h-[120px] object-contain" />
                    <div className="absolute inset-0 flex items-center justify-center">
                        <WarningIcon className="text-white" />
                    </div>
                </div>
                <div className="flex flex-col items-center justify-start gap-4 w-[346px] mb-6">
                    <h3 className="w-full text-[#151519] text-center text-base font-semibold leading-5 tracking-[0.02em]">
                        Error fetching payments
                    </h3>
                    <p className="w-full text-[#3A3A41] text-center text-base font-normal leading-5 tracking-[0.02em]">
                        We're having trouble fetching your payments at this time. Please try again or check your
                        connection.
                    </p>
                </div>
                <div className="flex flex-row items-center justify-center gap-3">
                    <Button variant="primary" size="medium" onClick={onRetry} data-testid="retry-button">
                        Try again
                    </Button>
                    <Button variant="outline" size="medium" onClick={handleGetSupport} data-testid="support-button">
                        Get support
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ErrorState;
