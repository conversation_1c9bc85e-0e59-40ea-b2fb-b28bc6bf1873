import { ITransfer } from "../types";

/**
 * Interface representing a recipient with status fields
 */
interface IRecipientWithStatus {
    transferStatus?: string;
    [key: string]: unknown; // For other properties that might exist
}

/**
 * Determines the appropriate status value to use for a recipient
 * @param recipient The recipient object containing status values
 * @returns The appropriate status value for the recipient
 */
export const getRecipientStatus = (recipient: IRecipientWithStatus): string | undefined =>
    // Only use transferStatus for all tabs - no difference based on activeTab
    recipient.transferStatus;

/**
 * Helper function to check if a transfer is a bulk transfer based on bulkTransactionRef
 *
 * This function determines bulk vs single transfer classification by examining the bulkTransactionRef
 * property from the API response. According to the business logic:
 * - Single Transfer: When "bulkTransactionRef" is null, the transaction represents a single payment
 * - Bulk Transfer: When "bulkTransactionRef" has a value, the transaction represents a bulk payment
 *
 * @param transfer The transfer object to check
 * @returns Boolean indicating if the transfer is a bulk transfer (bulkTransactionRef has a value)
 * @throws {Error} When transfer parameter is null or undefined
 *
 * @example
 * // Single transfer
 * isBulkTransfer({ bulkTransactionRef: null }) // returns false
 *
 * // Bulk transfer
 * isBulkTransfer({ bulkTransactionRef: "BULK123456" }) // returns true
 *
 * // Missing bulkTransactionRef defaults to single transfer
 * isBulkTransfer({}) // returns false
 */
export const isBulkTransfer = (transfer: ITransfer): boolean => {
    // Input validation: ensure transfer object exists
    if (!transfer) {
        throw new Error("Transfer object is required for bulk transfer detection");
    }

    // Primary logic: Check if bulkTransactionRef has a value
    // - If bulkTransactionRef is null or undefined, it's a single transfer
    // - If bulkTransactionRef has any non-null value, it's a bulk transfer
    const hasBulkRef = transfer.bulkTransactionRef !== null && transfer.bulkTransactionRef !== undefined;

    if (hasBulkRef) {
        // Additional validation: ensure bulkTransactionRef is a non-empty string when provided
        if (typeof transfer.bulkTransactionRef === "string") {
            return transfer.bulkTransactionRef.trim().length > 0;
        }
        // If it's not a string but is non-null/undefined, consider it a bulk transfer
        return true;
    }

    // Fallback logic: If bulkTransactionRef is null/undefined, check totalTransfers as secondary indicator
    // This provides backward compatibility and additional validation
    const totalTransfersCount = transfer.totalTransfers;

    // Additional validation: ensure totalTransfers is a valid number when provided
    if (totalTransfersCount !== undefined && totalTransfersCount !== null) {
        if (
            typeof totalTransfersCount !== "number" ||
            !Number.isInteger(totalTransfersCount) ||
            totalTransfersCount < 0 ||
            isNaN(totalTransfersCount)
        ) {
            return false;
        }
        // If totalTransfers > 1, it might be a bulk transfer even without bulkTransactionRef
        return totalTransfersCount > 1;
    }

    // Default to single transfer if no clear indicators
    return false;
};

/**
 * Helper function to calculate the total amount for a bulk transfer from the recipients array
 * This ensures we get an accurate total by summing all individual recipient amounts
 * @param transfer The transfer object containing recipients
 * @returns The total amount calculated from the recipients array, or the transfer's amount if recipients are not available
 */
export const calculateBulkTransferAmount = (transfer: ITransfer): number => {
    // Use type assertion only where necessary
    const transferWithRecipients = transfer as ITransfer & { recipients?: Array<{ amount: number | string }> };

    // If recipients array exists and has entries, sum their amounts
    if (transferWithRecipients.recipients && transferWithRecipients.recipients.length > 0) {
        return transferWithRecipients.recipients.reduce((sum: number, recipient) => {
            // Convert amount to number if it's a string (handles both formats safely)
            const recipientAmount =
                typeof recipient.amount === "string"
                    ? parseFloat((recipient.amount as string).replace(/,/g, ""))
                    : recipient.amount;

            return sum + (isNaN(recipientAmount) ? 0 : recipientAmount);
        }, 0);
    }

    // Fall back to the transfer amount if no recipients array is available
    // Convert transfer amount to number if it's a string
    return typeof transfer.amount === "string"
        ? parseFloat((transfer.amount as string).replace(/,/g, ""))
        : transfer.amount;
};
