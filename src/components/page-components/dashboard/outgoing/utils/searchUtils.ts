/**
 * Search Utilities - Enhanced Text Matching for Outgoing Payments
 *
 * Purpose: This file provides comprehensive search functionality for outgoing payment transactions.
 * It performs case-insensitive text matching across all relevant columns including the complete
 * counterparty information (both main name and secondary details like bank and account number).
 *
 * Functionality:
 * - Real-time search across text, number, and date columns
 * - Enhanced counterparty search including bank name, account number, and bulk transfer details
 * - Case-insensitive search with substring matching
 * - Null-safe operations with comprehensive error handling
 * - Supports both single transfers (bank + account) and bulk transfers (recipient count)
 *
 * Dependencies:
 * - ITransfer interface from outgoing module types
 *
 * Usage: Called by filterUtils.ts to perform client-side search filtering across all transfer
 * data fields. The enhanced search now includes the complete counterparty column information
 * as displayed in the UI, ensuring users can search by any visible text in the table.
 */

import { ITransfer } from "../types";
import { isBulkTransfer } from "./statusUtils";

/**
 * Format date for simple search matching
 * @param dateString Date string to format
 * @returns Formatted date string for searching
 */
const formatDateForSearch = (dateString: string | null): string => {
    if (!dateString) return "";

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;

        // Format date in multiple common formats for search
        const formatted = date.toLocaleDateString(); // MM/DD/YYYY
        const monthName = date.toLocaleDateString("en-US", { month: "long" }); // January
        const shortMonthName = date.toLocaleDateString("en-US", { month: "short" }); // Jan
        const year = date.getFullYear().toString();
        const dayMonth = date.toLocaleDateString("en-US", { day: "numeric", month: "long" }); // 1 January

        return `${formatted} ${monthName} ${shortMonthName} ${year} ${dayMonth}`;
    } catch {
        return dateString || "";
    }
};

/**
 * Format the second line of counterparty column for search matching
 * This generates the exact text displayed in the UI's counterparty column second line
 * @param transfer Transfer object to format
 * @returns Formatted second line text for searching
 */
const formatCounterpartySecondLine = (transfer: ITransfer): string => {
    if (!isBulkTransfer(transfer)) {
        // Single transfer: "BankName, AccountNumber" format
        const bank = transfer.bank ?? "";
        const accountNumber = transfer.accountNumber ?? "";
        return `${bank}, ${accountNumber}`;
    } else {
        // Bulk transfer: "X recipients" format
        return `${transfer.totalTransfers} recipients`;
    }
};

/**
 * Check if a transfer matches the search query using enhanced text matching
 * Now searches across all visible fields including the complete counterparty information:
 * - Main counterparty name
 * - Bank name (from 2nd line)
 * - Account number (from 2nd line)
 * - Bulk transfer recipient count text (from 2nd line)
 * - Narration, status, amount, and formatted date
 *
 * @param transfer Transfer object to check
 * @param query Search query string
 * @returns Boolean indicating if the transfer matches the query
 */
export const advancedSearchMatch = (transfer: ITransfer, query: string): boolean => {
    if (!transfer || !query || query.trim() === "") return true;

    const searchTerm = query.toLowerCase().trim();

    // Enhanced searchable fields including complete counterparty information
    const searchableFields = [
        // Original fields
        (transfer.counterparty || "").toLowerCase(),
        (transfer.narration || "").toLowerCase(),
        (transfer.status || "").toLowerCase(),
        (transfer.amount?.toString() || "").toLowerCase(),
        formatDateForSearch(transfer.date ?? null).toLowerCase(),
        // NEW: Enhanced counterparty 2nd line fields for comprehensive search
        (transfer.bank ?? "").toLowerCase(), // Bank name from 2nd line
        (transfer.accountNumber ?? "").toLowerCase(), // Account number from 2nd line
        formatCounterpartySecondLine(transfer).toLowerCase(), // Complete 2nd line text
    ];

    // Check if any field contains the search term as a substring (case-insensitive)
    return searchableFields.some((field) => field.includes(searchTerm));
};
