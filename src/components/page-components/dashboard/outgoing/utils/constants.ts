// Constants for transfer tabs and filter values
import { ITransferFilter, TransferTab } from "../types";

// Default empty filter state
export const emptyFilterState: ITransferFilter = {
    search: "",
    startDate: "",
    endDate: "",
    minAmount: "",
    maxAmount: "",
    amount: "",
    dateFilterType: "",
};

interface TabItem {
    id: TransferTab;
    label: string;
}

export const TRANSFER_TABS: TabItem[] = [
    {
        id: "instant",
        label: "Instant",
    },
    {
        id: "scheduled",
        label: "Scheduled",
    },
    {
        id: "recurring",
        label: "Recurring",
    },
];

export const dateFilterValues = [
    {
        label: "Last 30 days",
        value: "Last 30 days",
    },
    {
        label: "Last 3 months",
        value: "Last 3 months",
    },
    {
        label: "Last 6 months",
        value: "Last 6 months",
    },
    {
        label: "Custom date",
        value: "Custom date",
    },
];

export const amountFilterValue = [
    {
        label: "Is exactly",
        value: "Is exactly",
    },
    {
        label: "Is between",
        value: "Is between",
    },
    {
        label: "Is less than",
        value: "Is less than",
    },
    {
        label: "Is greater than",
        value: "Is greater than",
    },
];
