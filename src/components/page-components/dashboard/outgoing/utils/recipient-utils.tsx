import React from "react";
import { ITransfer } from "../types";
import { But<PERSON> } from "@/components/common/buttonv3";
import Badge from "@/components/common/badge";
import { useAppDispatch } from "@/redux/hooks";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { getStatusMapping } from "@/utils/status-mapping";

/**
 * Utility functions for handling recipient data in the Recipients tab
 * Used for bulk transfer recipient display with approval flow component
 */

/**
 * Get the display name for a recipient
 */
export function getRecipientName(recipient: ITransfer): string {
    return recipient.counterparty || "Unknown Recipient";
}

/**
 * Prepare recipient data from transfer object
 * For bulk transfers, this returns the recipients array
 * For single transfers, this returns the transfer as a single-item array
 */
export function prepareRecipientData(transfer: ITransfer): ITransfer[] {
    // For bulk transfers, return the recipients array if it exists
    if (transfer.recipients && transfer.recipients.length > 0) {
        return transfer.recipients;
    }

    // For single transfers, return the transfer itself as a single-item array
    return [transfer];
}

/**
 * Get visibility flags for status badges and action buttons
 */
export function getVisibilityFlags(
    transfer: ITransfer,
    activeTab: string,
    showStatusBadges?: boolean,
    showActionButtons?: boolean
) {
    return {
        hideBadges: showStatusBadges === false,
        hideButtons: showActionButtons === false,
    };
}

/**
 * Map recipients to approval flow format
 * This transforms recipient data into the format expected by the ApprovalFlow component
 */
export function mapRecipientsToApprovalFlow({
    recipients,
    transfer,
    activeTab,
    hideBadges,
    hideButtons,
    dispatch,
}: {
    recipients: ITransfer[];
    transfer: ITransfer;
    activeTab: string;
    hideBadges: boolean;
    hideButtons: boolean;
    dispatch: ReturnType<typeof useAppDispatch>;
    onRetry?: (recipientId: number | string) => void;
    onSendNow?: (recipientId: number | string) => void;
}) {
    return recipients.map((recipient, index) => ({
        id: recipient.id || `recipient-${index}`,
        name: getRecipientName(recipient),
        role: `${recipient.bank ?? "Bank"} • ${recipient.accountNumber || "Account"}`,
        approvalLevel: index + 1,
        status: recipient.status,
        amount: recipient.amount,
        narration: recipient.narration,
        // Custom properties for recipient display
        isRecipient: true,
        transferStatus: recipient.status,
        transferNarration: recipient.narration,
        bankName: recipient.bank,
        destinationAccount: recipient.accountNumber,
        destinationAccountName: recipient.counterparty,
    }));
}

/**
 * Get status badge component for a recipient
 * Uses centralized status mapping for consistency
 */
export function getRecipientStatusBadge(status: string): React.ReactNode {
    // Use centralized status mapping for consistency
    const statusMapping = getStatusMapping(status);

    return <Badge text={statusMapping.text} color={statusMapping.color} size="sm" />;
}

/**
 * Get action button for a recipient based on status
 * For bulk transfers, only show download receipt for successful transfers
 */
export function getRecipientActionButton(
    recipient: ITransfer,
    dispatch: ReturnType<typeof useAppDispatch>
): React.ReactNode {
    const { status, id, transferScheduledId } = recipient;

    const handleDownloadReceipt = async () => {
        try {
            // Use transferScheduledId if available, otherwise use id
            const transactionId = transferScheduledId ?? id;
            await dispatch(
                downloadReceipt({
                    transactionId,
                    isUserInitiated: true,
                })
            );
        } catch {
            //do nothing
        }
    };

    // Only show download receipt button for successful transfers
    if (status === "Successful") {
        return (
            <Button variant="outline" size="sm" onClick={handleDownloadReceipt}>
                Download receipt
            </Button>
        );
    }

    // Return null for all other statuses
    return null;
}

/**
 * Format amount for display
 */
export function formatRecipientAmount(amount: number): string {
    return new Intl.NumberFormat("en-NG", {
        style: "currency",
        currency: "NGN",
        minimumFractionDigits: 2,
    }).format(amount);
}
