"use client";

import React, { useState } from "react";
import SideDrawer from "@/components/common/drawer";
import { ITransfer } from "../types";
import { HeaderSection } from "./header-section";
import { TabSection } from "./tab-section";
import { FooterSection } from "./footer";
import ApprovalAlert from "./approval-alert";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
    updateTransferStatus,
    fetchTransfers,
    fetchScheduledTransfers,
    fetchRecurringTransfers,
} from "@/redux/actions/transferActions";
import { useApprovalRulePeopleInvolved } from "@/hooks/useApprovalRulePeopleInvolved";
import { sendCatchFeedback } from "@/functions/feedback";

interface OutgoingDetailsProps {
    transfer: ITransfer;
    isOpen: boolean;
    handleCloseDetails: () => void;
    activeTab?: string;
}

/**
 * Helper function to get payment type based on active tab
 * @param tab The active tab
 * @returns The corresponding payment type
 */
const getPaymentType = (tab: string): "INSTANT" | "SCHEDULED" | "RECURRING" => {
    if (tab === "instant") return "INSTANT";
    if (tab === "scheduled") return "SCHEDULED";
    return "RECURRING";
};

/**
 * Main component for displaying outgoing payment details in a side drawer
 *
 * This component renders different UI based on the payment type, status, and active tab:
 * - Shows different tabs (Details, People involved, Instances for recurring)
 * - Conditionally renders footer actions based on payment status
 * - Shows appropriate alerts based on payment status
 *
 * Features:
 * - Responsive layout with proper spacing
 * - Follows accessibility guidelines with proper ARIA attributes
 * - Uses semantic HTML elements for better structure
 * - Conditionally renders UI elements based on payment status
 *
 * @param handleCloseDetails - Function to close the side drawer
 * @param isOpen - Boolean to control the visibility of the side drawer
 * @param transfer - The transfer object containing payment details
 * @param activeTab - The active tab in the outgoing page (instant, scheduled, recurring)
 */
export function OutgoingDetails({
    handleCloseDetails,
    isOpen,
    transfer,
    activeTab = "instant",
}: Readonly<OutgoingDetailsProps>) {
    // Redux hooks
    const dispatch = useAppDispatch();
    const { updateTransferStatusLoading } = useAppSelector((state) => state.transfer);

    // Add state to track the active detail tab
    const [activeDetailTab, setActiveDetailTab] = useState("Details");

    // Function to handle status update
    const handleStatusUpdate = (newStatus: "CANCELLED") => {
        // Use the transferScheduledId directly, even if it's 0
        const transferId = transfer.transferScheduledId ?? transfer.paymentRequestId;

        // Validate that we have a valid ID - only reject if it's null or undefined
        if (transferId === null || transferId === undefined) {
            sendCatchFeedback(new Error("Cannot modify this transfer: Missing transfer ID"));
            return;
        }

        // Dispatch the updateTransferStatus action
        dispatch(
            updateTransferStatus({
                transferScheduledId: transferId,
                transferScheduleStatus: newStatus,
                paymentType: getPaymentType(activeTab),
                isUserInitiated: true,
            })
        );

        // Close the drawer after a short delay to show feedback
        setTimeout(() => {
            handleCloseDetails();
        }, 1000);

        // Refresh the transfer data after status update
        setTimeout(() => {
            const params = {
                pageNo: 0,
                pageSize: 10,
            };

            if (activeTab === "instant") {
                dispatch(
                    fetchTransfers({
                        ...params,
                        paymentType: "INSTANT",
                    })
                );
            } else if (activeTab === "scheduled") {
                dispatch(
                    fetchScheduledTransfers({
                        ...params,
                        paymentType: "SCHEDULED",
                    })
                );
            } else if (activeTab === "recurring") {
                dispatch(
                    fetchRecurringTransfers({
                        ...params,
                        paymentType: "RECURRING",
                    })
                );
            }
        }, 1500);
    };

    // Function to handle tab changes
    const handleTabChange = (tab: string) => {
        setActiveDetailTab(tab);
    };

    // Use bulkTransactionRef if available, otherwise use transactionRef (per API documentation)
    const reference = transfer?.bulkTransactionRef ?? transfer?.transactionRef;

    // Get people-involved data using the custom hook with deduplication
    const {
        data: peopleInvolvedData,
        loading: peopleLoading,
        error: peopleError,
    } = useApprovalRulePeopleInvolved({
        amount: transfer?.amount,
        type: "TRANSFER",
        reference,
        enabled: !!(transfer?.amount && reference), // Only enable if we have required data
    });

    // Calculate approval counts from people-involved endpoint data
    const approvedCount = peopleInvolvedData
        ? peopleInvolvedData.filter((person) => person.status === "APPROVE").length
        : 0;
    const totalApprovals = peopleInvolvedData ? peopleInvolvedData.length : 0;
    const hasApprovers = totalApprovals > 0;

    // Updated approval alert display logic based on new requirements:
    // 1. Show in Details and Instances tabs only
    // 2. Show for both single and bulk transfers always
    // 3. Only show when status is "Pending" or "Awaiting Approval"
    // 4. Only show when total approvers from /people-involved endpoint > 0
    // 5. Don't show while loading or if there's an error
    const showApprovalAlert =
        (transfer.status === "Pending" || transfer.status === "Awaiting Approval") &&
        (activeDetailTab === "Details" || activeDetailTab === "Instances") &&
        hasApprovers &&
        !peopleLoading &&
        !peopleError;

    // Function to handle "See approvers" action
    const handleSeeApprovers = () => {
        handleTabChange("People involved");
    };

    return (
        <div>
            <SideDrawer isOpen={isOpen} className="w-[550px] !max-w-[550px]" aria-label="Outgoing payment details">
                <div className="flex flex-col h-full">
                    {/* Header section - fixed at the top */}
                    <HeaderSection transfer={transfer} handleCloseDetails={handleCloseDetails} />

                    {/* Main content area - scrollable */}
                    <div className="flex flex-col h-[calc(100vh-137px)] overflow-hidden">
                        <TabSection
                            transfer={transfer}
                            activeTab={activeTab}
                            activeDetailTab={activeDetailTab}
                            handleTabChange={handleTabChange}
                        />

                        {/* Approval status alert - shown at the bottom of the content area */}
                        {showApprovalAlert && (
                            <div className="px-[24px] mt-auto mb-5">
                                <ApprovalAlert
                                    approvalCount={approvedCount}
                                    totalApprovals={totalApprovals}
                                    onSeeApprovers={handleSeeApprovers}
                                />
                            </div>
                        )}
                    </div>

                    {/* Footer section - fixed at the bottom */}
                    {transfer.status !== "Unknown" && (
                        <FooterSection
                            transfer={transfer}
                            activeTab={activeTab}
                            activeDetailTab={activeDetailTab}
                            handleStatusUpdate={handleStatusUpdate}
                            isUpdatingStatus={updateTransferStatusLoading}
                        />
                    )}
                </div>
            </SideDrawer>
        </div>
    );
}

export default OutgoingDetails;
