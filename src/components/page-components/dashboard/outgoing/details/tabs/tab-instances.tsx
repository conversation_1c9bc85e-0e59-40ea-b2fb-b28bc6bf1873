"use client";

import { ITransfer } from "../../types";
import { isBulkTransfer } from "../../utils/statusUtils";

interface TransferInstancesTabProps {
    transfer: ITransfer;
}

/**
 * Component that displays the instances tab content for a recurring outgoing payment
 * Since there's no API data for instances, always displays "No instances to display" message
 *
 * @param transfer - The transfer object containing payment details
 */
export default function TransferInstancesTab({ transfer }: Readonly<TransferInstancesTabProps>) {
    // Check if this is a bulk transfer
    const isBulk = isBulkTransfer(transfer);

    // For bulk transfers, use specialized rendering functions
    if (isBulk) {
        return renderBulkTransfer();
    }

    return renderSingleTransfer();
}

/**
 * Renders UI for bulk transfers based on transfer status
 */
function renderBulkTransfer() {
    // Since there's no API data for instances, always show the no instances message
    return (
        <div className="flex flex-col justify-center">
            <p className="text-[#3A3A41] text-[14px] font-normal">No instances to display.</p>
        </div>
    );
}

/**
 * Renders UI for single transfers based on transfer status
 */
function renderSingleTransfer() {
    // Since there's no API data for instances, always show the no instances message
    return (
        <div className="flex flex-col h-full overflow-auto" role="tabpanel" aria-label="Payment instances">
            <div className="flex flex-col justify-center">
                <p className="text-[#3A3A41] text-[14px] font-normal">No instances to display.</p>
            </div>
        </div>
    );
}
