import React, { useState } from "react";
import { ITransfer } from "../../types";
import SearchInput from "@/components/common/search-input";
import ApprovalFlow from "@/components/common/approval-flow";
import { useAppDispatch } from "@/redux/hooks";
import { Avatar, AvatarFallback } from "@/components/common/Avatar";
import LoadingIndicator from "@/components/common/loading-indicator";
import { downloadReceipt } from "@/redux/actions/transferActions";

import {
    prepareRecipientData,
    getVisibilityFlags,
    getRecipientName,
    getRecipientStatusBadge,
    formatRecipientAmount,
} from "../../utils/recipient-utils";

interface OutgoingRecipientsTabProps {
    transfer: ITransfer;
    activeTab?: string;
    showStatusBadges?: boolean;
    showActionButtons?: boolean;
    isLoading?: boolean;
    error?: string | null;
}

/**
 * Recipients tab for bulk transfers in the outgoing details drawer
 *
 * This component displays a list of all recipients included in a bulk payment.
 * It shows recipient details including name and amount.
 * For successful transfers, it includes a "Download receipt" button for each recipient.
 *
 * The component uses the enhanced ApprovalFlow component with custom rendering
 * to display recipients in the required format.
 *
 * @param transfer - The transfer object containing payment details and recipients
 * @param activeTab - The currently active tab (instant, scheduled, recurring)
 * @param showStatusBadges - Optional flag to control visibility of status badges
 * @param showActionButtons - Optional flag to control visibility of action buttons
 */
export default function OutgoingRecipientsTab({
    transfer,
    activeTab = "instant",
    showStatusBadges = true,
    showActionButtons = true,
    isLoading = false,
    error = null,
}: Readonly<OutgoingRecipientsTabProps>) {
    const [searchQuery, setSearchQuery] = useState("");
    const dispatch = useAppDispatch();

    // Early return for loading state
    if (isLoading) {
        return (
            <div className="flex flex-col h-full items-center justify-center">
                <LoadingIndicator size={32} />
                <p className="text-sm text-subText mt-4">Loading recipients...</p>
            </div>
        );
    }

    // Early return for error state
    if (error) {
        return (
            <div className="flex flex-col h-full items-center justify-center">
                <div className="text-center">
                    <p className="text-sm text-red-600 mb-2">Error loading recipients</p>
                    <p className="text-xs text-subText">{error}</p>
                </div>
            </div>
        );
    }

    // Validate transfer object
    if (!transfer) {
        return (
            <div className="flex flex-col h-full items-center justify-center">
                <p className="text-sm text-subText">No transfer data available</p>
            </div>
        );
    }

    // Get visibility flags for badges and buttons
    const { hideBadges, hideButtons } = getVisibilityFlags(transfer, activeTab, showStatusBadges, showActionButtons);

    // Prepare recipient data from transfer data or fallbacks
    let allRecipients: ITransfer[] = [];
    try {
        allRecipients = prepareRecipientData(transfer);
    } catch {
        return (
            <div className="flex flex-col h-full items-center justify-center">
                <p className="text-sm text-red-600">Error processing recipient data</p>
            </div>
        );
    }

    // Handle empty recipients case
    if (!allRecipients || allRecipients.length === 0) {
        return (
            <div className="flex flex-col h-full items-center justify-center">
                <p className="text-sm text-subText">No recipients found for this transfer</p>
            </div>
        );
    }

    // Filter recipients based on search query with error handling
    const filteredRecipients = allRecipients.filter((recipient) => {
        const name = getRecipientName(recipient) ?? "";
        return name.toLowerCase().includes(searchQuery.toLowerCase());
    });

    // Custom renderer for each recipient item
    const renderRecipientItem = (recipient: ITransfer, index: number) => {
        // Validate recipient data
        if (!recipient) {
            return null;
        }

        const recipientName = getRecipientName(recipient);
        if (!recipientName) {
            return null;
        }

        const initials = recipientName
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase();

        return (
            <div className="relative flex flex-row items-start gap-3 text-[#3A3A41]">
                {/* Avatar Column */}
                <div className="flex flex-col items-center w-10 flex-shrink-0">
                    <Avatar
                        className="flex h-10 w-10 flex-col justify-center items-center rounded-full"
                        style={{ backgroundColor: "#F9F5FF" }}
                    >
                        <AvatarFallback
                            className="text-[#3A3A41] text-base font-medium leading-5 tracking-[0.32px] text-center uppercase"
                            style={{ backgroundColor: "#F9F5FF" }}
                        >
                            {initials}
                        </AvatarFallback>
                    </Avatar>
                </div>

                {/* Content - Aligned with Avatar */}
                <div className="flex flex-col justify-between gap-1 flex-1 min-w-0">
                    {/* Top row: Name with Status Badge and Amount */}
                    <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                            <h3 className="text-[#151519] text-sm font-medium leading-[18px] tracking-[0.28px] capitalize">
                                {recipientName}
                            </h3>
                            {!hideBadges && (
                                <div className="flex-shrink-0">{getRecipientStatusBadge(recipient.status)}</div>
                            )}
                        </div>
                        {/* Amount positioned at top right */}
                        <div className="text-[#151519] text-sm font-semibold leading-[18px] tracking-[0.28px] flex-shrink-0">
                            {formatRecipientAmount(recipient.amount)}
                        </div>
                    </div>

                    {/* Second row: Narration and Download receipt link */}
                    <div className="flex items-center justify-between">
                        <p className="text-[#6B6B70] text-sm font-normal leading-[18px] tracking-[0.28px]">
                            {recipient.narration || "-"}
                        </p>
                        {/* Download receipt link - positioned at bottom right */}
                        {!hideButtons && recipient.status === "Successful" && (
                            <button
                                onClick={async () => {
                                    try {
                                        const transactionId = recipient.transferScheduledId ?? 0;
                                        await dispatch(
                                            downloadReceipt({
                                                transactionId,
                                                isUserInitiated: true,
                                            })
                                        );
                                    } catch {
                                        //do nothing
                                    }
                                }}
                                className="text-[#6B1BB3] text-sm font-normal leading-[18px] tracking-[0.28px] hover:underline bg-transparent border-none cursor-pointer"
                            >
                                Download receipt
                            </button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="flex flex-col h-full">
            {/* Search input */}
            <div className="mb-6 sm:mb-8">
                <SearchInput
                    placeholder="Search recipient"
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                    size="sm"
                />
            </div>

            {/* Handle empty search results */}
            {searchQuery && filteredRecipients.length === 0 ? (
                <div className="flex flex-col h-full items-center justify-center">
                    <p className="text-sm text-subText">No recipients found matching "{searchQuery}"</p>
                </div>
            ) : (
                /* Recipients list using ApprovalFlow with custom data and renderer */
                <div className="flex-grow overflow-y-auto max-h-[calc(100vh-300px)] sm:max-h-[calc(100vh-350px)] pb-4 sm:pb-6">
                    <ApprovalFlow
                        amount={transfer?.amount || 0}
                        avatarBackgroundColor="#F9F5FF"
                        showConnectorLines={false} // Remove connector lines as required
                        approverSpacing="mb-6 sm:mb-8" // 24px spacing on mobile, 32px on desktop
                        customData={filteredRecipients} // Pass custom recipient data
                        renderCustomItem={renderRecipientItem} // Custom renderer for recipients
                        shouldFetchAPI={false} // Don't fetch API data since we have custom data
                    />
                </div>
            )}
        </div>
    );
}
