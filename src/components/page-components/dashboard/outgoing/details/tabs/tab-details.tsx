import { formatNumberToNaira } from "@/functions/stringManipulations";
import { ITransfer } from "../../types";
import { formatDateLong, formatDate } from "@/functions/date";
import { TransactionId } from "@/components/common/transaction-id";
import { isBulkTransfer, calculateBulkTransferAmount } from "../../utils/statusUtils";

type OutgoingDetailsTabProps = {
    transfer: ITransfer;
    activeTab?: string;
    onSeeApprovers?: () => void;
};

/**
 * Helper function to format the recurring payment schedule
 * @param date The firstPaymentDate from the API in format "2025-03-28"
 * @returns Formatted string for the "On" field (e.g., "Mondays, 9:00am")
 */
const formatRecurringSchedule = (date: string | null | undefined, frequencyType: string | undefined): string => {
    if (!date) return "-";

    try {
        // Parse the date in format "2025-03-28"
        const paymentDate = new Date(date);
        if (isNaN(paymentDate.getTime())) {
            return "-";
        }

        const dayOfWeek = new Intl.DateTimeFormat("en-US", { weekday: "long" }).format(paymentDate);
        const time = paymentDate.toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: true });

        // For weekly frequency, show the day of week
        if (frequencyType?.toUpperCase() === "WEEKLY") {
            return `${dayOfWeek}s, ${time.toLowerCase()}`;
        }

        // For monthly frequency, show the day of month
        if (frequencyType?.toUpperCase() === "MONTHLY") {
            const dayOfMonth = paymentDate.getDate();
            const suffix = getDaySuffix(dayOfMonth);
            return `${dayOfMonth}${suffix} of each month, ${time.toLowerCase()}`;
        }

        // Default format for other frequencies
        return `${time.toLowerCase()}`;
    } catch {
        return "-";
    }
};

/**
 * Helper function to get the suffix for a day number
 */
const getDaySuffix = (day: number): string => {
    if (day > 3 && day < 21) return "th";
    switch (day % 10) {
        case 1:
            return "st";
        case 2:
            return "nd";
        case 3:
            return "rd";
        default:
            return "th";
    }
};

/**
 * Component that displays the details tab content for an outgoing payment
 * Shows information like counterparty, amount, dates, account details, etc.
 * Handles both single transfers and bulk transfers with different UI variations
 */
export default function OutgoingDetailsTab({ transfer, activeTab = "instant" }: Readonly<OutgoingDetailsTabProps>) {
    // Extract properties from transfer that should be displayed in the details tab
    const {
        id,
        date,
        counterparty,
        accountNumber,
        narration,
        amount,
        bank,
        frequency,
        firstPaymentDate,
        lastPaymentDate,
        frequencyType,
        totalTransfers,
        nextPaymentDate,
    } = transfer;

    // Check if this is a bulk transfer
    const isBulk = isBulkTransfer(transfer);

    // Calculate accurate amount for bulk transfers
    const displayAmount = isBulk ? calculateBulkTransferAmount(transfer) : amount;

    // For all bulk transfers, show the 5 fields in the image (Counterparty, Total amount, Date, Transaction reference, Narration)
    if (isBulk) {
        // Create an object with the details to show for bulk transfers
        const bulkDetailsToShow = {
            // For bulk transfers, counterparty shows the recipient count
            Counterparty: `${totalTransfers} recipients`,
            // Using the calculated amount for accuracy
            "Total amount": formatNumberToNaira(displayAmount),
            // Using date which is mapped from createdAt in the API with custom formatting
            Date: date ? formatDateLong(date) : "-",
            // Using id which is mapped from transactionRef in the API
            "Transaction reference": id || "-",
            // Always show narration for bulk transfers as per the design
            Narration: narration || "-",
        };

        // Convert the details object to an array for rendering
        const bulkTransferDetailsList = Object.entries(bulkDetailsToShow).map(([item, detail]) => ({
            item,
            detail,
            isTransactionReference: item === "Transaction reference",
        }));

        return (
            <div className="flex flex-col h-full">
                <div className="flex flex-col gap-[24px]">
                    {bulkTransferDetailsList.map(({ item, detail, isTransactionReference }) => (
                        <div key={item} className="flex items-start justify-between">
                            <p className="text-[#3A3A41] font-normal text-[14px] leading-[18px]">{item}</p>
                            <div className="flex items-center gap-[6px] text-right max-w-[60%]">
                                {isTransactionReference ? (
                                    <TransactionId
                                        id={detail}
                                        textSize={14}
                                        feedbackMessage="Transaction reference copied to clipboard"
                                    />
                                ) : (
                                    <p
                                        className={`font-medium text-[14px] leading-[18px] break-words text-right ${item === "Counterparty" ? "text-[#5C068C]" : "text-[#1d1d1f]"}`}
                                    >
                                        {detail}
                                    </p>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    // Original implementation for single transfers or non-pending bulk transfers
    // Create an object with the details to show in the specified order
    const detailsToShow = {
        // Using counterparty which is mapped from destinationAccountName in the API
        Counterparty: counterparty || "-",
        // Using amount which is mapped from totalAmount in the API, with the calculated value for bulk transfers
        [isBulk ? "Total amount" : "Amount"]: formatNumberToNaira(displayAmount),
        // Using date which is mapped from createdAt in the API with custom formatting
        [isBulk ? "Date" : "Date created"]: date ? formatDateLong(date) : "-",
        // Show frequency field only for recurring transfers
        ...(activeTab === "recurring"
            ? {
                  Frequency: frequency ?? frequencyType ?? "-",
              }
            : {}),
        // Show "On" field only for recurring transfers - using firstPaymentDate from API
        ...(activeTab === "recurring"
            ? {
                  On: formatRecurringSchedule(firstPaymentDate, frequencyType),
              }
            : {}),
        // Show "Ends" field only for recurring transfers - using lastPaymentDate from API
        ...(activeTab === "recurring"
            ? {
                  Ends: lastPaymentDate ? formatDate(lastPaymentDate) : "-",
              }
            : {}),
        // Show "Scheduled for" field only for scheduled transfers with real date or hyphen
        ...(activeTab === "scheduled"
            ? { "Scheduled for": nextPaymentDate ? formatDateLong(nextPaymentDate) : "-" }
            : {}),
        // Using bank which is from the API
        Bank: bank ?? "-",
        // Using accountNumber which is mapped from destinationAccount in the API
        "Account number": accountNumber || "-",
        // Using id which is mapped from transactionRef in the API
        "Transaction reference": id || "-",
        // Using narration which is mapped from transferNarration in the API
        Narration: narration || "-",
    };

    // Convert the details object to an array for rendering
    const transferDetailsList = Object.entries(detailsToShow).map(([item, detail]) => ({
        item,
        detail,
        isTransactionReference: item === "Transaction reference",
    }));

    return (
        <div className="flex flex-col h-full">
            <div className="flex flex-col gap-[24px]">
                {transferDetailsList.map(({ item, detail, isTransactionReference }) => (
                    <div key={item} className="flex items-start justify-between">
                        <p className="text-[#3A3A41] font-normal text-[14px] leading-[18px]">{item}</p>
                        <div className="flex items-center gap-[6px] text-right max-w-[60%]">
                            {isTransactionReference ? (
                                <TransactionId
                                    id={detail}
                                    textSize={14}
                                    feedbackMessage="Transaction reference copied to clipboard"
                                />
                            ) : (
                                <p className="text-[#1d1d1f] font-medium text-[14px] leading-[18px] break-words text-right">
                                    {detail}
                                </p>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
