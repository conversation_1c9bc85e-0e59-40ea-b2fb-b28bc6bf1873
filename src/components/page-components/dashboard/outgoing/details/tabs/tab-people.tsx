/**
 * Purpose: Displays the people involved in a transfer approval process within the outgoing payments module.
 *
 * Functionality: This component renders the approval workflow for a specific transfer by utilizing both the common
 * approval flow component and the updated ApproverListItem component. It integrates with the /v1/approval-rule/people-involved
 * endpoint using the transfer amount, "TRANSFER" type, and transactionRef as reference parameter to determine the required approvers.
 * The component handles different transfer statuses and tab contexts (instant, scheduled, recurring) with appropriate
 * visual styling and connector line colors.
 *
 * Dependencies: React hooks, common ApprovalFlow component, ApproverListItem component, approval data utils,
 * and callback functions for custom connector line styling.
 *
 * Usage: Used within the tab section of transfer details to show the approval workflow. Receives transfer
 * data and active tab context, passes the transfer amount, type, and transactionRef as reference to the ApprovalFlow component which handles
 * API calls and displays the appropriate approvers based on approval rules using the ApproverListItem component.
 */

import { useCallback, useEffect, useState } from "react";
import ApprovalFlow from "@/components/common/approval-flow";
import ApproverListItem from "@/components/common/approver-list-item";
import { useApprovalRulePeopleInvolved } from "@/hooks/useApprovalRulePeopleInvolved";
import { ApprovalData, sortApproversByLevel } from "@/utils/approval-data-utils";
import { ITransfer } from "../../types";

interface TransferPeopleTabProps {
    transfer?: ITransfer;
    activeTab?: string;
}

// Custom render function for ApprovalFlow to use ApproverListItem
const renderApproverItem = (approver: ApprovalData, index: number, transferStatus?: string, totalItems?: number) => {
    const isLast = totalItems ? index === totalItems - 1 : false;

    // Map transfer status to approver component props
    const getTransferStatusForApprover = () => {
        if (transferStatus === "Unknown") return "cancelled";
        if (transferStatus === "Successful") return "completed";
        return "pending";
    };

    return (
        <ApproverListItem
            key={`${approver.id}-${index}`}
            approver={approver}
            isLast={isLast}
            transferStatus={getTransferStatusForApprover()}
            spacing="mb-1"
        />
    );
};

export default function TransferPeopleTab({ transfer, activeTab = "instant" }: Readonly<TransferPeopleTabProps>) {
    // Default to "Unknown" if no transfer is provided
    const transferStatus = transfer?.status ?? "Unknown";

    // Determine if this is a recurring tab transaction
    const isRecurringTab = activeTab === "recurring";

    // State for managing approval data with additional tracking
    const [approvalData, setApprovalData] = useState<ApprovalData[]>([]);

    // Use bulkTransactionRef if available, otherwise use transactionRef (per API documentation)
    const reference = transfer?.bulkTransactionRef ?? transfer?.transactionRef;

    // Use the custom hook for API management with deduplication
    const {
        data: apiApprovalData,
        loading,
        error,
    } = useApprovalRulePeopleInvolved({
        amount: transfer?.amount,
        type: "TRANSFER",
        reference,
        enabled: !!(transfer?.amount && reference), // Only enable if we have required data
    });

    // Process API data when it changes
    useEffect(() => {
        // Process API data - handle all possible response types
        if (!loading && !error) {
            if (apiApprovalData && Array.isArray(apiApprovalData)) {
                const sortedData = sortApproversByLevel(apiApprovalData);
                setApprovalData(sortedData);
            } else if (apiApprovalData === null || apiApprovalData === undefined) {
                // API returned null/undefined - this might be a valid "no approvers" response
                setApprovalData([]);
            } else {
                // API returned unexpected data type
                setApprovalData([]);
            }
        } else if (error) {
            // Handle error case
            setApprovalData([]);
        }
    }, [apiApprovalData, loading, error]);

    // Custom render function for individual approvers when using ApproverListItem
    const renderCustomApproverItem = useCallback(
        (approver: ApprovalData, index: number) =>
            renderApproverItem(approver, index, transferStatus, approvalData.length),
        [transferStatus, approvalData.length]
    );

    // Custom render function to override connector line colors - memoized to prevent unnecessary recreations
    const renderConnectorLine = useCallback(
        (index: number) => {
            // For recurring unknown transactions, use light purple connector lines
            if (transferStatus === "Unknown" && isRecurringTab) {
                return "#E9D5FF"; // Light purple color for unknown recurring transactions
            }

            if (transferStatus === "Unknown" && !isRecurringTab) {
                return "#F9F9FA"; // Neutral background color for unknown status in other tabs
            }

            if (
                transferStatus === "Successful" ||
                transferStatus === "Processing" ||
                transferStatus === "Failed" ||
                transferStatus === "Pending" ||
                transferStatus === "Awaiting Approval" ||
                transferStatus === "Rejected Approval"
            ) {
                return "#F9F0FE"; // Default color for the 6 required statuses
            }

            // For pending/awaiting approval and other statuses
            if (index >= 2) {
                // The connectors after the second approver
                return "#E3E5E8"; // Neutral gray for pending connectors
            }

            return "#F9F0FE"; // Default color for approved connectors
        },
        [transferStatus, isRecurringTab]
    );

    // Handle loading state
    if (loading && transfer?.amount && reference) {
        return (
            <div className="flex flex-col h-full overflow-auto">
                <div className="flex justify-center items-center py-8">
                    <div className="text-sm text-gray-600">Loading approval information...</div>
                </div>
            </div>
        );
    }

    // Handle error state
    if (error) {
        return (
            <div className="flex flex-col h-full overflow-auto">
                <div className="flex flex-col items-center justify-center py-8 text-center">
                    <p className="text-sm text-red-600 mb-2">Error loading approval information</p>
                    <p className="text-xs text-gray-500">Please try again or contact support if the problem persists</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col h-full overflow-auto">
            {/* Always use ApprovalFlow with real API data and ApproverListItem renderer */}
            <ApprovalFlow
                avatarBackgroundColor="#F9F0FE"
                connectorLineColor="#F9F0FE"
                amount={transfer?.amount ?? 0}
                type="TRANSFER"
                renderConnectorLine={renderConnectorLine}
                approverSpacing="mb-1"
                shouldFetchAPI={false} // Don't fetch API data since we already have it
                customData={approvalData}
                renderCustomItem={renderCustomApproverItem}
                // Use reference from API response as reference parameter (per API documentation)
                reference={reference}
            />
        </div>
    );
}
