/**
 * Approval Display Logic for Outgoing Transfer Details
 *
 * This module contains the conditional logic that determines when
 * approval alerts should be displayed in the Details tab of outgoing transfers.
 */

/**
 * Transfer object interface
 */
interface Transfer {
    id: string;
    status: string;
    type?: string;
    amount?: number;
    currency?: string;
    hasMultipleNarrations?: boolean;
    [key: string]: unknown;
}

/**
 * Parameters for determining approval alert display
 */
interface ApprovalDisplayParams {
    transfer: Transfer | null;
    hasApprovers: boolean;
    activeDetailTab: string;
    activeTab: string;
    isBulk: boolean;
    hasSingleNarr: boolean;
}

/**
 * Determines whether the approval alert should be shown based on complex business rules
 *
 * Rules:
 * 1. Only show for transfers with "Pending" or "Awaiting Approval" status
 * 2. Only show when there are approvers needed (hasApprovers = true)
 * 3. For Details tab:
 *    - Show for all single and bulk transfers (no exceptions)
 * 4. For Instances tab:
 *    - Show only for single transfers (never for bulk)
 * 5. For other tabs (People involved, Recipients, etc.):
 *    - Never show
 *
 * @param params - Object containing all the parameters needed for the decision
 * @returns boolean - true if the approval alert should be displayed
 */
export function shouldShowApprovalAlert({
    transfer,
    hasApprovers,
    activeDetailTab,
    activeTab,
    isBulk,
    hasSingleNarr,
}: ApprovalDisplayParams): boolean {
    // Guard clauses - return false if basic conditions aren't met
    if (!transfer) {
        return false;
    }

    if (!transfer.status) {
        return false;
    }

    // Only show for pending transfers that require approval
    const isPendingStatus = transfer.status === "Pending" || transfer.status === "Awaiting Approval";
    if (!isPendingStatus) {
        return false;
    }

    // Only show when approvers are needed
    if (!hasApprovers) {
        return false;
    }

    // Handle different detail tabs
    switch (activeDetailTab) {
        case "Details":
            return shouldShowInDetailsTab(isBulk, hasSingleNarr);

        case "Instances":
            return shouldShowInInstancesTab(isBulk);

        case "People involved":
        case "Recipients":
        default:
            // Never show in other tabs
            return false;
    }
}

/**
 * Logic for Details tab - simplified scenario
 */
function shouldShowInDetailsTab(isBulk: boolean, hasSingleNarr: boolean): boolean {
    // For Details tab: show approval alert for both single and bulk transfers
    // No complex conditions based on narrations or active tab
    return true;
}

/**
 * Logic for Instances tab - simpler scenario
 */
function shouldShowInInstancesTab(isBulk: boolean): boolean {
    // In Instances tab, only show for single transfers
    // Never show for bulk transfers regardless of other conditions
    return !isBulk;
}

/**
 * Helper function to check if a transfer needs approval
 * This can be expanded in the future for more complex approval logic
 */
export function transferNeedsApproval(transfer: Transfer | null, hasApprovers: boolean): boolean {
    if (!transfer || !hasApprovers) {
        return false;
    }

    const isPendingStatus = transfer.status === "Pending" || transfer.status === "Awaiting Approval";
    return isPendingStatus;
}

/**
 * Helper function to get approval alert configuration
 * Returns the text and styling configuration for the approval alert
 */
export function getApprovalAlertConfig(approvedCount: number, totalCount: number) {
    return {
        heading: `${approvedCount} of ${totalCount} approvals granted`,
        supportingText: "This transfer requires approval before it can be processed.",
        buttonText: "See approvers",
        variant: "info" as const,
        showIcon: true,
        showSecondaryActionRightIcon: true,
    };
}

/**
 * Type definitions for external use
 */
export type { Transfer, ApprovalDisplayParams };
