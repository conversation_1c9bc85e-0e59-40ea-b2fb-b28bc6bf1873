"use client";

import React from "react";
import { Alert } from "@/components/common/alert";

interface ApprovalAlertProps {
    /** Number of approvals already granted */
    approvalCount?: number;
    /** Total number of approvals required */
    totalApprovals?: number;
    /** Callback when "See approvers" button is clicked */
    onSeeApprovers?: () => void;
    /** Additional CSS classes */
    className?: string;
}

/**
 * ApprovalAlert Component
 *
 * Displays an alert showing approval progress for multi-party approval transfers.
 * Shows the number of approvals granted out of total required approvals
 * and provides a button to view the list of approvers.
 */
const ApprovalAlert: React.FC<ApprovalAlertProps> = ({
    approvalCount = 0,
    totalApprovals = 0,
    onSeeApprovers,
    className,
}) => {
    const handleSeeApprovers = () => {
        if (onSeeApprovers) {
            onSeeApprovers();
        }
    };

    return (
        <Alert
            variant="neutral"
            heading={`${approvalCount} of ${totalApprovals} approvals granted`}
            supportingText="Your payment will be sent once it meets the above requirement"
            showIcon={false}
            showCloseButton={false}
            secondaryActionText="See approvers"
            onSecondaryAction={handleSeeApprovers}
            showSecondaryActionRightIcon={true}
            className={`[&_button]:!text-[#5C068C] [&_button]:font-medium [&_svg_path]:!stroke-[#5C068C] ${className ?? ""}`}
        />
    );
};

export default ApprovalAlert;
