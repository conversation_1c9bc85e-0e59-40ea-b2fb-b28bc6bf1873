import { ButtonGroup, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FooterSectionProps } from "./types";
import { createDownloadReceiptHandler, createStatusUpdateHandler } from "./handlers";
import { AppDispatch } from "@/redux";

/**
 * Creates button configurations for the successful status
 */
export const createSuccessfulButtons = (
    props: FooterSectionProps,
    dispatch: AppDispatch,
    downloadReceiptLoading: boolean
): ButtonGroup => {
    const { transfer } = props;

    return {
        layout: "standard",
        buttons: [
            {
                text: "Download Receipt",
                variant: "primary",
                ariaLabel: "Download transaction receipt",
                onClick: createDownloadReceiptHandler(transfer, dispatch),
                disabled: downloadReceiptLoading,
                loading: downloadReceiptLoading,
                fullWidth: false,
            },
        ],
    };
};

/**
 * Creates button configurations for cancelable statuses (awaiting approval and pending)
 */
const createCancelableButtons = (props: FooterSectionProps): ButtonGroup => {
    const { handleStatusUpdate, isUpdatingStatus, activeTab } = props;
    const isRecurring = activeTab === "recurring";

    // Don't show cancel buttons for instant transfers
    if (activeTab === "instant") {
        return {
            layout: "standard",
            buttons: [],
        };
    }

    const cancelText = isRecurring ? "Cancel mandate" : "Cancel Transaction";

    return {
        layout: "standard",
        buttons: [
            {
                text: cancelText,
                variant: "outline",
                ariaLabel: cancelText,
                onClick: createStatusUpdateHandler("Cancel", handleStatusUpdate),
                disabled: isUpdatingStatus,
                fullWidth: false,
            },
        ],
    };
};

/**
 * Creates button configurations for the awaiting approval status
 */
export const createAwaitingApprovalButtons = (props: FooterSectionProps): ButtonGroup => createCancelableButtons(props);

/**
 * Creates button configurations for rejected approval status
 */
export const createRejectedApprovalButtons = (): ButtonGroup =>
    // No buttons for rejected approval transfers
    ({
        layout: "standard",
        buttons: [],
    });

/**
 * Creates button configurations for processing status
 */
export const createProcessingButtons = (): ButtonGroup =>
    // No buttons for processing transfers
    ({
        layout: "standard",
        buttons: [],
    });

/**
 * Creates button configurations for pending transfers
 */
export const createPendingButtons = (props: FooterSectionProps): ButtonGroup => createCancelableButtons(props);

/**
 * Creates button configurations for failed transfers
 */
export const createFailedButtons = (): ButtonGroup =>
    // No buttons for failed transfers - both bulk and single transfers show no buttons
    ({
        layout: "standard",
        buttons: [],
    });

/**
 * Creates button configurations for in-progress transfers
 */
export const createInProgressButtons = (): ButtonGroup => ({
    layout: "standard",
    buttons: [],
});

/**
 * Helper function to determine which button renderer key to use
 */
export const getButtonRendererKey = (props: FooterSectionProps): ButtonRendererKey => {
    const { transfer } = props;
    const { status } = transfer;

    // Handle the 6 required statuses
    if (status === "Failed") {
        return "failed";
    }

    if (status === "Processing") {
        return "processing";
    }

    if (status === "Rejected Approval") {
        return "rejectedApproval";
    }

    if (status === "Successful") {
        return "successful";
    }

    if (status === "Awaiting Approval") {
        return "awaitingApproval";
    }

    if (status === "Pending") {
        return "pending";
    }

    // Default fallback
    return "default";
};

/**
 * Creates the appropriate button group based on the renderer key
 */
export const createButtonGroup = (
    rendererKey: ButtonRendererKey,
    props: FooterSectionProps,
    dispatch: AppDispatch,
    downloadReceiptLoading: boolean
): ButtonGroup => {
    // Map keys to button creation functions
    const buttonCreators: Record<ButtonRendererKey, () => ButtonGroup> = {
        successful: () => createSuccessfulButtons(props, dispatch, downloadReceiptLoading),
        awaitingApproval: () => createAwaitingApprovalButtons(props),
        pending: () => createPendingButtons(props),
        rejectedApproval: () => createRejectedApprovalButtons(),
        processing: () => createProcessingButtons(),
        failed: () => createFailedButtons(),
        default: () => {
            // Don't show cancel buttons for instant transfers even in default case
            if (props.activeTab === "instant") {
                return {
                    layout: "standard",
                    buttons: [],
                };
            }
            return createPendingButtons(props);
        },
    };

    return buttonCreators[rendererKey]();
};
