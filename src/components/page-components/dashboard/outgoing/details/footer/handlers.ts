import { sendCatchFeedback } from "@/functions/feedback";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { AppDispatch } from "@/redux";
import { ITransfer } from "../../types";
import { TransferStatusAction } from "./types";

/**
 * Creates a handler function for cancel action that supports both single and bulk transfers
 * @param actionName - The action name (should be "Cancel")
 * @param handleStatusUpdate - Optional callback function for status updates
 * @returns Handler function that handles the cancel action
 */
export const createStatusUpdateHandler =
    (actionName: string, handleStatusUpdate?: (status: TransferStatusAction) => void) => () => {
        // Only support cancel action
        if (actionName !== "Cancel") {
            sendCatchFeedback(new Error(`${actionName} operation is not supported`));
            return;
        }

        // If handleStatusUpdate callback is provided, call it
        if (handleStatusUpdate) {
            handleStatusUpdate("CANCELLED");
        }
    };

/**
 * Handler for downloading receipt or transaction history
 * @param transfer - The transfer object
 * @param dispatch - Redux dispatch function
 * @returns Handler function for download action
 */
export const createDownloadReceiptHandler = (transfer: ITransfer, dispatch: AppDispatch) => () => {
    // Get the transaction ID from the transfer object - use only transferScheduledId
    const transactionId = transfer.transferScheduledId;

    if (transactionId === undefined || transactionId === null) {
        // No valid transaction ID
        sendCatchFeedback(new Error("No valid transaction ID found for this transfer"));
        return;
    }

    // Dispatch the downloadReceipt action with the transaction ID
    dispatch(
        downloadReceipt({
            transactionId,
            isUserInitiated: true,
        })
    );
};
