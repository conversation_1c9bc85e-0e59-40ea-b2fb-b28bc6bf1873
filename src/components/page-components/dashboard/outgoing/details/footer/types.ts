import { ITransfer } from "../../types";

/**
 * Supported status update actions for transfers
 */
export type TransferStatusAction = "CANCELLED";

/**
 * Props for the FooterSection component
 */
export interface FooterSectionProps {
    transfer: ITransfer;
    activeTab: string;
    activeDetailTab: string;
    handleStatusUpdate?: (status: TransferStatusAction) => void;
    isUpdatingStatus?: boolean;
}

/**
 * Button configuration for a specific action
 */
export interface ButtonConfig {
    text: string;
    variant?: "primary" | "outline" | "secondary" | "text-neutral";
    ariaLabel: string;
    onClick: () => void;
    disabled?: boolean;
    fullWidth?: boolean;
    loading?: boolean;
    dataTestid?: string;
}

/**
 * Group of buttons for a specific transfer state
 */
export interface ButtonGroup {
    buttons: ButtonConfig[];
    layout: "standard" | "fullWidth";
}

/**
 * Button renderer key map to support specific button configurations
 */
export type ButtonRendererKey =
    | "successful"
    | "awaitingApproval"
    | "pending"
    | "rejectedApproval"
    | "processing"
    | "failed"
    | "default";
