import React from "react";
import { But<PERSON> } from "@/components/common/buttonv3";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { FooterSectionProps } from "./types";
import { createButtonGroup, getButtonRenderer<PERSON>ey } from "./button-configs";

/**
 * Footer section component for the outgoing details drawer
 * Displays action buttons based on transfer status and active tab
 */
export function FooterSection(props: Readonly<FooterSectionProps>) {
    const { transfer } = props;

    // Redux hooks
    const dispatch = useAppDispatch();
    const { downloadReceiptLoading } = useAppSelector((state) => state.transfer);

    // Don't render footer for unknown status transfers
    if (transfer.status === "Unknown") {
        return null;
    }

    // Get the appropriate button configuration for the current state
    const rendererKey = getButtonRendererKey(props);
    const buttonGroup = createButtonGroup(rendererKey, props, dispatch, downloadReceiptLoading);

    // Don't render footer if there are no buttons to display
    if (!buttonGroup.buttons || buttonGroup.buttons.length === 0) {
        return null;
    }

    // Determine the container class based on the layout
    const containerClass =
        buttonGroup.layout === "fullWidth"
            ? "border-t border-[#E3E5E8] p-[24px]"
            : "flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-[16px] px-[24px]";

    // For full width layout, wrap buttons in additional container
    if (buttonGroup.layout === "fullWidth") {
        return (
            <div className={containerClass}>
                <div className="w-full">
                    {buttonGroup.buttons.map((buttonConfig, index) => (
                        <div key={`${buttonConfig.text}-${index}`} className={index > 0 ? "mt-3" : ""}>
                            <Button
                                type="button"
                                size="medium"
                                variant={buttonConfig.variant}
                                fullWidth={buttonConfig.fullWidth}
                                onClick={buttonConfig.onClick}
                                loading={buttonConfig.loading}
                                disabled={buttonConfig.disabled}
                                aria-label={buttonConfig.ariaLabel}
                                data-testid={buttonConfig.dataTestid}
                                className={buttonConfig.fullWidth ? "full-width-button" : ""}
                                style={buttonConfig.fullWidth ? { width: "100%", display: "block" } : undefined}
                            >
                                {buttonConfig.text}
                            </Button>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    // For standard layout, render buttons side by side
    return (
        <div className={containerClass}>
            {buttonGroup.buttons.map((buttonConfig, index) => (
                <Button
                    key={`${buttonConfig.text}-${index}`}
                    type="button"
                    size="medium"
                    variant={buttonConfig.variant}
                    fullWidth={buttonConfig.fullWidth}
                    onClick={buttonConfig.onClick}
                    loading={buttonConfig.loading}
                    disabled={buttonConfig.disabled}
                    aria-label={buttonConfig.ariaLabel}
                    data-testid={buttonConfig.dataTestid}
                >
                    {buttonConfig.text}
                </Button>
            ))}
        </div>
    );
}
