import React from "react";
import { X } from "lucide-react";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/common/Avatar";
import Badge from "@/components/common/badge";
import { ITransfer } from "../types";
import { formatNumberToNaira, getNameInitials } from "@/functions/stringManipulations";
import { formatDateWithTime } from "@/functions/date";
import { isBulkTransfer, calculateBulkTransferAmount } from "../utils/statusUtils";
import { getStatusMapping } from "@/utils/status-mapping";

interface HeaderSectionProps {
    transfer: ITransfer;
    handleCloseDetails: () => void;
}

/**
 * SVG component for bulk transfer icon
 */
const BulkTransferIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="64"
        height="64"
        viewBox="0 0 24 24"
        fill="none"
        className="bg-[#F9F0FE] p-4 rounded-full"
    >
        <path
            d="M16 3.46776C17.4817 4.20411 18.5 5.73314 18.5 7.5C18.5 9.26686 17.4817 10.7959 16 11.5322M18 16.7664C19.5115 17.4503 20.8725 18.565 22 20M2 20C3.94649 17.5226 6.58918 16 9.5 16C12.4108 16 15.0535 17.5226 17 20M14 7.5C14 9.98528 11.9853 12 9.5 12C7.01472 12 5 9.98528 5 7.5C5 5.01472 7.01472 3 9.5 3C11.9853 3 14 5.01472 14 7.5Z"
            stroke="#3A3A41"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export function HeaderSection({ transfer, handleCloseDetails }: Readonly<HeaderSectionProps>) {
    // Check if this is a bulk transfer
    const isBulk = isBulkTransfer(transfer);

    // For bulk transfers, calculate the total amount from the recipients if available
    const displayAmount = isBulk ? calculateBulkTransferAmount(transfer) : transfer.amount;

    return (
        <div className="py-[16px] px-[24px]">
            <div>
                <button onClick={handleCloseDetails} className="ml-auto block" data-testid="close-btn">
                    <X color="#90909D" />
                </button>
            </div>
            <div className="flex justify-between mt-[24px] pb-[24px] border-b border-[#E3E5E8]">
                <div className="flex gap-[12px] items-center">
                    <div>
                        {isBulk ? (
                            <BulkTransferIcon />
                        ) : (
                            <Avatar className="w-[64px] h-[64px]">
                                <AvatarImage src="" alt="" />
                                <AvatarFallback className="text-[20px] leading-[26px] font-medium text-subText">
                                    {getNameInitials(transfer.counterparty)}
                                </AvatarFallback>
                            </Avatar>
                        )}
                    </div>
                    <div className="flex flex-col gap-[4px] justify-center h-[64px]">
                        <h4 className="text-[18px] leading-[24px] font-semibold text-[#151519]">
                            {isBulk ? `${transfer.totalTransfers} recipients` : transfer.counterparty}
                        </h4>
                        <p className="text-[14px] leading-[18px] font-normal text-[#3A3A41]">{transfer.narration}</p>
                        <p className="text-[14px] leading-[18px] font-normal text-[#3A3A41]">
                            {transfer.date ? formatDateWithTime(transfer.date) : "-"}
                        </p>
                    </div>
                </div>
                <div className="text-right">
                    <h4 className="text-[18px] leading-[24px] font-semibold text-[#151519]">
                        {formatNumberToNaira(displayAmount)}
                    </h4>
                    <Badge
                        text={getStatusMapping(transfer.status).text}
                        size="md"
                        color={getStatusMapping(transfer.status).color}
                        className="capitalize"
                    />
                </div>
            </div>
        </div>
    );
}
