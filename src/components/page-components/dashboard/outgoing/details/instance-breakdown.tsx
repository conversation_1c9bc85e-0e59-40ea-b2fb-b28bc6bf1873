"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import SideDrawer from "@/components/common/drawer";
import TabSwitch from "@/components/common/tab-switch";
import Badge from "@/components/common/badge";
import { ITransfer } from "../types";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import DetailsTab from "./tabs/tab-details";
import PeopleTab from "./tabs/tab-people";
import RecipientsTab from "./tabs/tab-recipients";
import { FooterSection } from "./footer";
import { getStatusMapping } from "@/utils/status-mapping";

interface InstanceBreakdownProps {
    isOpen: boolean;
    handleClose: () => void;
    transfer: ITransfer;
    instanceId: string;
    instanceDate: string;
    instanceStatus: string;
}

export default function InstanceBreakdown({
    isOpen,
    handleClose,
    transfer,
    instanceId,
    instanceDate,
    instanceStatus,
}: Readonly<InstanceBreakdownProps>) {
    // State for active tab
    const [activeTab, setActiveTab] = useState("Details");

    // Create an instance transfer object based on the parent transfer
    // This represents the specific instance we're viewing
    const instanceTransfer: ITransfer = {
        ...transfer,
        id: instanceId,
        date: instanceDate,
        status: instanceStatus as ITransfer["status"],
        // Ensure we pass the API status format (uppercase)
        transferStatus: instanceStatus ? instanceStatus.toUpperCase() : "UNKNOWN",
        // Ensure all recipients inherit the instance status
        recipients: (transfer.recipients || []).map((recipient) => ({
            ...recipient,
            // Override recipient status with the instance status (uppercase API format)
            transferStatus: instanceStatus ? instanceStatus.toUpperCase() : "UNKNOWN",
        })),
        // If there's a specific instance recipients array, use that instead with the status override
        ...(transfer.instanceRecipients && {
            recipients: transfer.instanceRecipients.map((recipient) => ({
                ...recipient,
                // Override recipient status with the instance status (uppercase API format)
                transferStatus: instanceStatus ? instanceStatus.toUpperCase() : "UNKNOWN",
            })),
        }),
        // Add a flag to identify this as an instance breakdown, so recipients tab can adjust behavior
        isInstanceBreakdown: true,
    };

    // Tabs configuration
    const tabs = [
        { id: "Details", label: "Details" },
        { id: "People involved", label: "People involved" },
        { id: "Recipients", label: "Recipients" },
    ];

    // Function to handle tab changes
    const handleTabChange = (tab: string) => {
        setActiveTab(tab);
    };

    // Render tab content with the download receipt handler
    const renderTabContent = () => {
        switch (activeTab) {
            case "Details":
                return <DetailsTab transfer={instanceTransfer} />;
            case "People involved":
                return <PeopleTab transfer={instanceTransfer} />;
            case "Recipients":
                return (
                    <RecipientsTab
                        transfer={instanceTransfer}
                        activeTab="instant"
                        // Specify that this is a breakdown view to show proper badges and buttons
                        showStatusBadges={true}
                        showActionButtons={true}
                    />
                );
            default:
                return <DetailsTab transfer={instanceTransfer} />;
        }
    };

    return (
        <SideDrawer isOpen={isOpen} className="w-[550px] !max-w-[550px]" aria-label="Instance breakdown">
            <div className="flex flex-col h-full">
                {/* Header section */}
                <div className="py-[16px] px-[24px]">
                    <div>
                        <button onClick={handleClose} className="ml-auto block" data-testid="close-btn">
                            <X color="#90909D" />
                        </button>
                    </div>
                    <div className="flex justify-between mt-[24px] pb-[24px] border-b border-[#E3E5E8]">
                        <div className="flex gap-[12px] items-center">
                            <div>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="64"
                                    height="64"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    className="bg-[#F9F0FE] p-4 rounded-full"
                                >
                                    <path
                                        d="M16 3.46776C17.4817 4.20411 18.5 5.73314 18.5 7.5C18.5 9.26686 17.4817 10.7959 16 11.5322M18 16.7664C19.5115 17.4503 20.8725 18.565 22 20M2 20C3.94649 17.5226 6.58918 16 9.5 16C12.4108 16 15.0535 17.5226 17 20M14 7.5C14 9.98528 11.9853 12 9.5 12C7.01472 12 5 9.98528 5 7.5C5 5.01472 7.01472 3 9.5 3C11.9853 3 14 5.01472 14 7.5Z"
                                        stroke="#3A3A41"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>
                            <div className="flex flex-col gap-[4px] justify-center h-[64px]">
                                <h4 className="text-[18px] leading-[24px] font-semibold text-[#151519]">
                                    {transfer.totalTransfers} recipients
                                </h4>
                                <p className="text-[14px] leading-[18px] font-normal text-[#3A3A41]">
                                    {transfer.narration}
                                </p>
                                <p className="text-[14px] leading-[18px] font-normal text-[#3A3A41]">{instanceDate}</p>
                            </div>
                        </div>
                        <div className="text-right">
                            <h4 className="text-[18px] leading-[24px] font-semibold text-[#151519]">
                                {formatNumberToNaira(transfer.amount)}
                            </h4>
                            <Badge
                                text={getStatusMapping(instanceStatus).text}
                                size="md"
                                color={getStatusMapping(instanceStatus).color}
                                className="capitalize"
                            />
                        </div>
                    </div>
                </div>

                {/* Main content area with tabs */}
                <div className="flex flex-col h-[calc(100vh-137px)] overflow-hidden">
                    {/* Tab Switch */}
                    <div className="px-[24px] border-b border-[#E3E5E8]">
                        <TabSwitch tabs={tabs} activeTab={activeTab} onChange={handleTabChange} tabSpacing="h-[24px]" />
                    </div>

                    {/* Tab content */}
                    <div className="flex-1 overflow-auto p-[24px]">{renderTabContent()}</div>
                </div>

                {/* Footer section - fixed at the bottom */}
                {instanceStatus !== "Cancelled" && (
                    <FooterSection transfer={instanceTransfer} activeTab="instant" activeDetailTab={activeTab} />
                )}
            </div>
        </SideDrawer>
    );
}
