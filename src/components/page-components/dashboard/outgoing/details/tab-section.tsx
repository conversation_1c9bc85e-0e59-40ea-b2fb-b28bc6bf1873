import React from "react";
import TabSwitch from "@/components/common/tab-switch";
import { ITransfer } from "../types";
import { isBulkTransfer } from "../utils/statusUtils";
import OutgoingDetailsTab from "./tabs/tab-details";
import OutgoingPeopleTab from "./tabs/tab-people";
import OutgoingInstancesTab from "./tabs/tab-instances";
import OutgoingRecipientsTab from "./tabs/tab-recipients";

interface TabSectionProps {
    transfer: ITransfer;
    activeTab: string;
    activeDetailTab: string;
    handleTabChange: (tab: string) => void;
}

export function TabSection({ transfer, activeTab, activeDetailTab, handleTabChange }: Readonly<TabSectionProps>) {
    // Function to determine which tabs to show based on transfer status and type
    const getTabsAndPanels = () => {
        // Default tabs that are always shown
        const tabs = ["Details"];
        const panels = [<OutgoingDetailsTab key="details" transfer={transfer} activeTab={activeTab} />];

        // Add Instances tab only for transfers in the recurring tab
        if (activeTab === "recurring") {
            tabs.push("Instances");
            panels.push(<OutgoingInstancesTab key="instances" transfer={transfer} />);
        }

        // Add People involved tab
        tabs.push("People involved");
        panels.push(<OutgoingPeopleTab key="people involved" transfer={transfer} activeTab={activeTab} />);

        // Add Recipients tab only for bulk transfers
        if (isBulkTransfer(transfer)) {
            tabs.push("Recipients");
            panels.push(<OutgoingRecipientsTab key="recipients" transfer={transfer} activeTab={activeTab} />);
        }

        return { tabs, panels };
    };

    const { tabs, panels } = getTabsAndPanels();
    const activeTabIndex = tabs.findIndex((tab) => tab === activeDetailTab);

    // Check if Recipients tab is included in the tabs list
    const hasRecipientsTab = tabs.includes("Recipients");

    // Use different tabSpacing for Recipients tab
    const tabSpacing = hasRecipientsTab && activeDetailTab === "Recipients" ? "h-[20px]" : "h-[32px]";

    return (
        <div className="px-[24px] flex flex-col flex-grow overflow-hidden">
            <TabSwitch
                tabs={tabs}
                activeTab={activeDetailTab}
                onChange={handleTabChange}
                tabSpacing={tabSpacing}
                panelClassName="h-full overflow-hidden"
            />
            <div className="flex-grow overflow-hidden">
                {activeTabIndex >= 0 && (
                    <div className="h-full overflow-hidden flex flex-col">{panels[activeTabIndex]}</div>
                )}
            </div>
        </div>
    );
}
