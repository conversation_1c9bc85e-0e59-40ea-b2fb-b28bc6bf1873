import { But<PERSON> } from "@/components/common/buttonv3";
import { CheckboxLabel } from "@/components/common/checkbox-label";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { sendCatchFeedback } from "@/functions/feedback";
import {
    convertDateToMonthYear,
    convertFirstLetterToUppercase,
    enumToCapitalizedPhrase,
} from "@/functions/stringManipulations";
import { submitDigitalApplication } from "@/redux/actions/digitalOnboardingAction";
import {
    openDigitalBusinessInformationFormDialog,
    openDigitalBusinessOwnersFormDialog,
    openDigitalUploadApplicationFormDialog,
} from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { FC, useState } from "react";
import { IAccountApplicationProps } from "./account-application";
import DigitalReviewItem from "./components/digital-review-item";
import SubmitApplicationModal from "./components/submit-application-modal";

const digitalFormSteps = [
    { id: "1", label: "Business Information" },
    { id: "2", label: "Business Owners" },
    { id: "3", label: "Business Documents" },
    { id: "4", label: "Review" },
];

const DigitalOnboardingReview: FC<IAccountApplicationProps> = ({ isOpen, onClose, handlePrev, onboarding }) => {
    const dispatch = useAppDispatch();
    const [submitModalOpen, setSubmitModalOpen] = useState(false);
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);
    const digitalRegData = useAppSelector((state) => state.onboarding?.businessInfoBody);
    const ownersFormData = useAppSelector((state) => state.businessOwners.owners);
    const [confirmation, setConfirmation] = useState(false);
    const [loading, setLoading] = useState(false);

    interface BusinessOwnersData {
        [key: string]: string[];
    }

    const businessInfoData = {
        "Legal Business Name":
            enumToCapitalizedPhrase(digitalRegData?.businessRegistrationType || onboarding?.registrationType || "") ??
            "",
        "Country of Registration": `Registered in ${digitalRegData?.countryOfRegistration || onboarding?.countryOfRegistration}`,
        "Date of Incorporation": `Incorporated ${convertDateToMonthYear(digitalRegData?.yearOfRegistration || onboarding?.yearOfRegistration || "")}`,
    };

    const businessOwnersData: BusinessOwnersData = {};

    ownersFormData?.forEach((owner: { role: string; fullLegalName: string }, idx: number) => {
        const role = owner.role === "OWNER" ? "Director" : `Shareholder${idx + 1}`;

        if (!businessOwnersData[role]) {
            businessOwnersData[role] = [];
        }

        businessOwnersData[role].push(
            `${owner.fullLegalName}, ${convertFirstLetterToUppercase(owner.role.toLowerCase())}`
        );
    });

    const documentsData = {
        "Certificate of incorporation": "Certificate of incorporation",
        "CAC Form 1.1": "CAC Form 1.1",
        "Other documents": "6 other documents",
    };

    const handleEditBusinessInfo = () => {
        handlePrev?.();
        dispatch(openDigitalBusinessInformationFormDialog());
    };

    const handleEditBusinessOwners = () => {
        handlePrev?.();
        dispatch(openDigitalBusinessOwnersFormDialog());
    };

    const handleEditDocuments = () => {
        handlePrev?.();
        dispatch(openDigitalUploadApplicationFormDialog());
    };

    const submitApplication = async () => {
        try {
            setLoading(true);
            await dispatch(submitDigitalApplication({ onboardingId: onboarding?.id })).unwrap();
            setSubmitModalOpen(true);
        } catch (error) {
            sendCatchFeedback(error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={() => setShowExitConfirmation(true)}
            title="Account Application"
            showSupport
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={() => {
                onClose();
                setShowExitConfirmation(false);
            }}
            onCancelExit={() => setShowExitConfirmation(false)}
        >
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={digitalFormSteps} currentStep={4} />
                </nav>

                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[600px]">
                        <h2 className="text-center text-[#151518] text-2xl font-bold leading-[30px] tracking-tight mb-3">
                            Almost done, Review!
                        </h2>
                        <p className="text-center text-[#151518] font-medium text-sm leading-[30px] tracking-tight mb-10">
                            Review your application details before submitting
                        </p>

                        <div className="border rounded-2xl py-8 px-4 space-y-8">
                            <DigitalReviewItem
                                title="Business information"
                                data={businessInfoData}
                                onEdit={handleEditBusinessInfo}
                            />
                            <DigitalReviewItem
                                title="Business owners"
                                data={businessOwnersData}
                                onEdit={handleEditBusinessOwners}
                            />
                            <DigitalReviewItem
                                title="Business documents"
                                data={documentsData}
                                onEdit={handleEditDocuments}
                                showBullets={true}
                            />
                        </div>

                        <div className="mt-5">
                            <CheckboxLabel
                                checked={confirmation}
                                onChange={(checked) => setConfirmation(checked)}
                                label="I confirm that all information provided is true and accurate"
                            />
                        </div>

                        <div className="flex justify-end mt-8 gap-2">
                            <Button variant="outline" onClick={handleEditDocuments}>
                                Previous
                            </Button>
                            <Button
                                variant="primary"
                                onClick={submitApplication}
                                loading={loading}
                                disabled={!confirmation}
                            >
                                Submit
                            </Button>
                        </div>
                    </div>
                </main>
                <SubmitApplicationModal open={submitModalOpen} onClose={onClose} />
            </div>
        </FullScreenDrawer>
    );
};

export default DigitalOnboardingReview;
