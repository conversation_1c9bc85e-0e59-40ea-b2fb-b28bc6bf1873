import { Button } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import LabelInput from "@/components/common/label-input";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { businessValidationForm } from "@/redux/actions/digitalOnboardingAction";
import { openDigitalBusinessInformationFormDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch } from "@/redux/hooks";
import { setBusinessIdentifiers } from "@/redux/slices/onboardingSlice";
import { BusinessValidationForm } from "@/redux/types/digital-onboarding";
import { useFormik } from "formik";
import { FC, useEffect, useState } from "react";
import * as yup from "yup";
import { IAccountApplicationProps } from "./account-application";

const DigitalBusinessValidationForm: FC<IAccountApplicationProps> = ({ isOpen, onClose, handlePrev, onboarding }) => {
    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState<boolean>(false);

    const formik = useFormik({
        initialValues: {
            businessRegistrationNumber: "",
            taxIdentificationNumber: "",
        },
        onSubmit: (values) => {
            submitValues(values);
        },
        validationSchema: yup.object({
            taxIdentificationNumber: yup
                .string()
                .required("TIN is required")
                .matches(/^\d{8}-\d{4}$/, "TIN must be in the format ********-0000"),
            businessRegistrationNumber: yup
                .string()
                .matches(/^RC\d{7}$/, "Business Registration Number must be in the format *********")
                .required("Business Registration Number is required"),
        }),
    });

    const submitValues = async (values: BusinessValidationForm) => {
        setLoading(true);
        try {
            // 1) Validate and early-return on errors
            const errors = await formik.validateForm();
            if (Object.keys(errors).length > 0) {
                Object.keys(errors).forEach((field) => formik.setFieldTouched(field, true, false));
                sendCatchFeedback("Please fill in all required fields correctly");
                return;
            }

            // 2) Dispatch & unwrap will throw if the thunk was rejected
            const { tinSuccess, rcsuccess, businessName } = await dispatch(businessValidationForm(values)).unwrap();

            // 3) Both valid → success
            if (tinSuccess && rcsuccess) {
                sendFeedback("TIN and RC Validated Successfully", "success");
                dispatch(
                    setBusinessIdentifiers({
                        businessRegistrationNumber: formik.values.businessRegistrationNumber,
                        taxIdentificationNumber: formik.values.taxIdentificationNumber,
                        businessName,
                    })
                );
                formik.resetForm();
                dispatch(openDigitalBusinessInformationFormDialog());
                return;
            }

            // 4) Build a message using "but" if exactly one failed
            let message: string;
            if (!tinSuccess && !rcsuccess) {
                // both failed
                message = "TIN and RC validation failed. Please check your inputs.";
            } else if (tinSuccess) {
                message = "TIN validated successfully, but RC validation failed. Please check your inputs.";
            } else {
                // !tinSuccess && rcsuccess
                message = "RC validated successfully, but TIN validation failed. Please check your inputs.";
            }
            sendCatchFeedback(message);
        } catch (err: unknown) {
            // 5) Network / unexpected errors
            const msg = err instanceof Error ? err.message : "Submission failed. Please try again.";
            sendCatchFeedback(msg);
        } finally {
            setLoading(false);
        }
    };

    const formatTIN = (value: string) => {
        if (!value) return value;
        const digits = value.replace(/\D/g, "");
        const limitedDigits = digits.substring(0, 12);
        if (limitedDigits.length > 8) {
            const formatted = limitedDigits.substring(0, 8) + "-" + limitedDigits.substring(8);
            return formatted;
        }
        return limitedDigits;
    };

    useEffect(() => {
        const currentTIN = formik.values.taxIdentificationNumber;
        const formattedTIN = formatTIN(currentTIN);

        if (currentTIN !== formattedTIN) {
            formik.setFieldValue("taxIdentificationNumber", formattedTIN);
        }
    }, [formik, formik.values.taxIdentificationNumber]);

    // RC Validation
    const formatRC = (value: string) => {
        // RC would precede number and delete can
        // only happen on other values
        if (!value) return "RC";

        // Ensure it starts with RC
        let sanitized = value.toUpperCase();
        if (!sanitized.startsWith("RC")) {
            sanitized = "RC" + sanitized.replace(/^RC/i, "");
        }

        // Only keep numeric characters after RC
        const digits = sanitized.slice(2).replace(/\D/g, "").substring(0, 7);
        return `RC${digits}`;
    };

    useEffect(() => {
        const currentRC = formik.values.businessRegistrationNumber;
        const formattedRC = formatRC(currentRC);

        if (currentRC !== formattedRC) {
            formik.setFieldValue("businessRegistrationNumber", formattedRC);
        }
    }, [formik, formik.values.businessRegistrationNumber]);

    const isButtonDisabled = Object.keys(formik.touched).length === 0 || Object.keys(formik.errors).length > 0;
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);

    const handleExit = () => {
        setShowExitConfirmation(true);
    };

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleExit}
            title="Account Application"
            showExitConfirmation={showExitConfirmation}
            onCancelExit={() => setShowExitConfirmation(false)}
            onConfirmExit={() => {
                onClose();
                setShowExitConfirmation(false);
                formik.resetForm();
            }}
        >
            <div className="flex min-h-fit relative flex-1">
                <main className="flex-1 px-14 py-[120px] flex flex-col items-center">
                    <div className="flex justify-center">
                        <h2
                            className="text-center text-[#151518] text-2xl max-w-[500px] font-bold leading-[30px] tracking-tight mb-12"
                            data-testid="digital-business-validation-title"
                        >
                            To begin, let's validate your registered business
                        </h2>
                    </div>

                    <section className="w-full max-w-[452px]">
                        <form onSubmit={formik.handleSubmit} noValidate>
                            <LabelInput
                                formik={formik}
                                name="businessRegistrationNumber"
                                label="Business Registration Number (RC number)"
                                type="text"
                                className="mb-5"
                                maxLength={9}
                                showError={formik.touched.businessRegistrationNumber}
                                onKeyDown={(e) => {
                                    const cursorPos = e.currentTarget.selectionStart || 0;
                                    if (
                                        cursorPos < 2 &&
                                        (e.key === "Backspace" || e.key === "Delete" || e.key === "ArrowLeft")
                                    ) {
                                        e.preventDefault();
                                    }
                                }}
                            />

                            <LabelInput
                                formik={formik}
                                name="taxIdentificationNumber"
                                label="Tax Identification Number (TIN)"
                                type="text"
                                maxLength={13}
                                showError={formik.touched.taxIdentificationNumber}
                            />

                            <div className="flex justify-end mt-8">
                                <Button variant="outline" className="mr-2" onClick={handlePrev}>
                                    Previous
                                </Button>
                                <Button
                                    disabled={isButtonDisabled || !formik.isValid}
                                    type="submit"
                                    variant="primary"
                                    size="medium"
                                    loading={loading}
                                >
                                    Continue
                                </Button>
                            </div>
                        </form>
                    </section>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default DigitalBusinessValidationForm;
