// LIBRARY IMPORTS
import { FC } from "react";

// PROJECT IMPORTS
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import {
    openDigitalApplicationFormDialog,
    openManualDownloadFormApplicationDialog,
} from "@/redux/features/uiDialogSlice";
import { useAppDispatch } from "@/redux/hooks";
import { BusinessInfoResponse } from "@/redux/slices/onboarding/businessOwnersSlice";
import InfoCard from "./components/account-info-card";
import { DigitalFormIcon, ManualFormIcon } from "./icons/icons";

export interface IAccountApplicationProps {
    isOpen: boolean;
    onClose: () => void;
    handlePrev?: () => void;
    onboarding?: BusinessInfoResponse | null;
}

const AccountApplication: FC<IAccountApplicationProps> = ({ isOpen, onClose }) => {
    const dispatch = useAppDispatch();

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Account Application">
            <div className="flex min-h-fit relative flex-1">
                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[840px]">
                        <div className="flex justify-center">
                            <h2
                                className="text-center text-[#151518] text-2xl max-w-[500px] font-bold leading-[30px] tracking-tight mb-10"
                                data-testid="payment-info-title"
                            >
                                Choose how you would like to complete your account opening form
                            </h2>
                        </div>
                        <div className="w-full flex justify-around">
                            <InfoCard
                                title={"Digital form"}
                                description={"Complete your account opening form online."}
                                time={"10 min"}
                                icon={<DigitalFormIcon />}
                                onClick={() => dispatch(openDigitalApplicationFormDialog())}
                            />
                            <InfoCard
                                title={"Manual form"}
                                description={"Download, fill, and upload account opening form."}
                                time={"15-20 min"}
                                icon={<ManualFormIcon />}
                                onClick={() => dispatch(openManualDownloadFormApplicationDialog())}
                            />
                        </div>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default AccountApplication;
