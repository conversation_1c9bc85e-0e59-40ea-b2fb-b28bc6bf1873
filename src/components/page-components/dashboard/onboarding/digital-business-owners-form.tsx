import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import Dropdown from "@/components/common/dropdown";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { businessOwnersAccountForm } from "@/redux/actions/digitalOnboardingAction";
import { openDigitalUploadApplicationFormDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { deleteOwner, IBusinessOwner, setOwners } from "@/redux/slices/onboarding/businessOwnersSlice";
import { useFormik } from "formik";
import { FC, useEffect, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import * as yup from "yup";
import { IAccountApplicationProps } from "./account-application";
import DigitalBusinessOwnerCard from "./components/digital-business-owner-card";
import DigitalBusinessOwnerInfoForm from "./components/digital-business-owner-info-form";

const number = [
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
    { label: "4", value: "4" },
    { label: "5", value: "5" },
];

const DigitalBusinessOwnersForm: FC<IAccountApplicationProps> = ({ isOpen, onClose, handlePrev, onboarding }) => {
    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState<boolean>(false);
    const [openInfoModal, setOpenInfoModal] = useState(false);
    const [currentOwnerIndex, setCurrentOwnerIndex] = useState<number | null>(null);
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);
    const ownersFormData = useAppSelector((state) => state.businessOwners.owners);

    useEffect(() => {
        if (onboarding?.businessOwners?.length) {
            const validOwners = onboarding.businessOwners
                .map((owner: IBusinessOwner) => {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const formattedDocuments = (owner.documents || []).map((doc: any) => {
                        let formattedFile;
                        if (doc.file) {
                            formattedFile = typeof doc.file === "object" ? doc.file : { name: doc.file, size: 0 };
                        } else {
                            formattedFile = undefined;
                        }

                        return {
                            documentType: doc.documentType,
                            file: formattedFile,
                        };
                    });

                    return {
                        ...owner,
                        id: owner.id || "",
                        bvn: owner.bvn || "",
                        fullLegalName: owner.fullLegalName || "",
                        dateOfBirth: owner.dateOfBirth || "",
                        nationality: owner.nationality || "",
                        role: owner.role || "",
                        ownershipPercentage: owner.ownershipPercentage || "",
                        identityDocumentType: owner.identityDocumentType || "",
                        residentialAddress: owner.residentialAddress || "",
                        city: owner.city || "",
                        state: owner.state || "",
                        proofOfAddressType: owner.proofOfAddressType || "",
                        documents: formattedDocuments,
                    };
                })
                .filter((owner) => owner.bvn && owner.fullLegalName);

            dispatch(setOwners(validOwners));
            formik.setValues({
                numberOfOwners: validOwners.length.toString(),
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch, onboarding]);

    const formik = useFormik({
        initialValues: useMemo(() => ({ numberOfOwners: "" }), []),
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            numberOfOwners: yup.string().required("Please select a number"),
        }),
        enableReinitialize: true,
    });

    const handleAddOwnerInfo = (index: number) => {
        setCurrentOwnerIndex(index);
        setOpenInfoModal(true);
    };

    const handleDeleteOwner = (index: number) => {
        dispatch(deleteOwner(index));
    };

    const submitValues = async () => {
        setLoading(true);
        try {
            const submissionData = ownersFormData.map((owner) => ({
                ...owner,
                documents: owner.documents?.filter((doc) => doc.file && doc.documentType), // Ensure documents have file and type
            }));

            await dispatch(
                businessOwnersAccountForm({
                    businessOwners: submissionData,
                    onboardingId: onboarding?.id?.toString() ?? "",
                })
            ).unwrap();
            sendFeedback("Submitted Successfully", "success");
            dispatch(openDigitalUploadApplicationFormDialog());
        } catch (error) {
            sendCatchFeedback(error ?? "Submission failed. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleExit = () => {
        setShowExitConfirmation(true);
    };

    const digitalFormSteps = [
        { id: "1", label: "Business Information" },
        { id: "2", label: "Business Owners" },
        { id: "3", label: "Business Documents" },
        { id: "4", label: "Review" },
    ];

    const isSubmitDisabled =
        !formik.values.numberOfOwners || parseInt(formik.values.numberOfOwners) !== ownersFormData.length;

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleExit}
            title="Account Application"
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={() => {
                onClose();
                setShowExitConfirmation(false);
            }}
            onCancelExit={() => setShowExitConfirmation(false)}
        >
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={digitalFormSteps} currentStep={2} />
                </nav>
                <main className="flex-1 px-14 py-[120px] flex flex-col items-center">
                    <div className="flex justify-center">
                        <h2
                            className="text-center text-[#151518] text-2xl max-w-[500px] font-bold leading-[30px] tracking-tight mb-12"
                            data-testid="manual-download-title"
                        >
                            Business owners
                        </h2>
                    </div>
                    <section className="w-full max-w-[452px]">
                        <form onSubmit={formik.handleSubmit} noValidate>
                            <Dropdown
                                value={{
                                    label: formik.values.numberOfOwners,
                                    value: formik.values.numberOfOwners,
                                }}
                                options={number}
                                name="numberOfOwners"
                                label="How many people own or control your business?"
                                size="sm"
                                formik={formik}
                                showError={formik.touched.numberOfOwners}
                            />

                            {parseInt(formik.values.numberOfOwners) >= 1 && (
                                <div className="mt-8 text-sm font-semibold">
                                    <h3 data-testid="add-owners-info-heading">
                                        Add their personal information before you continue
                                    </h3>
                                    <div className="mt-5 space-y-5">
                                        {Array.from({ length: parseInt(formik.values.numberOfOwners) }).map(
                                            (_, idx) => {
                                                const owner = ownersFormData[idx]
                                                    ? {
                                                          ...ownersFormData[idx],
                                                          id: ownersFormData[idx]?.id || uuidv4(),
                                                      }
                                                    : undefined;
                                                const key = owner?.id || owner?.fullLegalName || `owner-${idx}`;

                                                return (
                                                    <DigitalBusinessOwnerCard
                                                        id={idx}
                                                        key={key}
                                                        onAdd={() => handleAddOwnerInfo(idx)}
                                                        onDelete={handleDeleteOwner}
                                                        ownerData={owner}
                                                    />
                                                );
                                            }
                                        )}
                                    </div>
                                </div>
                            )}
                            <div className="flex justify-end mt-8">
                                <Button type="button" variant="outline" className="mr-2" onClick={handlePrev}>
                                    Previous
                                </Button>
                                <Button
                                    loading={loading}
                                    disabled={isSubmitDisabled}
                                    type="submit"
                                    variant="primary"
                                    size="medium"
                                >
                                    Save and continue
                                </Button>
                            </div>
                        </form>
                    </section>
                    <DigitalBusinessOwnerInfoForm
                        open={openInfoModal}
                        onClose={() => setOpenInfoModal(false)}
                        headerText="Business owner details"
                        initialData={currentOwnerIndex !== null ? ownersFormData[currentOwnerIndex] : undefined}
                    />
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default DigitalBusinessOwnersForm;
