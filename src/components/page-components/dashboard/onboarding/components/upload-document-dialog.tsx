"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import SideDrawer from "@/components/common/drawer";
import FileAttachment from "@/components/common/file-attachment";
import { sendFeedback } from "@/functions/feedback";
import { uploadBusinessDocument } from "@/redux/actions/digitalOnboardingAction";
import { submitDocumentationForm } from "@/redux/actions/manualOnboardingActions";
import { useAppDispatch } from "@/redux/hooks";
import { BusinessInfoResponse } from "@/redux/slices/onboarding/businessOwnersSlice";
import { BusinessDocumentType } from "@/redux/types/digital-onboarding";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { ArrowDownToLine, Info } from "lucide-react";
import { useEffect, useState } from "react";
import downloadFile from "../libs/download-file";

type Props = {
    open: boolean;
    onClose: () => void;
    headerText: string;
    businessCategory?: string;
    activeField: { label: string; value: string } | null;
    setActiveField: (field: { label: string; value: string } | null) => void;
    isDigitalUpload: boolean;
    onUploadSuccess: (fieldName: string, fileName: string) => void;
    onboarding?: BusinessInfoResponse | null;
};

const UploadDocumentDialog = ({
    open,
    onClose,
    headerText,
    activeField,
    setActiveField,
    onUploadSuccess,
    businessCategory,
    isDigitalUpload,
    onboarding,
}: Props) => {
    const [file, setFile] = useState<File | null>(null);
    const [localLoading, setLocalLoading] = useState(false);
    const dispatch = useAppDispatch();

    useEffect(() => {
        if (!open) {
            setFile(null);
            setLocalLoading(false);
            setLocalLoading(false);
        }
    }, [open]);

    const handleFileChange = (files: FileList) => {
        if (files.length > 0) {
            setFile(files[0]);
        }
    };

    const handleUpload = async () => {
        if (!businessCategory && !isDigitalUpload) {
            return sendFeedback("Select a business category", "error");
        }

        if (!activeField || !file) return;

        setLocalLoading(true);

        try {
            if (!isDigitalUpload) {
                await dispatch(
                    submitDocumentationForm({
                        //@ts-ignore
                        businessCategory,
                        documentType: activeField.value,
                        file,
                    })
                ).unwrap(); // Ensure this unwraps the action correctly
            } else {
                await dispatch(
                    uploadBusinessDocument({
                        documentType: activeField.value as BusinessDocumentType,
                        file,
                        onboardingId: onboarding?.id?.toString() ?? "",
                    })
                ).unwrap(); // Same for the digital upload case
            }

            sendFeedback("Document uploaded successfully", "success");
            onUploadSuccess(activeField.value, file.name);
            handleClose();
        } catch (error) {
            console.error("Upload failed:", error); // Log the error for debugging
            sendFeedback("Upload failed. Please try again.", "error");
        } finally {
            setLocalLoading(false);
        }
    };

    const handleClose = () => {
        setFile(null);
        setActiveField(null);
        onClose();
    };

    return (
        <SideDrawer isOpen={open}>
            <div className="p-6 max-w-md w-full bg-white rounded-lg">
                {/* Header */}
                <div className="flex justify-between items-center border-b pb-3">
                    <h2 className="text-lg font-semibold">Upload {headerText}</h2>
                    <button onClick={handleClose} className="text-gray-500 hover:text-gray-700">
                        <XMarkIcon className="w-5 h-5" />
                    </button>
                </div>

                <p className="text-sm text-gray-600 mt-4">
                    This document shows your company has authorized opening a corporate bank account with FCMB.
                </p>

                <div className="p-4 bg-gray-100 rounded-lg flex space-y-2 mt-6">
                    <div className="mt-3">
                        <Info className="w-5 h-5" />
                    </div>
                    <div className="ml-4">
                        <div className="flex items-center space-x-2 text-gray-600">
                            <span className="text-sm font-medium">Not sure what to upload?</span>
                        </div>
                        <p className="text-sm text-gray-500">
                            You can use our template or view a sample to understand what's required.
                        </p>

                        <div className="flex space-x-4 text-sm font-medium text-purple-600">
                            <Button
                                leftIcon={<ArrowDownToLine className="w-4 h-4" />}
                                variant="text-primary"
                                onClick={() => downloadFile("documents/sample.pdf", "sample.pdf")}
                            >
                                Download template
                            </Button>
                            <Button
                                variant="text-primary"
                                onClick={() => window.open("documents/sample.pdf", "_blank")}
                            >
                                View sample
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="mt-6">
                    <h3 className="text-sm font-semibold">Requirements</h3>
                    <ul className="text-sm text-gray-600 mt-2 list-disc list-inside">
                        <li>Must be signed by authorized company directors</li>
                        <li>Company letterhead required</li>
                        <li>Must be dated within the last 3 months</li>
                    </ul>
                </div>

                <div className="mt-10">
                    <FileAttachment
                        headerText="Attachments"
                        maxSize={10}
                        acceptedTypes={["pdf", "png", "jpg"]}
                        className="w-full h-auto"
                        width="100%"
                        descriptionText="We accept PDF, PNG & JPG files, up to 10MB"
                        data-testid="file-attachment"
                        onFilesSelected={handleFileChange}
                        onFileRemoved={() => setFile(null)}
                    />
                </div>

                <div className="flex justify-end gap-2 mt-4">
                    <Button onClick={handleClose}>Cancel</Button>
                    <Button disabled={!file} onClick={handleUpload} loading={localLoading ? true : undefined}>
                        Upload
                    </Button>
                </div>
            </div>
        </SideDrawer>
    );
};

export default UploadDocumentDialog;
