"use client";

import { But<PERSON> } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { formatDate } from "@/functions/date";
import {
    closeAccountApplicationDialog,
    closeDigitalApplicationFormDialog,
    closeDigitalOnboardingReviewDialog,
} from "@/redux/features/uiDialogSlice";
import { useAppDispatch } from "@/redux/hooks";
import { BusinessInfoResponse, clearOwners } from "@/redux/slices/onboarding/businessOwnersSlice";
import { setBusinessIdentifiers, setBusinessInfoBody, setBusinessInfoResponse } from "@/redux/slices/onboardingSlice";
import { MailOpen } from "lucide-react";
import { FC } from "react";
import { AccountSubmitted } from "../icons/icons";

interface ComponentProps {
    open: boolean;
    onClose: () => void;
    pendingOnboarding?: boolean;
    pendingOnboardingData?: BusinessInfoResponse | null;
}

const SubmitApplicationModal: FC<ComponentProps> = ({ open, onClose, pendingOnboarding, pendingOnboardingData }) => {
    const dispatch = useAppDispatch();
    const resetAllStates = () => {
        onClose();
        dispatch(setBusinessInfoBody(null));
        dispatch(setBusinessInfoResponse(null));
        dispatch(
            setBusinessIdentifiers({
                businessRegistrationNumber: "",
                taxIdentificationNumber: "",
                businessName: "",
            })
        );
        dispatch(clearOwners());

        dispatch(closeAccountApplicationDialog());
        dispatch(closeDigitalApplicationFormDialog());
        dispatch(closeDigitalOnboardingReviewDialog());
    };
    const handleDoneClick = async () => {
        resetAllStates();
    };

    return (
        <FullScreenDrawer isOpen={open} onClose={onClose}>
            <div className="flex justify-center items-center">
                <div className="w-full max-w-lg text-center">
                    <div className="flex justify-center">
                        <AccountSubmitted />
                    </div>
                    <h2 className="text-2xl font-bold mt-4 text-black">Application submitted!</h2>
                    <p className="text-subText text-base mt-2">We've received your application and documents.</p>

                    <div className="p-4 mt-8 w-full border rounded-[20px] text-left">
                        <h3 className="font-semibold mb-4">What's next?</h3>
                        <Stepper
                            steps={
                                pendingOnboarding && pendingOnboardingData
                                    ? [
                                          {
                                              id: "1",
                                              label: "Documents submitted",
                                              content: `Submitted ${formatDate((pendingOnboardingData?.createdAt ?? pendingOnboardingData?.createdDate) as string)}`,
                                          },
                                          {
                                              id: "2",
                                              label: "Review in progress",
                                              content: "Our team will review within 1-3 business days",
                                          },
                                          {
                                              id: "3",
                                              label: "Review completed",
                                              content: "You’ll be notified of the outcome",
                                          },
                                      ]
                                    : [
                                          {
                                              id: "1",
                                              label: "Documents submitted",
                                              content: "All required documents have been uploaded",
                                          },
                                          {
                                              id: "2",
                                              label: "Review in progress",
                                              content: "Our team will review within 1-3 business days",
                                          },
                                          {
                                              id: "3",
                                              label: "Review completed",
                                              content: "You’ll be notified of the outcome",
                                          },
                                      ]
                            }
                            currentStep={pendingOnboarding ? 1 : 2}
                            labelColor="#151519"
                            connectorHeight={40}
                            indicatorSize={28}
                            fontSize={14}
                        />
                        <div className="flex items-start bg-gray-50 p-4 mt-8 rounded-lg">
                            <span className="mt-5">
                                <MailOpen className="h-6 w-6 text-gray-500" />
                            </span>
                            <div className="ml-3">
                                <p className="font-medium text-gray-900">We'll keep you updated</p>
                                <p className="text-gray-500 text-sm">
                                    You'll receive an email on the status of your application. Make sure to check your
                                    inbox regularly.
                                </p>
                            </div>
                        </div>
                    </div>

                    <Button onClick={pendingOnboarding ? onClose : handleDoneClick} className="!w-full mt-8">
                        Done
                    </Button>
                </div>
            </div>
        </FullScreenDrawer>
    );
};

export default SubmitApplicationModal;
