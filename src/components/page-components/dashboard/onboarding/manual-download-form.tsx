// LIBRARY IMPORTS
import { FC, useEffect, useState } from "react";

// PROJECT IMPORTS
import { Button } from "@/components/common/buttonv3";
import FileAttachment from "@/components/common/file-attachment";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { sendFeedback } from "@/functions/feedback";
import { uploadManualAccountForm } from "@/redux/actions/manualOnboardingActions";
import { openManualUploadFormApplicationDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { DocumentIcon } from "@heroicons/react/24/outline";
import { IAccountApplicationProps } from "./account-application";
import { FileUploadIcon, UploadCompletedIcon } from "./icons/icons";
import downloadFile from "./libs/download-file";

const ManualDownloadFormApplication: FC<IAccountApplicationProps> = ({ isOpen, onClose }) => {
    // const [isDownloadClicked, setIsDownloadClicked] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [file, setFile] = useState<File | null>(null);
    const dispatch = useAppDispatch();

    const { loading, success } = useAppSelector((state) => state.manualOnboarding.uploadManualAccountForm);

    const downloadSample = () => {
        setIsDownloading(true);

        try {
            downloadFile("documents/sample.pdf", "sample.pdf");
            // setIsDownloadClicked(true);
        } catch {
            sendFeedback("Failed to download sample document. Please try again.", "error");
        }

        setIsDownloading(false);
    };

    const handleFileChange = (files: FileList) => {
        if (files.length > 0) {
            setFile(files[0]);
        }
    };

    const manualFormSteps = [
        { id: "1", label: "Download form" },
        { id: "2", label: "Upload documents" },
    ];

    const handleFileUpload = async () => {
        if (file) {
            await dispatch(
                uploadManualAccountForm({
                    file,
                })
            );
        }
    };

    useEffect(() => {
        if (success) {
            sendFeedback("Account Opening Form submitted", "success");
            onClose();
            dispatch(openManualUploadFormApplicationDialog());
            setFile(null);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [success]);

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Account Application" showSupport>
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={manualFormSteps} currentStep={1} />
                </nav>
                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[500px]">
                        <div className="flex justify-center">
                            <h2
                                className="text-center text-[#151518] text-2xl max-w-[500px] font-bold leading-[30px] tracking-tight mb-10"
                                data-testid="manual-download-title"
                            >
                                Manual account opening
                            </h2>
                        </div>
                        <p className="font-bold mb-2">Follow the instructions below</p>
                        <div className="flex justify-center">
                            <div className="w-full p-6 bg-white border rounded-2xl">
                                <div className="space-y-4">
                                    <div className="flex items-start">
                                        <div className="mr-2 mt-2">
                                            <DocumentIcon className="w-6" />
                                        </div>
                                        <div className="mb-4">
                                            <h2 className="text-lg font-semibold">Download account opening form</h2>
                                            <p className="text-gray-600 text-sm mb-3">
                                                Get started by downloading our account opening form. Complete all the
                                                required sections and sign where necessary.
                                            </p>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                loading={isDownloading}
                                                onClick={downloadSample}
                                            >
                                                Download
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-start">
                                        <div className="mr-2 mt-2">
                                            <UploadCompletedIcon />
                                        </div>
                                        <div>
                                            <h2 className="text-lg font-semibold">Upload completed form</h2>
                                            <p className="text-gray-600 text-sm">
                                                Once you're done, upload the form for submission.
                                            </p>
                                        </div>
                                    </div>
                                    <div>
                                        <FileAttachment
                                            headerText=" "
                                            maxSize={10}
                                            acceptedTypes={["pdf", "png", "jpg"]}
                                            className="w-full h-auto"
                                            width="100%"
                                            icon={<FileUploadIcon />}
                                            descriptionText="We accept PDF, PNG & JPG files, up to 10MB"
                                            data-testid="file-attachment"
                                            onFilesSelected={handleFileChange}
                                            initialFile={file}
                                            onFileRemoved={() => setFile(null)}
                                        />
                                    </div>
                                    <div className="flex justify-end mt-4">
                                        <Button variant="outline" className="mr-2" onClick={onClose}>
                                            Previous
                                        </Button>
                                        <Button
                                            disabled={!file}
                                            type="button"
                                            variant="primary"
                                            size="medium"
                                            loading={loading}
                                            onClick={handleFileUpload}
                                        >
                                            Continue
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default ManualDownloadFormApplication;
