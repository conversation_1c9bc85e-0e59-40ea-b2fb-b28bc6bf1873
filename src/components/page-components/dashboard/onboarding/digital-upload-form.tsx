"use client";

import { Alert } from "@/components/common/alert";
import Badge from "@/components/common/badge";
import { Button } from "@/components/common/buttonv3";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { sendFeedback } from "@/functions/feedback";
import { openDigitalOnboardingReviewDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch } from "@/redux/hooks";
import { useFormik } from "formik";
import { Info } from "lucide-react";
import { FC, useMemo, useState } from "react";
import * as yup from "yup";
import { IAccountApplicationProps } from "./account-application";
import UploadDocumentDialog from "./components/upload-document-dialog";
import { CheckIcon } from "./icons/icons";
import { digitalOnboardingDocuments } from "./libs/mock-data";

const DigitalUploadFormApplication: FC<IAccountApplicationProps> = ({ isOpen, onClose, handlePrev, onboarding }) => {
    const dispatch = useAppDispatch();
    const [documentModalOpen, setDocumentModalOpen] = useState(false);
    const [activeField, setActiveField] = useState<{ label: string; value: string } | null>(null);
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);

    interface FormValues {
        businessCategory: string;
        [key: string]: string | null;
    }

    // Fetched initial document values from onboarding prop
    const formik = useFormik<FormValues>({
        initialValues: useMemo(
            () => ({
                businessCategory: "",
                ...Object.fromEntries(
                    digitalOnboardingDocuments.map((doc) => [
                        doc.value,
                        onboarding?.documents?.find((item) => item.documentType === doc.value)?.filePath ?? "",
                    ])
                ),
            }),
            [onboarding]
        ),
        validationSchema: yup.object({
            ...Object.fromEntries(
                digitalOnboardingDocuments.map((doc) => [doc.value, yup.string().required(`${doc.label} is required`)])
            ),
        }),
        onSubmit: () => {
            sendFeedback("Submitted successfully", "success");
            dispatch(openDigitalOnboardingReviewDialog());
        },
        enableReinitialize: true,
    });

    const handleUpload = (field: { label: string; value: string }) => {
        setActiveField(field);
        setDocumentModalOpen(true);
    };

    const handleSubmit = () => {
        sendFeedback("Submitted successfully", "success");
        dispatch(openDigitalOnboardingReviewDialog());
    };

    const handleUploadSuccess = (fieldName: string, fileName: string) => {
        formik.setFieldValue(fieldName, fileName);
        formik.validateField(fieldName);
        setDocumentModalOpen(false);
    };

    const isAllUploaded = digitalOnboardingDocuments.every((doc) => !!formik.values[doc.value]);

    const digitalFormSteps = [
        { id: "1", label: "Business Information" },
        { id: "2", label: "Business Owners" },
        { id: "3", label: "Business Documents" },
        { id: "4", label: "Review" },
    ];

    const handleExit = () => {
        setShowExitConfirmation(true);
    };

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleExit}
            title="Account Application"
            showSupport
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={() => {
                onClose();
                setShowExitConfirmation(false);
            }}
            onCancelExit={() => setShowExitConfirmation(false)}
        >
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={digitalFormSteps} currentStep={3} />
                </nav>

                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[500px]">
                        <h2 className="text-center text-[#151518] text-2xl font-bold leading-[30px] tracking-tight mb-10">
                            Upload Documents
                        </h2>

                        <div className="w-full !mb-7">
                            <Alert
                                variant="neutral"
                                heading=""
                                supportingText="As a Private Incorporated Company, you are required to submit the documents below for your account opening application."
                                showCloseButton={false}
                            />
                        </div>

                        <form onSubmit={formik.handleSubmit} className="w-full">
                            <div className="w-full border rounded-lg p-4">
                                <div className="flex justify-between items-center border-b pb-2 mb-2">
                                    <h3 className="text-lg font-semibold">Required Documents</h3>
                                    <Badge
                                        text={`${digitalOnboardingDocuments.filter((doc) => !!formik.values[doc.value]).length}/${digitalOnboardingDocuments.length} uploaded`}
                                        color="brand"
                                    />
                                </div>
                                <ul className="space-y-2">
                                    {digitalOnboardingDocuments.map((doc) => (
                                        <li key={doc.value} className="flex justify-between items-center text-sm">
                                            <span className="capitalize">{doc.label}</span>
                                            <Button
                                                leftIcon={formik.values[doc.value] ? <CheckIcon /> : null}
                                                variant={"text-primary"}
                                                type="button"
                                                className="my-1 font-medium  disabled:!text-primary disabled:!opacity-100"
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    handleUpload(doc);
                                                }}
                                                // disabled={!!formik.values[doc.value]} // allowed for edit
                                            >
                                                {formik.values[doc.value] ? "Uploaded" : "Upload"}
                                            </Button>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div className="justify-between items-center gap-3 flex mt-3">
                                <div className="flex space-y-2">
                                    <div className="mt-2 mr-1">
                                        <Info className="w-4 h-4" />
                                    </div>

                                    <p className="text-xs">Upload all required documents to continue</p>
                                </div>
                            </div>
                            <div className="flex justify-end mt-8">
                                <Button type="button" variant="outline" className="mr-2" onClick={handlePrev}>
                                    Previous
                                </Button>
                                <Button
                                    type="submit"
                                    onClick={handleSubmit}
                                    variant="primary"
                                    size="medium"
                                    disabled={!isAllUploaded}
                                >
                                    Submit
                                </Button>
                            </div>
                        </form>
                    </div>

                    <UploadDocumentDialog
                        open={documentModalOpen}
                        onClose={() => setDocumentModalOpen(false)}
                        headerText={activeField ? activeField.label : ""}
                        activeField={activeField}
                        setActiveField={setActiveField}
                        onUploadSuccess={handleUploadSuccess}
                        businessCategory={formik.values.businessCategory}
                        isDigitalUpload
                        onboarding={onboarding}
                    />
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default DigitalUploadFormApplication;
