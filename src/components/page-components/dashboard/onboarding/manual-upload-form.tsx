"use client";

import Badge from "@/components/common/badge";
import { But<PERSON> } from "@/components/common/buttonv3";
import Dropdown from "@/components/common/dropdown";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { sendFeedback } from "@/functions/feedback";
import { concludeManualOnboarding } from "@/redux/actions/manualOnboardingActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useFormik } from "formik";
import { Info } from "lucide-react";
import { FC, useMemo, useState } from "react";
import * as yup from "yup";
import { IAccountApplicationProps } from "./account-application";
import SubmitApplicationModal from "./components/submit-application-modal";
import UploadDocumentDialog from "./components/upload-document-dialog";
import { CheckIcon } from "./icons/icons";
import { businessRegistrationTypes, manualOnboardingDocuments } from "./libs/mock-data";

const ManualUploadFormApplication: FC<IAccountApplicationProps> = ({ isOpen, onClose }) => {
    const [documentModalOpen, setDocumentModalOpen] = useState(false);
    const [submitModalOpen, setSubmitModalOpen] = useState(false);
    const [activeField, setActiveField] = useState<{ label: string; value: string } | null>(null);
    const dispatch = useAppDispatch();
    const onboardingData = useAppSelector((state) => state.onboarding.businessInfoResponse.data);

    interface FormValues {
        businessCategory: string;
        [key: string]: string | null;
    }

    const formik = useFormik<FormValues>({
        initialValues: useMemo(
            () => ({
                businessCategory: "",
                ...Object.fromEntries(
                    manualOnboardingDocuments.map((doc) => [
                        doc.value,
                        onboardingData?.documents?.find((item) => item.documentType === doc.value)?.filePath ?? "",
                    ])
                ),
            }),
            [onboardingData]
        ),
        validationSchema: yup.object({
            businessCategory: yup.string().required("Business category is required"),
            ...Object.fromEntries(
                manualOnboardingDocuments.map((doc) => [doc.value, yup.string().required(`${doc.label} is required`)])
            ),
        }),
        onSubmit: async () => {
            sendFeedback("Manual onboarding completed successfully", "success");
            setSubmitModalOpen(true);
            await dispatch(concludeManualOnboarding());
        },
        enableReinitialize: true,
    });

    const handleUpload = (field: { label: string; value: string }) => {
        setActiveField(field);
        setDocumentModalOpen(true);
    };

    const handleUploadSuccess = (fieldName: string, fileName: string) => {
        formik.setFieldValue(fieldName, fileName);
    };

    const isAllUploaded = manualOnboardingDocuments.every((doc) => formik.values[doc.value]);

    return (
        <FullScreenDrawer isOpen={isOpen} onClose={onClose} title="Account Application" showSupport>
            <div className="flex min-h-fit relative flex-1">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper
                        steps={[
                            { id: "1", label: "Download form" },
                            { id: "2", label: "Upload documents" },
                        ]}
                        currentStep={2}
                    />
                </nav>

                <main className="flex-1 px-14 py-8 overflow-auto  flex flex-col items-center">
                    <div className="w-full max-w-[500px]">
                        <h2 className="text-center text-[#151518] text-2xl font-bold leading-[30px] tracking-tight mb-10">
                            Upload Documents
                        </h2>

                        <div className="w-full space-y-7">
                            <Dropdown
                                options={businessRegistrationTypes}
                                name="businessCategory"
                                label="Business category"
                                size="sm"
                                className="mb-5"
                                formik={formik}
                            />
                        </div>

                        <form onSubmit={formik.handleSubmit} className="w-full">
                            <div className="w-full border rounded-lg p-4">
                                <div className="flex justify-between items-center border-b pb-2 mb-2">
                                    <h3 className="text-lg font-semibold">Required Documents</h3>
                                    <Badge
                                        text={`${manualOnboardingDocuments.filter((doc) => formik.values[doc.value]).length}/${manualOnboardingDocuments.length} uploaded`}
                                        color="brand"
                                    />
                                </div>
                                <ul className="space-y-2">
                                    {manualOnboardingDocuments.map((doc) => (
                                        <li key={doc.value} className="flex justify-between items-center text-sm">
                                            <span className="capitalize">{doc.label}</span>
                                            <Button
                                                leftIcon={formik.values[doc.value] ? <CheckIcon /> : null}
                                                variant={"text-primary"}
                                                className="my-1 disabled:!text-primary disabled:!opacity-100 font-medium"
                                                onClick={() => handleUpload(doc)}
                                                disabled={!!formik.values[doc.value]}
                                            >
                                                {formik.values[doc.value] ? "Uploaded" : "Upload"}
                                            </Button>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div className="justify-between items-center gap-3 flex mt-6">
                                <div className="flex space-y-2">
                                    <div className="mt-2 mr-1">
                                        <Info className="w-5 h-5" />
                                    </div>

                                    <p className="text-sm">Upload all required documents to continue</p>
                                </div>
                                <Button type="submit" disabled={!isAllUploaded} variant="primary" size="medium">
                                    Proceed
                                </Button>
                            </div>
                        </form>
                    </div>

                    <UploadDocumentDialog
                        open={documentModalOpen}
                        onClose={() => setDocumentModalOpen(false)}
                        headerText={activeField ? activeField.label : ""}
                        activeField={activeField}
                        setActiveField={setActiveField}
                        onUploadSuccess={handleUploadSuccess}
                        businessCategory={formik.values.businessCategory}
                        isDigitalUpload={false}
                    />

                    <SubmitApplicationModal
                        open={submitModalOpen}
                        onClose={() => {
                            setSubmitModalOpen(false);
                            onClose();
                        }}
                    />
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default ManualUploadFormApplication;
