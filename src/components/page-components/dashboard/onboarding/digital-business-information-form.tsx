import { But<PERSON> } from "@/components/common/buttonv3";
import DatePicker from "@/components/common/date-picker";
import Dropdown from "@/components/common/dropdown";
import FileAttachment from "@/components/common/file-attachment";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { Label } from "@/components/common/label";
import LabelInput from "@/components/common/label-input";
import { RadioGroup, RadioGroupItem } from "@/components/common/radio-group";
import Stepper from "@/components/common/stepper";
import LabelTextArea from "@/components/common/text-area";
import Tooltip from "@/components/common/tooltip";
import { InfoIcon } from "@/components/icons/auth";
import { formatDateForStorage } from "@/functions/date";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { findOptionByValue } from "@/functions/stringManipulations";
import {
    businessInformationAccountForm,
    getForeignCountries,
    updateBusinessInformationAccountForm,
} from "@/redux/actions/digitalOnboardingAction";
import { openDigitalBusinessOwnersFormDialog } from "@/redux/features/uiDialogSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { BusinessInformationAccountForm, ProofOfAddressField } from "@/redux/types/digital-onboarding";
import { useFormik } from "formik";
import { HelpCircleIcon } from "lucide-react";
import { FC, useEffect, useMemo, useState } from "react";
import * as yup from "yup";
import { setBusinessInfoBody } from "../../../../redux/slices/onboardingSlice";
import { OptionProp } from "../outgoing/types";
import { IAccountApplicationProps } from "./account-application";
import {
    businessCategories,
    businessIndustry,
    businessRegistrationTypes,
    nigeriaStatesAndCities,
} from "./libs/mock-data";

export const mapStringToDate = (value: string | Date | null | undefined): Date | null => {
    if (!value) return null;

    const date = new Date(value);

    return isNaN(date.getTime()) ? null : date;
};

const DigitalBusinessInformationForm: FC<IAccountApplicationProps> = ({ isOpen, onClose, handlePrev, onboarding }) => {
    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState<boolean>(false);
    const [countries, setCountries] = useState<OptionProp[]>([]);
    const [showExitConfirmation, setShowExitConfirmation] = useState(false);

    const taxIdentificationNumber = useAppSelector((state) => state.onboarding.taxIdentificationNumber);
    const businessRegistrationNumber = useAppSelector((state) => state.onboarding.businessRegistrationNumber);
    const businessName = useAppSelector((state) => state.onboarding.businessName);

    const handleFileChange = (files: FileList) => {
        formik.setFieldTouched("proofOfAddress", true);
        if (files.length > 0) {
            const file = files[0];
            formik.setFieldValue("proofOfAddress", file);
        }
    };

    useEffect(() => {
        const fetchCountries = async () => {
            if (taxIdentificationNumber) {
                formik.setFieldValue("taxIdentificationNumber", taxIdentificationNumber);
            }

            if (businessRegistrationNumber) {
                formik.setFieldValue("businessRegistrationNumber", businessRegistrationNumber);
            }
            const resultAction = await dispatch(getForeignCountries());

            if (getForeignCountries.fulfilled.match(resultAction)) {
                const rawCountries = resultAction.payload as { name: string }[];

                let transformed = rawCountries.map((item) => ({
                    label: item.name,
                    value: item.name,
                }));

                const hasNigeria = transformed.some((item) => item.value.toLowerCase() === "nigeria");

                if (!hasNigeria) {
                    transformed.unshift({ label: "Nigeria", value: "Nigeria" });
                } else {
                    transformed = [
                        transformed.find((item) => item.value.toLowerCase() === "nigeria")!,
                        ...transformed.filter((item) => item.value.toLowerCase() !== "nigeria"),
                    ];
                }

                setCountries(transformed);
            } else {
                sendCatchFeedback("Failed to fetch countries");
            }
        };

        fetchCountries();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch]);

    const digitalFormSteps = [
        { id: "1", label: "Business Information" },
        { id: "2", label: "Business Owners" },
        { id: "3", label: "Business Documents" },
        { id: "4", label: "Review" },
    ];

    const formik = useFormik<BusinessInformationAccountForm & { proofOfAddress: ProofOfAddressField }>({
        initialValues: {
            legalBusinessName: onboarding?.legalBusinessName || businessName || "",
            isBusinessNameTradingName: onboarding?.isBusinessNameTradingName ?? true,
            tradingName: onboarding?.tradingName ?? "",
            countryOfRegistration: onboarding?.countryOfRegistration ?? "",
            businessIndustry: onboarding?.businessIndustry ?? "",
            businessCategory: onboarding?.businessCategory ?? "",
            businessDescription: onboarding?.businessDescription ?? "",
            businessRegistrationType: onboarding?.registrationType ?? "",
            businessEmailAddress: onboarding?.businessEmail ?? "",
            businessWebsite: onboarding?.businessWebsite ?? "",
            businessAddress: onboarding?.businessAddress ?? "",
            proofOfAddress: onboarding?.proofOfAddressPath
                ? {
                      name: onboarding.proofOfAddressPath.split("/").pop() ?? "proof_of_address",
                      size: 0,
                      type: "application/octet-stream",
                  }
                : null,
            corporateId: onboarding?.corporateId?.toString() ?? "",
            registrationType: onboarding?.registrationType ?? "",
            businessEmail: onboarding?.businessEmail ?? "",
            businessRegistrationNumber: onboarding?.businessRegistrationNumber ?? "",
            taxIdentificationNumber: onboarding?.taxIdentificationNumber ?? "",
            isTradingNameSameAsBusinessName: onboarding?.isTradingNameSameAsBusinessName ?? true,
            yearOfRegistration: onboarding?.yearOfRegistration ?? "",
            city: onboarding?.city ?? "",
            state: onboarding?.state ?? "",
            isAddressDifferentFromOperational: onboarding?.isAddressDifferentFromOperational ?? false,
            operationalAddress: onboarding?.operationalAddress ?? "",
        },
        onSubmit: (values) => {
            submitValues(values);
        },
        validationSchema: yup.object({
            legalBusinessName: yup.string().required("Business name is required"),
            isBusinessNameTradingName: yup.boolean().required("Please provide an answer"),
            tradingName: yup
                .string()
                .when(["isBusinessNameTradingName"], ([isBusinessNameTradingName], schema) =>
                    isBusinessNameTradingName === false
                        ? schema.required("Trading name is required")
                        : schema.notRequired()
                ),
            countryOfRegistration: yup.string().required("Country of registration is required"),
            businessIndustry: yup.string().required("Business industry is required"),
            businessCategory: yup.string().required("Business category is required"),
            businessDescription: yup.string().required("Business description is required"),
            yearOfRegistration: yup.string().required("Year of Registration is required"),
            businessRegistrationType: yup.string().required("Business registration type is required"),
            businessEmailAddress: yup
                .string()
                .email("Please enter a valid email address")
                .required("Business email address is required"),
            businessAddress: yup.string().required("Business address is required"),
            isAddressDifferentFromOperational: yup.boolean().required("Please provide an answer"),
            operationalAddress: yup
                .string()
                .when(["isAddressDifferentFromOperational"], ([isAddressDifferent], schema) =>
                    isAddressDifferent === true
                        ? schema.required("Operational address is required")
                        : schema.notRequired()
                ),
            city: yup.string().required("City is required"),
            state: yup.string().required("State is required"),
            proofOfAddress: yup.mixed().required("Proof of address is required"),
        }),
        enableReinitialize: true,
    });

    const submitValues = async (values: BusinessInformationAccountForm) => {
        setLoading(true);
        const formattedValues = {
            ...values,
            proofOfAddress: values.proofOfAddress
                ? {
                      name: values.proofOfAddress.name,
                      size: values.proofOfAddress.size,
                      type: values.proofOfAddress.type,
                  }
                : null,
            yearOfRegistration: formatDateForStorage(
                values.yearOfRegistration ? new Date(values.yearOfRegistration) : undefined
            ),
        };

        dispatch(setBusinessInfoBody(formattedValues as BusinessInformationAccountForm));

        const errors = await formik.validateForm();

        if (Object.keys(errors).length === 0) {
            try {
                // In the time of edit
                if (onboarding?.businessInfoCompleted) {
                    sendFeedback("Updated Successfully", "success");
                    setLoading(false);
                    await dispatch(updateBusinessInformationAccountForm({ ...values, onboarding })).unwrap();
                    return dispatch(openDigitalBusinessOwnersFormDialog());
                }

                const response = await dispatch(businessInformationAccountForm(values)).unwrap();
                dispatch(setBusinessInfoBody(response.data as BusinessInformationAccountForm));

                sendFeedback("Submitted Successfully", "success");
                setLoading(false);
                dispatch(openDigitalBusinessOwnersFormDialog());
                formik.resetForm();
            } catch (error) {
                setLoading(false);
                sendCatchFeedback(error ?? "Submission failed. Please try again.");
            }
        } else {
            Object.keys(errors).forEach((field) => {
                formik.setFieldTouched(field, true, false);
            });
            setLoading(false);
            sendCatchFeedback("Please fill in all required fields correctly");
        }
    };

    const handleExit = () => {
        setShowExitConfirmation(true);
    };

    const isButtonDisabled = Object.keys(formik.errors).length > 0;

    const stateOptions = useMemo(
        () =>
            Object.keys(nigeriaStatesAndCities).map((item) => ({
                label: item,
                value: item,
            })),
        []
    );

    const cityOptions = useMemo(
        () =>
            formik.values.state && nigeriaStatesAndCities[formik.values.state as keyof typeof nigeriaStatesAndCities]
                ? (nigeriaStatesAndCities[formik.values.state as keyof typeof nigeriaStatesAndCities] || []).map(
                      (item) => ({
                          label: item,
                          value: item,
                      })
                  )
                : [],
        [formik.values.state]
    );

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleExit}
            title="Account Application"
            showSupport
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={() => {
                onClose();
                setShowExitConfirmation(false);
            }}
            onCancelExit={() => setShowExitConfirmation(false)}
        >
            <div className="flex min-h-fit relative flex-1 overflow-y-auto">
                <nav className="absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={digitalFormSteps} currentStep={1} />
                </nav>
                <main className="flex-1 px-14 py-8 flex flex-col items-center">
                    <div className="w-full max-w-[500px]">
                        <div className="flex justify-center">
                            <h2
                                className="text-center text-[#151518] text-2xl max-w-[500px] font-bold leading-[30px] tracking-tight mb-10"
                                data-testid="digital-business-information-form-title"
                            >
                                Business Information
                            </h2>
                        </div>
                        <div className="flex items-center justify-center">
                            <div className="min-h-[516px] flex-col justify-start items-start gap-8 inline-flex w-full overflow-y-auto">
                                <form
                                    onSubmit={formik.handleSubmit}
                                    className="self-stretch flex-col justify-end gap-8 flex"
                                >
                                    <div className="space-y-5">
                                        <LabelInput
                                            formik={formik}
                                            name="legalBusinessName"
                                            label="Legal Business Name"
                                            type="text"
                                            showError={formik.touched.legalBusinessName}
                                            disabled // prefilled from TIN and RC validation
                                        />
                                        <div>
                                            <h4 className="font-semibold text-xs mb-5">
                                                Is this the same as the trading name for your business?
                                            </h4>
                                            <RadioGroup
                                                defaultValue="comfortable"
                                                className="flex flex-col gap-4"
                                                value={formik.values.isBusinessNameTradingName ? "yes" : "no"}
                                                onValueChange={(value) =>
                                                    formik.setFieldValue("isBusinessNameTradingName", value === "yes")
                                                }
                                            >
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="yes" id="r1" />
                                                    <Label htmlFor="r1" className="text-xs">
                                                        Yes
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="no" id="r2" />
                                                    <Label htmlFor="r2" className="text-xs">
                                                        No
                                                    </Label>
                                                </div>
                                            </RadioGroup>
                                        </div>
                                        {!formik.values.isBusinessNameTradingName && (
                                            <LabelInput
                                                formik={formik}
                                                name="tradingName"
                                                label="Trading Name"
                                                type="text"
                                                showError={formik.touched.tradingName}
                                            />
                                        )}
                                        <Dropdown
                                            options={countries}
                                            defaultValue={findOptionByValue(
                                                formik.values.countryOfRegistration,
                                                countries
                                            )}
                                            name="countryOfRegistration"
                                            label="Country of registration"
                                            size="sm"
                                            formik={formik}
                                            showError={formik.touched.countryOfRegistration}
                                        />
                                        <Dropdown
                                            options={businessIndustry}
                                            defaultValue={findOptionByValue(
                                                formik.values.businessIndustry,
                                                businessIndustry
                                            )}
                                            name="businessIndustry"
                                            label="Business Industry"
                                            size="sm"
                                            formik={formik}
                                            showError={formik.touched.businessIndustry}
                                        />
                                        <Dropdown
                                            options={businessCategories}
                                            defaultValue={findOptionByValue(
                                                formik.values.businessCategory,
                                                businessCategories
                                            )}
                                            name="businessCategory"
                                            label="Business Category"
                                            size="sm"
                                            formik={formik}
                                            showError={formik.touched.businessCategory}
                                        />
                                        <LabelTextArea
                                            label="Business Description"
                                            name="businessDescription"
                                            formik={formik}
                                            placeholder="Write a brief description of your business here"
                                            className="text-sm"
                                        />
                                        <DatePicker
                                            label="Year of Registration"
                                            value={
                                                formik.values.yearOfRegistration
                                                    ? new Date(formik.values.yearOfRegistration)
                                                    : undefined
                                            }
                                            onChange={(value) => {
                                                formik.setFieldValue("yearOfRegistration", value?.toISOString());
                                            }}
                                            error={
                                                formik.touched.yearOfRegistration && formik.errors.yearOfRegistration
                                                    ? formik.errors.yearOfRegistration
                                                    : undefined
                                            }
                                            placeholder="Select year of registration"
                                            disableFuture
                                        />
                                        <Dropdown
                                            options={businessRegistrationTypes}
                                            defaultValue={findOptionByValue(
                                                formik.values.businessRegistrationType,
                                                businessRegistrationTypes
                                            )}
                                            name="businessRegistrationType"
                                            label="Business registration type"
                                            size="sm"
                                            formik={formik}
                                            showError={formik.touched.businessRegistrationType}
                                        />
                                        <LabelInput
                                            formik={formik}
                                            name="businessEmailAddress"
                                            label="Business email address"
                                            type="email"
                                            showError={formik.touched.businessEmailAddress}
                                        />
                                        <LabelInput
                                            formik={formik}
                                            name="businessWebsite"
                                            label="Business website"
                                            type="text"
                                            showError={formik.touched.businessWebsite}
                                        />
                                        <LabelInput
                                            formik={formik}
                                            name="businessAddress"
                                            label="Business address"
                                            type="text"
                                            showError={formik.touched.businessAddress}
                                        />

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                                            <Dropdown
                                                options={stateOptions}
                                                defaultValue={findOptionByValue(formik.values.state, stateOptions)}
                                                name="state"
                                                label="State"
                                                size="sm"
                                                formik={formik}
                                                showError={formik.touched.state}
                                            />
                                            <Dropdown
                                                options={cityOptions}
                                                defaultValue={findOptionByValue(formik.values.city, cityOptions)}
                                                name="city"
                                                label="City"
                                                size="sm"
                                                formik={formik}
                                                showError={formik.touched.city}
                                            />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-xs mb-5">
                                                Is this address different from the operational address?
                                            </h4>
                                            <RadioGroup
                                                defaultValue="comfortable"
                                                className="flex flex-col gap-4"
                                                value={formik.values.isAddressDifferentFromOperational ? "yes" : "no"}
                                                onValueChange={(value) =>
                                                    formik.setFieldValue(
                                                        "isAddressDifferentFromOperational",
                                                        value === "yes"
                                                    )
                                                }
                                            >
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="yes" id="r1" />
                                                    <Label htmlFor="r1" className="text-xs">
                                                        Yes
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="no" id="r2" />
                                                    <Label htmlFor="r2" className="text-xs">
                                                        No
                                                    </Label>
                                                </div>
                                            </RadioGroup>
                                        </div>
                                        {formik.values.isAddressDifferentFromOperational && (
                                            <LabelInput
                                                formik={formik}
                                                name="operationalAddress"
                                                label="Operational Address"
                                                type="text"
                                                showError={formik.touched.operationalAddress}
                                            />
                                        )}
                                        <div className="relative">
                                            <FileAttachment
                                                initialFile={
                                                    formik.values.proofOfAddress instanceof File
                                                        ? formik.values.proofOfAddress
                                                        : null
                                                }
                                                storedFile={
                                                    formik.values.proofOfAddress &&
                                                    !(formik.values.proofOfAddress instanceof File)
                                                        ? {
                                                              name: formik.values.proofOfAddress.name,
                                                              size: formik.values.proofOfAddress.size,
                                                          }
                                                        : null
                                                }
                                                headerText={
                                                    <div className="flex items-center gap-[6px]">
                                                        Upload proof of address
                                                        <Tooltip
                                                            position="right"
                                                            label="Examples include; bank statement, utility bill, tax assessment, or government issued letter"
                                                        >
                                                            <HelpCircleIcon size={12} color="#151519" />
                                                        </Tooltip>
                                                    </div>
                                                }
                                                maxSize={10}
                                                acceptedTypes={["pdf", "png", "jpg"]}
                                                className="w-full h-auto"
                                                width="100%"
                                                onFilesSelected={handleFileChange}
                                                onFileRemoved={() => formik.setFieldValue("proofOfAddress", null)}
                                            />
                                            {formik.touched.proofOfAddress && formik.errors.proofOfAddress && (
                                                <div className="flex items-center mt-2 gap-[0.25rem] text-[14px] font-medium leading-[18px] tracking-[0.28px] text-error">
                                                    <InfoIcon data-testid="info-icon" />
                                                    <span className="text-[14px] font-medium text-error leading-[18px] tracking-[0.28px]">
                                                        {formik.errors.proofOfAddress}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex justify-end mt-8">
                                        <Button variant="outline" className="mr-2" onClick={handlePrev}>
                                            Previous
                                        </Button>
                                        <Button
                                            loading={loading}
                                            disabled={isButtonDisabled}
                                            type="submit"
                                            variant="primary"
                                            size="medium"
                                        >
                                            Continue
                                        </Button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default DigitalBusinessInformationForm;
