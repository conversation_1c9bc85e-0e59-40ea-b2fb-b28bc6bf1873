import { But<PERSON> } from "@/components/common/buttonv3";
import CloseX from "@/components/common/close-x";
import SideDrawer from "@/components/common/drawer";
import Dropdown from "@/components/common/dropdown";
import FileAttachment from "@/components/common/file-attachment";
import LabelTextArea from "@/components/common/text-area";
import { sendFeedback } from "@/functions/feedback";
import { useFormik } from "formik";
import { FC, useMemo, useState } from "react";
import * as yup from "yup";
import { feedbackCategoryOptions } from "./data";

interface Props {
    isOpen: boolean;
    onClose: () => void;
}

const FeedbackModal: FC<Props> = ({ isOpen, onClose }) => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                documentType: "",
                account: "",
                chequeSize: "",
                feedbackCategory: "",
                feedback: "",
            }),
            []
        ),
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            documentType: yup.string().required("Required"),
            account: yup.string().required("Required"),
            chequeSize: yup.string().required("Required"),
            feedbackCategory: yup.string().required("Required"),
        }),
        enableReinitialize: true,
    });

    const submitValues = async () => {
        //   Close this modal
        sendFeedback("Thank you! Your feedback has been submitted.");
        onClose();
    };

    return (
        <SideDrawer isOpen={isOpen}>
            <div className="flex flex-col h-full" data-testid="FeedbackModal">
                {/* Header */}
                <div>
                    <div className="border-b border-[#E3E5E8] p-4 flex justify-between items-start">
                        <h2 className="font-semibold text-black">
                            <span className="text-[#90909D]">Support</span> {">"} Leave feedback
                        </h2>
                        <CloseX onClick={onClose} data-testid="close-button" color="#90909D" />
                    </div>
                </div>

                {/* Content */}
                <div className="flex flex-col py-8 h-full">
                    <p className="text-subText text-sm leading-[18px] mb-10 px-6">
                        Help us build a better banking experience. We'd love to hear your thoughts on improving FCMB
                        Corporate banking.
                    </p>
                    <form onSubmit={formik.handleSubmit} className="flex flex-col h-full">
                        <div className="px-6 flex flex-col gap-5 ">
                            <Dropdown
                                options={feedbackCategoryOptions.map(({ label, value }) => ({
                                    label,
                                    value,
                                }))}
                                name="feedbackCategory"
                                value={{
                                    label: formik.values.feedbackCategory,
                                    value: formik.values.feedbackCategory,
                                }}
                                formik={formik}
                                label="What feedback would you like to leave us?"
                                data-testid="feedback-category-dropdown"
                            />
                            <LabelTextArea
                                formik={formik}
                                name="feedback"
                                label="Share feedback"
                                rows={4}
                                placeholder="Tell us how we can improve..."
                            />

                            <FileAttachment
                                maxSize={10}
                                acceptedTypes={["pdf", "png", "jpg"]}
                                onFilesSelected={(files) => setSelectedFile(files[0])}
                                width="100%"
                                headerText="Attachments (optional)"
                                onFileRemoved={() => setSelectedFile(null)}
                            />
                        </div>

                        <div className="flex justify-center mt-auto border-t border-t-[#E3E5E8] py-4 px-6">
                            <Button
                                type="submit"
                                size="medium"
                                data-testid="submit-feedback-button"
                                className="!w-full"
                            >
                                Submit feedback
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </SideDrawer>
    );
};

export default FeedbackModal;
