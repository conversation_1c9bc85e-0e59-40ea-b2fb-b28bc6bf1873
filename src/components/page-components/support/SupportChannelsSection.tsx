import { Button } from "@/components/common/buttonv3";
import { supportChannels } from "./data";
import { TimeIcon } from "./icons";

const SupportChannelsSection = () => (
    <div className="p-8 pb-0">
        <h3 className="text-sm font-semibold">Support channels</h3>
        <div className="mt-5 mb-8 flex flex-col gap-4">
            {supportChannels.map((channel) => (
                <div key={channel.channel} className="flex items-center justify-between w-full">
                    <div className="flex gap-3 items-center">
                        <p className="text-sm">{channel.channel}</p>
                        <div className="bg-[#ECFDF3] px-[6px] py-[5px] flex items-center justify-center rounded-full gap-1">
                            <TimeIcon />
                            <span className="text-success font-medium text-xs">{channel.duration}</span>
                        </div>
                    </div>
                    <Button
                        className="px-[14px] py-2 items-center justify-center flex gap-[6px] bg-white border border-[#DBDBE1] rounded-lg"
                        onClick={channel.action}
                        variant="outline"
                        leftIcon={channel.icon}
                        disabled={channel.disabled}
                    >
                        {channel.actionText}
                    </Button>
                </div>
            ))}
        </div>
        <div className="bg-[#E3E5E8] h-[1px] w-full" />
    </div>
);

export default SupportChannelsSection;
