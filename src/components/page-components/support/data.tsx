import { ReactNode } from "react";
import { ChatIcon, EmailIcon, PhoneIcon } from "./icons";

interface SupportChannelType {
    channel: string;
    duration: string;
    icon: ReactNode;
    actionText: string;
    action: () => void;
    disabled?: boolean;
}

export const supportChannels: SupportChannelType[] = [
    {
        channel: "Live chat",
        duration: "1 min",
        icon: <ChatIcon />,
        actionText: "Chat now",
        action: () => null,
        disabled: true,
    },
    {
        channel: "Call",
        duration: "1 min",
        icon: <PhoneIcon />,
        actionText: "07003290000",
        action: () => window.open("tel:07003290000", "WindowName", "noopener"),
    },
    {
        channel: "Whatsapp",
        duration: "2 min",
        icon: <ChatIcon />,
        actionText: "Chat now",
        action: () => window.open("https://wa.me/+2349099999815", "WindowName", "noopener"),
    },
    {
        channel: "Email",
        duration: "1 min",
        icon: <EmailIcon />,
        actionText: "Send an email",
        action: () => window.open("mailto:<EMAIL>", "WindowName", "noopener"),
    },
];

export interface FAQType {
    question: string;
    answer: string;
}

export const FAQs: FAQType[] = [
    {
        question: "I'm not getting a verification email",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
    {
        question: "I can't set up 2FA",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
    {
        question: "My verification code isn't working",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
    {
        question: "I'm having issues logging in",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
    {
        question: "Forgot my security question",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
    {
        question: "I'm not getting a verification email",
        answer: "Be sure to check your spam folder. Important emails can get lost there sometimes.",
    },
];

export const bankDocumentTypeOptions = [
    {
        label: "Cheque book",
        value: "Cheque book",
    },
    {
        label: "Reference letter",
        value: "Reference letter",
    },
    {
        label: "Letter of indebtedness",
        value: "Letter of indebtedness",
    },
];

export const accountOptions = [
    {
        label: "Main account - 01******9245",
        value: "Main account - 01******9245",
    },
    {
        label: "Sub account - 01******7890",
        value: "Sub account - 01******7890",
    },
];

export const collectionBranchOptions = [
    {
        label: "FCMB, Lekki Admiralty way",
        value: "FCMB, Lekki Admiralty way",
    },
];

export const checkSizeOptions = [
    {
        label: "50 leaves",
        value: "50 leaves",
    },
    {
        label: "100 leaves",
        value: "100 leaves",
    },
    {
        label: "200 leaves",
        value: "200 leaves",
    },
];

export type BankRequestFormikValues = {
    documentType: string;
    account: string;
    chequeSize: string;
    collectionBranch: string;
    additionalInstructions: string;
};

export const feedbackCategoryOptions = [
    {
        label: "Category 1",
        value: "Category 1",
    },
];
