"use client";

import SideDrawer from "@/components/common/drawer";
import { closeSupportDialog } from "@/redux/features/supportDialog";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import RelationshipManagerSection from "./RelationshipManagerSection";
import SupportChannelsSection from "./SupportChannelsSection";
import CloseX from "@/components/common/close-x";

const SupportDialog = () => {
    const { isOpen } = useAppSelector((state) => state.supportDialog);
    const dispatch = useAppDispatch();

    return (
        <SideDrawer isOpen={isOpen}>
            <div className="flex flex-col h-full">
                {/* Header */}
                <div>
                    <div className="border-b border-[#E3E5E8] p-4 flex justify-between items-center">
                        <h2 className="text-xl font-semibold text-black">Support</h2>
                        <CloseX
                            onClick={() => dispatch(closeSupportDialog())}
                            data-testid="close-button"
                            color="#90909D"
                        />
                    </div>
                </div>
                {/* Sections */}
                {/* <QuickAnswerSection /> */}
                {/* <BankDocumentsSection /> */}
                <SupportChannelsSection />
                <RelationshipManagerSection />
                {/* <FeedbackSection /> */}
            </div>
        </SideDrawer>
    );
};

export default SupportDialog;
