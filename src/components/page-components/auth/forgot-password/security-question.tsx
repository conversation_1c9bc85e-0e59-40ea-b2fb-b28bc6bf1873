import { userAxios } from "@/api/axios";
import { But<PERSON> } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { handleError } from "@/lib/utils";
import { validateUserSecurityQuestion } from "@/redux/actions/auth/signinActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { PATH_AUTH } from "@/routes/path";
import { useFormik } from "formik";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import * as yup from "yup";

const SecurityQuestion = () => {
    const params = useSearchParams();
    const email = params.get("email") ?? "";
    const token = params.get("token") ?? "";
    const [question, setQuestion] = useState("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const {
        error: validateUserSecurityQuestionError,
        success: validateUserSecurityQuestionSuccess,
        loading: validateUserSecurityQuestionLoading,
    } = useAppSelector((state) => state.signin?.validateUserSecurityQuestion || {});
    const router = useRouter();

    const mfaMethod = params.get("mfaMethod");

    const currentStep = useAppSelector((state) => state.signin.currentLoginStep);

    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: {
            question,
            answer: "",
        },
        enableReinitialize: true,
        onSubmit: () => {
            handleFormSubmit();
        },
        validationSchema: yup.object({
            question: yup.string().required("Question is required"),
            answer: yup.string().required("Answer is required"),
        }),
    });

    const handleFormSubmit = () => {
        dispatch(
            validateUserSecurityQuestion({
                userEmail: email,
                question: question, // Using the state question which now matches formik
                answer: formik.values.answer,
            })
        );
    };

    const getRandomIndex = (length: number): number => Math.floor(Math.random() * length);

    useEffect(() => {
        const fetchQuestions = async () => {
            if (!email) {
                return;
            }
            setLoading(true);
            try {
                const response = await userAxios(`/v1/user-security-questions?email=${email}`);

                // Generate the random index once
                const randomIndex = getRandomIndex(response.data.length);
                const selectedQuestion = response.data[randomIndex].question;

                // Use the same question for both state and formik
                setQuestion(selectedQuestion);
                formik.setFieldValue("question", selectedQuestion);

                setError(null);
            } catch (error) {
                console.error("Error fetching question:", error);
                setError(handleError(error));
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [email]);

    useEffect(() => {
        if (error) {
            sendCatchFeedback(error);
        }
        if (validateUserSecurityQuestionError) {
            sendCatchFeedback(validateUserSecurityQuestionError, () =>
                dispatch(signinActions.clearState("validateUserSecurityQuestion"))
            );
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [error, validateUserSecurityQuestionError]);

    useEffect(() => {
        if (validateUserSecurityQuestionSuccess) {
            sendFeedback("Security question validated successfully", "success", () =>
                dispatch(signinActions.clearState("validateUserSecurityQuestion"))
            );
            router.push(`${PATH_AUTH.resetPassword}?email=${email}&token=${token}`);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateUserSecurityQuestionSuccess, currentStep, email]);

    if (mfaMethod !== "SECURITY_QUESTION" || !email || !token) {
        return <div>This is a broken link, please check the link you used to get here and try again.</div>;
    }

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!question) {
        return <div>No question available.</div>;
    }

    return (
        <div className="space-y-4">
            <div className="text-center mb-[40px]">
                <h2 className="text-2xl font-bold text-black">Let's make sure it's you</h2>
                <p className="text-base font-normal text-subText mt-[12px]">
                    Provide the answer to your security question below
                </p>
            </div>

            <form className="grid gap-8" onSubmit={formik.handleSubmit}>
                <LabelInput name="answer" label={question} placeholder="Enter your answer" formik={formik} />
                <Button
                    loading={validateUserSecurityQuestionLoading}
                    disabled={validateUserSecurityQuestionLoading}
                    type="submit"
                    fullWidth
                    size="lg"
                >
                    Continue
                </Button>
            </form>
        </div>
    );
};

export default SecurityQuestion;
