"use client";

import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useEffect, useState } from "react";
import OTPContainer from "../otp-container";
import { useRouter, useSearchParams } from "next/navigation";
import { useOtp } from "@/hooks/useOtp";
import { PATH_AUTH } from "@/routes/path";

const SmsVerification = () => {
    const params = useSearchParams();
    const mfaMethod = params.get("mfaMethod");
    const userPhoneNumber = params.get("phoneNumber");
    const email = params.get("email");
    const token = params.get("token");
    const router = useRouter();

    const [otpValue, setOtpValue] = useState("");
    const {
        validateOtp,
        resendOtp,
        sendOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,
        clearOtpState,
    } = useOtp();

    const handleResendOTP = async () => {
        resendOtp(String(userPhoneNumber));
    };

    const submitForm = async () => {
        validateOtp({
            receiver: String(userPhoneNumber),
            otp: otpValue,
        });
    };

    useEffect(() => {
        if (!userPhoneNumber) {
            return;
        }
        sendOtp({
            receiver: String(userPhoneNumber),
            receiverType: "SMS",
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userPhoneNumber]);

    useEffect(() => {
        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success", () => clearOtpState("resend"));
        }

        if (validateOtpSuccess) {
            sendFeedback("OTP verified successfully", "success", () => clearOtpState("validate"));
            setOtpValue("");
            router.push(`${PATH_AUTH.resetPassword}?email=${email}&token=${token}`);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateOtpSuccess, resendOtpSuccess]);

    useEffect(() => {
        if (resendOtpError) {
            sendCatchFeedback(resendOtpError, () => clearOtpState("resend"));
        }

        if (validateOtpError) {
            sendCatchFeedback(validateOtpError, () => clearOtpState("validate"));
            setOtpValue("");
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateOtpError, resendOtpError]);

    if (mfaMethod !== "SMS" || !email || !token || !userPhoneNumber) {
        return <div>This is a broken link, please check the link you used to get here and try again.</div>;
    }

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            onResend={handleResendOTP}
            loading={validateOtpLoading}
            resendLoading={resendOtpLoading}
            variant="autoVerify"
            title="Check your mobile for OTP"
            type="phone"
            subtitle="Enter the SMS OTP sent to your mobile"
            resendText="Send New Code"
        />
    );
};

export default SmsVerification;
