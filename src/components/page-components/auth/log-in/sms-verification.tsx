"use client";

import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useEffect, useState } from "react";
import OTPContainer from "../otp-container";
import { storageManager } from "@/lib/custom";
import { verifyDevice } from "@/redux/actions/auth/signinActions";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { useRouter, useSearchParams } from "next/navigation";
import { useOtp } from "@/hooks/useOtp";
import useRouteAfterLogin from "@/hooks/useRouteAfterLogin";
import { PATH_AUTH } from "@/routes/path";
import { cookies } from "@/lib/cookies";

const SmsVerification = () => {
    const params = useSearchParams();
    const mfaMethod = params.get("mfa");
    const userPhoneNumber = params.get("phone");
    const router = useRouter();
    const route = useRouteAfterLogin();

    const {
        error: verifyDeviceError,
        success: verifyDeviceSuccess,
        loading: verifyDeviceLoading,
    } = useAppSelector((state) => state.signin?.verifyDevice || {});
    const [otpValue, setOtpValue] = useState("");
    const {
        validateOtp,
        resendOtp,
        sendOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,
        clearOtpState,
        clearOtpStates,
    } = useOtp();

    const emailAddress = storageManager.getItem("preLoginData")?.username ?? "";

    const dispatch = useAppDispatch();

    const handleResendOTP = async () => {
        resendOtp(String(userPhoneNumber));
    };

    const submitForm = async () => {
        const token = cookies.getToken();
        if (token) return;

        validateOtp({
            receiver: String(userPhoneNumber),
            otp: otpValue,
            loginFlag: true,
        });
    };

    useEffect(() => {
        sendOtp({
            receiver: String(userPhoneNumber),
            receiverType: "SMS",
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userPhoneNumber]);

    useEffect(() => {
        if (validateOtpSuccess) {
            clearOtpState("validate");
            dispatch(verifyDevice(emailAddress));
        }

        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success");
        }

        if (verifyDeviceSuccess) {
            sendFeedback("Login successful", "success", () => dispatch(signinActions.clearState("verifyDevice")));
            setOtpValue("");
            clearOtpStates();
            router.replace(route);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateOtpSuccess, resendOtpSuccess, emailAddress, verifyDeviceSuccess]);

    useEffect(() => {
        if (resendOtpError) {
            sendCatchFeedback(resendOtpError);
        }

        if (validateOtpError) {
            sendCatchFeedback(validateOtpError);
            setOtpValue("");
        }

        if (verifyDeviceError) {
            sendCatchFeedback(verifyDeviceError);
            dispatch(signinActions.clearState("verifyDevice"));
            router.replace(PATH_AUTH.login);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateOtpError, resendOtpError, verifyDeviceError]);

    if (mfaMethod !== "sms") {
        return null;
    }

    if (verifyDeviceLoading) {
        return <div>Verifying Device...</div>;
    }

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            onResend={handleResendOTP}
            loading={validateOtpLoading}
            resendLoading={resendOtpLoading}
            variant="autoVerify"
            title="Check your mobile for OTP"
            type="phone"
            subtitle="Enter the SMS OTP sent to your mobile"
            resendText="Send New Code"
        />
    );
};

export default SmsVerification;
