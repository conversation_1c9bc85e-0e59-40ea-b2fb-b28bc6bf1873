"use client";

//NEXT IMPORTS
import { useRouter } from "next/navigation";

//OTHER IMPORTS
import { useFormik } from "formik";

// PROJECT IMPORTS
import { PreLoginFormType } from "@/types/auth";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/solid";
import { storageManager } from "@/lib/custom";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { useEffect, useMemo } from "react";
import * as yup from "yup";
import { useAppDispatch } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { cookies } from "@/lib/cookies";
import { openSupportDialog } from "@/redux/features/supportDialog";

const PreLoginForm = () => {
    const router = useRouter();
    const dispatch = useAppDispatch();

    const initialValues: PreLoginFormType = {
        username: "",
        action: "",
    };

    const formik = useFormik({
        initialValues: useMemo(() => initialValues, []),
        onSubmit: (value) => {
            handleRedirect(value);
        },
        validationSchema: yup.object({
            username: yup
                .string()
                .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, "Enter a valid email")
                .required("Email is required"),
        }),
    });

    const handleRedirect = (values: PreLoginFormType) => {
        // Store username in localStorage - no need to JSON.stringify as storageManager handles it
        storageManager.setItem("preLoginData", { username: values?.username });

        // Set username in Redux state
        dispatch(signinActions.setUsername(values?.username));

        // Redirect to one of the following
        switch (values?.action?.toLowerCase()) {
            case "password":
                dispatch(signinActions.setLoginStep(1));
                return router.push("/auth/login/password");
            default:
                dispatch(signinActions.setLoginStep(1));
                return router.push("/auth/login/password");
        }
    };

    useEffect(() => {
        // Clear login data from storage
        storageManager.removeItem("preLoginData");

        // Reset login flow in Redux
        dispatch(signinActions.resetLoginFlow());

        // Clear auth tokens from cookies
        cookies.clearAllTokens();
    }, [dispatch]);

    return (
        <>
            <div className="flex flex-col w-full max-w-md p-8">
                <div className="font-bold self-center text-[1.5rem] sm:text-2xl text-black">Log in to your account</div>
                <div className="mt-1 text-xs self-center items-center sm:text-sm text-gray-500">
                    Log in with your email address or use a code.
                </div>
                <div className="mt-5">
                    <form role="form" onSubmit={formik.handleSubmit}>
                        <div className="mb-8">
                            <LabelInput
                                formik={formik}
                                name="username"
                                label="Email address"
                                placeholder="Email address"
                                useFormik
                            />
                        </div>

                        <div className="text-sm flex justify-between items-center mb-3">
                            <Button
                                type="submit"
                                onClick={() => formik.setFieldValue("action", "password")}
                                variant="primary"
                                size="lg"
                                className="!w-full"
                            >
                                Use password
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
            <div className="flex justify-center items-center">
                <a
                    href="#"
                    target="_blank"
                    className="
                    inline-flex
                    items-center
                    text-gray-700
                    font-medium
                    text-xs text-center
                    "
                >
                    <QuestionMarkCircleIcon className="w-5 text-purple-800" />
                    <span className="text-sm ml-2">Having trouble logging in?</span>
                </a>
                <button
                    onClick={() => dispatch(openSupportDialog())}
                    className="text-sm ml-2 text-purple-600 font-semibold hover:underline cursor-pointer"
                >
                    Get support
                </button>
            </div>
        </>
    );
};
export default PreLoginForm;
