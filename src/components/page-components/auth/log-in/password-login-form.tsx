"use client";

//REACT ,NEXT AND OTHER  IMPORTS
import { useFormik } from "formik";
import Link from "next/link";
import { useEffect, useMemo } from "react";

// ICON IMPORTS
import { QuestionMarkCircleIcon } from "@heroicons/react/24/solid";

// PROJECT IMPORTS
import { But<PERSON> } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { authenticateUser, verifyDevice } from "@/redux/actions/auth/signinActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { PATH_AUTH } from "@/routes/path";
import * as yup from "yup";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { storageManager } from "@/lib/custom";
import { LoginStep } from "@/lib/login-utils";
import { useRouter } from "next/navigation";
import { cookies } from "@/lib/cookies";
import useRouteAfterLogin from "@/hooks/useRouteAfterLogin";
import { openSupportDialog } from "@/redux/features/supportDialog";

const PasswordLoginForm = () => {
    const { error, loading, message, mfaMethod, userPhoneNumber } = useAppSelector(
        (state) => state.signin?.authenticateUser || {}
    );

    const {
        error: verifyDeviceError,
        success: verifyDeviceSuccess,
        loading: verifyDeviceLoading,
    } = useAppSelector((state) => state.signin?.verifyDevice || {});

    const dispatch = useAppDispatch();
    const router = useRouter();
    const route = useRouteAfterLogin();

    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                username: storageManager.getItem("preLoginData")?.username ?? "",
                password: "",
            }),
            []
        ),
        onSubmit: () => {
            handleFormSubmit();
        },
        validationSchema: yup.object({
            username: yup
                .string()
                .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, "Enter a valid email")
                .required("Email address is required"),
            password: yup.string().required("Password is required"),
        }),
    });

    const handleFormSubmit = () => {
        // Store username in localStorage - no need to JSON.stringify as storageManager handles it
        storageManager.setItem("preLoginData", { username: formik.values.username });

        // Set username in Redux state
        dispatch(signinActions.setUsername(formik.values.username));

        // Set current login step (password login)
        dispatch(signinActions.setLoginStep(LoginStep.CREDENTIALS));

        // Authenticate user
        dispatch(
            authenticateUser({
                username: formik.values.username,
                password: formik.values.password,
            })
        );
    };

    useEffect(() => {
        if (error) {
            sendCatchFeedback(error, () => dispatch(signinActions.clearLoginError()));
        }

        if (verifyDeviceError) {
            sendCatchFeedback(verifyDeviceError);
            dispatch(signinActions.clearState("verifyDevice"));
        }
    }, [error, verifyDeviceError, dispatch, router]);

    useEffect(() => {
        if (message) {
            sendFeedback(message, "success", () => dispatch(signinActions.clearLoginMessage()));

            if (mfaMethod === "NO_MFA") {
                dispatch(verifyDevice(formik.values.username));
                return;
            }

            if (mfaMethod === "AUTHENTICATOR") {
                router.push(`${PATH_AUTH.twoFaVerification}?mfa=authenticator`);
            }

            if (mfaMethod === "SMS") {
                router.push(`${PATH_AUTH.twoFaVerification}?mfa=sms&phone=${userPhoneNumber}`);
            }

            if (mfaMethod === "SECURITY_QUESTION") {
                router.push(`${PATH_AUTH.twoFaVerification}?mfa=security-question`);
            }
        }

        if (verifyDeviceSuccess) {
            sendFeedback("Login successful", "success", () => {
                dispatch(signinActions.clearLoginMessage());
            });
            router.replace(route);
        }
    }, [message, verifyDeviceSuccess, formik.values.username, userPhoneNumber, mfaMethod, dispatch, router, route]);

    useEffect(() => {
        // Set login step
        dispatch(signinActions.setLoginStep(LoginStep.CREDENTIALS));

        // Clear auth tokens when accessing the password login page
        cookies.clearAllTokens();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <div className=" flex flex-col w-full max-w-md p-8">
                <div className="font-bold self-center text-xl sm:text-2xl text-black">Log in to your account</div>
                <div className="mt-1 text-xs self-center items-center sm:text-sm text-subText">
                    Log in with your email address and password
                </div>
                <div className="mt-5">
                    <form onSubmit={formik.handleSubmit} className="w-full" data-testid="form">
                        <div className="grid gap-[20px]">
                            <LabelInput formik={formik} name="username" label="Email address" />
                            <LabelInput formik={formik} name="password" label="Password" type="password" />
                        </div>

                        <div className="mt-2">
                            <Link href={`${PATH_AUTH.forgotPassword}?email=${formik.values.username}`}>
                                <Button type="button" variant="text-primary">
                                    Forgot Password?
                                </Button>
                            </Link>
                        </div>
                        <div className="mt-8">
                            <Button
                                type="submit"
                                data-testid="submit-button"
                                loading={loading ?? verifyDeviceLoading}
                                disabled={loading ?? verifyDeviceLoading}
                                className="!w-full"
                                size="lg"
                            >
                                Continue with password
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
            <div className="flex justify-center items-center">
                <a
                    href="#"
                    target="_blank"
                    className="
          inline-flex
          items-center
          text-gray-700
          font-medium
          text-xs text-center
        "
                >
                    <QuestionMarkCircleIcon className="w-5 text-purple-800" />
                    <span className="text-sm ml-2">Having trouble to logging in?</span>
                </a>
                <button
                    onClick={() => dispatch(openSupportDialog())}
                    className="text-sm ml-2 text-purple-600 font-semibold hover:underline cursor-pointer"
                >
                    Get support
                </button>
            </div>
        </>
    );
};
export default PasswordLoginForm;
