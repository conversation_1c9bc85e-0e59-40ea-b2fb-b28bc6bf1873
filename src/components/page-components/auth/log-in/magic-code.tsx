"use client";

// LIBRARY IMPORTS
import { useEffect, useState } from "react";

//PROJECT IMPORTS
import { maskEmail } from "@/functions/facilities";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { generateMagicCode, verifyMagicCode } from "@/redux/actions/auth/signinActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { storageManager } from "@/lib/custom";
import { MailIcon } from "@/components/icons/auth";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { LoginStep } from "@/lib/login-utils";
import OTPContainer from "../otp-container";

const MagicCode = () => {
    const [otpValue, setOtpValue] = useState("");
    const dispatch = useAppDispatch();

    const { verifyMagicCode: verifyMagicCodeState } = useAppSelector((state) => state.signin ?? {});

    const email = storageManager.getItem("preLoginData")?.username ?? "";

    const submitForm = async () => {
        // Set current login step (magic code login)
        dispatch(signinActions.setLoginStep(LoginStep.MAGIC_CODE));

        // Set username in Redux state
        dispatch(signinActions.setUsername(email));

        await dispatch(
            verifyMagicCode({
                username: email,
                code: otpValue,
                isMfaEnabled: "true",
            })
        );
    };

    const handleResendOTP = async () => {
        dispatch(generateMagicCode({ username: email }));
        return;
    };

    useEffect(() => {
        dispatch(generateMagicCode({ username: email }));

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (verifyMagicCodeState.success) {
            sendFeedback("Login successful", "success");
            dispatch(signinActions.clearGenerateMagicSuccess());
            dispatch(signinActions.clearVerifyMagicCodeSuccess());

            // The router navigation is now handled by the login flow in the Redux slice
            // based on whether MFA is required or not
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [verifyMagicCodeState.success]);

    useEffect(() => {
        if (verifyMagicCodeState.error) {
            sendCatchFeedback("Unable to login. Pls try again", () => {
                dispatch(signinActions.clearGenerateMagicError());
            });
            setOtpValue("");
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [verifyMagicCodeState.error]);

    return (
        <>
            <OTPContainer
                value={otpValue}
                setValue={setOtpValue}
                onSubmit={submitForm}
                onResend={handleResendOTP}
                loading={verifyMagicCodeState.loading}
                variant="autoVerify"
                title="Check your email"
                type="email"
                icon={<MailIcon />}
                subtitle={
                    <p className="text-[#3A3A41]">
                        We&apos;ve sent a 6 digit verification code to <b>{email ? maskEmail(email) : "--"}</b>
                    </p>
                }
                resendText="Resend Code"
                className="custom-container-class"
            />
        </>
    );
};

export default MagicCode;
