"use client";

import { useEffect, useState, useRef } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { useRouter } from "next/navigation";
import { Loader2, AlertCircle } from "lucide-react";
import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import { Button } from "@/components/common/buttonv3";
import { CheckmarkCircleIcon, HelpIcon } from "@/components/icons/auth";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { PATH_AUTH } from "@/routes/path";
import { saveBusinessInfo } from "@/redux/slices/auth/signupSlice2";
import { useAppDispatch } from "@/redux/hooks";
import { acctsAxios as axios } from "@/api/axios";
import { openSupportDialog } from "@/redux/features/supportDialog";

// Mock business categories - replace with actual data
const BUSINESS_CATEGORIES = [
    { label: "Limited liability company", value: "limited_liability_company" },
    { label: "Sole proprietorship", value: "sole_proprietorship" },
    { label: "Partnership", value: "partnership" },
    { label: "Corporation", value: "corporation" },
    { label: "Non-profit organization", value: "non_profit" },
];

// Validation schema
const validationSchema = yup.object({
    businessCategory: yup.string().required("Business industry is required"),
    accountName: yup.string().required("Account name is required"),
    corporateAccountNumber: yup
        .string()
        .matches(/^\d+$/, "Corporate account number must contain only numbers")
        .matches(/^\d{10}$/, "Corporate account number must be exactly 10 digits")
        .required("Corporate account number is required"),
});

const BusinessInfoForm = () => {
    const router = useRouter();
    const [accountVerified, setAccountVerified] = useState(false);
    const [fetchingDetails, setFetchingDetails] = useState(false);
    const [fetchDetailsError, setFetchDetailsError] = useState<string | null>(null);
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const dispatch = useAppDispatch();

    // Initialize form with Formik
    const formik = useFormik({
        initialValues: {
            corporateAccountNumber: "",
            accountName: "",
            businessCategory: "",
        },
        validationSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: () => {
            submitForm();
        },
    });

    // Handle form submission
    const submitForm = async () => {
        // Validate form before submission
        const errors = await formik.validateForm();

        if (Object.keys(errors).length > 0 || !accountVerified) {
            // Touch all fields to show errors
            Object.keys(formik.values).forEach((field) => {
                formik.setFieldTouched(field, true);
            });
            return;
        }

        dispatch(
            saveBusinessInfo({
                corporateAccountNumber: formik.values.corporateAccountNumber,
                accountName: formik.values.accountName,
                businessCategory: formik.values.businessCategory,
            })
        );

        router.push(PATH_AUTH.validateBusiness);
    };

    // Fetch account details using Axios
    const fetchAccountDetails = async (accountNumber: string) => {
        if (!accountNumber || accountNumber.length !== 10) {
            return;
        }

        setFetchingDetails(true);
        setFetchDetailsError(null);

        try {
            const response = await axios.get(`/api/v1/accounts/corporate/${accountNumber}/details`);
            if (response.data) {
                formik.setFieldValue("accountName", response.data.businessName);
                setAccountVerified(true);
                sendFeedback("Account details verified successfully", "success");
            }
        } catch (error) {
            setFetchDetailsError("Failed to verify account. Please try again.");
            sendCatchFeedback("Failed to verify account. Please try again.");
            formik.setFieldValue("accountName", "");
            setAccountVerified(false);
        } finally {
            setFetchingDetails(false);
        }
    };

    // Handle account number changes with proper debouncing
    useEffect(() => {
        // Reset verification when account number changes
        if (
            formik.values.corporateAccountNumber.length !== 10 ||
            !/^[0-9]+$/.test(formik.values.corporateAccountNumber)
        ) {
            setAccountVerified(false);
            formik.setFieldValue("accountName", "");
            setFetchDetailsError(null);
        }

        // Clear any existing timer
        if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
        }

        // Set new timer if account number is complete and is a number
        if (
            formik.values.corporateAccountNumber.length === 10 &&
            /^[0-9]+$/.test(formik.values.corporateAccountNumber)
        ) {
            timerRef.current = setTimeout(() => {
                fetchAccountDetails(formik.values.corporateAccountNumber);
            }, 500);
        }

        // Cleanup function
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
                timerRef.current = null;
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formik.values.corporateAccountNumber]);

    // Determine if form is valid for submission
    const isFormValid =
        formik.values.corporateAccountNumber.length === 10 &&
        formik.values.accountName?.trim() !== "" &&
        formik.values.businessCategory !== "" &&
        accountVerified &&
        Object.keys(formik.errors).length === 0;

    return (
        <section className="w-full max-w-[472px] mt-6">
            <form onSubmit={formik.handleSubmit} className="bg-white p-6 pb-0" noValidate>
                {/* Corporate Account Number */}
                <div className="mb-5">
                    <LabelInput
                        formik={formik}
                        name="corporateAccountNumber"
                        label="Corporate account number"
                        placeholder="Enter the 10 digit corporate account number"
                        maxLength={10}
                        showError={formik.touched.corporateAccountNumber}
                        disabled={fetchingDetails}
                    />
                </div>

                {/* Account Name */}
                <div className="relative mb-5">
                    <LabelInput
                        formik={formik}
                        name="accountName"
                        label="Account name"
                        disabled={true}
                        readOnly={true}
                        placeholder={
                            fetchingDetails ? "Verifying account..." : "Complete account number to fetch account name"
                        }
                    />

                    {/* Verification Status Indicator */}
                    <div className="absolute right-3 top-[55%] transform">
                        {fetchingDetails && <Loader2 className="h-5 w-5 animate-spin text-primary" />}

                        {accountVerified && <CheckmarkCircleIcon />}

                        {fetchDetailsError && <AlertCircle className="h-5 w-5 text-red-500" />}
                    </div>
                </div>

                {/* Business industry */}
                <div className="mb-6">
                    <Dropdown
                        options={BUSINESS_CATEGORIES}
                        name="businessCategory"
                        label="Business industry"
                        placeholder="Choose one"
                        size="sm"
                        formik={formik}
                        showError={formik.touched.businessCategory}
                    />
                </div>

                {/* Submit Button */}
                <Button fullWidth type="submit" className="w-full" disabled={!isFormValid || fetchingDetails}>
                    Continue
                </Button>
            </form>

            {/* Support Footer */}
            <footer className="mt-5">
                <div className="text-gray-600 text-sm flex items-center gap-2 justify-center">
                    <HelpIcon />
                    <span>Having trouble signing up?</span>
                    <button
                        type="button"
                        className="text-primary font-medium hover:underline"
                        onClick={() => dispatch(openSupportDialog())}
                    >
                        Get Support
                    </button>
                </div>
            </footer>
        </section>
    );
};

export default BusinessInfoForm;
