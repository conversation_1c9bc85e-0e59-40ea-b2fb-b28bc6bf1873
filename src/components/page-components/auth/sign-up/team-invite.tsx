"use client";

import React, { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/common/buttonv3";
import { BuildingIcon, CheckmarkIcon, ShieldIcon } from "@/components/icons/auth";
import { getNameInitials } from "@/functions/stringManipulations";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchTeamMemberData } from "@/redux/actions/auth/signupActions";
import { PATH_AUTH } from "@/routes/path";
import { resetSignupForm } from "@/redux/slices/auth/signupSlice2";

export interface IInvitePage {
    invitedBy: string;
    role: string;
    roleDescription: string;
    rolePermissions: string[];
}

const TeamInvite: React.FC = () => {
    const router = useRouter();
    const params = useSearchParams();
    const emailAddress = params.get("email");
    const { teamMember } = useAppSelector((state) => state.signupNew);
    const dispatch = useAppDispatch();

    const handleContinueSetup = () => {
        router.push(`${PATH_AUTH.signup}?option=team-invite`);
    };

    useEffect(() => {
        dispatch(resetSignupForm());
        dispatch(fetchTeamMemberData(emailAddress as string));

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [emailAddress]);

    if (!teamMember) return null;

    return (
        <div className="w-[90%] max-w-[544px] mx-auto border border-[#E3E5E8] rounded-[20px]" data-testid="team-invite">
            <div className="flex items-center gap-x-[12px] justify-start p-[20px]">
                <div className="w-[60px] h-[60px] flex items-center justify-center rounded-2xl bg-[#FFF5FB] p-4">
                    <BuildingIcon />
                </div>

                <h2 className="text-[20px] font-bold text-black text-start">
                    Join {teamMember?.corporateName || "--"} on FCMB Corporate Internet Banking
                </h2>
            </div>

            <div className="border-t border-[#E3E5E8] w-full" />

            <div>
                <div className="py-8 px-6">
                    <div>
                        <label className="block mb-4 text-base font-semibold text-black">Invited by</label>
                        <div className="flex items-center gap-x-2">
                            <div className="w-10 h-10 flex items-center justify-center rounded-full text-subText font-medium bg-[#F9F0FE] text-base">
                                {getNameInitials(
                                    `${teamMember?.invitedBy?.firstName} ${teamMember?.invitedBy?.lastName}`
                                )}
                            </div>
                            <div>
                                <span className="text-sm font-semibold">
                                    {teamMember.invitedBy?.firstName ?? ""} {teamMember.invitedBy?.lastName ?? ""}
                                </span>
                                <p className="text-sm font-normal text-subText capitalize">
                                    {teamMember?.invitedBy?.roleName?.toLowerCase() ?? ""}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-8">
                        <label className="block text-base font-semibold text-black mb-5">Your Role</label>
                        <div className="mt-5 bg-[#F9F9FA] p-5 gap-y-3 rounded-[12px]">
                            <p className="text-sm font-medium text-black capitalize">
                                {teamMember?.role?.type?.toLowerCase() ?? ""}
                            </p>
                            <p className="text-sm font-normal text-subText">{teamMember?.role?.name ?? ""}</p>
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex items-center gap-x-2">
                            <ShieldIcon />
                            <h3 className="block text-base font-semibold text-black">Role Permissions</h3>
                        </div>
                        <div className="mt-5 bg-[#F9F9FA] p-5 gap-y-3 rounded-[12px]">
                            <p className="text-sm font-medium text-black mb-5">{teamMember?.role?.name ?? ""}</p>
                            <ul className="flex flex-col gap-4">
                                {teamMember?.permissions?.map((permission, index) => (
                                    <li key={index} className="flex items-center gap-[11px]">
                                        <CheckmarkIcon />

                                        <span className="text-sm text-gray-600">{permission.name}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="px-6 mb-6">
                    <Button fullWidth onClick={handleContinueSetup} data-testid="continue-button">
                        Continue to Setup
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default TeamInvite;
