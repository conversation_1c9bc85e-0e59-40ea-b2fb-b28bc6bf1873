"use client";
import { useMemo, useState, useEffect, useRef } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { useRouter } from "next/navigation";
import LabelInput from "@/components/common/label-input";
import DatePicker from "@/components/common/date-picker";
import { Button } from "@/components/common/buttonv3";
import { HelpIcon, InfoSuccessIcon } from "@/components/icons/auth";
import { SpinnerIcon } from "../forgot-password/icons";
import { AlertCircle } from "lucide-react";
import { Tick } from "@/components/icons/bill-payment-icons";
import { PATH_AUTH } from "@/routes/path";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { saveDirectorInfo } from "@/redux/slices/auth/signupSlice2";
import { userAxios as axios } from "@/api/axios";
import { openSupportDialog } from "@/redux/features/supportDialog";

type IValidate = {
    loading: boolean;
    success: boolean;
    error: string | null;
    email?: string;
};

type IFormValue = {
    fullName: string;
    dob: string;
    bvn: string;
};

const defaultValues: IFormValue = {
    fullName: "",
    dob: "",
    bvn: "",
};

const validationSchema = yup.object({
    fullName: yup.string().required("Director's first name is required"),
    dob: yup.string().required("Date of birth is required"),
    bvn: yup
        .string()
        .matches(/^\d+$/, "BVN must contain only numbers")
        .matches(/^\d{11}$/, "BVN must be exactly 11 digits")
        .required("BVN is required"),
});

const ValidateBusinessForm = () => {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { businessInfo } = useAppSelector((state) => state.signupNew);
    const [validate, setValidate] = useState<IValidate>({
        loading: false,
        success: false,
        error: null,
    });

    // Use a ref to track validation timer and prevent excessive API calls
    const validationTimerRef = useRef<NodeJS.Timeout | null>(null);

    const formik = useFormik({
        initialValues: useMemo(() => defaultValues, []),
        onSubmit: async (values) => {
            await submitValues(values);
        },
        validationSchema,
    });

    const verifyDirector = async (values: IFormValue) => {
        const formatDate = (dateString: string) => {
            const date = new Date(dateString);
            const day = date.getDate().toString().padStart(2, "0");
            const month = date.toLocaleString("en-US", { month: "short" });
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        };

        const dobPart = values.dob.split("T")[0];
        const formattedDob = formatDate(dobPart);
        try {
            const response = await axios.post("/v1/corporate/verify-director", {
                corporateAccountNumber: businessInfo?.corporateAccountNumber,
                directorFirstName: values.fullName,
                directorDateOfBirth: formattedDob,
                directorBVN: values.bvn,
            });

            if (response.data && response.data.success) {
                setValidate({
                    loading: false,
                    success: true,
                    error: null,
                    email: response.data.email,
                });
                return true;
            } else {
                setValidate({
                    loading: false,
                    success: false,
                    error: "Verification failed. Please check your details.",
                });
                return false;
            }
        } catch (error) {
            setValidate({
                loading: false,
                success: false,
                error: "Invalid details. Please try again.",
            });
            return false;
        }
    };

    const validateFields = async () => {
        // Check if all fields are filled and there are no validation errors
        const allFieldsFilled =
            formik.values.fullName.trim() !== "" &&
            formik.values.dob !== "" &&
            formik.values.bvn.length === 11 &&
            /^[0-9]+$/.test(formik.values.bvn);

        if (allFieldsFilled) {
            setValidate({ loading: true, success: false, error: null });

            // Call the verification endpoint
            await verifyDirector(formik.values);
        } else {
            // Reset validation state if fields are incomplete or have errors
            setValidate({ loading: false, success: false, error: null });
        }
    };

    useEffect(() => {
        // Clear any existing timer
        if (validationTimerRef.current) {
            clearTimeout(validationTimerRef.current);
        }

        // Set a new timer to debounce validation calls
        validationTimerRef.current = setTimeout(() => {
            validateFields();
        }, 500);

        // Cleanup function
        return () => {
            if (validationTimerRef.current) {
                clearTimeout(validationTimerRef.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formik.values]);

    const submitValues = async (values: IFormValue) => {
        // If not already validated, validate before submission
        if (!validate.success) {
            const isValid = await verifyDirector(values);
            if (!isValid) return;
        }

        // Include the email from validation response along with form values
        dispatch(
            saveDirectorInfo({
                ...values,
                corporateEmailAddress: validate.email as string,
            })
        );

        router.replace(PATH_AUTH.verifyBusinessEmail);
    };

    const isButtonDisabled =
        Object.keys(formik.touched).length === 0 ||
        Object.keys(formik.errors).length > 0 ||
        formik.isSubmitting ||
        !validate.success;

    return (
        <div className="w-full max-w-[472px] mt-[48px]">
            <div className="flex items-center gap-3 p-3 bg-[#F9F0FE] rounded-lg w-full mb-5">
                <InfoSuccessIcon width="40" height="40" />
                <span className="text-[#7707B6] font-normal leading-[18px] text-sm text-left">
                    You'll need to provide the full name, BVN and date of birth of any director listed on your business
                    account.
                </span>
            </div>
            <form onSubmit={formik.handleSubmit} className="mt-8">
                <div className="grid gap-[20px]">
                    <LabelInput
                        formik={formik}
                        name="fullName"
                        label="Director's first name"
                        showError={formik.touched.fullName}
                        placeholder="Enter first name"
                    />
                    <DatePicker
                        label="Director’s date of birth"
                        value={formik.values.dob ? new Date(formik.values.dob) : undefined}
                        hint="Date of birth of director listed above"
                        placeholder="dd/mm/yy"
                        onChange={(value) => {
                            formik.setFieldValue("dob", value ? value.toISOString() : "");
                        }}
                        error={formik.touched.dob && formik.errors.dob ? formik.errors.dob : undefined}
                    />
                    <div>
                        <LabelInput
                            formik={formik}
                            name="bvn"
                            label="Director's BVN"
                            showError={formik.touched.bvn}
                            placeholder="Enter 11-digit BVN"
                            maxLength={11}
                        />
                        {validate.loading && (
                            <div className="mt-3 flex items-center gap-[6px] w-full">
                                <SpinnerIcon />
                                <span className="text-sm font-medium">Validating BVN</span>
                            </div>
                        )}
                        {validate.success && !validate.loading && (
                            <div className="mt-1 text-[#039855] font-medium text-sm flex items-center gap-1">
                                <Tick color="#12B76A" />
                                <span>All details matched</span>
                            </div>
                        )}
                        {validate.error && !validate.loading && (
                            <div className="mt-1 text-amber-600 text-sm font-medium flex items-center gap-1">
                                <AlertCircle size={12} />
                                <span>{validate.error}</span>
                            </div>
                        )}
                    </div>
                </div>
                <div className="mt-8">
                    <Button type="submit" className="!w-full" size={"lg"} disabled={isButtonDisabled}>
                        Continue
                    </Button>
                </div>
            </form>
            <footer className="mt-[20px]">
                <div className="text-subText text-[0.875rem] text-center flex items-center gap-[0.25rem] justify-center font-medium">
                    <HelpIcon /> Having trouble signing up?
                    <button
                        onClick={() => dispatch(openSupportDialog())}
                        className="text-primary hover:underline cursor-pointer"
                    >
                        Get Support
                    </button>
                </div>
            </footer>
        </div>
    );
};

export default ValidateBusinessForm;
