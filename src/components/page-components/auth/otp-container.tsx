"use client";

import {
    type ReactNode,
    type FormEvent,
    useState,
    useEffect,
    Dispatch,
    SetStateAction,
    HTMLInputTypeAttribute,
} from "react";
import OTPInput from "@/components/common/otp-input";
import Timer from "@/components/common/timer";
import { Button } from "@/components/common/buttonv3";
import { SpinnerIcon } from "./forgot-password/icons";

export type OTPVariant = "withButton" | "autoVerify";

export interface OTPContainerProps {
    // Core props
    value: string;
    setValue: Dispatch<SetStateAction<string>>;
    loading?: boolean;
    onSubmit?: (otp: string) => Promise<void> | void;
    error?: string | null; // Add error prop

    // Variant control
    variant?: OTPVariant;
    autoVerifyLength?: number;
    type: "phone" | "email" | "pin";
    inputType?: HTMLInputTypeAttribute;

    // Resend functionality
    onResend?: () => Promise<void> | void;
    resendLoading?: boolean;
    initialTimerSeconds?: number;

    // UI customization
    title?: string | ReactNode;
    subtitle?: string | ReactNode;
    buttonText?: string;
    resendText?: string;
    verifyingText?: string;
    note?: ReactNode;
    icon?: ReactNode;

    // Additional customization
    className?: string;
    inputClassName?: string;
    buttonClassName?: string;
    showLoadingIndicator?: boolean;
    disabled?: boolean;
}

const OTPContainer = ({
    // Core props
    value,
    setValue,
    loading = false,
    onSubmit,
    error, // Destructure error prop

    // Variant control
    variant = "withButton",
    autoVerifyLength = 6,
    inputType,
    type = "email",

    // Resend functionality
    onResend,
    resendLoading = false,
    initialTimerSeconds = 300,

    // UI customization
    title,
    subtitle,
    buttonText = "Verify and continue",
    resendText = "Resend code",
    verifyingText = "Verifying code...",
    note,
    icon,

    // Additional customization
    className = "",
    inputClassName = "",
    buttonClassName = "",
    showLoadingIndicator = true,
    disabled = false,
}: OTPContainerProps) => {
    const [timer, setTimer] = useState(initialTimerSeconds);

    // Handle form submission
    const handleSubmit = async (e?: FormEvent<HTMLFormElement>) => {
        if (e) e.preventDefault();
        if (onSubmit) await onSubmit(value);
    };

    // Handle resend
    const handleResend = async () => {
        if (onResend) {
            await onResend();
            setTimer(initialTimerSeconds); // Reset timer after resend
        }
    };

    // Auto-verify when OTP is complete
    useEffect(() => {
        if (variant === "autoVerify" && value.length === autoVerifyLength && onSubmit) {
            handleSubmit();
        }
    }, [value, variant, autoVerifyLength]);

    return (
        <div className="w-full flex flex-col justify-center items-center">
            {icon && <div className="mb-[41px]">{icon}</div>}
            {/* Title and subtitle */}
            {title &&
                (typeof title === "string" ? (
                    <h1 className="text-[1.5rem] font-bold mb-[1.25px] text-center">{title}</h1>
                ) : (
                    title
                ))}

            {subtitle && (
                <div className="mb-[2.5rem] mt-[5px] max-w-[400px] text-center">
                    {typeof subtitle === "string" ? <p className="text-[#3A3A41]">{subtitle}</p> : subtitle}
                </div>
            )}

            {/* OTP Form */}
            <form onSubmit={handleSubmit} className="w-full max-w-[420px]">
                <OTPInput value={value} setValue={setValue} type={inputType} />

                {/* Error message */}
                {error && <div className="mt-3 text-red-500 text-sm text-center">{error}</div>}

                {/* Resend timer/button */}
                {onResend && !loading && (
                    <div className="mt-3 text-[#3A3A41] text-sm text-center flex items-center gap-[0.25rem] justify-center font-medium">
                        {type && type === "email" && "Didn't get an email?"}
                        {timer > 0 ? (
                            <>
                                {" "}
                                Resend code in
                                <Timer currentTime={timer} setCurrentTime={setTimer} />
                            </>
                        ) : (
                            <button
                                type="button"
                                className="text-primary"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleResend();
                                }}
                                disabled={resendLoading}
                            >
                                {resendText}
                            </button>
                        )}
                    </div>
                )}

                {/* Loading indicator for auto-verify */}
                {variant === "autoVerify" && loading && showLoadingIndicator && (
                    <div className="mt-3 flex items-center gap-[6px] w-full justify-center">
                        <SpinnerIcon />
                        <span className="text-sm font-medium">{verifyingText}</span>
                    </div>
                )}

                {note}

                {/* Submit button */}
                {variant === "withButton" && (
                    <Button
                        type="submit"
                        loading={loading}
                        fullWidth
                        disabled={disabled || value.length < autoVerifyLength}
                        className={"mt-8"}
                    >
                        {buttonText}
                    </Button>
                )}
            </form>
        </div>
    );
};

export default OTPContainer;
