"use client";

import { Avatar, AvatarFallback } from "@/components/common/Avatar";
import Badge from "@/components/common/badge";
import React from "react";

// Connector Line component
const ConnectorLine = ({ color = "#E3E5E8" }: { color?: string }) => (
    <svg
        width="2"
        height="30"
        viewBox="0 0 2 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
        data-testid="connector-line"
    >
        <path id="Vector 8" d="M1 0L0.999999 30" stroke={color} strokeWidth="2" strokeDasharray="4 4" />
    </svg>
);

/**
 * Interface for approver data from API
 */
interface ApproverData {
    id: number;
    userId: number;
    name: string;
    role: string;
    approvalLevel: number;
    status: "APPROVE" | "DECLINE" | null;
    date: string | null;
}

/**
 * Props for the ApproverListItem component
 */
interface ApproverListItemProps {
    /** The approver data from API */
    approver: ApproverData;
    /** Whether this is the last item in the list (no connector line) */
    isLast?: boolean;
    /** Transfer status to override individual approver status */
    transferStatus?: "cancelled" | "completed" | "pending";
    /** Custom CSS class name */
    className?: string;
    /** Custom spacing class (default: "mb-1") */
    spacing?: string;
}

/**
 * ApproverListItem component displays individual approver information
 * with status-based styling as per PRD specifications
 */
export default function ApproverListItem({
    approver,
    isLast = false,
    transferStatus,
    className = "",
    spacing = "mb-1",
}: Readonly<ApproverListItemProps>) {
    // Handle missing or empty approver
    if (!approver) {
        throw new Error("ApproverListItem requires an approver prop");
    }

    // Generate initials from name
    const generateInitials = (name: string): string => {
        if (!name || name.trim() === "") return "";
        return name
            .trim()
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase();
    };

    const initials = generateInitials(approver.name);

    // Determine status and styling based on transfer status or individual status
    const getStatusInfo = () => {
        if (transferStatus === "cancelled") {
            return {
                statusText: "Cancelled",
                statusColor: "#D92D20", // Red
                subtitleText: approver.date ?? "",
                avatarBackground: "#F9F0FE", // Purple
                connectorColor: "#F9F9FA", // Gray
            };
        }

        switch (approver.status) {
            case "APPROVE":
                return {
                    statusText: "Approved",
                    statusColor: "#039855", // Green
                    subtitleText: approver.date ?? "",
                    avatarBackground: "#F9F0FE", // Purple
                    connectorColor: "#F9F0FE", // Purple
                };
            case "DECLINE":
                return {
                    statusText: "Declined",
                    statusColor: "#D92D20", // Red
                    subtitleText: approver.date ?? "",
                    avatarBackground: "#F9F0FE", // Purple
                    connectorColor: "#F9F9FA", // Gray
                };
            default: // null or pending
                return {
                    statusText: "",
                    statusColor: "#3A3A41", // Gray
                    subtitleText: "Pending approval",
                    avatarBackground: "#F9F9FA", // Gray
                    connectorColor: "#F9F9FA", // Gray
                };
        }
    };

    const statusInfo = getStatusInfo();

    // Create aria-label for accessibility
    const statusText = statusInfo.statusText || statusInfo.subtitleText;
    const dateText = statusInfo.statusText && approver.date ? `, ${approver.date}` : "";
    const ariaLabel = `${approver.name}, ${approver.role}, ${statusText}${dateText}`;

    return (
        <li
            className={`relative flex flex-row items-start gap-2 text-[#3A3A41] ${spacing} ${className}`}
            aria-label={ariaLabel}
        >
            {/* Avatar Column */}
            <div className="flex flex-col items-center w-10">
                <Avatar
                    className="flex h-10 px-[7px] py-[14px] flex-col justify-center items-center gap-[10px] self-stretch rounded-[20px]"
                    style={{
                        backgroundColor: statusInfo.avatarBackground,
                    }}
                >
                    <AvatarFallback
                        className="text-[#3A3A41] text-base font-medium leading-5 tracking-[0.32px] text-center uppercase"
                        style={
                            {
                                backgroundColor: statusInfo.avatarBackground,
                                "--text-edge": "cap",
                                "--leading-trim": "both",
                            } as React.CSSProperties
                        }
                    >
                        {initials}
                    </AvatarFallback>
                </Avatar>
                {!isLast && (
                    <div className="-mt-1">
                        <ConnectorLine color={statusInfo.connectorColor} />
                    </div>
                )}
            </div>

            {/* Content - Aligned with Avatar */}
            <div className="flex flex-col justify-between gap-[3px] flex-1" data-testid="content-container">
                <div className="flex items-start justify-between gap-2 w-full" data-testid="top-row">
                    <div className="flex items-center gap-2">
                        <h3 className="text-[#151519] text-sm font-medium leading-[18px] tracking-[0.28px] capitalize">
                            {approver.name ?? ""}
                        </h3>
                        <Badge text={approver.role} size="sm" color="brand" className="capitalize" />
                    </div>
                    {/* Status positioned on the far right */}
                    <div className="ml-auto flex items-center" data-testid="status-container">
                        {statusInfo.statusText && (
                            <p
                                className="text-sm font-normal leading-[18px] tracking-[0.28px]"
                                style={{ color: statusInfo.statusColor }}
                            >
                                {statusInfo.statusText}
                            </p>
                        )}
                    </div>
                </div>
                <div className="flex items-center">
                    <p className="text-sm font-normal leading-[18px] tracking-[0.28px]" style={{ color: "#3A3A41" }}>
                        {statusInfo.subtitleText}
                    </p>
                </div>
            </div>
        </li>
    );
}
