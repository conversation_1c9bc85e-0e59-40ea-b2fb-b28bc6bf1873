// components/Tooltip.tsx
import { useState, type ReactNode } from "react";

interface TooltipProps {
    label: string;
    children: ReactNode;
    position?: "top" | "bottom" | "left" | "right";
}

const Tooltip = ({ label, children, position = "top" }: TooltipProps) => {
    const [show, setShow] = useState(false);

    const getPositionClasses = () => {
        switch (position) {
            case "top":
                return "bottom-full mb-2 left-1/2 -translate-x-1/2";
            case "bottom":
                return "top-full mt-2 left-1/2 -translate-x-1/2";
            case "left":
                return "right-full mr-2 top-1/2 -translate-y-1/2";
            case "right":
                return "left-full ml-2 top-1/2 -translate-y-1/2";
            default:
                return "";
        }
    };

    return (
        <div className="relative" onMouseEnter={() => setShow(true)} onMouseLeave={() => setShow(false)}>
            {children}
            {show && (
                <div
                    className={`absolute z-50 w-max max-w-full min-w-[300px] p-3 font-semibold text-sm text-white bg-[#212126] rounded-md shadow-sm ${getPositionClasses()}`}
                >
                    {label}
                </div>
            )}
        </div>
    );
};

export default Tooltip;
