import React from "react";
import { Tick } from "@/components/icons/bill-payment-icons";
import { InfoIcon } from "@/components/icons/auth";

interface PasswordCriteria {
    label: string;
    test: (password: string) => boolean;
}

interface PasswordValidationProps {
    password: string;
    isVisible: boolean;
}

const PasswordValidation: React.FC<PasswordValidationProps> = ({ password, isVisible }) => {
    const criteria: PasswordCriteria[] = [
        {
            label: "A capital letter",
            test: (pwd: string) => /[A-Z]/.test(pwd),
        },
        {
            label: "A number",
            test: (pwd: string) => /[0-9]/.test(pwd),
        },
        {
            label: "A special character (e.g., @, #, $, %)",
            test: (pwd: string) => /[!@#$%^&*(),.?":{}|<>]/.test(pwd),
        },
        {
            label: "At least 14 characters",
            test: (pwd: string) => pwd.length >= 14,
        },
    ];

    if (!isVisible) return null;

    return (
        <div className="mt-2 space-y-2">
            {criteria.map((criterion) => {
                const isMet = criterion.test(password);
                return (
                    <div key={criterion.label} className="flex items-center gap-2">
                        {isMet ? <Tick color="#12B76A" /> : <InfoIcon color="#D92D20" />}
                        <span className={"text-sm text-black"}>{criterion.label}</span>
                    </div>
                );
            })}
        </div>
    );
};

export default PasswordValidation;
