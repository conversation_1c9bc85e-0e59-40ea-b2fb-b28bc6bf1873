"use client";
import { ColumnDef, flexRender, Table as ReactTable } from "@tanstack/react-table";

import { Table } from ".";
import LoadingIndicator from "../loading-indicator";
import { TableBody } from "./table-body";
import { TableCell } from "./table-cell";
import { TableHead } from "./table-head";
import { TableHeader } from "./table-header";
import TableNoData from "./table-no-data";
import { TableRow } from "./table-row";

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    table: ReactTable<TData>;
    loading?: boolean;
    emptyTabletitle?: string;
    emptyTabledescription?: string;
    width?: string;
    onRowClick?: (row: TData) => void;
}

export function DataTable<TData, TValue>({
    columns,
    table,
    loading,
    emptyTabletitle,
    emptyTabledescription,
    width = "",
    onRowClick,
}: Readonly<DataTableProps<TData, TValue>>) {
    return (
        <div
            className="rounded-md max-h-[calc(100vh-400px)] overflow-y-auto w-full overflow-x-auto"
            data-testid="data-table"
        >
            <Table style={{ width }}>
                <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                                <TableHead key={header.id} className="!h-[40px]">
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(header.column.columnDef.header, header.getContext())}
                                </TableHead>
                            ))}
                        </TableRow>
                    ))}
                </TableHeader>
                <TableBody>
                    {loading ? (
                        <tr className={"bg-white px-3 border border-[#EAECF0] border-x-0 py-10"}>
                            <td className="px-3 py-3 text-sm align-middle" colSpan={columns.length ?? 4}>
                                <div className="flex items-center justify-center p-10 py-20">
                                    <LoadingIndicator />
                                </div>
                            </td>
                        </tr>
                    ) : (
                        <>
                            {table?.getRowModel()?.rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={row.getIsSelected() && "selected"}
                                        onClick={onRowClick ? () => onRowClick(row.original) : undefined}
                                        className={onRowClick ? "cursor-pointer" : undefined}
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableNoData
                                    colSpan={columns.length}
                                    title={emptyTabletitle}
                                    description={emptyTabledescription}
                                />
                            )}
                        </>
                    )}
                </TableBody>
            </Table>
        </div>
    );
}
