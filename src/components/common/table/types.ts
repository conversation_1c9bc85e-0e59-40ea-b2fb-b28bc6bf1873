import React from "react";

export interface WithId {
    _id: string | number;
}

export interface TableProps<T extends WithId> {
    data: T[];
    tableHeaders: string[];
    loading: boolean;
    menuItems?: menuItemType<T>[];
    bodyStyle?: React.CSSProperties;
    headerStyle?: React.CSSProperties;
}

// eslint-disable-next-line
export type rowType<T = any> = { row: { value: T[keyof T]; headerName: string }[] }[];

export type menuItemType<T> = {
    label: string | React.ReactNode;
    onClick: (data: T) => void;
    style?: React.CSSProperties;
    permission?: boolean;
    icon?: React.ReactNode;
    disabled?: boolean;
};
