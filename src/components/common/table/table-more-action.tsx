"use client";

import { Ref, useRef } from "react";
import Popup from "reactjs-popup";
import { PopupActions } from "reactjs-popup/dist/types";
import { menuItemType } from "./types";
import { MoreIcon } from "@/components/icons/table";

export interface WithId {
    id: string | number;
}

interface TableMoreActionProps<T extends WithId> {
    data: T;
    menuItems?: menuItemType<T>[];
}

const TableMoreAction = <T extends WithId>({ data, menuItems }: TableMoreActionProps<T>) => {
    const menuRef: Ref<PopupActions> | null = useRef(null);

    return (
        <div className="w-full flex justify-center">
            <Popup
                ref={menuRef}
                trigger={
                    <button
                        key={data.id}
                        data-testid="more-action-btn"
                        className="flex items-center justify-center hover:bg-gray-200 duration-500 rounded-full w-7 h-7"
                    >
                        <MoreIcon />
                    </button>
                }
                closeOnDocumentClick
                offsetX={10}
                offsetY={5}
                position="bottom right"
                contentStyle={{
                    padding: 8,
                    borderRadius: 14,
                    boxShadow: "0px 0px 7px 0px rgba(0, 0, 0, 0.07)",
                    border: "1px solid #E3E5E8",

                    minWidth: "189px",
                    width: "fit-content",
                    backgroundColor: "#FFF",
                }}
                arrow={false}
                closeOnEscape
            >
                <ul className="flex flex-col w-full gap-3">
                    {menuItems?.map((item, index) => (
                        <li key={index + 1}>
                            <button
                                className="w-full px-3 py-2 text-[14px]  text-[#151519] hover:bg-[#F9FAFB] cursor-pointer duration-200 flex items-start font-medium leading-[18px] tracking-[0.28px] disabled:opacity-50 disabled:cursor-not-allowed"
                                style={item.style}
                                onClick={() => {
                                    item.onClick(data);
                                    menuRef.current && menuRef.current.close();
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter" || e.key === " ") {
                                        item.onClick(data);
                                        menuRef.current && menuRef.current.close();
                                    }
                                }}
                                type="button"
                                role="menuitem"
                                disabled={item.disabled}
                            >
                                {item.icon ? (
                                    <span className="whitespace-nowrap flex items-center gap-1.5">
                                        {item.icon} {item.label}
                                    </span>
                                ) : (
                                    item.label
                                )}
                            </button>
                        </li>
                    ))}
                </ul>
            </Popup>
        </div>
    );
};

export default TableMoreAction;
