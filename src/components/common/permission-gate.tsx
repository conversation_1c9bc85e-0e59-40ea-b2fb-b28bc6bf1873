"use client";

import React, { ReactNode } from "react";
import { usePermissions } from "@/contexts/PermissionContext";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";

interface PermissionGateProps {
    permission?: string;
    permissions?: string[];
    requireAll?: boolean;
    fallback?: ReactNode;
    children: ReactNode;
    // New props for enhanced functionality
    showLoadingState?: boolean;
    loadingFallback?: ReactNode;
    showErrorState?: boolean;
    errorFallback?: ReactNode;
    validatePermissions?: boolean; // Check if permissions exist in system
}

/**
 * A component that conditionally renders its children based on user permissions
 * Enhanced to work with dynamic permissions and provide better loading/error states
 *
 * @param permission - A single permission to check
 * @param permissions - An array of permissions to check
 * @param requireAll - If true, the user must have all permissions in the array. If false, the user must have at least one.
 * @param fallback - Content to render if the user doesn't have the required permissions
 * @param children - Content to render if the user has the required permissions
 * @param showLoadingState - Whether to show loading state while permissions are being fetched
 * @param loadingFallback - Custom loading component
 * @param showErrorState - Whether to show error state if permission fetching fails
 * @param errorFallback - Custom error component
 * @param validatePermissions - Whether to validate that permissions exist in the system
 */
const PermissionGate: React.FC<PermissionGateProps> = ({
    permission,
    permissions = [],
    requireAll = false,
    fallback = null,
    children,
    showLoadingState = true,
    loadingFallback = null,
    showErrorState = true,
    errorFallback = null,
    validatePermissions = false,
}) => {
    const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();
    const { getLoadingState, getErrorState, permissionExists } = usePermissionCheck();

    const loadingState = getLoadingState();
    const errorState = getErrorState();

    // Show loading state if permissions are still being fetched
    if (showLoadingState && loadingState.isAnyLoading) {
        return loadingFallback || <div className="animate-pulse bg-gray-200 h-4 w-24 rounded" />;
    }

    // Show error state if there are permission errors
    if (showErrorState && errorState.hasAnyError) {
        return errorFallback || <div className="text-red-500 text-sm">Permission system error</div>;
    }

    // If no permissions are specified, render the children
    if (!permission && permissions.length === 0) {
        return <>{children}</>;
    }

    // Validate permissions exist in system if requested
    if (validatePermissions) {
        const permissionsToCheck = permission ? [permission] : permissions;
        const invalidPermissions = permissionsToCheck.filter((p) => !permissionExists(p));

        if (invalidPermissions.length > 0) {
            console.warn("PermissionGate: Invalid permissions detected:", invalidPermissions);
            if (process.env.NODE_ENV === "development") {
                return (
                    <div className="text-orange-500 text-sm border border-orange-300 p-2 rounded">
                        Invalid permissions: {invalidPermissions.join(", ")}
                    </div>
                );
            }
            return <>{fallback}</>;
        }
    }

    // Check for a single permission
    if (permission && hasPermission(permission)) {
        return <>{children}</>;
    }

    // Check for multiple permissions
    if (permissions.length > 0) {
        if (requireAll && hasAllPermissions(permissions)) {
            return <>{children}</>;
        }

        if (!requireAll && hasAnyPermission(permissions)) {
            return <>{children}</>;
        }
    }

    // If the user doesn't have the required permissions, render the fallback
    return <>{fallback}</>;
};

export default PermissionGate;
