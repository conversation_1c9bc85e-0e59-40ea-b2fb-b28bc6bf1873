"use client";

import React from "react";
import Checkbox from "@/components/common/checkbox";

interface TermsCheckboxProps {
    checked: boolean;
    onCheckedChange: (checked: boolean) => void;
    error?: boolean;
    className?: string;
}

/**
 * Terms and Conditions checkbox component
 * Displays a checkbox with links to Terms of Service and Privacy Policy
 * Used in authentication forms to ensure user agreement before proceeding
 */
const TermsCheckbox: React.FC<TermsCheckboxProps> = ({ checked, onCheckedChange, error, className = "" }) => {
    // Generate unique ID for accessibility
    const checkboxId = React.useId();

    return (
        <div className={`${className} ${error ? "text-red-500" : ""}`}>
            <Checkbox
                id={checkboxId}
                checked={checked}
                onCheckedChange={onCheckedChange}
                styledLabel={
                    <span className="text-sm font-normal leading-[18px] text-[#151519] select-none">
                        I agree to FCMB's{" "}
                        <a
                            href="/documents/FCMB Corporate Internet Banking – User Terms and Conditions.pdf"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary underline hover:no-underline"
                            onClick={(e) => e.stopPropagation()}
                        >
                            Terms of Service
                        </a>{" "}
                        and{" "}
                        <a
                            href="/documents/FCMB-Corporate-Internet-Banking-Privacy-Policy.pdf"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary underline hover:no-underline"
                            onClick={(e) => e.stopPropagation()}
                        >
                            Privacy Policy
                        </a>
                    </span>
                }
            />
            {error && <p className="text-xs text-red-500 mt-1">You must agree to the terms to continue</p>}
        </div>
    );
};

export default TermsCheckbox;
