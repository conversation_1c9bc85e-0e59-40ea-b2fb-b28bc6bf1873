/**
 * Permission Fallback Components
 *
 * These components provide graceful fallback UI when the permission system
 * encounters errors or is unavailable.
 */

"use client";

import React from "react";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import permissionsService from "@/services/permissionsService";

interface PermissionFallbackProps {
    children: React.ReactNode;
    fallbackComponent?: React.ComponentType<{ error: string; onRetry: () => void }>;
}

/**
 * Default fallback component for permission errors
 */
const DefaultPermissionFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
        <div className="flex items-start">
            <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                    />
                </svg>
            </div>
            <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-yellow-800">Permission System Unavailable</h3>
                <div className="mt-2 text-sm text-yellow-700">
                    <p>The permission system is temporarily unavailable. Some features may be limited.</p>
                    {error && <p className="mt-1 text-xs text-yellow-600">Error: {error}</p>}
                </div>
                <div className="mt-3">
                    <button
                        onClick={onRetry}
                        className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                        Retry
                    </button>
                </div>
            </div>
        </div>
    </div>
);

/**
 * Permission fallback wrapper that handles permission system failures
 */
export const PermissionFallback: React.FC<PermissionFallbackProps> = ({
    children,
    fallbackComponent: FallbackComponent = DefaultPermissionFallback,
}) => {
    const { getErrorState, getLoadingState } = usePermissionCheck();
    const errorState = getErrorState();
    const loadingState = getLoadingState();

    const handleRetry = async () => {
        try {
            await permissionsService.refresh();
        } catch (error) {
            console.error("Failed to retry permission system:", error);
        }
    };

    // Show fallback if there are errors and system is not loading
    if (errorState.hasAnyError && !loadingState.isAnyLoading) {
        const error = errorState.systemPermissionsError ?? errorState.dynamicPermissionsError ?? "Unknown error";

        return <FallbackComponent error={error} onRetry={handleRetry} />;
    }

    // Render children normally if no errors
    return <>{children}</>;
};

/**
 * Higher-order component that wraps components with permission fallback
 */
export const withPermissionFallback = <P extends object>(
    Component: React.ComponentType<P>,
    fallbackOptions?: {
        fallbackComponent?: React.ComponentType<{ error: string; onRetry: () => void }>;
        showRetryButton?: boolean;
    }
) => {
    const WrappedComponent: React.FC<P> = (props) => (
        <PermissionFallback {...fallbackOptions}>
            <Component {...props} />
        </PermissionFallback>
    );

    WrappedComponent.displayName = `withPermissionFallback(${Component.displayName ?? Component.name})`;
    return WrappedComponent;
};

/**
 * Permission health indicator component
 */
export const PermissionHealthIndicator: React.FC<{
    showDetails?: boolean;
    className?: string;
}> = ({ showDetails = false, className = "" }) => {
    const [permissionState, setPermissionState] = React.useState(permissionsService.getState());

    React.useEffect(() => {
        const unsubscribe = permissionsService.subscribe(setPermissionState);
        return unsubscribe;
    }, []);

    const getStatusColor = () => {
        if (permissionState.isLoaded && !permissionState.error && permissionState.permissionsList.length > 0) {
            return "bg-green-500";
        } else if (permissionState.isLoaded && permissionState.permissionsList.length > 0) {
            return "bg-yellow-500";
        } else {
            return "bg-red-500";
        }
    };

    const getStatusText = () => {
        if (permissionState.isLoaded && !permissionState.error && permissionState.permissionsList.length > 0) {
            return "Healthy";
        } else if (permissionState.isLoaded && permissionState.permissionsList.length > 0) {
            return "Degraded";
        } else {
            return "Unhealthy";
        }
    };

    if (!showDetails) {
        return (
            <div className={`flex items-center space-x-2 ${className}`}>
                <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
                <span className="text-xs text-gray-600">Permissions: {getStatusText()}</span>
            </div>
        );
    }

    const isHealthy = permissionState.isLoaded && !permissionState.error && permissionState.permissionsList.length > 0;

    return (
        <div className={`bg-white rounded-lg shadow-sm border p-3 ${className}`}>
            <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Permission System Status</h4>
                <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
            </div>

            <div className="space-y-1 text-xs text-gray-600">
                <div className="flex justify-between">
                    <span>Status:</span>
                    <span className="font-medium">{getStatusText()}</span>
                </div>
                <div className="flex justify-between">
                    <span>Loaded:</span>
                    <span>{permissionState.isLoaded ? "Yes" : "No"}</span>
                </div>
                <div className="flex justify-between">
                    <span>Permissions:</span>
                    <span>{permissionState.permissionsList.length}</span>
                </div>
                <div className="flex justify-between">
                    <span>Last Fetched:</span>
                    <span>
                        {permissionState.lastFetched
                            ? new Date(permissionState.lastFetched).toLocaleTimeString()
                            : "Never"}
                    </span>
                </div>
                {permissionState.error && (
                    <div className="flex justify-between text-red-600">
                        <span>Error:</span>
                        <span>Yes</span>
                    </div>
                )}
            </div>

            {!isHealthy && (
                <button
                    onClick={() => permissionsService.refresh()}
                    className="mt-2 w-full bg-blue-50 hover:bg-blue-100 text-blue-700 py-1 px-2 rounded text-xs font-medium transition-colors"
                >
                    Retry
                </button>
            )}
        </div>
    );
};

/**
 * Emergency access component for critical functionality when permissions fail
 */
export const EmergencyAccess: React.FC<{
    children: React.ReactNode;
    emergencyPermissions: string[];
    reason?: string;
}> = ({ children, emergencyPermissions, reason = "Emergency access granted due to permission system failure" }) => {
    const { getErrorState } = usePermissionCheck();
    const errorState = getErrorState();

    // Only grant emergency access if there are permission system errors
    if (errorState.hasAnyError) {
        console.warn("EmergencyAccess: Granting emergency access", {
            permissions: emergencyPermissions,
            reason,
        });

        return (
            <div>
                <div className="bg-orange-50 border border-orange-200 rounded p-2 mb-2">
                    <p className="text-orange-800 text-xs">⚠️ Emergency access mode: {reason}</p>
                </div>
                {children}
            </div>
        );
    }

    // Normal permission checking should handle access
    return null;
};

export default PermissionFallback;
