"use client";
import { SupportIcon } from "@/components/icons/bill-payment-icons";
import ReactModal from "react-modal";
import { But<PERSON> } from "../buttonv3";
import CloseX from "../close-x";
import DeleteConfirmation from "../delete-confirmation";
import { openSupportDialog } from "@/redux/features/supportDialog";
import { useAppDispatch } from "@/redux/hooks";

/**
 * A support button component that appears at the bottom left of the drawer
 * @internal
 */
const SupportButton = () => {
    const dispatch = useAppDispatch();

    return (
        <div className="absolute bottom-0 left-0 right-0 bg-white  border-[#dbdbe0] flex-shrink-0">
            <div className="w-full px-14 py-4 bg-white">
                <Button
                    variant="text-neutral"
                    leftIcon={<SupportIcon className="w-5 h-5" />}
                    className="flex h-10 items-center gap-2 px-3.5 py-2"
                    aria-label="Get support"
                    type="button"
                    onClick={() => dispatch(openSupportDialog())}
                >
                    <span className="text-base font-semibold">Support</span>
                </Button>
            </div>
        </div>
    );
};

interface Props {
    /** Controls the visibility of the drawer */
    isOpen: boolean;
    /** Additional CSS classes to apply to the drawer */
    className?: string;
    /** Content to be rendered inside the drawer */
    children: React.ReactNode;
    /** Callback function called when the drawer should close */
    onClose: () => void;
    /** Title displayed at the top of the drawer */
    title?: string;
    /** Whether to show the support button at the bottom left */
    showSupport?: boolean;
    /** Whether to show a confirmation dialog when closing */
    showExitConfirmation?: boolean;
    /** Whether to disable default padding in the content area */
    disablePadding?: boolean;
    /** Callback function called when exit is confirmed */
    onConfirmExit?: () => void;
    /** Callback function called when exit is cancelled */
    onCancelExit?: () => void;
}

/**
 * A full-screen drawer component that slides in from the right.
 * Supports optional exit confirmation, support button, and customizable padding.
 *
 * @example
 * ```tsx
 * // Basic usage
 * <FullScreenDrawer
 *   isOpen={isOpen}
 *   onClose={() => setIsOpen(false)}
 *   title="My Drawer"
 * >
 *   <div>Content goes here</div>
 * </FullScreenDrawer>
 *
 * // With exit confirmation and support button
 * <FullScreenDrawer
 *   isOpen={isOpen}
 *   onClose={handleClose}
 *   title="Edit Profile"
 *   showExitConfirmation={true}
 *   showSupport={true}
 *   onConfirmExit={handleConfirmExit}
 *   onCancelExit={handleCancelExit}
 * >
 *   <form>Form content</form>
 * </FullScreenDrawer>
 * ```
 */
const FullScreenDrawer = ({
    title,
    isOpen,
    className = "",
    onClose,
    showSupport,
    showExitConfirmation = false,
    children,
    disablePadding,
    onConfirmExit = () => {},
    onCancelExit = () => {},
}: Props) => (
    <ReactModal
        isOpen={isOpen}
        onRequestClose={onClose}
        className={`max-w-none w-screen h-screen p-0 rounded-none outline-none ${className}`}
        overlayClassName="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
        ariaHideApp={false}
    >
        <div className="w-full h-full bg-white flex flex-col relative overflow-hidden">
            {/* Header - Fixed */}
            <header className="w-full h-16 px-14 py-4 bg-white border-b border-[#dbdbe0] flex justify-between items-center flex-shrink-0">
                <h1
                    className="text-[#151518] text-xl font-semibold leading-relaxed tracking-tight"
                    data-testid="modal-title"
                >
                    {title}
                </h1>
                <CloseX onClick={onClose} />
            </header>

            {/* Content - Scrollable */}
            <div
                className={`flex flex-col flex-1 overflow-y-auto ${disablePadding ? "" : "px-14 py-8"} ${showSupport ? "pb-20" : ""}`}
            >
                {children}
            </div>

            {/* Support - Fixed at bottom */}
            {showSupport && <SupportButton />}

            {/* Exit Modal */}
            <DeleteConfirmation
                isOpen={showExitConfirmation}
                onClose={onCancelExit}
                onConfirm={onConfirmExit}
                title="Are you sure you want to exit?"
                subtitle="Your progress will be lost if you exit now."
                confirmButtonText="Yes, exit"
                cancelButtonText="No, continue"
            />
        </div>
    </ReactModal>
);

export default FullScreenDrawer;
