/**
 * FileAttachment Component
 *
 * Purpose: Provides a drag-and-drop file upload interface with progress tracking and validation
 *
 * Functionality: This component handles file selection through drag-and-drop or click interactions,
 * validates file types and sizes, simulates upload progress, and provides visual feedback during
 * the upload process. It supports customizable file size limits, accepted file types, and
 * styling options while maintaining accessibility standards.
 *
 * Dependencies: React hooks (useRef, useState, useEffect), custom icons from bill-payment-icons,
 * and upload progress component for visual feedback during file uploads.
 *
 * Usage: Used throughout the application for file upload functionality in forms and data entry
 * screens. Accepts callbacks for file selection, upload state changes, and file removal events.
 * Default file size limit is set to 10MB to comply with platform standards for consistent
 * user experience and server load management.
 */

"use client";

import { File05Icon, UploadCloudIcon } from "@/components/icons/bill-payment-icons";
import { sendFeedback } from "@/functions/feedback";
import { useEffect, useRef, useState, type ReactNode } from "react";
import { IUploadState, UploadProgress } from "./upload-progress";

export interface FileAttachmentProps {
    // File size limit in MB - default set to 10MB per platform standards for consistent user experience
    maxSize?: number;
    acceptedTypes?: string[];
    onFilesSelected?: (files: FileList) => void;
    onUploadStateChange?: (isComplete: boolean) => void;
    onFileRemoved?: () => void;
    className?: string;
    width?: string;
    icon?: ReactNode;
    progressIcon?: ReactNode;
    headerText?: ReactNode;
    descriptionText?: string;
    initialFile?: File | null;
    storedFile?: {
        name: string;
        size: number;
    } | null;
}

export default function FileAttachment({
    // Updated default from 20MB to 10MB to comply with platform file size standards
    // This ensures consistent user experience and prevents server overload
    maxSize = 10,
    acceptedTypes = ["pdf", "png", "jpg", "csv"],
    onFilesSelected,
    onUploadStateChange,
    onFileRemoved,
    className = "",
    width = "282px",
    icon = <UploadCloudIcon className="w-5 h-5" />,
    progressIcon = <File05Icon className="w-5 h-5" />,
    headerText = "Attachments",
    descriptionText,
    initialFile,
    storedFile,
}: Readonly<FileAttachmentProps>) {
    const [isDragging, setIsDragging] = useState(false);
    const [uploadState, setUploadState] = useState<IUploadState | null>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const uploadControllerRef = useRef<AbortController | null>(null);

    useEffect(() => {
        if ((initialFile ?? storedFile) && !uploadState) {
            const file = initialFile ?? storedFile;
            setUploadState({
                fileName: file?.name ?? "",
                fileSize: file?.size ?? 0,
                progress: 100,
                timeRemaining: 0,
                isComplete: true,
            });
            onUploadStateChange?.(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialFile, storedFile, onUploadStateChange]);

    const handleDragEnter = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        handleFiles(e.dataTransfer.files);
    };

    const handleClick = () => {
        inputRef.current?.click();
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            handleClick();
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) handleFiles(files);
    };

    const handleFiles = (files: FileList) => {
        if (files.length === 0) return;

        const file = files[0];
        const fileType = file.name.split(".").pop()?.toLowerCase();

        if (!fileType || !acceptedTypes.includes(fileType)) {
            sendFeedback(`Only ${acceptedTypes.join(", ")} files are supported`, "error");
            return;
        }

        if (file.size > maxSize * 1024 * 1024) {
            sendFeedback(`File size must be less than ${maxSize}MB`, "error");
            return;
        }

        simulateUpload(file);
        if (onFilesSelected) onFilesSelected(files);
    };

    const simulateUpload = (file: File) => {
        if (uploadControllerRef.current) {
            uploadControllerRef.current.abort();
        }

        uploadControllerRef.current = new AbortController();

        const fileSizeInMB = file.size / (1024 * 1024);
        const totalUploadTimeInSeconds = Math.max(Math.ceil(fileSizeInMB), 1);
        const updateInterval = 100;
        const progressIncrement = (100 * updateInterval) / (totalUploadTimeInSeconds * 1000);

        setUploadState({
            fileName: file.name,
            fileSize: file.size,
            progress: 0,
            timeRemaining: totalUploadTimeInSeconds,
            isComplete: false,
        });
        onUploadStateChange?.(false);

        let progress = 0;
        const interval = setInterval(() => {
            if (uploadControllerRef.current?.signal.aborted) {
                clearInterval(interval);
                setUploadState(null);
                onUploadStateChange?.(false);
                return;
            }

            progress = Math.min(progress + progressIncrement, 100);
            const timeRemaining = Math.ceil((totalUploadTimeInSeconds * (100 - progress)) / 100);

            if (progress >= 100) {
                clearInterval(interval);
                setUploadState((prev: IUploadState | null) =>
                    prev
                        ? {
                              ...prev,
                              progress: 100,
                              isComplete: true,
                          }
                        : null
                );
                onUploadStateChange?.(true);
                return;
            }

            setUploadState((prev: IUploadState | null) =>
                prev
                    ? {
                          ...prev,
                          progress: Math.floor(progress),
                          timeRemaining,
                      }
                    : null
            );
        }, updateInterval);
    };

    const handleCancel = () => {
        if (uploadControllerRef.current) {
            uploadControllerRef.current.abort();
        }
        setUploadState(null);
        onUploadStateChange?.(false);
        if (inputRef.current) {
            inputRef.current.value = "";
        }
        onFileRemoved?.();
    };

    const defaultDescription = `We accept ${acceptedTypes.join(", ").toUpperCase()} files, up to ${maxSize}MB`;

    if (uploadState) {
        return (
            <div className="w-full relative rounded-lg bg-white border border-[#E3E5E8] p-3" style={{ width }}>
                <UploadProgress state={uploadState} onCancel={handleCancel} icon={progressIcon} />
            </div>
        );
    }

    return (
        <div className={"flex flex-col items-start gap-2.5"} style={{ width }} data-testid="file-attachment-root">
            <div className="text-[12px] font-semibold normal-case w-full leading-[16px] tracking-[0.24px] relative">
                {headerText}
            </div>

            <button
                type="button"
                className={`
          w-full
          flex items-center
          px-3 py-8
          gap-3
          rounded-lg
          border border-dashed border-[#E3E5E8]
          transition-colors duration-200
          ${isDragging ? "bg-[#F9F9FA]" : "bg-white"}
          hover:bg-[#F9F9FA] active:bg-[#E3E5E8]
          ${className}
        `}
                onClick={handleClick}
                onKeyDown={handleKeyPress}
                onDragEnter={handleDragEnter}
                onDragOver={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                aria-label={`Upload files. Accepted types: ${acceptedTypes.join(", ")}. Maximum size: ${maxSize}MB`}
            >
                <input
                    ref={inputRef}
                    type="file"
                    className="sr-only"
                    accept={acceptedTypes.map((type) => `.${type}`).join(",")}
                    onChange={handleFileChange}
                    aria-label={`Choose a file. Accepted types: ${acceptedTypes.join(", ")}. Maximum size: ${maxSize}MB`}
                    data-testid="file-upload-input"
                />

                <div className="w-[16.667px] h-[15px] flex-shrink-0 flex items-center justify-center">{icon}</div>

                <p
                    className="flex-1 text-[14px] leading-[18px] text-left"
                    style={{
                        color: "#3A3A41",
                        fontStyle: "normal",
                        fontWeight: 400,
                    }}
                >
                    {descriptionText ?? defaultDescription}
                </p>
            </button>
        </div>
    );
}
