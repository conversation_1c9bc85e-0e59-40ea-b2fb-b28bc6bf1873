import React from "react";
import { Tick } from "@/components/icons/bill-payment-icons";

interface ConfirmPasswordValidationProps {
    password: string;
    confirmPassword: string;
    isVisible: boolean;
}

const ConfirmPasswordValidation: React.FC<ConfirmPasswordValidationProps> = ({
    password,
    confirmPassword,
    isVisible,
}) => {
    if (!isVisible || !confirmPassword) return null;

    const passwordsMatch = password === confirmPassword;
    const hasConfirmPassword = confirmPassword.length > 0;

    if (!hasConfirmPassword) return null;

    return (
        <div className="mt-2">
            {passwordsMatch ? (
                <div className="mt-1 text-[#039855] font-medium text-sm flex items-center gap-1">
                    <Tick color="#12B76A" />
                    <span>Passwords match</span>
                </div>
            ) : (
                <>
                    {/* <div className="flex items-center gap-[0.25rem] text-[#3A3A41] text-[14px] font-medium leading-[18px] tracking-[0.28px] mt-2">
                    <InformationCircleIcon className="w-5 pt-[1.17px] px-[1.17px] text-[#90909D]" />{" "}
                    <span className="text-[14px] font-medium leading-[18px] tracking-[0.28px]">Passwords must match</span>
                </div> */}
                </>
            )}
        </div>
    );
};

export default ConfirmPasswordValidation;
