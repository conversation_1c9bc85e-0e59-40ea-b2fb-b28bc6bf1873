import React, { useState } from "react";
import { FormikProps } from "formik";
import LabelInput from "@/components/common/label-input";
import PasswordValidation from "@/components/common/password-validation";
import ConfirmPasswordValidation from "@/components/common/confirm-password-validation";

interface PasswordInputProps<T> {
    formik: FormikProps<T>;
    passwordFieldName: string;
    confirmPasswordFieldName?: string;
    label?: string;
    confirmLabel?: string;
    className?: string;
    showValidation?: boolean;
}

const PasswordInput = <T,>({
    formik,
    passwordFieldName,
    confirmPasswordFieldName,
    label = "Password",
    confirmLabel = "Confirm password",
    className = "",
    showValidation = true,
}: PasswordInputProps<T>) => {
    const [isPasswordFocused, setIsPasswordFocused] = useState(false);
    const [isConfirmPasswordFocused, setIsConfirmPasswordFocused] = useState(false);

    const password = formik.values[passwordFieldName as keyof T] as string;
    const confirmPassword = confirmPasswordFieldName
        ? (formik.values[confirmPasswordFieldName as keyof T] as string)
        : "";

    // Check if component validation is visible for confirm password
    const isConfirmValidationVisible = isConfirmPasswordFocused || confirmPassword.length > 0;

    return (
        <div className={className}>
            <div className="relative">
                <LabelInput
                    formik={formik}
                    name={passwordFieldName}
                    label={label}
                    type="password"
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => setIsPasswordFocused(false)}
                    placeholder="Create a strong password"
                />
                {showValidation && (
                    <PasswordValidation password={password} isVisible={isPasswordFocused || password.length > 0} />
                )}
            </div>

            {confirmPasswordFieldName && (
                <div className="mt-4">
                    <div className="relative">
                        <LabelInput
                            formik={formik}
                            name={confirmPasswordFieldName}
                            label={confirmLabel}
                            type="password"
                            onFocus={() => setIsConfirmPasswordFocused(true)}
                            onBlur={() => setIsConfirmPasswordFocused(false)}
                            showError={
                                formik.touched[confirmPasswordFieldName as keyof T] &&
                                formik.errors[confirmPasswordFieldName as keyof T] &&
                                !isConfirmValidationVisible
                            }
                            placeholder="Re-enter your password"
                        />
                        {showValidation && (
                            <ConfirmPasswordValidation
                                password={password}
                                confirmPassword={confirmPassword}
                                isVisible={isConfirmValidationVisible}
                            />
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default PasswordInput;
