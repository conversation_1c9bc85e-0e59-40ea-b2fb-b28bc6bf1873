"use client";

import React from "react";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import PermissionGate from "@/components/common/permission-gate";
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";

/**
 * Example component demonstrating how to use the permission system
 */
const PermissionExample: React.FC = () => {
    const { hasPermission, canPerformAction, isLoadingPermissions } = usePermissionCheck();
    const { permissions: dynamicPermissions, isLoaded } = useDynamicPermissions();

    // Example of checking a single permission using dynamic permissions
    const canViewAccounts = hasPermission(
        dynamicPermissions.ACCOUNTS_PERMISSIONS?.VIEW_ACCOUNTS_LIST || "View list of accounts"
    );

    // Example of checking if user can perform an action using dynamic permissions
    const canExportTransactions = canPerformAction(
        dynamicPermissions.TRANSACTIONS_PERMISSIONS?.EXPORT_TRANSACTIONS || "Export transactions"
    );

    if (isLoadingPermissions) {
        return <div>Loading permissions...</div>;
    }

    return (
        <div className="p-4 border rounded-md">
            <h2 className="text-xl font-bold mb-4">Permission System Example</h2>

            <div className="mb-4">
                <h3 className="text-lg font-semibold">Direct Permission Checks:</h3>
                <p>Can view accounts: {canViewAccounts ? "Yes" : "No"}</p>
                <p>Can export transactions: {canExportTransactions ? "Yes" : "No"}</p>
            </div>

            <div className="mb-4">
                <h3 className="text-lg font-semibold">Using PermissionGate Component:</h3>

                {/* Example with a single permission */}
                <PermissionGate
                    permission={
                        dynamicPermissions.ACCOUNTS_PERMISSIONS?.DOWNLOAD_ACCOUNT_STATEMENTS ||
                        "Download account statements"
                    }
                    fallback={<p className="text-red-500">You don't have permission to download statements</p>}
                >
                    <button className="bg-blue-500 text-white px-4 py-2 rounded">Download Account Statement</button>
                </PermissionGate>

                {/* Example with multiple permissions (any) */}
                <div className="mt-2">
                    <PermissionGate
                        permissions={[
                            dynamicPermissions.TRANSACTIONS_PERMISSIONS?.VIEW_TRANSACTION_DETAILS ||
                                "View transaction details",
                            dynamicPermissions.TRANSACTIONS_PERMISSIONS?.SEARCH_TRANSACTIONS || "Search transactions",
                        ]}
                        fallback={<p className="text-red-500">You need transaction view or search permissions</p>}
                    >
                        <button className="bg-green-500 text-white px-4 py-2 rounded">View Transaction Details</button>
                    </PermissionGate>
                </div>

                {/* Example with multiple permissions (all) */}
                <div className="mt-2">
                    <PermissionGate
                        permissions={[
                            TRANSACTIONS_PERMISSIONS.EXPORT_TRANSACTIONS,
                            TRANSACTIONS_PERMISSIONS.GENERATE_TRANSACTION_RECEIPTS,
                        ]}
                        requireAll={true}
                        fallback={
                            <p className="text-red-500">You need both export and receipt generation permissions</p>
                        }
                    >
                        <button className="bg-purple-500 text-white px-4 py-2 rounded">
                            Export Transactions with Receipts
                        </button>
                    </PermissionGate>
                </div>
            </div>
        </div>
    );
};

export default PermissionExample;
