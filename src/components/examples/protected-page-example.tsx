"use client";

import React from "react";
import withPer<PERSON><PERSON>he<PERSON> from "@/components/hoc/withPermissionCheck";
// Using hardcoded permission string for this example
// In a real application, you might want to use dynamic permissions

/**
 * Example of a page that requires specific permissions to access
 */
const ProtectedPageContent: React.FC = () => (
    <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Protected Page</h1>
        <p>
            This page is only accessible to users with the required permissions. If a user without the necessary
            permissions tries to access this page directly, they will be redirected to the dashboard or another
            specified page.
        </p>
    </div>
);

// Wrap the component with the permission check HOC
const ProtectedPage = withPermissionCheck(ProtectedPageContent, {
    // Specify the permissions required to access this page
    requiredPermissions: ["View team members", "View and edit roles"],
    // User needs any of these permissions (not all)
    requireAll: false,
    // Where to redirect if user doesn't have permissions
    redirectTo: "/dashboard",
});

export default ProtectedPage;
