import { checkPermissions, getUserPermissions } from "@/utils/server-permission-check";
// Note: For server-side components, we use hardcoded permission strings
// since we can't use client-side hooks. The server-side permission checking
// fetches permissions directly from the API.

/**
 * Example of a server component that uses server-side permission checking
 * This provides immediate protection without any client-side delay
 */
export default async function ServerProtectedPage() {
    // Check if the user has the required permissions
    // This will automatically redirect if the user doesn't have permissions
    await checkPermissions(["View team members"], false, "/dashboard");

    // If we get here, the user has the required permissions
    // We can also get the user's permissions to conditionally render parts of the page
    const userPermissions = await getUserPermissions();

    const canManageRoles = userPermissions.includes("Create custom roles");

    return (
        <div className="p-4">
            <h1 className="text-2xl font-bold mb-4">Server Protected Page</h1>
            <p className="mb-4">
                This page is protected at the server level. If a user without the required permissions tries to access
                this page, they will be redirected immediately without any client-side rendering or delay.
            </p>

            {canManageRoles && (
                <div className="mt-4 p-4 bg-blue-100 rounded">
                    <h2 className="text-xl font-semibold">Role Management</h2>
                    <p>This section is only visible to users who can manage roles.</p>
                </div>
            )}
        </div>
    );
}
