/**
 * Dynamic Permissions Constants
 *
 * This file provides backward-compatible access to permissions that are now
 * fetched dynamically from the API. It maintains the same export structure
 * as the original static permissions file while providing live data.
 *
 * Usage:
 * - For immediate access (may be empty initially): import directly from this file
 * - For reactive access: use the useDynamicPermissions hook
 * - For guaranteed loaded data: use the hook with loading states
 */

import permissionsService from "@/services/permissionsService";
import type { DynamicPermissions } from "@/types/permissions";
import { transformToBackwardCompatibleFormat, createEmptyPermissionsStructure } from "@/utils/permissionTransforms";

// Initialize the permissions service
permissionsService.initialize();

/**
 * Get current permissions state
 * Note: This may return empty objects initially until permissions are loaded
 */
function getCurrentPermissions(): DynamicPermissions {
    const state = permissionsService.getState();

    if (!state.isLoaded) {
        // Return empty structure if not loaded yet
        return createEmptyPermissionsStructure();
    }

    // Transform to backward-compatible format
    return transformToBackwardCompatibleFormat(state.allPermissions);
}

// Create a proxy object that always returns current permissions
const createPermissionsProxy = () =>
    new Proxy({} as DynamicPermissions, {
        get(_, prop: string) {
            const currentPermissions = getCurrentPermissions();
            return currentPermissions[prop as keyof DynamicPermissions];
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ownKeys(_) {
            const currentPermissions = getCurrentPermissions();
            return Object.keys(currentPermissions);
        },
        has(_, prop: string) {
            const currentPermissions = getCurrentPermissions();
            return prop in currentPermissions;
        },
        getOwnPropertyDescriptor(_, prop: string) {
            const currentPermissions = getCurrentPermissions();
            if (prop in currentPermissions) {
                return {
                    enumerable: true,
                    configurable: true,
                    value: currentPermissions[prop as keyof DynamicPermissions],
                };
            }
            return undefined;
        },
    });

// Create the dynamic permissions object
const dynamicPermissions = createPermissionsProxy();

// Export individual permission modules for backward compatibility
export const OVERVIEW_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().OVERVIEW_PERMISSIONS;
        },
    }
);

export const SETTINGS_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().SETTINGS_PERMISSIONS;
        },
    }
);

export const OUTGOING_PAYMENTS_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().OUTGOING_PAYMENTS_PERMISSIONS;
        },
    }
);

export const SEND_MONEY_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().SEND_MONEY_PERMISSIONS;
        },
    }
);

export const ACCOUNTS_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().ACCOUNTS_PERMISSIONS;
        },
    }
);

export const TRANSACTIONS_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().TRANSACTIONS_PERMISSIONS;
        },
    }
);

export const BILL_PAYMENTS_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().BILL_PAYMENTS_PERMISSIONS;
        },
    }
);

export const BENEFICIARIES_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().BENEFICIARIES_PERMISSIONS;
        },
    }
);

export const TEAM_MANAGEMENT_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().TEAM_MANAGEMENT_PERMISSIONS;
        },
    }
);

export const SUPPORT_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().SUPPORT_PERMISSIONS;
        },
    }
);

export const ONBOARDING_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().ONBOARDING_PERMISSIONS;
        },
    }
);

export const ALL_PERMISSIONS = new Proxy(
    {},
    {
        get() {
            return getCurrentPermissions().ALL_PERMISSIONS;
        },
    }
);

// Export the complete permissions object
export default dynamicPermissions;

/**
 * Utility functions for working with dynamic permissions
 */

/**
 * Check if permissions are loaded
 */
export const arePermissionsLoaded = (): boolean => permissionsService.getState().isLoaded;

/**
 * Get loading state
 */
export const arePermissionsLoading = (): boolean => permissionsService.getState().isLoading;

/**
 * Get error state
 */
export const getPermissionsError = (): string | null => permissionsService.getState().error;

/**
 * Refresh permissions
 */
export const refreshPermissions = (): Promise<void> => permissionsService.refresh();

/**
 * Wait for permissions to be loaded
 */
export const waitForPermissions = (): Promise<DynamicPermissions> =>
    new Promise((resolve, reject) => {
        const state = permissionsService.getState();

        if (state.isLoaded) {
            resolve(getCurrentPermissions());
            return;
        }

        if (state.error) {
            reject(new Error(state.error));
            return;
        }

        const unsubscribe = permissionsService.subscribe((newState) => {
            if (newState.isLoaded) {
                unsubscribe();
                resolve(getCurrentPermissions());
            } else if (newState.error) {
                unsubscribe();
                reject(new Error(newState.error));
            }
        });

        // Initialize if not already done
        permissionsService.initialize();
    });
