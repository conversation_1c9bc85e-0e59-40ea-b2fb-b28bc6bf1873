import { NextResponse, type NextRequest } from "next/server";
import { LoginStep } from "@/lib/login-utils";
import { PATH_AUTH } from "./routes/path";

// Define login routes and their required steps
const LOGIN_ROUTES: Record<string, LoginStep> = {
    [PATH_AUTH.login]: LoginStep.CREDENTIALS,
    ["/auth/login/magic-code"]: LoginStep.MAGIC_CODE,
    [PATH_AUTH.twoFaVerification]: LoginStep.MFA_VERIFICATION,
};

// Function to handle login flow middleware
export function handleLoginMiddleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    const response = NextResponse.next();

    // If user is accessing the main login page, clear auth tokens
    if (pathname === PATH_AUTH.login) {
        console.log("User accessing main login page, clearing auth tokens");
        response.cookies.delete("auth_token");
        response.cookies.delete("refresh_token");
    }

    // Get login progress from cookie
    const loginProgressCookie = request.cookies.get("login_progress")?.value;
    let currentStep = LoginStep.CREDENTIALS;

    if (loginProgressCookie) {
        try {
            currentStep = parseInt(loginProgressCookie, 10) as LoginStep;
        } catch (error) {
            console.error("Error parsing login progress cookie:", error);
        }
    }

    // Get the required step for the current route
    const requiredStep = LOGIN_ROUTES[pathname];

    // If this is a login route with a required step
    if (requiredStep) {
        console.log(`Login route: ${pathname}, required step: ${requiredStep}, current step: ${currentStep}`);

        // If the user is trying to access a step they haven't reached yet
        if (requiredStep > currentStep && requiredStep !== LoginStep.CREDENTIALS) {
            let redirectRoute = PATH_AUTH.login; // Default fallback
            for (const [route, step] of Object.entries(LOGIN_ROUTES)) {
                if (step === currentStep) {
                    redirectRoute = route;
                    break;
                }
            }

            console.log(`User trying to access step ${requiredStep} but has only reached step ${currentStep}`);
            const redirectResponse = NextResponse.redirect(new URL(redirectRoute, request.url));

            // Clear auth tokens when redirecting back to an earlier login step
            if (redirectRoute === PATH_AUTH.login) {
                redirectResponse.cookies.delete("auth_token");
                redirectResponse.cookies.delete("refresh_token");
                console.log("Cleared auth tokens when redirecting to login page");
            }

            return redirectResponse;
        }
    }

    // Allow access to the requested login route
    return response;
}
