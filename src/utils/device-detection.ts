/**
 * Utility functions for detecting browser and device information
 */

interface DeviceInfo {
    browserName: string;
    browserVersion: string;
    osName: string;
    deviceType: string;
    deviceName: string;
}

/**
 * Detects browser name and version from user agent
 */
export function detectBrowser(): { name: string; version: string } {
    if (typeof window === "undefined" || !window.navigator) {
        return { name: "unknown", version: "unknown" };
    }

    const userAgent = window.navigator.userAgent;
    let browserName = "unknown";
    let version = "unknown";

    // Detect Chrome
    if (/Chrome/.test(userAgent) && !/Chromium|Edge|Edg|OPR|Opera/.test(userAgent)) {
        browserName = "Chrome";
        const match = userAgent.match(/Chrome\/(\d+\.\d+)/);
        if (match) version = match[1];
    }
    // Detect Firefox
    else if (/Firefox/.test(userAgent)) {
        browserName = "Firefox";
        const match = userAgent.match(/Firefox\/(\d+\.\d+)/);
        if (match) version = match[1];
    }
    // Detect Safari
    else if (/Safari/.test(userAgent) && !/Chrome|Chromium|Edge|Edg|OPR|Opera/.test(userAgent)) {
        browserName = "Safari";
        const match = userAgent.match(/Version\/(\d+\.\d+)/);
        if (match) version = match[1];
    }
    // Detect Edge
    else if (/Edge|Edg/.test(userAgent)) {
        browserName = "Edge";
        const match = userAgent.match(/Edge\/(\d+\.\d+)|Edg\/(\d+\.\d+)/);
        if (match) version = match[1] || match[2];
    }
    // Detect Opera
    else if (/OPR|Opera/.test(userAgent)) {
        browserName = "Opera";
        const match = userAgent.match(/OPR\/(\d+\.\d+)|Opera\/(\d+\.\d+)/);
        if (match) version = match[1] || match[2];
    }

    return { name: browserName, version };
}

/**
 * Detects operating system from user agent
 */
export function detectOS(): string {
    if (typeof window === "undefined" || !window.navigator) {
        return "unknown";
    }

    const userAgent = window.navigator.userAgent;

    if (/Windows/.test(userAgent)) {
        return "Windows";
    } else if (/Macintosh|Mac OS X/.test(userAgent)) {
        return "macOS";
    } else if (/Linux/.test(userAgent)) {
        return "Linux";
    } else if (/Android/.test(userAgent)) {
        return "Android";
    } else if (/iPhone|iPad|iPod/.test(userAgent)) {
        return "iOS";
    }

    return "unknown";
}

/**
 * Detects device type from user agent
 */
export function detectDeviceType(): string {
    if (typeof window === "undefined" || !window.navigator) {
        return "desktop";
    }

    const userAgent = window.navigator.userAgent;

    if (/Mobi|Android|iPhone|iPad|iPod|Windows Phone/.test(userAgent)) {
        if (/iPad/.test(userAgent)) {
            return "tablet";
        }
        return "mobile";
    }

    return "desktop";
}

/**
 * Gets a friendly device name based on browser and OS
 */
export function getDeviceName(browserName: string, osName: string): string {
    return `${osName} ${browserName}`;
}

/**
 * Gets the browser icon path based on browser name
 */
export function getBrowserIconPath(browserName: string): string {
    const browserNameLower = browserName.toLowerCase();

    // Map browser names to icon paths
    const iconPaths: Record<string, string> = {
        chrome: "/icons/browser-icons/chrome.svg",
        firefox: "/icons/browser-icons/firefox.svg",
        safari: "/icons/browser-icons/safari.svg",
        edge: "/icons/browser-icons/edge.svg",
        opera: "/icons/browser-icons/opera.svg",
    };

    // Return the icon path or a default icon if not found
    return iconPaths[browserNameLower] || "/icons/browser-icons/chrome.svg";
}

/**
 * Gets complete device information
 */
export function getDeviceInfo(): DeviceInfo & { browserIconPath: string } {
    const { name: browserName, version: browserVersion } = detectBrowser();
    const osName = detectOS();
    const deviceType = detectDeviceType();
    const deviceName = getDeviceName(browserName, osName);
    const browserIconPath = getBrowserIconPath(browserName);

    return {
        browserName,
        browserVersion,
        osName,
        deviceType,
        deviceName,
        browserIconPath,
    };
}
