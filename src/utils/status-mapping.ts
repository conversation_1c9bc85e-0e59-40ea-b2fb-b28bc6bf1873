// Define the badge colors that match the Badge component
export type BadgeColor = "success" | "warning" | "error" | "neutral" | "brand";

// Define the return type for status mapping
export interface StatusMapping {
    /** The display text for the status */
    text: string;
    /** The color variant for badge styling */
    color: BadgeColor;
    /** The raw status value (normalized) */
    rawStatus: string;
}

// Define supported transfer types
export type TransferType = "instant" | "scheduled" | "recurring";

// Define all possible status values as constants to prevent typos
export const STATUS_VALUES = {
    // Success states
    SUCCESSFUL: "Successful",

    // Pending/Warning states
    PENDING: "Pending",
    AWAITING_APPROVAL: "Awaiting Approval",

    // Processing states
    PROCESSING: "Processing",

    // Error states
    FAILED: "Failed",
    REJECTED_APPROVAL: "Rejected Approval",

    // Unknown states
    UNKNOWN: "Unknown",
} as const;

// Type for status values
export type StatusValue = (typeof STATUS_VALUES)[keyof typeof STATUS_VALUES];

/**
 * Normalizes status input to handle various formats and cases
 * @param status The raw status string
 * @returns Normalized status string
 */
function normalizeStatus(status: string): string {
    if (!status || typeof status !== "string") {
        return "";
    }

    // Convert to lowercase and trim whitespace
    const normalized = status.toLowerCase().trim();

    // Handle common variations
    const statusMappings: Record<string, string> = {
        successful: STATUS_VALUES.SUCCESSFUL,
        pending: STATUS_VALUES.PENDING,
        "awaiting approval": STATUS_VALUES.AWAITING_APPROVAL,
        awaiting_approval: STATUS_VALUES.AWAITING_APPROVAL,
        processing: STATUS_VALUES.PROCESSING,
        failed: STATUS_VALUES.FAILED,
        "rejected approval": STATUS_VALUES.REJECTED_APPROVAL,
        rejected_approval: STATUS_VALUES.REJECTED_APPROVAL,
    };

    return statusMappings[normalized] || status;
}

/**
 * Maps a status to its appropriate color based on the status type
 * @param status The normalized status string
 * @returns The appropriate badge color
 */
function getStatusColor(status: string): BadgeColor {
    switch (status) {
        case STATUS_VALUES.SUCCESSFUL:
            return "success";

        case STATUS_VALUES.FAILED:
        case STATUS_VALUES.REJECTED_APPROVAL:
            return "error";

        case STATUS_VALUES.PENDING:
        case STATUS_VALUES.AWAITING_APPROVAL:
            return "warning";

        case STATUS_VALUES.PROCESSING:
            return "brand";

        default:
            return "neutral";
    }
}

/**
 * Main function to get status mapping for any transfer type
 *
 * This function provides consistent status mapping across all transfer types,
 * ensuring that the same status appears the same way regardless of context.
 *
 * @param status The raw status string from the API or component
 * @param transferType The type of transfer (instant, scheduled, recurring)
 * @returns StatusMapping object with text, color, and rawStatus
 */
export function getStatusMapping(status: string | undefined | null): StatusMapping {
    // Handle null/undefined/empty status
    if (!status) {
        return {
            text: STATUS_VALUES.UNKNOWN,
            color: "neutral",
            rawStatus: STATUS_VALUES.UNKNOWN,
        };
    }

    // Normalize the status
    const normalizedStatus = normalizeStatus(status);

    // Get the appropriate color
    const color = getStatusColor(normalizedStatus);

    // For now, all transfer types use the same mapping
    // This structure allows for future differentiation if needed
    return {
        text: normalizedStatus || status,
        color,
        rawStatus: normalizedStatus || status,
    };
}

/**
 * Utility function to check if a status represents a successful state
 * @param status The status to check
 * @returns True if the status represents success
 */
export function isSuccessfulStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.SUCCESSFUL;
}

/**
 * Utility function to check if a status represents a pending state
 * @param status The status to check
 * @returns True if the status represents a pending state
 */
export function isPendingStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.PENDING;
}

/**
 * Utility function to check if a status represents an awaiting approval state
 * @param status The status to check
 * @returns True if the status represents awaiting approval
 */
export function isAwaitingApprovalStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.AWAITING_APPROVAL;
}

/**
 * Utility function to check if a status represents a failed state
 * @param status The status to check
 * @returns True if the status represents failure
 */
export function isFailedStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.FAILED;
}

/**
 * Utility function to check if a status represents a rejected approval state
 * @param status The status to check
 * @returns True if the status represents rejected approval
 */
export function isRejectedApprovalStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.REJECTED_APPROVAL;
}

/**
 * Utility function to check if a status represents a processing state
 * @param status The status to check
 * @returns True if the status represents processing
 */
export function isProcessingStatus(status: string): boolean {
    const normalizedStatus = normalizeStatus(status);
    return normalizedStatus === STATUS_VALUES.PROCESSING;
}

/**
 * Get all available status values
 * @returns Array of all possible status values
 */
export function getAllStatusValues(): StatusValue[] {
    return Object.values(STATUS_VALUES);
}

/**
 * Legacy compatibility function for existing getStatusColor usage
 * This maintains backward compatibility with existing code while
 * encouraging migration to the new getStatusMapping function
 *
 * @deprecated Use getStatusMapping instead for better consistency
 * @param status The status to get color for
 * @returns The badge color
 */
export function getStatusColorLegacy(status: string): BadgeColor {
    return getStatusColor(normalizeStatus(status));
}
