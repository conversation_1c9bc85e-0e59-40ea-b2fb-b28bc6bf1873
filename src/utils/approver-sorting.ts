/**
 * Utility functions for sorting approver data
 */

interface ApproverData {
    id: number;
    userId: number;
    name: string;
    role: string;
    approvalLevel: number;
    status: "APPROVE" | "DECLINE" | null;
    date: string | null;
}

/**
 * Sort approvers by their status in the correct order:
 * 1. Approved (APPROVE) - at the top
 * 2. Declined (DECLINE) - in the middle
 * 3. Pending (null) - at the bottom
 *
 * Within each status group, sort by approval level (ascending)
 */
export function sortApproversByStatus(approvers: ApproverData[]): ApproverData[] {
    return [...approvers].sort((a, b) => {
        // Define status priority (lower number = higher priority)
        const getStatusPriority = (status: "APPROVE" | "DECLINE" | null): number => {
            if (status === "APPROVE") return 1; // Approved first
            if (status === "DECLINE") return 2; // Declined second
            return 3; // Pending (null) last
        };

        const priorityA = getStatusPriority(a.status);
        const priorityB = getStatusPriority(b.status);

        // If status priority is different, sort by status
        if (priorityA !== priorityB) {
            return priorityA - priorityB;
        }

        // If status is the same, sort by approval level (ascending)
        return a.approvalLevel - b.approvalLevel;
    });
}

/**
 * Sort approvers by approval level only (original behavior)
 */
export function sortApproversByLevel(approvers: ApproverData[]): ApproverData[] {
    return [...approvers].sort((a, b) => a.approvalLevel - b.approvalLevel);
}
