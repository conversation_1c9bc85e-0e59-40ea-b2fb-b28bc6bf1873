/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Super Admin Permission Handler
 *
 * This utility provides helper functions for handling Super Admin (roleId 0) permissions
 * and debugging permission-related issues.
 */

import { jwtDecode } from "jwt-decode";
import { cookies } from "@/lib/cookies";
import type { DecodedToken } from "@/utils/server-permission-check";

/**
 * Check if the current user is a Super Admin (roleId 0)
 */
export const isSuperAdmin = (): boolean => {
    try {
        const token = cookies.getToken();
        if (!token) return false;

        const decodedToken = jwtDecode(token) as DecodedToken;
        return decodedToken?.roleid === 0;
    } catch (error) {
        console.error("Error checking Super Admin status:", error);
        return false;
    }
};

/**
 * Get the current user's role ID from the JWT token
 */
export const getCurrentUserRoleId = (): number | null => {
    try {
        const token = cookies.getToken();
        if (!token) return null;

        const decodedToken = jwtDecode(token) as DecodedToken;
        return decodedToken?.roleid ?? null;
    } catch (error) {
        console.error("Error getting user role ID:", error);
        return null;
    }
};

/**
 * Get the current user's ID from the JWT token
 */
export const getCurrentUserId = (): string | null => {
    try {
        const token = cookies.getToken();
        if (!token) return null;

        const decodedToken = jwtDecode(token) as DecodedToken;
        return decodedToken?.userId?.toString() ?? null;
    } catch (error) {
        console.error("Error getting user ID:", error);
        return null;
    }
};

/**
 * Debug function to log current user's role and permission status
 */
export const debugUserPermissionStatus = (): void => {
    const roleId = getCurrentUserRoleId();
    const userId = getCurrentUserId();
    const isSuper = isSuperAdmin();

    console.group("🔍 User Permission Debug Info");
    console.log("User ID:", userId);
    console.log("Role ID:", roleId);
    console.log("Is Super Admin:", isSuper);

    if (isSuper) {
        console.log("✅ Super Admin detected - should have all permissions");
        console.log("🚫 Should NOT make API call to /v1/role/0");
        console.log("✅ Should fetch all permissions from /v1/permissions");
        console.log("📋 Fixed locations:");
        console.log("  - src/redux/actions/permissionsActions.ts (fetchUserPermissions)");
        console.log("  - src/redux/actions/rolesActions.ts (getRoleById)");
        console.log("  - src/utils/server-permission-check.ts (checkPermission & getUserPermissions)");
    } else {
        console.log("👤 Regular user - will fetch role-specific permissions");
        console.log(`📞 Will make API call to /v1/role/${roleId}`);
    }
    console.groupEnd();
};

/**
 * Validate that Super Admin permissions are working correctly
 */
export const validateSuperAdminPermissions = async (): Promise<{
    isValid: boolean;
    errors: string[];
    permissionCount: number;
}> => {
    const errors: string[] = [];
    let permissionCount = 0;

    try {
        if (!isSuperAdmin()) {
            errors.push("User is not a Super Admin");
            return { isValid: false, errors, permissionCount };
        }

        // Check if we can access the Redux store
        const { store } = await import("@/redux/index");
        const state = store.getState();

        const userPermissions = state.permissions?.userPermissions || [];
        const systemPermissions = state.permissions?.systemPermissions || [];

        permissionCount = userPermissions.length;

        if (userPermissions.length === 0) {
            errors.push("Super Admin has no user permissions loaded");
        }

        if (systemPermissions.length === 0) {
            errors.push("No system permissions loaded");
        }

        // Super Admin should have all available permissions
        if (userPermissions.length > 0 && systemPermissions.length > 0) {
            if (userPermissions.length !== systemPermissions.length) {
                errors.push(
                    `Super Admin permission mismatch: has ${userPermissions.length} permissions but system has ${systemPermissions.length}`
                );
            }
        }

        const isValid = errors.length === 0;

        if (isValid) {
            console.log(`✅ Super Admin validation passed: ${permissionCount} permissions`);
        } else {
            console.error("❌ Super Admin validation failed:", errors);
        }

        return { isValid, errors, permissionCount };
    } catch (error) {
        errors.push(`Validation error: ${error instanceof Error ? error.message : "Unknown error"}`);
        return { isValid: false, errors, permissionCount };
    }
};

/**
 * Helper function to check if a specific permission error is related to Super Admin
 */
export const isSuperAdminPermissionError = (error: any): boolean => {
    if (!error) return false;

    const errorMessage = error.message || error.toString();

    // Check for common Super Admin related errors
    const superAdminErrorPatterns = [
        /Role with identifier 0 does not exist/i,
        /error\.msg\.role\.not\.found/i,
        /\/v1\/role\/0/i,
        /roleId.*0.*not.*found/i,
    ];

    return superAdminErrorPatterns.some((pattern) => pattern.test(errorMessage));
};

/**
 * Get a user-friendly error message for Super Admin permission issues
 */
export const getSuperAdminErrorMessage = (error: any): string => {
    if (!isSuperAdminPermissionError(error)) {
        return "Unknown permission error";
    }

    return "Super Admin role detected but permission initialization failed. This has been automatically handled - you should have full access to all features.";
};

/**
 * Development helper to simulate different role scenarios
 */
export const simulateRoleForTesting = (roleId: number): void => {
    if (process.env.NODE_ENV !== "development") {
        console.warn("simulateRoleForTesting can only be used in development mode");
        return;
    }

    console.warn(`🧪 TESTING MODE: Simulating roleId ${roleId}`);

    // This would require modifying the token or Redux state for testing
    // Implementation depends on your testing strategy
};

const superAdminPermissionHandler = {
    isSuperAdmin,
    getCurrentUserRoleId,
    getCurrentUserId,
    debugUserPermissionStatus,
    validateSuperAdminPermissions,
    isSuperAdminPermissionError,
    getSuperAdminErrorMessage,
    simulateRoleForTesting,
};

export default superAdminPermissionHandler;

