import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { redirect } from "next/navigation";
import { PATH_PROTECTED } from "@/routes/path";

// Define interfaces for type safety
export interface DecodedToken {
    roleid: number;
    exp?: number;
    iat?: number;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
}

export interface Permission {
    id: number;
    name: string;
    appModule: string;
    endpoint: string;
    method: string;
    createdDate: string | null;
    createdBy: string | null;
    lastModifiedDate: string | null;
    lastModifiedBy: string | null;
}

/**
 * Note about "unreachable code" warnings:
 *
 * In Next.js, the redirect() function throws an error that gets caught by Next.js's
 * error boundary, so code after redirect() is technically unreachable.
 * However, we keep the return statements for:
 * 1. Type safety (function signature requires a boolean return)
 * 2. Testing scenarios where redirect might be mocked
 * 3. Future-proofing against API changes
 */

/**
 * Server-side utility to check if a user has the required permissions
 * This can be used in Server Components or API routes
 *
 * @param requiredPermissions - Array of permissions required to access the resource
 * @param requireAll - If true, all permissions are required. If false, any one is sufficient.
 * @param redirectPath - Path to redirect to if user doesn't have permissions
 * @returns Boolean indicating if the user has the required permissions
 */
export async function checkPermissions(
    requiredPermissions: string[],
    requireAll: boolean = false,
    redirectPath: string = PATH_PROTECTED.root
): Promise<boolean> {
    try {
        // Get the token from cookies
        const cookieStore = cookies();
        const token = cookieStore.get("auth_token")?.value;

        if (!token) {
            // In Next.js, redirect() throws an error and doesn't actually return
            // The return false is only reached in tests or if redirect is mocked
            redirect(PATH_PROTECTED.root);
            return false; // This line is unreachable in normal operation
        }

        // Decode the token to get the roleId
        const decodedToken = jwtDecode(token) as DecodedToken;
        const roleId = decodedToken?.roleid;

        if (roleId === undefined || roleId === null) {
            redirect(redirectPath);
            return false;
        }

        let roleData: any;

        // Special handling for Super Admin (roleId 0)
        if (roleId === 0) {
            // Super Admin gets all available permissions
            const systemPermissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/permissions`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!systemPermissionsResponse.ok) {
                redirect(redirectPath);
                return false;
            }

            const allPermissions = await systemPermissionsResponse.json();

            // Create a mock role data structure for Super Admin with all permissions
            roleData = {
                permissions: allPermissions.map((p: any) => p.id), // All permission IDs
                name: "Super Admin",
            };
        } else {
            // Regular role - fetch role details including permissions
            const roleResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/role/${roleId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!roleResponse.ok) {
                redirect(redirectPath);
                return false;
            }

            roleData = await roleResponse.json();
        }

        if (!roleData?.permissions) {
            redirect(redirectPath);
            return false;
        }

        // Fetch all permissions to map IDs to permission names
        const permissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/permissions`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!permissionsResponse.ok) {
            redirect(redirectPath);
            return false;
        }

        const allPermissions = await permissionsResponse.json();

        // Map permission IDs to permission names
        const userPermissions = roleData.permissions
            .map((permId: number) => {
                const permission = allPermissions.find((p: Permission) => p.id === permId);
                return permission ? permission.name : null;
            })
            .filter(Boolean);

        // Check if the user has the required permissions
        if (requireAll) {
            // User must have all required permissions
            const hasAllRequired = requiredPermissions.every((permission) => userPermissions.includes(permission));

            if (!hasAllRequired) {
                redirect(redirectPath);
                return false;
            }

            return true;
        } else {
            // User must have at least one of the required permissions
            const hasAnyRequired = requiredPermissions.some((permission) => userPermissions.includes(permission));

            if (!hasAnyRequired) {
                redirect(redirectPath);
                return false;
            }

            return true;
        }
    } catch (error) {
        console.error("Error checking permissions:", error);
        redirect(redirectPath);
        return false;
    }
}

/**
 * Server-side utility to get user permissions
 * This can be used in Server Components or API routes
 *
 * @returns Array of permission names the user has
 */
export async function getUserPermissions(): Promise<string[]> {
    try {
        // Get the token from cookies
        const cookieStore = cookies();
        const token = cookieStore.get("auth_token")?.value;

        if (!token) {
            return [];
        }

        // Decode the token to get the roleId
        const decodedToken = jwtDecode(token) as DecodedToken;
        const roleId = decodedToken?.roleid;

        if (roleId === undefined || roleId === null) {
            return [];
        }

        let roleData: any;

        // Special handling for Super Admin (roleId 0)
        if (roleId === 0) {
            // Super Admin gets all available permissions
            const systemPermissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/permissions`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!systemPermissionsResponse.ok) {
                return [];
            }

            const allPermissions = await systemPermissionsResponse.json();

            // Return all permission names for Super Admin
            return allPermissions.map((p: any) => p.name);
        } else {
            // Regular role - fetch role details including permissions
            const roleResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/role/${roleId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!roleResponse.ok) {
                return [];
            }

            roleData = await roleResponse.json();
        }

        // For regular roles, process the permissions

        if (!roleData?.permissions) {
            return [];
        }

        // Fetch all permissions to map IDs to permission names
        const permissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/permissions`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!permissionsResponse.ok) {
            return [];
        }

        const allPermissions = await permissionsResponse.json();

        // Map permission IDs to permission names
        const userPermissions = roleData.permissions
            .map((permId: number) => {
                const permission = allPermissions.find((p: Permission) => p.id === permId);
                return permission ? permission.name : null;
            })
            .filter(Boolean);

        return userPermissions;
    } catch (error) {
        console.error("Error getting user permissions:", error);
        return [];
    }
}

/**
 * Server-side utility to get all available permissions from the system
 * Useful for role management and permission validation
 */
export async function getAllSystemPermissions(): Promise<Permission[]> {
    try {
        const cookieStore = cookies();
        const token = cookieStore.get("auth_token")?.value;

        if (!token) {
            return [];
        }

        const permissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_USER_API_URL}/v1/permissions`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!permissionsResponse.ok) {
            return [];
        }

        return await permissionsResponse.json();
    } catch (error) {
        console.error("Error getting system permissions:", error);
        return [];
    }
}

/**
 * Server-side utility to validate that permissions exist in the system
 * Useful for development and debugging
 */
export async function validatePermissions(permissionNames: string[]): Promise<{
    valid: boolean;
    missing: string[];
    existing: string[];
}> {
    try {
        const systemPermissions = await getAllSystemPermissions();
        const systemPermissionNames = new Set(systemPermissions.map((p) => p.name));

        const missing = permissionNames.filter((p) => !systemPermissionNames.has(p));
        const existing = permissionNames.filter((p) => systemPermissionNames.has(p));

        return {
            valid: missing.length === 0,
            missing,
            existing,
        };
    } catch (error) {
        console.error("Error validating permissions:", error);
        return {
            valid: false,
            missing: permissionNames,
            existing: [],
        };
    }
}
