/**
 * Permission Transformation Utilities
 *
 * Shared utilities for transforming API permissions data to backward-compatible format.
 * This eliminates code duplication between useDynamicPermissions.ts and dynamicPermissions.ts
 */

import type { DynamicPermissions } from "@/types/permissions";
import type { AllPermissions } from "@/services/permissionsService";

/**
 * Mapping from API module names to our expected structure
 */
export const MODULE_MAPPING: Record<string, keyof DynamicPermissions> = {
    OVERVIEW_PERMISSIONS: "OVERVIEW_PERMISSIONS",
    DASHBOARD_PERMISSIONS: "OVERVIEW_PERMISSIONS",
    SETTINGS_PERMISSIONS: "SETTINGS_PERMISSIONS",
    USER_MANAGEMENT_PERMISSIONS: "SETTINGS_PERMISSIONS",
    OUTGOING_PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
    PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
    SEND_MONEY_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
    TRANSFER_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
    ACCOUNTS_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
    ACCOUNT_MANAGEMENT_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
    TRANSACTIONS_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
    TRANSACTION_MANAGEMENT_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
    BILL_PAYMENTS_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
    BILL_MANAGEMENT_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
    BENEFICIARIES_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
    BENEFICIARY_MANAGEMENT_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
    TEAM_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
    ROLE_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
    SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
    CUSTOMER_SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
    ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
    USER_ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
};

/**
 * Create an empty DynamicPermissions structure
 */
export function createEmptyPermissionsStructure(): DynamicPermissions {
    return {
        OVERVIEW_PERMISSIONS: {},
        SETTINGS_PERMISSIONS: {},
        OUTGOING_PAYMENTS_PERMISSIONS: {},
        SEND_MONEY_PERMISSIONS: {},
        ACCOUNTS_PERMISSIONS: {},
        TRANSACTIONS_PERMISSIONS: {},
        BILL_PAYMENTS_PERMISSIONS: {},
        BENEFICIARIES_PERMISSIONS: {},
        TEAM_MANAGEMENT_PERMISSIONS: {},
        SUPPORT_PERMISSIONS: {},
        ONBOARDING_PERMISSIONS: {},
        ALL_PERMISSIONS: {},
    };
}

/**
 * Transform API permissions to backward-compatible format
 */
export function transformToBackwardCompatibleFormat(
    allPermissions: AllPermissions | Record<string, unknown>
): DynamicPermissions {
    // Create empty structure
    const result = createEmptyPermissionsStructure();

    // Populate permissions from API data
    Object.entries(allPermissions).forEach(([apiModule, permissions]) => {
        const targetModule = MODULE_MAPPING[apiModule] || "ALL_PERMISSIONS";

        // Ensure permissions is an object (handle both AllPermissions and generic Record types)
        const permissionsObj = typeof permissions === "object" && permissions !== null ? permissions : {};

        // Add to specific module
        if (targetModule !== "ALL_PERMISSIONS") {
            result[targetModule] = { ...result[targetModule], ...permissionsObj };
        }

        // Add to ALL_PERMISSIONS
        result.ALL_PERMISSIONS = { ...result.ALL_PERMISSIONS, ...permissionsObj };
    });

    return result;
}

/**
 * Normalize permission name to constant format
 */
export const normalizePermissionName = (name: string): string => name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, "_")
        .replace(/_+/g, "_")
        .replace(/(^_|_$)/g, "");

/**
 * Normalize module name to constant format
 */
export const normalizeModuleName = (moduleName: string): string => {
    const normalized = moduleName
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, "_")
        .replace(/_+/g, "_")
        .replace(/(^_|_$)/g, "");

    return normalized.endsWith("_PERMISSIONS") ? normalized : `${normalized}_PERMISSIONS`;
};

/**
 * Group permissions by module with custom grouping rules
 */
export const groupPermissionsByModule = (
    permissions: Array<{ name: string; appModule: string }>
): Record<string, Record<string, string>> => {
    const moduleGroups: Record<string, Record<string, string>> = {};

    permissions.forEach((permission) => {
        const moduleName = normalizeModuleName(permission.appModule || "General");
        const permissionKey = normalizePermissionName(permission.name);

        if (!moduleGroups[moduleName]) {
            moduleGroups[moduleName] = {};
        }

        moduleGroups[moduleName][permissionKey] = permission.name;
    });

    return moduleGroups;
};

/**
 * Create permission lookup map for fast checking
 */
export const createPermissionLookup = (permissions: string[]): Set<string> => new Set(permissions);

/**
 * Check if user has permission with fallback logic
 */
export const checkPermissionWithFallback = (
    userPermissions: string[],
    requiredPermission: string,
    fallbackPermissions: string[] = []
): boolean => {
    // Check primary permission
    if (userPermissions.includes(requiredPermission)) {
        return true;
    }

    // Check fallback permissions
    return fallbackPermissions.some((fallback) => userPermissions.includes(fallback));
};
