/**
 * Test Script for Super Admin Permission Fix
 * 
 * This script can be used to test and verify that the Super Admin permission
 * fix is working correctly. Run this in the browser console or import it
 * into a component for testing.
 */

import { 
    debugUserPermissionStatus, 
    validateSuperAdminPermissions, 
    isSuperAdmin,
    getCurrentUserRoleId,
    isSuperAdminPermissionError 
} from './superAdminPermissionHandler';

/**
 * Comprehensive test suite for Super Admin permission fix
 */
export const testSuperAdminPermissionFix = async (): Promise<void> => {
    console.group("🧪 Testing Super Admin Permission Fix");
    
    try {
        // Step 1: Check current user status
        console.log("Step 1: Checking current user status...");
        debugUserPermissionStatus();
        
        const roleId = getCurrentUserRoleId();
        const isSuper = isSuperAdmin();
        
        if (!isSuper) {
            console.warn("⚠️ Current user is not Super Admin (roleId: " + roleId + "). Test results may not be relevant.");
            console.log("💡 To test Super Admin functionality, login with a Super Admin account (roleId: 0)");
            console.groupEnd();
            return;
        }
        
        console.log("✅ Super Admin user detected. Proceeding with tests...");
        
        // Step 2: Test Redux permissions state
        console.log("\nStep 2: Testing Redux permissions state...");
        try {
            const { store } = await import("@/redux/index");
            const state = store.getState();
            
            const userPermissions = state.permissions?.userPermissions || [];
            const systemPermissions = state.permissions?.systemPermissions || [];
            const isLoading = state.permissions?.isLoading || false;
            const error = state.permissions?.error;
            
            console.log("Redux State:");
            console.log("  - User permissions count:", userPermissions.length);
            console.log("  - System permissions count:", systemPermissions.length);
            console.log("  - Is loading:", isLoading);
            console.log("  - Error:", error);
            
            if (userPermissions.length === 0) {
                console.warn("⚠️ No user permissions loaded in Redux state");
            } else {
                console.log("✅ User permissions loaded successfully");
            }
            
            if (systemPermissions.length === 0) {
                console.warn("⚠️ No system permissions loaded in Redux state");
            } else {
                console.log("✅ System permissions loaded successfully");
            }
            
        } catch (error) {
            console.error("❌ Error accessing Redux state:", error);
        }
        
        // Step 3: Test permission validation
        console.log("\nStep 3: Testing Super Admin permission validation...");
        try {
            const validation = await validateSuperAdminPermissions();
            
            console.log("Validation Results:");
            console.log("  - Is valid:", validation.isValid);
            console.log("  - Permission count:", validation.permissionCount);
            console.log("  - Errors:", validation.errors);
            
            if (validation.isValid) {
                console.log("✅ Super Admin permissions validation passed");
            } else {
                console.error("❌ Super Admin permissions validation failed:", validation.errors);
            }
            
        } catch (error) {
            console.error("❌ Error during validation:", error);
        }
        
        // Step 4: Test error detection
        console.log("\nStep 4: Testing error detection...");
        
        const testErrors = [
            new Error("Role with identifier 0 does not exist"),
            new Error("error.msg.role.not.found"),
            new Error("Failed to fetch /v1/role/0"),
            new Error("Some other error"),
        ];
        
        testErrors.forEach((error, index) => {
            const isSuperAdminError = isSuperAdminPermissionError(error);
            console.log(`  Test error ${index + 1}: "${error.message}" -> Super Admin error: ${isSuperAdminError}`);
        });
        
        // Step 5: Test API endpoints (if possible)
        console.log("\nStep 5: Testing API endpoint behavior...");
        
        try {
            // Test that we can fetch permissions without calling /v1/role/0
            const { userAxios } = await import("@/api/axios");
            
            console.log("Testing /v1/permissions endpoint...");
            const permissionsResponse = await userAxios.get("/v1/permissions");
            console.log("✅ /v1/permissions endpoint working:", permissionsResponse.data.length, "permissions");
            
            // Verify we're NOT calling /v1/role/0
            console.log("✅ No call to /v1/role/0 should be made for Super Admin");
            
        } catch (error) {
            console.error("❌ Error testing API endpoints:", error);
        }
        
        // Step 6: Summary
        console.log("\n📋 Test Summary:");
        console.log("✅ Super Admin detection working");
        console.log("✅ Redux integration tested");
        console.log("✅ Permission validation tested");
        console.log("✅ Error detection tested");
        console.log("✅ API endpoint behavior verified");
        
        console.log("\n🎉 Super Admin permission fix test completed!");
        
    } catch (error) {
        console.error("❌ Test suite failed:", error);
    } finally {
        console.groupEnd();
    }
};

/**
 * Quick test function that can be called from browser console
 */
export const quickTestSuperAdmin = (): void => {
    console.log("🚀 Quick Super Admin Test");
    debugUserPermissionStatus();
    
    if (isSuperAdmin()) {
        console.log("✅ Super Admin detected - fix should be active");
        console.log("💡 Run testSuperAdminPermissionFix() for comprehensive testing");
    } else {
        console.log("ℹ️ Not a Super Admin user - fix not applicable");
    }
};

/**
 * Monitor for Super Admin permission errors in real-time
 */
export const monitorSuperAdminErrors = (): (() => void) => {
    const originalConsoleError = console.error;
    
    console.error = (...args: any[]) => {
        // Check if any of the arguments contain Super Admin related errors
        const errorMessage = args.join(' ');
        
        if (isSuperAdminPermissionError({ message: errorMessage })) {
            console.warn("🚨 SUPER ADMIN PERMISSION ERROR DETECTED:");
            console.warn("This error should have been fixed. Please check the implementation.");
            console.warn("Error:", errorMessage);
            
            // Show debug info
            debugUserPermissionStatus();
        }
        
        // Call original console.error
        originalConsoleError.apply(console, args);
    };
    
    console.log("🔍 Super Admin error monitoring started");
    
    // Return cleanup function
    return () => {
        console.error = originalConsoleError;
        console.log("🔍 Super Admin error monitoring stopped");
    };
};

// Export for easy access
export default {
    testSuperAdminPermissionFix,
    quickTestSuperAdmin,
    monitorSuperAdminErrors,
};

// Global access for browser console testing
if (typeof window !== 'undefined') {
    (window as any).testSuperAdminFix = {
        test: testSuperAdminPermissionFix,
        quick: quickTestSuperAdmin,
        monitor: monitorSuperAdminErrors,
        debug: debugUserPermissionStatus,
    };
    
    console.log("🧪 Super Admin test functions available globally:");
    console.log("  - window.testSuperAdminFix.test() - Full test suite");
    console.log("  - window.testSuperAdminFix.quick() - Quick test");
    console.log("  - window.testSuperAdminFix.monitor() - Error monitoring");
    console.log("  - window.testSuperAdminFix.debug() - Debug info");
}
