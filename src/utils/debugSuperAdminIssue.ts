/**
 * Debug utility to help identify remaining Super Admin API call issues
 */

import { cookies } from "@/lib/cookies";
import { jwtDecode } from "jwt-decode";
import type { DecodedToken } from "@/utils/server-permission-check";

/**
 * Debug function to identify what might still be making /api/v1/role/0 calls
 */
export const debugSuperAdminApiCalls = (): void => {
    console.group("🔍 Debugging Super Admin API Calls");
    
    try {
        const token = cookies.getToken();
        if (!token) {
            console.warn("❌ No token found");
            console.groupEnd();
            return;
        }

        const decodedToken = jwtDecode(token) as DecodedToken;
        const roleId = decodedToken?.roleid;
        const userId = decodedToken?.userId;

        console.log("Current user info:");
        console.log("- Role ID:", roleId);
        console.log("- User ID:", userId);
        console.log("- Base API URL:", process.env.NEXT_PUBLIC_USER_API_URL);
        
        if (roleId === 0) {
            console.log("✅ Super Admin detected");
            console.log("🔧 All fixed functions:");
            console.log("  ✅ fetchUserPermissions (permissionsActions.ts) - calls /v1/permissions");
            console.log("  ✅ getRoleById (rolesActions.ts) - creates mock role");
            console.log("  ✅ editRole (rolesActions.ts) - prevents editing Super Admin");
            console.log("  ✅ checkPermission (server-permission-check.ts) - handles Super Admin");
            console.log("  ✅ getUserPermissions (server-permission-check.ts) - handles Super Admin");
            
            console.warn("🚫 If you still see /api/v1/role/0 errors, check:");
            console.log("  1. Legacy permission services or fallbacks");
            console.log("  2. Components directly calling role APIs");
            console.log("  3. Server-side rendering calling role APIs");
            console.log("  4. Other axios instances besides userAxios");
            
            // Test Redux state
            console.log("\n📊 Testing Redux permissions state...");
            try {
                // @ts-ignore - accessing global Redux store for debugging
                const state = window.__REDUX_STORE__?.getState?.();
                if (state?.permissions) {
                    console.log("Redux permissions state:", {
                        isReady: state.permissions.isReady,
                        userPermissionsCount: state.permissions.userPermissions?.length || 0,
                        systemPermissionsCount: state.permissions.systemPermissions?.length || 0,
                        userPermissionsLoading: state.permissions.userPermissionsLoading,
                        systemPermissionsLoading: state.permissions.systemPermissionsLoading,
                        errors: {
                            userPermissionsError: state.permissions.userPermissionsError,
                            systemPermissionsError: state.permissions.systemPermissionsError,
                        }
                    });
                } else {
                    console.warn("Redux permissions state not found");
                }
            } catch (error) {
                console.warn("Could not access Redux state:", error);
            }
        } else {
            console.log(`👤 Regular user (roleId: ${roleId}) - should work normally`);
        }
        
    } catch (error) {
        console.error("❌ Error in debug function:", error);
    }
    
    console.groupEnd();
};

/**
 * Monitor network requests to catch any remaining /role/0 calls
 */
export const monitorRoleApiCalls = (): void => {
    console.log("🕵️ Starting network monitoring for role API calls...");
    
    // Override fetch to monitor API calls
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
        const [url] = args;
        const urlString = typeof url === 'string' ? url : url.toString();
        
        if (urlString.includes('/role/0')) {
            console.error("🚨 DETECTED PROBLEMATIC API CALL:", urlString);
            console.trace("Call stack:");
        } else if (urlString.includes('/role/')) {
            console.log("📞 Role API call detected:", urlString);
        }
        
        return originalFetch.apply(window, args);
    };
    
    console.log("✅ Network monitoring active. Check console for any /role/0 calls.");
};

/**
 * Stop network monitoring
 */
export const stopMonitoringRoleApiCalls = (): void => {
    // This is a simplified version - in practice you'd need to store the original fetch
    console.log("🛑 Network monitoring stopped (refresh page to fully reset)");
};

/**
 * Run comprehensive debug check
 */
export const runSuperAdminDebugCheck = (): void => {
    console.clear();
    console.log("🔧 Super Admin Debug Check Starting...");
    
    debugSuperAdminApiCalls();
    monitorRoleApiCalls();
    
    console.log("\n📋 Next steps:");
    console.log("1. Navigate around the app (especially team management)");
    console.log("2. Check console for any /role/0 API calls");
    console.log("3. If you see errors, note which component/action triggered them");
    console.log("4. Run stopMonitoringRoleApiCalls() when done");
};

// Export for browser console usage
if (typeof window !== 'undefined') {
    (window as any).debugSuperAdmin = {
        debugSuperAdminApiCalls,
        monitorRoleApiCalls,
        stopMonitoringRoleApiCalls,
        runSuperAdminDebugCheck
    };
}

export default {
    debugSuperAdminApiCalls,
    monitorRoleApiCalls,
    stopMonitoringRoleApiCalls,
    runSuperAdminDebugCheck
};
