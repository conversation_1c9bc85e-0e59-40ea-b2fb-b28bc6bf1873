// TypeScript interfaces for approval data
export interface ApprovalData {
    id: number;
    userId: number;
    name: string;
    role: string;
    approvalLevel: number;
    status: "APPROVE" | "DECLINE" | null;
    date: string | null;
}

export interface CategorizedApprovers {
    approved: ApprovalData[];
    declined: ApprovalData[];
    pending: ApprovalData[];
}

export interface ApprovalProgress {
    approved_count: number;
    declined_count: number;
    pending_count: number;
    total_count: number;
    approval_percentage: number;
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Sorts approvers by their approval level in ascending order
 * @param approvers Array of approval data
 * @returns New sorted array (does not mutate original)
 */
export function sortApproversByLevel(approvers: ApprovalData[]): ApprovalData[] {
    if (!Array.isArray(approvers)) {
        return [];
    }

    return [...approvers].sort((a, b) => {
        // Handle cases where approvalLevel might be undefined/null
        const levelA = a.approvalLevel ?? Number.MAX_SAFE_INTEGER;
        const levelB = b.approvalLevel ?? Number.MAX_SAFE_INTEGER;
        return levelA - levelB;
    });
}

/**
 * Categorizes approvers by their approval status
 * @param approvers Array of approval data
 * @returns Object containing categorized approvers
 */
export function categorizeApproversByStatus(approvers: ApprovalData[]): CategorizedApprovers {
    if (!Array.isArray(approvers)) {
        return {
            approved: [],
            declined: [],
            pending: [],
        };
    }

    const result: CategorizedApprovers = {
        approved: [],
        declined: [],
        pending: [],
    };

    approvers.forEach((approver) => {
        switch (approver.status) {
            case "APPROVE":
                result.approved.push(approver);
                break;
            case "DECLINE":
                result.declined.push(approver);
                break;
            case null:
            case undefined:
            default:
                result.pending.push(approver);
                break;
        }
    });

    return result;
}

/**
 * Calculates approval progress statistics
 * @param approvers Array of approval data
 * @returns Object containing approval counts and percentage
 */
export function calculateApprovalProgress(approvers: ApprovalData[]): ApprovalProgress {
    if (!Array.isArray(approvers)) {
        return {
            approved_count: 0,
            declined_count: 0,
            pending_count: 0,
            total_count: 0,
            approval_percentage: 0,
        };
    }

    const categorized = categorizeApproversByStatus(approvers);
    const total_count = approvers.length;
    const approved_count = categorized.approved.length;
    const declined_count = categorized.declined.length;
    const pending_count = categorized.pending.length;

    // Calculate approval percentage (rounded to 2 decimal places)
    const approval_percentage = total_count > 0 ? Math.round((approved_count / total_count) * 10000) / 100 : 0;

    return {
        approved_count,
        declined_count,
        pending_count,
        total_count,
        approval_percentage,
    };
}

/**
 * Validates approval data for consistency and completeness
 * @param approvers Array of approval data or null/undefined
 * @returns Validation result with errors and warnings
 */
export function validateApprovalData(approvers: ApprovalData[] | null | undefined): ValidationResult {
    const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: [],
    };

    // Check if input is valid
    if (approvers === null || approvers === undefined) {
        result.isValid = false;
        result.errors.push("Invalid input: expected array");
        return result;
    }

    if (!Array.isArray(approvers)) {
        result.isValid = false;
        result.errors.push("Invalid input: expected array");
        return result;
    }

    // Handle empty array
    if (approvers.length === 0) {
        return result;
    }

    const approvalLevels = new Set<number>();
    const requiredFields = ["id", "userId", "name", "role", "approvalLevel"];

    approvers.forEach((approver, index) => {
        // Check if approver is an object
        if (typeof approver !== "object" || approver === null) {
            result.isValid = false;
            result.errors.push(`Invalid approver type at index ${index}: expected object`);
            return;
        }

        // Check required fields
        const missingFields = requiredFields.filter(
            (field) =>
                approver[field as keyof ApprovalData] === undefined ||
                approver[field as keyof ApprovalData] === null ||
                approver[field as keyof ApprovalData] === ""
        );

        if (missingFields.length > 0) {
            result.isValid = false;
            result.errors.push(`Missing required fields in approver at index ${index}`);
        }

        // Validate approval level
        if (typeof approver.approvalLevel === "number") {
            if (approver.approvalLevel < 1) {
                result.isValid = false;
                result.errors.push(`Invalid approval level ${approver.approvalLevel} at index ${index}`);
            }

            // Check for duplicate levels
            if (approvalLevels.has(approver.approvalLevel)) {
                result.warnings.push(`Duplicate approval level found: ${approver.approvalLevel}`);
            } else {
                approvalLevels.add(approver.approvalLevel);
            }
        }

        // Validate status
        if (
            approver.status !== null &&
            approver.status !== undefined &&
            !["APPROVE", "DECLINE"].includes(approver.status)
        ) {
            result.isValid = false;
            result.errors.push(`Invalid status "${approver.status}" at index ${index}`);
        }

        // Check for missing dates on approved/declined statuses
        if (
            (approver.status === "APPROVE" || approver.status === "DECLINE") &&
            (approver.date === null || approver.date === undefined || approver.date === "")
        ) {
            result.warnings.push(`Missing date for approved/declined status at index ${index}`);
        }
    });

    return result;
}

// Utility function to create a formatted approval progress string
export function formatApprovalProgress(progress: ApprovalProgress): string {
    return `${progress.approved_count} of ${progress.total_count} approvals granted`;
}

// Utility function to determine if approval process is complete
export function isApprovalComplete(approvers: ApprovalData[]): boolean {
    const progress = calculateApprovalProgress(approvers);
    return progress.pending_count === 0 && progress.approved_count > 0;
}

// Utility function to get the next pending approver
export function getNextPendingApprover(approvers: ApprovalData[]): ApprovalData | null {
    const sorted = sortApproversByLevel(approvers);
    const categorized = categorizeApproversByStatus(sorted);

    return categorized.pending.length > 0 ? categorized.pending[0] : null;
}
