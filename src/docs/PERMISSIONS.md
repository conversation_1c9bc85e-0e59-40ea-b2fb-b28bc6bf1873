# Role-Based Access Control (RBAC) System

This document explains how to use the RBAC system to control access to features and functionality in the application.

## Overview

The RBAC system allows you to:

1. Check if a user has specific permissions
2. Conditionally render UI elements based on permissions
3. Protect routes/pages based on permissions
4. Control access to actions based on permissions

## Components and Hooks

The RBAC system consists of the following components and hooks:

- `PermissionProvider`: Context provider that fetches and stores user permissions
- `usePermissions`: Hook to access the permission context
- `usePermissionCheck`: Hook with utility functions for checking permissions
- `PermissionGate`: Component for conditional rendering based on permissions
- `withPermissionCheck`: HOC for protecting routes/pages
- `checkPermissions`: Server-side utility for checking permissions
- `getUserPermissions`: Server-side utility for getting user permissions

## How to Use

### 1. Check Permissions in a Component

```tsx
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";

const MyComponent = () => {
    const { hasPermission } = usePermissionCheck();
    const { permissions: dynamicPermissions } = useDynamicPermissions();

    // Check if user has a specific permission using dynamic permissions
    const canViewAccounts = hasPermission(
        dynamicPermissions.ACCOUNTS_PERMISSIONS?.VIEW_ACCOUNTS_LIST || "View list of accounts"
    );

    return <div>{canViewAccounts ? <AccountsList /> : <p>You don't have permission to view accounts</p>}</div>;
};
```

### 2. Conditionally Render UI Elements

```tsx
import PermissionGate from "@/components/common/permission-gate";
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";

const TransactionActions = () => {
    return (
        <div>
            {/* Only shown if user has the permission */}
            <PermissionGate permission={TRANSACTIONS_PERMISSIONS.EXPORT_TRANSACTIONS}>
                <button>Export Transactions</button>
            </PermissionGate>

            {/* Check multiple permissions (any) */}
            <PermissionGate
                permissions={[
                    TRANSACTIONS_PERMISSIONS.FLAG_REPORT_SUSPICIOUS_TRANSACTIONS,
                    TRANSACTIONS_PERMISSIONS.VIEW_DISPUTE_STATUS,
                ]}
            >
                <button>Report Issue</button>
            </PermissionGate>

            {/* Check multiple permissions (all) */}
            <PermissionGate
                permissions={[
                    TRANSACTIONS_PERMISSIONS.VIEW_TRANSACTION_DETAILS,
                    TRANSACTIONS_PERMISSIONS.VIEW_APPROVAL_TRAIL,
                ]}
                requireAll={true}
                fallback={<p>Insufficient permissions</p>}
            >
                <button>View Full Details</button>
            </PermissionGate>
        </div>
    );
};
```

### 3. Protect Routes/Pages

#### Client-Side Protection (HOC)

```tsx
import withPermissionCheck from "@/components/hoc/withPermissionCheck";

const TeamManagementPage = () => {
    return (
        <div>
            <h1>Team Management</h1>
            {/* Page content */}
        </div>
    );
};

// Wrap the component with the permission check HOC
export default withPermissionCheck(TeamManagementPage, {
    requiredPermissions: ["View team members"],
    redirectTo: "/dashboard",
});
```

#### Server-Side Protection (Server Components)

For immediate protection without any client-side delay, use the server-side permission check in a Server Component:

```tsx
import { checkPermissions, getUserPermissions } from "@/utils/server-permission-check";

export default async function ServerProtectedPage() {
    // This will automatically redirect if the user doesn't have permissions
    await checkPermissions(["View team members"], false, "/dashboard");

    // If we get here, the user has the required permissions
    // We can also get the user's permissions to conditionally render parts of the page
    const userPermissions = await getUserPermissions();

    return (
        <div>
            <h1>Team Management</h1>
            {/* Page content */}
        </div>
    );
}
```

### 4. Check Permissions for Actions

```tsx
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";
import { sendCatchFeedback } from "@/functions/feedback";

const BeneficiaryActions = () => {
    const { canPerformAction } = usePermissionCheck();
    const { permissions: dynamicPermissions } = useDynamicPermissions();

    const handleDeleteBeneficiary = () => {
        const deletePermission =
            dynamicPermissions.BENEFICIARIES_PERMISSIONS?.DELETE_BENEFICIARY || "Delete a beneficiary";

        // Check if user can perform this action
        if (!canPerformAction(deletePermission)) {
            sendCatchFeedback("You don't have permission to delete beneficiaries");
            return;
        }

        // Proceed with the action
        // ...
    };

    const deletePermission = dynamicPermissions.BENEFICIARIES_PERMISSIONS?.DELETE_BENEFICIARY || "Delete a beneficiary";

    return (
        <button onClick={handleDeleteBeneficiary} disabled={!canPerformAction(deletePermission)}>
            Delete Beneficiary
        </button>
    );
};
```

## Dynamic Permissions

The system now uses dynamic permissions fetched from the API instead of static constants. Use the `useDynamicPermissions` hook to access permissions:

```tsx
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";

const MyComponent = () => {
    const { permissions: dynamicPermissions, isLoaded } = useDynamicPermissions();

    // Use dynamic permissions with fallback strings
    const canViewAccounts = hasPermission(
        dynamicPermissions.ACCOUNTS_PERMISSIONS?.VIEW_ACCOUNTS_LIST || "View list of accounts"
    );

    // Always provide fallback strings for reliability
    return <div>{canViewAccounts && <AccountsList />}</div>;
};
```

## Best Practices

1. **Use Dynamic Permissions**: Always use the dynamic permissions from the `useDynamicPermissions` hook with fallback strings
2. **Provide Fallbacks**: When using `PermissionGate`, provide a fallback UI for users without permissions
3. **Disable Actions**: Disable buttons/actions that the user doesn't have permission to perform
4. **Check Before API Calls**: Always check permissions before making API calls
5. **Handle Loading State**: Handle the `isLoadingPermissions` state appropriately
6. **Combine with Authentication**: Make sure the user is authenticated before checking permissions
7. **Prefer Server-Side Checks**: For route protection, prefer server-side permission checks when possible for immediate protection
8. **Double Protection**: Implement both server-side and client-side permission checks for critical routes

## Troubleshooting

If permissions aren't working as expected:

1. Check if the user is authenticated
2. Check if the permissions are being fetched correctly
3. Verify that the permission names match exactly with the backend
4. Check the browser console for any errors
5. Make sure the `PermissionProvider` is properly set up in the component tree
