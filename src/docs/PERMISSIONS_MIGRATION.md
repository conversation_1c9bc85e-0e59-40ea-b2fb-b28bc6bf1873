# Permission System Migration Guide

This guide helps you migrate from the static permission constants to the new dynamic Redux-based permission system.

## Overview

The permission system has been modernized to:
- Eliminate static `src/constants/permissions.ts` file
- Use Redux for centralized permission management
- Fetch permissions dynamically from the API
- Provide session-based caching
- Maintain backward compatibility

## Migration Steps

### 1. Replace Static Permission Imports

**Before:**
```tsx
import { ACCOUNTS_PERMISSIONS, TRANSACTIONS_PERMISSIONS } from "@/constants/permissions";
```

**After:**
```tsx
import { useDynamicPermissions } from "@/hooks/useDynamicPermissions";
```

### 2. Update Permission Checks in Components

**Before:**
```tsx
const MyComponent = () => {
    const { hasPermission } = usePermissionCheck();
    
    const canViewAccounts = hasPermission(ACCOUNTS_PERMISSIONS.VIEW_ACCOUNTS_LIST);
    
    return <div>{canViewAccounts && <AccountsList />}</div>;
};
```

**After:**
```tsx
const MyComponent = () => {
    const { hasPermission } = usePermissionCheck();
    const { permissions: dynamicPermissions } = useDynamicPermissions();
    
    const canViewAccounts = hasPermission(
        dynamicPermissions.ACCOUNTS_PERMISSIONS?.VIEW_ACCOUNTS_LIST || "View list of accounts"
    );
    
    return <div>{canViewAccounts && <AccountsList />}</div>;
};
```

### 3. Update PermissionGate Components

**Before:**
```tsx
<PermissionGate permission={TRANSACTIONS_PERMISSIONS.EXPORT_TRANSACTIONS}>
    <ExportButton />
</PermissionGate>
```

**After:**
```tsx
const { permissions: dynamicPermissions } = useDynamicPermissions();

<PermissionGate 
    permission={dynamicPermissions.TRANSACTIONS_PERMISSIONS?.EXPORT_TRANSACTIONS || "Export transactions"}
>
    <ExportButton />
</PermissionGate>
```

### 4. Update HOC Permission Checks

**Before:**
```tsx
const ProtectedPage = withPermissionCheck(MyComponent, {
    requiredPermissions: [TEAM_MANAGEMENT_PERMISSIONS.VIEW_TEAM_MEMBERS],
    requireAll: false,
    redirectTo: "/dashboard",
});
```

**After:**
```tsx
const ProtectedPage = withPermissionCheck(MyComponent, {
    requiredPermissions: ["View team members"],
    requireAll: false,
    redirectTo: "/dashboard",
});
```

### 5. Server-Side Permission Checks (No Changes Required)

Server-side permission checking remains unchanged as it directly fetches from the API:

```tsx
// This continues to work as before
await checkPermissions(["View team members"], false, "/dashboard");
```

## Key Benefits

1. **Dynamic Updates**: Permissions are fetched from the API and stay in sync with backend changes
2. **Better Performance**: One-time fetch per session with intelligent caching
3. **Centralized State**: Redux-based state management for consistent permission data
4. **Session Management**: Automatic cache clearing on logout and refresh on login
5. **Backward Compatibility**: Existing APIs continue to work

## Best Practices

1. **Always Provide Fallbacks**: Use fallback permission strings for reliability
   ```tsx
   dynamicPermissions.ACCOUNTS_PERMISSIONS?.VIEW_ACCOUNTS_LIST || "View list of accounts"
   ```

2. **Check Loading States**: Handle loading states when permissions are being fetched
   ```tsx
   const { permissions, isLoaded } = useDynamicPermissions();
   
   if (!isLoaded) {
       return <LoadingSpinner />;
   }
   ```

3. **Use Descriptive Permission Names**: Use clear, descriptive permission strings that match the backend
   ```tsx
   // Good
   "View list of accounts"
   "Export transactions"
   
   // Avoid
   "accounts_view"
   "export_tx"
   ```

## Troubleshooting

### Permission Not Found
If a permission is not found in the dynamic permissions, check:
1. The permission exists in the backend
2. The fallback string matches the backend permission name
3. The user's role has the required permission

### Loading Issues
If permissions are not loading:
1. Check network connectivity
2. Verify the API endpoints are accessible
3. Check browser console for errors
4. Ensure the user is properly authenticated

### Cache Issues
If permissions seem outdated:
1. Clear browser localStorage
2. Log out and log back in
3. Check if the backend permissions have changed

## Migration Checklist

- [ ] Replace static permission imports with `useDynamicPermissions`
- [ ] Update all permission checks to use dynamic permissions with fallbacks
- [ ] Update PermissionGate components
- [ ] Update HOC permission checks to use permission strings
- [ ] Test all permission-protected routes and components
- [ ] Verify loading states work correctly
- [ ] Test logout/login permission refresh
- [ ] Update documentation and comments

## Support

If you encounter issues during migration:
1. Check the browser console for errors
2. Verify the Redux DevTools show permission state
3. Test with different user roles
4. Consult the team for backend permission changes
