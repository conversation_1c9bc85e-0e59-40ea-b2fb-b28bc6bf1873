# Route Guard Implementation

This document explains how the route guard works for both login and signup flows.

## Overview

The route guard ensures that users follow the correct sequence of steps in the login and signup flows. It prevents users from skipping steps or accessing steps they haven't reached yet.

## Implementation

The route guard is implemented using Next.js middleware. There are two separate middleware functions:

1. `handleLoginMiddleware` - Handles the login flow
2. `handleSignupMiddleware` - Handles the signup flow

Both middleware functions are called from the main middleware function in `middleware.ts`.

## Login Flow

The login flow consists of the following steps:

1. **CREDENTIALS** - Password login
2. **MAGIC_CODE** - Alternative login with magic code
3. **MFA_VERIFICATION** - MFA verification (if enabled)
4. **DEVICE_VERIFICATION** - Device verification
5. **TRUST_DEVICE** - Trust device flow (if needed)
6. **COMPLETED** - Login completed

The login flow is managed by the following files:

- `middleware-login.ts` - Middleware function for the login flow
- `login-utils.ts` - Helper functions for the login flow

Key functions:

- `canAccessLoginStep(currentStep, targetStep)` - Checks if a user can access a specific login step
- `getLoginStepRoute(step)` - Gets the route for a specific login step

## Signup Flow

The signup flow consists of the following steps:

1. **PERSONAL_INFO** - Personal information
2. **VERIFY_PERSONAL_EMAIL** - Verify personal email
3. **VERIFY_PHONE_NUMBER** - Verify phone number
4. **BUSINESS_INFO** - Business information
5. **VALIDATE_BUSINESS** - Validate business
6. **VERIFY_BUSINESS_EMAIL** - Verify business email
7. **MFA_SETUP** - MFA setup
8. **COMPLETE** - Signup completed

The signup flow is managed by the following files:

- `middleware-signup.ts` - Middleware function for the signup flow
- `session-utils.ts` - Helper functions for the signup flow

Key functions:

- `canAccessSignupStep(currentStep, targetStep)` - Checks if a user can access a specific signup step
- `getSignupStepRoute(step)` - Gets the route for a specific signup step

## How It Works

1. When a user requests a page, the middleware checks if it's a login or signup route
2. If it is, the middleware determines the current step from the cookie
3. The middleware then checks if the user can access the requested step
4. If the user can access the step, the middleware allows the request
5. If the user cannot access the step, the middleware redirects to the appropriate step

## Rules for Step Access

The rules for step access are the same for both login and signup flows:

1. Users can always go back to previous steps
2. Users can only advance one step at a time
3. Users cannot skip steps

## Cookies

The current step is stored in a cookie:

- `login_progress` - For the login flow
- `signup_progress` - For the signup flow

These cookies are set by the `setLoginProgress` and `setSignupProgress` functions, respectively.

## Debugging

Both middleware functions include extensive logging to help debug issues. You can see these logs in the server console.

## Recent Fixes

The following issues were fixed in the signup middleware:

1. Added `canAccessSignupStep` function to check if a user can access a specific signup step
2. Added `getSignupStepRoute` function to get the route for a specific signup step
3. Updated the middleware to use these functions
4. Improved error handling and logging

These changes ensure that the signup flow follows the same pattern as the login flow, making the code more consistent and easier to maintain.
