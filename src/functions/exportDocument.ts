import { format } from "date-fns";
import { download, generateCsv, mkConfig } from "export-to-csv";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { sendCatchFeedback } from "./feedback";
import { convertToSnakeCase } from "./stringManipulations";

export const exportAsPDF = ({
    data,
    columns,
    startDate,
    endDate,
    documentTitle,
}: {
    data: unknown[];
    columns: { header: string; key: string }[];
    startDate?: string;
    endDate?: string;
    documentTitle: string;
}) => {
    try {
        const font = "helvetica";

        // Initialize PDF
        const doc = new jsPDF({ format: "a4", orientation: "landscape", unit: "mm" });

        // Colors
        const primary = [92, 6, 140]; // FCMB purple
        const darkText = [33, 37, 41];
        const lightGray = [240, 240, 240];
        const footerText = [100, 100, 100];

        // --- HEADER BAR ---
        doc.setFillColor(primary[0], primary[1], primary[2]);
        doc.rect(0, 0, doc.internal.pageSize.width, 15, "F");

        doc.setFont(font, "bold").setFontSize(18).setTextColor(255, 255, 255);
        doc.text(documentTitle, 14, 10);

        doc.setFont(font, "normal").setFontSize(8);
        doc.text(`Generated on: ${format(new Date(), "dd MMM yyyy HH:mm")}`, doc.internal.pageSize.width - 14, 10, {
            align: "right",
        });

        // --- REPORT INFO BOX ---
        doc.setFillColor(lightGray[0], lightGray[1], lightGray[2]);
        doc.rect(0, 15, doc.internal.pageSize.width, 20, "F");

        doc.setTextColor(darkText[0], darkText[1], darkText[2]).setFontSize(10);

        // Date Range
        doc.setFont(font, "bold").text("Date Range:", 14, 22);
        doc.setFont(font, "normal");
        if (startDate && endDate) {
            doc.text(`${format(startDate, "dd MMM yyyy")} - ${format(endDate, "dd MMM yyyy")}`, 40, 22);
        }

        // Total Records
        doc.setFont(font, "bold").text("Total Records:", doc.internal.pageSize.width - 80, 22);
        doc.setFont(font, "normal").text(`${data.length}`, doc.internal.pageSize.width - 50, 22);

        // Export Type
        doc.setFont(font, "bold").text("Export Type:", doc.internal.pageSize.width - 80, 28);
        doc.setFont(font, "normal").text("PDF Report", doc.internal.pageSize.width - 50, 28);

        // --- TABLE HEADERS & ROWS ---
        const headers = columns.map((col) => col.header);
        const rows = data.map((row) =>
            columns.map(({ key }) => {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const value = row ? (row as any)[key] : null;

                if (key === "timestamp" && value) {
                    try {
                        return format(new Date(value), "dd MMM yyyy HH:mm");
                    } catch {
                        return String(value);
                    }
                }

                return value ?? "";
            })
        );

        // --- TABLE ---
        // @ts-ignore
        doc.autoTable({
            head: [headers],
            body: rows,
            startY: 40,
            styles: {
                fontSize: 9,
                cellPadding: 2,
                font,
                lineColor: [220, 220, 220],
                lineWidth: 0.1,
            },
            headStyles: {
                fillColor: [44, 62, 80],
                textColor: [255, 255, 255],
                fontStyle: "bold",
            },
            alternateRowStyles: {
                fillColor: lightGray,
            },
            margin: { top: 40, right: 14, bottom: 20, left: 14 },
            rowPageBreak: "auto",

            // Footer + continuation header
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            didDrawPage: (data: any) => {
                // Footer
                doc.setFontSize(8).setTextColor(footerText[0], footerText[1], footerText[2]);
                doc.text(
                    `Page ${data.pageNumber} of ${doc.internal.getNumberOfPages()}`,
                    data.settings.margin.left,
                    doc.internal.pageSize.height - 10
                );

                doc.setFont(font, "normal");
                doc.text(
                    "FCMB",
                    doc.internal.pageSize.width - data.settings.margin.right,
                    doc.internal.pageSize.height - 10,
                    { align: "right" }
                );

                // Header for additional pages
                if (data.pageNumber > 1) {
                    doc.setFillColor(primary[0], primary[1], primary[2]);
                    doc.rect(0, 0, doc.internal.pageSize.width, 10, "F");

                    doc.setFont(font, "bold").setFontSize(10).setTextColor(255, 255, 255);
                    doc.text(`${documentTitle} (Continued)`, 14, 7);
                }
            },
        });

        // --- SAVE FILE ---
        const filename = `${convertToSnakeCase(documentTitle)}_${format(new Date(), "yyyyMMdd_HHmm")}.pdf`;
        doc.save(filename);
    } catch (error) {
        sendCatchFeedback("Error generating PDF. Please try a different format or with fewer rows.");
    }
};

export const exportAsCSV = ({
    data,
    columns,
    documentTitle,
}: {
    data: unknown[];
    columns: { header: string; key: string }[];
    documentTitle: string;
}) => {
    try {
        const headers = columns.map((col) => col.header);

        const filename = `${convertToSnakeCase(documentTitle)}_${format(new Date(), "yyyyMMdd_HHmm")}`;

        const options = mkConfig({
            fieldSeparator: ",",
            decimalSeparator: ".",
            useBom: true,
            useKeysAsHeaders: false,
            columnHeaders: headers,
            filename,
            showColumnHeaders: true,
        });

        // Format each row
        const formattedRows = data.map((row) => {
            const newRow: Record<string, string> = {};

            columns.forEach(({ key, header }) => {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const value = row ? (row as any)[key] : "";

                if (key === "amount" && typeof value === "number") {
                    newRow[header] = `₦${value.toLocaleString("en-NG", { minimumFractionDigits: 2 })}`;
                } else if (key === "timestamp" && value) {
                    try {
                        newRow[header] = format(new Date(value), "dd MMM yyyy HH:mm");
                    } catch {
                        newRow[header] = String(value);
                    }
                } else if (key === "status") {
                    newRow[header] = value ? String(value).toUpperCase() : "";
                } else {
                    newRow[header] = value != null ? String(value) : "";
                }
            });

            return newRow;
        });

        const csv = generateCsv(options)(formattedRows);
        download(options)(csv);
    } catch (error) {
        sendCatchFeedback("Error generating CSV. Please try again with a different format.");
    }
};
