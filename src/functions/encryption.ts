import CryptoJS from "crypto-js";

export const encryptItem = <T>(item: T, key: string) => {
    const input = typeof item === "string" ? item : JSON.stringify(item);
    const encryptedText = CryptoJS.AES.encrypt(input, key).toString();
    return encryptedText;
};

export const decryptItem = (item: string | CryptoJS.lib.CipherParams, key: string) => {
    const decryptedText = CryptoJS.AES.decrypt(item, key);
    const originalSessionDetails = decryptedText.toString(CryptoJS.enc.Utf8);
    return originalSessionDetails;
};

// Converts a base64 string to an ArrayBuffer
function base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
}

// Converts an ArrayBuffer to a base64 string
function arrayBufferToBase64(buffer: ArrayBuffer): string {
    let binary = "";
    const bytes = new Uint8Array(buffer);
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

// ---- 🟢 AES Encryption and Decryption ----

/**
 * Encrypt an object using AES algorithm.
 */
export function encryptObjectAES(obj: object, secretKey: string): string {
    const jsonString = JSON.stringify(obj); // Convert the object to a JSON string
    return encryptAES(jsonString, secretKey); // Encrypt the JSON string
}

/**
 * Encrypt a string using AES algorithm.
 */
export function encryptAES(text: string, secret: string): string {
    const key = CryptoJS.enc.Base64.parse(secret); // AES-256 needs 32 bytes
    const iv = CryptoJS.enc.Utf8.parse("0000000000000000"); // 16-byte IV compatible with JAVA

    const encrypted = CryptoJS.AES.encrypt(text, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });

    return encrypted.toString();
}

/**
 * Decrypt an AES encrypted string back to a JSON object.
 */
export function decryptObjectAES(encryptedText: string, secretKey: string): object {
    const decrypted = decryptAES(encryptedText, secretKey);
    return JSON.parse(decrypted); // Convert back to object
}

/**
 * Decrypt an AES encrypted string.
 */
export function decryptAES(cipherText: string, secret: string): string {
    const key = CryptoJS.enc.Base64.parse(secret);
    const iv = CryptoJS.enc.Utf8.parse("0000000000000000");

    const cipherParams = CryptoJS.lib.CipherParams.create({
        ciphertext: CryptoJS.enc.Base64.parse(cipherText),
    });

    const decrypted = CryptoJS.AES.decrypt(cipherParams, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
}

// ---- 🟢 RSA Encryption and Decryption ----

// Embedding the public key directly in the code because
// using an environment variable would be ineffective for this
// kind of public key, also using a file means we need to put
// it in the public folder which also requires a fetch for every request
// last reason is because public keys can be sent to anyone as the main
// security is the private key
const PUBLIC_KEY_PEM = `-----BEGIN PUBLIC KEY-----
MIICITANBgkqhkiG9w0BAQEFAAOCAg4AMIICCQKCAgBbyY49iTpMJK2R5sZ/SFkA
ADJgdMpsCb0BGmHJIMLss6dmkyZGEOob+cWcZV/jDrSsWOuvykYQj5qYuvhnm0iG
v2TIaZjH6ziL243XeNx3ZO2w/IJW6+T75S00yWciJT4sufpdHlmSyGix1z9stAt+
yTS+dJps4YMwP8u8iDtOZV89UHwXthyOm7LyBfWEGK36G4OHcyLPzR4HgfLPy7bK
vnEPvZ80+6vUj2MjgFGMypt5FWuaxX84g6IglEIjb7wHxL/JtnBRCrMxF+g2MmCM
exH8xsIKh1mLAFR86uulSPIoiy/nTkCSECyA2/HLu+7YhQBnKhj1B4Eda72KqBpG
qnMQcu3nruHjkW/3Jmfb8XMjtyxXMeBZw+jpeLiYyhT4U93rp08hImnPM0/YhLrN
5wZldnWNa4ZKiuGkFPL/jEwa1TprNvKOir9d1OhdadIT6Rn677aLBFY/vmnFwue9
OnZB7vVRQFSVZPUmMG5EtdZNUJKltEQ3koi84mC+wXviedQaWxWiOa7ye3pk9nrA
+lY9fC3Rk3Ri8wPICyLKZyg5fZeb80CBmJhUjN4E+uXau8qtTuDrYzfoaSh3iRgS
EWlyjpWXAmPCYjhBXeoUL18yMgIUXXrF9cl7cCGXYHkVm2wlJKXqi24qOR6yo6Bm
SFWdMjfexieywteY/c09OwIDAQAB
-----END PUBLIC KEY-----`;

/**
 * Encrypt an object using RSA algorithm (with embedded public key).
 */
export async function encryptObjectRSA(obj: object): Promise<string> {
    const jsonString = JSON.stringify(obj); // Convert the object to a JSON string
    return await encryptWithRSA(jsonString); // Encrypt the JSON string
}

/**
 * Encrypt a string using RSA algorithm (with embedded public key).
 */
export async function encryptWithRSA(text: string): Promise<string> {
    const publicKey = await importPublicKey(PUBLIC_KEY_PEM); // Import public key
    const encoded = new TextEncoder().encode(text);
    const encrypted = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, publicKey, encoded);
    return arrayBufferToBase64(encrypted); // Return as base64 string
}

/**
 * Import a public key for RSA encryption.
 */
async function importPublicKey(pem: string): Promise<CryptoKey> {
    const keyBuffer = publicPemToBuffer(pem);
    return await window.crypto.subtle.importKey("spki", keyBuffer, { name: "RSA-OAEP", hash: "SHA-256" }, true, [
        "encrypt",
    ]);
}

/**
 * Convert a PEM formatted public key to an ArrayBuffer.
 */
function publicPemToBuffer(pem: string): ArrayBuffer {
    const b64Lines = pem
        .replace("-----BEGIN PUBLIC KEY-----", "")
        .replace("-----END PUBLIC KEY-----", "")
        .replace(/\s+/g, "");
    return base64ToArrayBuffer(b64Lines);
}

// API Encryption functions

export async function generateAPIRequestKey(): Promise<{ encrypted: string; raw: string }> {
    const keyWA = CryptoJS.lib.WordArray.random(16);

    // 2) serialize to Base64
    const rawBase64 = CryptoJS.enc.Base64.stringify(keyWA);

    // 3) RSA-encrypt that Base64 string
    const encrypted = await encryptWithRSA(rawBase64);

    return {
        encrypted,
        raw: rawBase64,
    };
}
