import { AxiosResponse } from "axios";
import <PERSON> from "papapar<PERSON>";

// Define a generic type for CSV rows
type CsvRow = Record<string, string | number | boolean | null>;

export const convertCsvToArrayOfObjects = async (file: File): Promise<CsvRow[]> => {
    try {
        const result = await new Promise<Papa.ParseResult<CsvRow>>((resolve, reject) => {
            Papa.parse<CsvRow>(file, {
                header: true,
                dynamicTyping: (field) => {
                    // Keep these fields as strings to preserve leading zeros
                    const forceStringFields = ["ToAccount", "ToBankCode"];
                    return !forceStringFields.includes(String(field)); // `false` means "don’t type-convert"
                },
                skipEmptyLines: true,
                complete: resolve,
                error: reject,
            });
        });

        return result.data.map((row, index) => ({
            ...row,
            data_id: String(index + 1).padStart(3, "0"), // Format as "001", "002", etc.
        }));
    } catch (error) {
        console.error("Error parsing CSV:", error);
        return [];
    }
};

export const downloadCSVFromAPI = async (response: AxiosResponse<Blob>, fileName?: string) => {
    const blob = response.data;

    // Get filename from content-disposition header
    const disposition = response.headers["content-disposition"];
    const filenameMatch = disposition?.match(/filename="?([^"]+)"?/);

    const filename = filenameMatch?.[1] || fileName || "download.csv";

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
};

/**
 * Converts an array of data to a CSV File object using a custom transformer.
 *
 * @param data - The original data array of any type.
 * @param transform - A function that maps each item to a flat object for CSV.
 * @param fileName - Optional file name for the CSV file.
 * @returns File - A CSV File object.
 */
export function generateGenericCSVFile<T>(
    data: T[],
    transform: (item: T) => Record<string, string | number>,
    fileName: string = "data.csv"
): File {
    const transformedData = data.map(transform);
    const csvString = Papa.unparse(transformedData);
    const blob = new Blob([csvString], { type: "text/csv" });

    return new File([blob], fileName, { type: "text/csv" });
}
