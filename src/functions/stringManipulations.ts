import { sendFeedback } from "./feedback";

export const formatNumber = (number: number, fractionDigits?: number) =>
    new Intl.NumberFormat("en-NG", {
        maximumFractionDigits: fractionDigits ?? 0,
    }).format(Number(number));

export const formatNumberToNaira = (number: number, fractionDigits?: number) => {
    const maxFractionDigits = fractionDigits ?? 2;
    const minFractionDigits = Math.min(2, maxFractionDigits);

    return new Intl.NumberFormat("en-NG", {
        style: "currency",
        currency: "NGN",
        minimumFractionDigits: minFractionDigits,
        maximumFractionDigits: maxFractionDigits,
    }).format(Number(number));
};

export const getNameInitials = (name: string) =>
    name
        ?.toString()
        ?.split(" ")
        .slice(0, 2)
        .map((n) => n[0])
        .join("");

export const copyToClipboard = async (value: string) => {
    await navigator.clipboard.writeText(value);
    sendFeedback("Copied to clipboard", "success");
};

export const generateAuthQRCodeValue = () => {
    const appName = "CIB";
    const appSecret = "CIB";
    return `otpauth://totp/${appName}?secret=${appSecret}`;
};

export function convertCamelCaseToWords(input: string): string {
    return (
        input
            // Add a space before any uppercase letter
            .replace(/([a-z])([A-Z])/g, "$1 $2")
            // Capitalize the first letter of each word
            .replace(/\b\w/g, (char) => char.toUpperCase())
    );
}

export function convertFirstLetterToUppercase(value: string): string {
    return value.substring(0, 1).toUpperCase() + value.substring(1);
}

export const maskNumber = (value: string | number, length?: number): string => {
    const str = value.toString(); // Convert to string
    if (str.length <= (length ?? 4)) return str; // If length or fewer digits, return as is
    return "*".repeat(str.length - (length ?? 4)) + str.slice(-(length ?? 4));
};

export function convertToCamelCase(str: string): string {
    return str
        .toLowerCase()
        .split("_")
        .map((word, index) => (index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)))
        .join("");
}

export function enumToCapitalizedPhrase(enumString: string) {
    if (typeof enumString !== "string") {
        throw new TypeError("Input must be a string");
    }
    if (enumString.trim() === "") {
        return "";
    }
    const words = enumString.split("_");
    const capitalizedWords = words.map((word) => {
        if (word.length === 0) return "";
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    });
    return capitalizedWords.join(" ");
}

export function convertDateToMonthYear(dateString: string | Date) {
    try {
        const date = new Date(dateString);

        if (isNaN(date.getTime())) {
            throw new Error("Invalid date format");
        }

        const months = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ];

        const monthName = months[date.getMonth()];
        const year = date.getFullYear();

        return `${monthName}, ${year}`;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
        return `Error: ${error.message}`;
    }
}

export const findOptionByValue = (value: string, options: { label: string; value: string }[]) =>
    options.find((option) => option.value === value) || null;

export const convertToSnakeCase = (str: string): string =>
    str
        // Add underscore between camelCase and PascalCase boundaries
        .replace(/([a-z])([A-Z])/g, "$1_$2")
        .replace(/([A-Z]+)([A-Z][a-z])/g, "$1_$2")
        // Replace space, dash, and dot with underscore
        // eslint-disable-next-line no-useless-escape
        .replace(/[\s\-\.]+/g, "_")
        // Remove non-word characters (except underscore)
        .replace(/[^\w]/g, "")
        // Convert to lowercase
        .toLowerCase()
        // Replace multiple underscores with one
        .replace(/_+/g, "_")
        // Trim leading/trailing underscores
        .replace(/^_+|_+$/g, "");
