/**
 * This file contains facilities eg. static data and so on.
 */
"use client";
import { LoginFormType } from "@/types/auth";
import { useEffect, useState } from "react";

export const useFetchUserData = () => {
    const [data, setData] = useState<LoginFormType | null>(null);
    useEffect(() => {
        const storedData = localStorage.getItem("preLoginData");
        if (storedData) {
            setData(JSON.parse(storedData));
        }
    }, []);
    return data;
};

export const maskEmail = (email: string): string => {
    const [localPart, domain] = email.split("@");
    const maskedLocalPart = localPart.slice(0, 2) + "****" + localPart.slice(-1);
    return `${maskedLocalPart}@${domain}`;
};

export const renderFormatAmount = (amountParam: number, currencyParam: string = "NGN") => {
    const currencyMap: Record<string, string> = {
        ngn: "NGN",
        gbp: "GBP",
        usd: "USD",
        eur: "EUR",
    };

    const currencyCode = currencyMap[currencyParam.toLowerCase()] ?? "NGN";
    const locale = currencyCode === "USD" ? "en-US" : "en-NG"; // Use 'en-US' only for USD

    return new Intl.NumberFormat(locale, {
        style: "currency",
        currency: currencyCode,
    }).format(Number(amountParam));
};

export const formatNumber = (num: number, precision = 2) => {
    const map = [
        { suffix: "T", threshold: 1e12 },
        { suffix: "B", threshold: 1e9 },
        { suffix: "M", threshold: 1e6 },
        { suffix: "K", threshold: 1e3 },
        { suffix: "", threshold: 1 },
    ];

    const found = map.find((x) => Math.abs(num) >= x.threshold);

    if (found) {
        const formatted = (num / found.threshold).toFixed(precision) + found.suffix;
        return formatted;
    }

    return parseFloat(num.toString());
};

export const maskNumber = (accountNumber: string, visibleDigits: number = 4): string => {
    if (!accountNumber || visibleDigits < 0) return accountNumber;

    if (accountNumber.length <= visibleDigits) {
        return accountNumber;
    }

    const maskedPart = "*".repeat(accountNumber.length - visibleDigits);
    return maskedPart + accountNumber.slice(-visibleDigits);
};
