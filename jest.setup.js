import "@testing-library/jest-dom";
import dotenv from "dotenv";
dotenv.config({ path: ".env.test" });

// Radix UI resize observer mock
global.ResizeObserver = class {
    constructor(callback) {
        this.callback = callback;
    }
    observe() {}
    unobserve() {}
    disconnect() {}
};

// Globally mock the export csv function
// because it is causing jest to fail because
// commonjs import and export reasons
jest.mock("export-to-csv", () => ({
    mkConfig: jest.fn(),
    generateCsv: jest.fn(),
    download: jest.fn(),
}));
