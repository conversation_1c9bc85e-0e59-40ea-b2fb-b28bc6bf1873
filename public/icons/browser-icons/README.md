# Browser Icons

This directory contains SVG icons for different browsers used in the trust device flow.

## Available Icons

- **chrome.svg**: Google Chrome browser icon
- **firefox.svg**: Mozilla Firefox browser icon
- **safari.svg**: Apple Safari browser icon
- **edge.svg**: Microsoft Edge browser icon
- **opera.svg**: Opera browser icon

## Usage

These icons are used in the trust device flow to display the browser icon in the device list. The appropriate icon is selected based on the browser detected using the `device-detection.ts` utility.

### Example

```typescript
import { getDeviceInfo } from "@/utils/device-detection";

// Get device information including browser icon path
const deviceInfo = getDeviceInfo();

// Use the browser icon path in the request
const body = {
  // ...other fields
  logo: deviceInfo.browserIconPath,
  // ...other fields
};
```

## Adding New Icons

To add a new browser icon:

1. Create an SVG file with the browser name (lowercase) as the filename
2. Add the icon path to the `iconPaths` object in the `getBrowserIconPath` function in `device-detection.ts`

```typescript
const iconPaths: Record<string, string> = {
  'chrome': '/icons/browser-icons/chrome.svg',
  'firefox': '/icons/browser-icons/firefox.svg',
  'safari': '/icons/browser-icons/safari.svg',
  'edge': '/icons/browser-icons/edge.svg',
  'opera': '/icons/browser-icons/opera.svg',
  'new-browser': '/icons/browser-icons/new-browser.svg', // Add new browser here
};
```
