name: Preprod Image Build
on:
    workflow_dispatch:
        inputs:
            environment:
                type: choice
                description: Select Target Environemnt
                required: true
                options:
                    - "rova-infra-preprd"

permissions:
    id-token: write
    contents: read
jobs:
    build:
        name: build
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v2

            # - name: Configure AWS credentials
            #   uses: aws-actions/configure-aws-credentials@v1
            #   with:
            #       audience: sts.amazonaws.com
            #       role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_PREPROD }}
            #       aws-region: eu-west-1

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
                  aws-region: eu-west-1

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1

            - name: Fetch secrets from AWS Secrets Manager
              uses: aws-actions/aws-secretsmanager-get-secrets@v2
              with:
                  secret-ids: |
                      ,preprod/cib-client-frontend
                  parse-json-secrets: false
                  name-transformation: lowercase

            - name: Write secret to .env file
              run: |
                  echo "${{ env.preprod_cib_client_frontend  }}" > .env

            - name: Build, tag, and push the image to Amazon ECR
              id: build-image
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  ECR_REPOSITORY: rova-cba-cib-client-frontend-preprod
                  IMAGE_TAG: latest
              run: |
                  # Build a docker image and push it to ECR 
                  docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
                  echo "Pushing image to ECR..."
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
                  echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
