# Dynamic Permissions System Guide

## Problem Solved

Previously, our permission system used hardcoded permission strings like `"View all transactions"`. This created several issues:

1. **Backend Changes Break Frontend**: If the backend changed a permission name from "View all transactions" to "View transactions", the frontend would break.
2. **No Type Safety**: Hardcoded strings provided no compile-time validation.
3. **Maintenance Overhead**: Every permission change required frontend code updates.
4. **Inconsistency**: Different components might use slightly different permission strings.

## Solution: Dynamic Permission System

Our new system fetches permissions dynamically from the backend and provides type-safe constants that automatically update when backend permissions change.

## How It Works

### 1. Backend Integration
- Permissions are fetched from `/api/v1/permissions` endpoint
- System permissions define what permissions exist in the system
- User permissions define what the current user can do
- All stored in Redux for centralized state management

### 2. Dynamic Constants
Instead of hardcoded strings, we use dynamic constants that resolve at runtime:

```typescript
// ❌ Old way (hardcoded)
const ProtectedPage = withPermissionCheck(Component, {
    requiredPermissions: ["View all transactions"], // Breaks if backend changes this
});

// ✅ New way (dynamic)
const ProtectedPage = withDynamicPermissionCheck(Component, {
    permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS, // Automatically updates
});
```

### 3. Type Safety
The system provides full TypeScript support:

```typescript
const { PERMISSIONS } = usePermissionConstants();

// TypeScript will autocomplete and validate these
PERMISSIONS.TRANSACTIONS.VIEW_ALL
PERMISSIONS.USERS.CREATE
PERMISSIONS.SETTINGS.EDIT
```

## Usage Examples

### Basic Component Protection

```typescript
import { withDynamicPermissionCheck, PERMISSION_CHECKERS } from "@/components/hoc/withDynamicPermissionCheck";

const ProtectedPage = withDynamicPermissionCheck(MyComponent, {
    permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
    requireAll: false,
    redirectTo: "/dashboard",
    fallbackPermissions: ["View all transactions"], // Safety net
});
```

### Custom Permission Logic

```typescript
const ProtectedPage = withDynamicPermissionCheck(MyComponent, {
    permissions: (PERMISSIONS) => [
        PERMISSIONS.TRANSACTIONS.VIEW_ALL,
        PERMISSIONS.TRANSACTIONS.EXPORT,
    ],
    requireAll: true, // User needs BOTH permissions
});
```

### Multiple Permission Options

```typescript
const ProtectedPage = withDynamicPermissionCheck(MyComponent, {
    permissions: (PERMISSIONS) => [
        PERMISSIONS.TRANSACTIONS.VIEW_ALL,
        PERMISSIONS.TRANSACTIONS.VIEW_OWN,
    ],
    requireAll: false, // User needs ANY of these permissions
});
```

### Module-Based Permissions

```typescript
import { createPermissionChecker } from "@/components/hoc/withDynamicPermissionCheck";

const ADMIN_CHECKER = createPermissionChecker.module('USERS', ['VIEW_ALL', 'CREATE', 'EDIT']);

const ProtectedPage = withDynamicPermissionCheck(AdminPanel, {
    permissions: ADMIN_CHECKER,
    requireAll: true,
});
```

## Pre-built Permission Checkers

The system includes common permission patterns:

```typescript
import { PERMISSION_CHECKERS } from "@/components/hoc/withDynamicPermissionCheck";

// Single permissions
PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS
PERMISSION_CHECKERS.CREATE_TRANSFERS
PERMISSION_CHECKERS.DASHBOARD_ACCESS

// Multiple permissions (any one required)
PERMISSION_CHECKERS.MANAGE_TRANSACTIONS // CREATE, EDIT, or DELETE
PERMISSION_CHECKERS.USER_MANAGEMENT     // Any user management permission
PERMISSION_CHECKERS.ROLE_MANAGEMENT     // Any role management permission
```

## Component-Level Permission Checking

For conditional rendering within components:

```typescript
import { usePermissionConstants } from "@/hooks/usePermissionConstants";

const MyComponent = () => {
    const { PERMISSIONS } = usePermissionConstants();
    const { hasPermission } = usePermissions();
    
    return (
        <div>
            {hasPermission(PERMISSIONS.TRANSACTIONS.CREATE) && (
                <button>Create Transaction</button>
            )}
            
            {hasPermission(PERMISSIONS.TRANSACTIONS.EXPORT) && (
                <button>Export</button>
            )}
        </div>
    );
};
```

## Backend Permission Changes

When the backend changes permissions, the frontend automatically adapts:

### Scenario 1: Permission Renamed
```
Backend changes: "View all transactions" → "View transactions"
Frontend: Automatically uses new name, no code changes needed
```

### Scenario 2: New Permission Added
```
Backend adds: "Bulk edit transactions"
Frontend: Add to PERMISSIONS.TRANSACTIONS.BULK_EDIT in usePermissionConstants
```

### Scenario 3: Permission Removed
```
Backend removes: "Delete transaction"
Frontend: System warns in development, uses fallback if provided
```

## Development Tools

### Permission Validation
In development mode, the system validates permissions:

```typescript
// Console warnings for invalid permissions
console.warn('Permission "Invalid Permission" not found in system permissions');

// Validation helper
const { validatePermissions } = usePermissionConstants();
const result = validatePermissions(['Valid Permission', 'Invalid Permission']);
// result: { valid: ['Valid Permission'], invalid: ['Invalid Permission'] }
```

### Permission Debugging
```typescript
const { 
    getAllPermissionNames,
    getPermissionCount,
    getPermissionsByModule 
} = usePermissionConstants();

console.log('All permissions:', getAllPermissionNames());
console.log('Total permissions:', getPermissionCount());
console.log('Transaction permissions:', getPermissionsByModule('transactions'));
```

## Migration Guide

### Step 1: Replace withPermissionCheck
```typescript
// Before
import withPermissionCheck from "@/components/hoc/withPermissionCheck";

const Protected = withPermissionCheck(Component, {
    requiredPermissions: ["View all transactions"],
});

// After
import { withDynamicPermissionCheck, PERMISSION_CHECKERS } from "@/components/hoc/withDynamicPermissionCheck";

const Protected = withDynamicPermissionCheck(Component, {
    permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
    fallbackPermissions: ["View all transactions"], // Temporary safety net
});
```

### Step 2: Update Permission Strings
```typescript
// Before
hasPermission("View all transactions")

// After
const { PERMISSIONS } = usePermissionConstants();
hasPermission(PERMISSIONS.TRANSACTIONS.VIEW_ALL)
```

### Step 3: Remove Fallbacks (Optional)
Once you're confident the dynamic system is working:

```typescript
const Protected = withDynamicPermissionCheck(Component, {
    permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
    // Remove fallbackPermissions after testing
});
```

## Benefits

1. **Automatic Updates**: Frontend automatically adapts to backend permission changes
2. **Type Safety**: Full TypeScript support with autocomplete and validation
3. **Centralized Management**: All permissions managed through Redux
4. **Development Tools**: Built-in validation and debugging tools
5. **Backward Compatibility**: Gradual migration path with fallback support
6. **Performance**: Cached permissions with efficient lookups
7. **Consistency**: Standardized permission naming and usage patterns

## Best Practices

1. **Use Pre-built Checkers**: Prefer `PERMISSION_CHECKERS` over custom logic when possible
2. **Provide Fallbacks**: Include `fallbackPermissions` during migration
3. **Validate in Development**: Use validation tools to catch permission issues early
4. **Group Related Permissions**: Use module-based organization
5. **Document Custom Permissions**: Add comments for complex permission logic
6. **Test Permission Changes**: Verify behavior when backend permissions change
