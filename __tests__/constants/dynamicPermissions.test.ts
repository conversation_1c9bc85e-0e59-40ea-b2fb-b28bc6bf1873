import * as dynamicPermissionsModule from "@/constants/dynamicPermissions";
import permissionsService from "@/services/permissionsService";

jest.mock("@/services/permissionsService", () => {
    const isLoaded = true;
    const error = null;
    const isLoading = false;
    const allPermissions = {
        OVERVIEW_PERMISSIONS: { VIEW: "VIEW" },
        SETTINGS_PERMISSIONS: { EDIT: "EDIT" },
    };
    return {
        __esModule: true,
        default: {
            initialize: jest.fn(),
            getState: jest.fn(() => ({
                isLoaded,
                isLoading,
                error,
                allPermissions,
            })),
            refresh: jest.fn(),
            subscribe: jest.fn(() => () => {}),
        },
    };
});

describe("dynamicPermissions constants", () => {
    it("should export permission modules as proxies", () => {
        expect(dynamicPermissionsModule.OVERVIEW_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.SETTINGS_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.OUTGOING_PAYMENTS_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.SEND_MONEY_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.ACCOUNTS_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.TRANSACTIONS_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.BILL_PAYMENTS_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.BENEFICIARIES_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.TEAM_MANAGEMENT_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.SUPPORT_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.ONBOARDING_PERMISSIONS).toBeDefined();
        expect(dynamicPermissionsModule.ALL_PERMISSIONS).toBeDefined();
    });

    it("should return permissions from the proxy", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((dynamicPermissionsModule.OVERVIEW_PERMISSIONS as unknown as { [key: string]: any }).VIEW.VIEW).toBe(
            "VIEW"
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((dynamicPermissionsModule.SETTINGS_PERMISSIONS as unknown as { [key: string]: any }).EDIT.EDIT).toBe(
            "EDIT"
        );
    });

    it("should export arePermissionsLoaded, arePermissionsLoading, getPermissionsError, refreshPermissions", () => {
        expect(typeof dynamicPermissionsModule.arePermissionsLoaded()).toBe("boolean");
        expect(typeof dynamicPermissionsModule.arePermissionsLoading()).toBe("boolean");
        expect(["string", "object"].includes(typeof dynamicPermissionsModule.getPermissionsError())).toBe(true);
        expect(dynamicPermissionsModule.refreshPermissions).toBeInstanceOf(Function);
    });

    it("should export waitForPermissions and resolve", async () => {
        await expect(dynamicPermissionsModule.waitForPermissions()).resolves.toBeDefined();
    });

    it("should return empty objects when permissions are not loaded", () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            isLoading: true,
            error: null,
            allPermissions: {},
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((dynamicPermissionsModule.OVERVIEW_PERMISSIONS as unknown as { [key: string]: any }).ANY).toEqual({});
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((dynamicPermissionsModule.SETTINGS_PERMISSIONS as unknown as { [key: string]: any }).ANY).toEqual({});
    });

    it("should return {} for all permission group proxies when not loaded", () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            isLoading: true,
            error: null,
            allPermissions: {},
        });
        const groups = [
            "OUTGOING_PAYMENTS_PERMISSIONS",
            "SEND_MONEY_PERMISSIONS",
            "ACCOUNTS_PERMISSIONS",
            "TRANSACTIONS_PERMISSIONS",
            "BILL_PAYMENTS_PERMISSIONS",
            "BENEFICIARIES_PERMISSIONS",
            "TEAM_MANAGEMENT_PERMISSIONS",
            "SUPPORT_PERMISSIONS",
            "ONBOARDING_PERMISSIONS",
            "ALL_PERMISSIONS",
        ];
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        for (const group of groups) {
            expect(
                (
                    dynamicPermissionsModule[group as keyof typeof dynamicPermissionsModule] as unknown as {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        [key: string]: any;
                    }
                ).ANY
            ).toEqual({});
        }
    });

    it("should return correct values for all permission group proxies when loaded", () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            isLoading: false,
            error: null,
            allPermissions: {
                OUTGOING_PAYMENTS_PERMISSIONS: { PAY: "PAY" },
                SEND_MONEY_PERMISSIONS: { SEND: "SEND" },
                ACCOUNTS_PERMISSIONS: { VIEW: "VIEW" },
                TRANSACTIONS_PERMISSIONS: { EXPORT: "EXPORT" },
                BILL_PAYMENTS_PERMISSIONS: { PAY: "PAY" },
                BENEFICIARIES_PERMISSIONS: { ADD: "ADD" },
                TEAM_MANAGEMENT_PERMISSIONS: { MANAGE: "MANAGE" },
                SUPPORT_PERMISSIONS: { CONTACT: "CONTACT" },
                ONBOARDING_PERMISSIONS: { START: "START" },
                ALL_PERMISSIONS: { ALL: "ALL" },
            },
        });
        const groups = [
            ["OUTGOING_PAYMENTS_PERMISSIONS", "PAY"],
            ["SEND_MONEY_PERMISSIONS", "SEND"],
            ["ACCOUNTS_PERMISSIONS", "VIEW"],
            ["TRANSACTIONS_PERMISSIONS", "EXPORT"],
            ["BILL_PAYMENTS_PERMISSIONS", "PAY"],
            ["BENEFICIARIES_PERMISSIONS", "ADD"],
            ["TEAM_MANAGEMENT_PERMISSIONS", "MANAGE"],
            ["SUPPORT_PERMISSIONS", "CONTACT"],
            ["ONBOARDING_PERMISSIONS", "START"],
            ["ALL_PERMISSIONS", "ALL"],
        ];
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        for (const [group, key] of groups) {
            expect(
                (
                    dynamicPermissionsModule[group as keyof typeof dynamicPermissionsModule] as unknown as {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        [key: string]: any;
                    }
                )[key][key]
            ).toBe(key);
        }
    });

    it("getOwnPropertyDescriptor returns undefined for missing property", () => {
        const dynamicPermissions = dynamicPermissionsModule.default;
        const desc = Object.getOwnPropertyDescriptor(dynamicPermissions, "NOT_A_PERMISSION");
        expect(desc).toBeUndefined();
    });

    it("proxy get trap returns undefined for missing property", () => {
        const dynamicPermissions = dynamicPermissionsModule.default;
        // @ts-expect-error
        expect(dynamicPermissions.NOT_A_PERMISSION).toBeUndefined();
    });

    it("waitForPermissions resolves after subscription", async () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let callback: any;
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            isLoading: true,
            error: null,
            allPermissions: {},
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService.subscribe as jest.Mock).mockImplementation((cb: any) => {
            callback = cb;
            return () => {};
        });
        const promise = dynamicPermissionsModule.waitForPermissions();
        // Simulate permissions loaded
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        callback({
            isLoaded: true,
            isLoading: false,
            error: null,
            allPermissions: { OVERVIEW_PERMISSIONS: { VIEW: "VIEW" } },
        });
        await expect(promise).resolves.toBeDefined();
    });

    it("should reject waitForPermissions on error", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            isLoading: false,
            error: "Some error",
            allPermissions: {},
        });
        await expect(dynamicPermissionsModule.waitForPermissions()).rejects.toThrow("Some error");
    });

    it("proxy traps: ownKeys, has, getOwnPropertyDescriptor", () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            isLoading: false,
            error: null,
            allPermissions: {
                OVERVIEW_PERMISSIONS: { VIEW: "VIEW" },
                SETTINGS_PERMISSIONS: { EDIT: "EDIT" },
            },
        });
        const dynamicPermissions = dynamicPermissionsModule.default;
        const keys = Reflect.ownKeys(dynamicPermissions);
        expect(keys).toContain("OVERVIEW_PERMISSIONS");
        expect("OVERVIEW_PERMISSIONS" in dynamicPermissions).toBe(true);
        const desc = Object.getOwnPropertyDescriptor(dynamicPermissions, "OVERVIEW_PERMISSIONS");
        expect(desc).toBeDefined();
        expect(desc?.value).toBeDefined();
    });
});
