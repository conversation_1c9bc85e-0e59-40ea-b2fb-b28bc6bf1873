// app/error.test.tsx
import { render, screen, fireEvent } from "@testing-library/react";
import GlobalError from "@/app/error.tsx";
import { useRouter } from "next/navigation";

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
}));

describe("GlobalError", () => {
    const mockError = new Error("Test error");
    const mockReset = jest.fn();
    const mockRouter = {
        back: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useRouter.mockReturnValue(mockRouter);
    });

    it("renders error message and buttons", () => {
        render(<GlobalError error={mockError} reset={mockReset} />);

        // Check if error message is displayed
        expect(screen.getByText("Uh-oh! Something didn’t go as planned.")).toBeInTheDocument();
        expect(
            screen.getByText("It looks like something went off-track. Let’s give it another go!")
        ).toBeInTheDocument();

        // Check if buttons are present
        expect(screen.getByText("Back")).toBeInTheDocument();
        expect(screen.getByText("Try again")).toBeInTheDocument();
    });

    it("calls reset function when Try again button is clicked", () => {
        render(<GlobalError error={mockError} reset={mockReset} />);

        const tryAgainButton = screen.getByText("Try again");
        fireEvent.click(tryAgainButton);

        expect(mockReset).toHaveBeenCalledTimes(1);
    });

    it("calls router.back when Back button is clicked", () => {
        render(<GlobalError error={mockError} reset={mockReset} />);

        const backButton = screen.getByText("Back");
        fireEvent.click(backButton);

        expect(mockRouter.back).toHaveBeenCalledTimes(1);
    });

    it("logs error to console", () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        render(<GlobalError error={mockError} reset={mockReset} />);

        expect(consoleSpy).toHaveBeenCalledWith("Global error boundary caught:", mockError);
        consoleSpy.mockRestore();
    });
});
