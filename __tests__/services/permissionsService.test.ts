import permissionsService from "@/services/permissionsService";
import { userAxios } from "@/api/axios";

describe("permissionsService", () => {
    beforeEach(() => {
        localStorage.clear();
        jest.clearAllMocks();
    });

    it("should initialize and get state", async () => {
        expect(typeof permissionsService.initialize).toBe("function");
        expect(typeof permissionsService.getState).toBe("function");
        await permissionsService.initialize();
        expect(permissionsService.getState()).toBeDefined();
    });

    it("should save and load cache", () => {
        const perms = { MOD: { VIEW: "VIEW" } };
        const list = [{ id: 1, name: "PERM" }];
        // @ts-ignore
        permissionsService.saveToCache(perms, list);
        // @ts-ignore
        const loaded = permissionsService.loadFromCache();
        expect(loaded).toBeTruthy();
        if (loaded) {
            expect(loaded.permissions).toEqual(perms);
            expect(loaded.permissionsList).toEqual(list);
        }
    });

    it("should handle cache expiration", () => {
        const now = Date.now();
        // @ts-ignore
        expect(permissionsService.isCacheExpired(now - 100000000)).toBe(true);
        // @ts-ignore
        expect(permissionsService.isCacheExpired(now)).toBe(false);
    });

    it("should clear cache without error", () => {
        permissionsService.clearCache();
        expect(localStorage.getItem("PERMISSIONS_CACHE_KEY")).toBeNull();
    });

    it("should handle fallback cache", () => {
        const perms = { MOD: { VIEW: "VIEW" } };
        const list = [{ id: 1, name: "PERM" }];
        // @ts-ignore
        permissionsService.saveToFallbackCache(perms, list);
        // @ts-ignore
        const loaded = permissionsService.loadFromFallbackCache();
        expect(loaded).toBeTruthy();
    });

    it("should handle errors in cache methods gracefully", () => {
        const orig = localStorage.setItem;
        localStorage.setItem = jest.fn(() => {
            throw new Error("fail");
        });
        // @ts-ignore
        expect(() => permissionsService.saveToCache({}, [])).not.toThrow();
        localStorage.setItem = orig;
    });

    it("should get and check permissions", () => {
        expect(Array.isArray(permissionsService.getAllPermissionsList())).toBe(true);
        expect(typeof permissionsService.hasPermission).toBe("function");
    });

    it("should normalize module names and generate constant keys", () => {
        expect(permissionsService["normalizeModuleName"]).toBeInstanceOf(Function);
        expect(permissionsService["generateConstantKey"]).toBeInstanceOf(Function);
        expect(permissionsService["normalizeModuleName"]("Test Module")).toContain("PERMISSIONS");
        expect(permissionsService["generateConstantKey"]("perm".toUpperCase())).toBeDefined();
    });

    it("should handle errors in saveToFallbackCache gracefully", () => {
        const orig = localStorage.setItem;
        localStorage.setItem = jest.fn(() => {
            throw new Error("fail");
        });
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect(() =>
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (permissionsService as any)["saveToFallbackCache"]({ MOD: { VIEW: "VIEW" } }, [{ id: 1, name: "PERM" }])
        ).not.toThrow();
        localStorage.setItem = orig;
    });

    it("should handle errors in loadFromCache gracefully", () => {
        const orig = localStorage.getItem;
        localStorage.getItem = jest.fn(() => {
            throw new Error("fail");
        });
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect(() => (permissionsService as any)["loadFromCache"]()).not.toThrow();
        localStorage.getItem = orig;
    });

    it("should handle errors in loadFromFallbackCache gracefully", () => {
        const orig = localStorage.getItem;
        localStorage.getItem = jest.fn(() => {
            throw new Error("fail");
        });
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect(() => (permissionsService as any)["loadFromFallbackCache"]()).not.toThrow();
        localStorage.getItem = orig;
    });

    it("should handle errors in clearCache gracefully", () => {
        const orig = localStorage.removeItem;
        localStorage.removeItem = jest.fn(() => {
            throw new Error("fail");
        });
        expect(() => permissionsService.clearCache()).not.toThrow();
        localStorage.removeItem = orig;
    });

    it("should handle errors in saveToCache and saveToFallbackCache via fetchPermissions", async () => {
        const origSetItem = localStorage.setItem;
        localStorage.setItem = jest.fn(() => {
            throw new Error("fail");
        });
        const origUserAxiosGet = userAxios.get;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        userAxios.get = jest.fn(
            () =>
                Promise.resolve({
                    data: [{ id: 1, name: "PERM", appModule: "MOD", endpoint: "/", method: "GET" }],
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                }) as unknown as Promise<any>
        );
        await permissionsService.fetchPermissions();
        localStorage.setItem = origSetItem;
        userAxios.get = origUserAxiosGet;
    });

    it("should handle errors in loadFromCache via initialize", async () => {
        const origGetItem = localStorage.getItem;
        localStorage.getItem = jest.fn(() => {
            throw new Error("fail");
        });
        await permissionsService.initialize();
        localStorage.getItem = origGetItem;
    });

    it("should handle errors in loadFromFallbackCache via initialize fallback", async () => {
        const origGetItem = localStorage.getItem;
        localStorage.getItem = jest.fn((key) => {
            if (key === "system_permissions_cache" || key === "system_permissions_timestamp") {
                return null;
            }
            throw new Error("fail");
        });
        const origUserAxiosGet = userAxios.get;
        userAxios.get = jest.fn(() => Promise.reject(new Error("fail")));
        await permissionsService.initialize();
        localStorage.getItem = origGetItem;
        userAxios.get = origUserAxiosGet;
    });

    it("should use minimal defaults if all cache and fetch fail", async () => {
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const origLoadFromCache = (permissionsService as any)["loadFromCache"];
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const origLoadFromFallbackCache = (permissionsService as any)["loadFromFallbackCache"];
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const origSetState = (permissionsService as any)["setState"];
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["loadFromCache"] = jest.fn(() => null);
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["loadFromFallbackCache"] = jest.fn(() => null);
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["setState"] = jest.fn();
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["tryFallbackMechanisms"]();
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((permissionsService as any)["setState"]).toHaveBeenCalledWith(
            expect.objectContaining({
                allPermissions: expect.any(Object),
                permissionsList: expect.any(Array),
                isLoaded: true,
                error: expect.stringContaining("minimal permissions"),
            })
        );
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["loadFromCache"] = origLoadFromCache;
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["loadFromFallbackCache"] = origLoadFromFallbackCache;
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (permissionsService as any)["setState"] = origSetState;
    });

    it("should detect network errors in isNetworkError", () => {
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const svc: any = permissionsService;
        expect(svc.isNetworkError({ code: "NETWORK_ERROR" })).toBe(true);
        expect(svc.isNetworkError({ message: "network timeout" })).toBe(true);
        expect(svc.isNetworkError({ name: "NetworkError" })).toBe(true);
        expect(svc.isNetworkError({ response: { status: 500 } })).toBe(true);
        expect(svc.isNetworkError({ message: "other error" })).toBe(false);
        expect(svc.isNetworkError(null)).toBe(false);
    });

    it("should retry fetchPermissions on network error and stop after max attempts", async () => {
        const origSetTimeout = global.setTimeout;
        const setTimeoutSpy = jest.spyOn(global, "setTimeout").mockImplementation((fn: () => void) => {
            fn();
            return 0 as unknown as NodeJS.Timeout;
        });
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const svc: any = permissionsService;
        const origIsNetworkError = svc.isNetworkError;
        svc.isNetworkError = () => true;
        const fetchSpy = jest.spyOn(svc, "fetchPermissions");
        await svc.fetchPermissions(false, 0);
        expect(fetchSpy).toHaveBeenCalled();
        svc.isNetworkError = origIsNetworkError;
        setTimeoutSpy.mockRestore();
        fetchSpy.mockRestore();
        global.setTimeout = origSetTimeout;
    });
});
