import { sendFeedback } from "../../src/functions/feedback";
import {
    convertCamelCaseToWords,
    convertDateToMonthYear,
    convertFirstLetterToUppercase,
    convertToCamelCase,
    convertToSnakeCase,
    copyToClipboard,
    enumToCapitalizedPhrase,
    findOptionByValue,
    formatNumber,
    formatNumberToNaira,
    generateAuthQRCodeValue,
    getNameInitials,
    maskNumber,
} from "../../src/functions/stringManipulations";

// Mock the sendFeedback function
jest.mock("../../src/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

describe("String Manipulations Utils", () => {
    describe("formatNumber", () => {
        test("should format number with no fraction digits (default)", () => {
            expect(formatNumber(12345)).toBe("12,345");
        });

        test("should format number with given fraction digits", () => {
            expect(formatNumber(12345.6789, 2)).toBe("12,345.68");
        });

        test("should handle negative and zero correctly", () => {
            expect(formatNumber(-12345.6789, 2)).toBe("-12,345.68");
            expect(formatNumber(0)).toBe("0");
        });
    });

    describe("formatNumberToNaira", () => {
        test("should format a number to Naira currency (default 2 decimal places)", () => {
            expect(formatNumberToNaira(1000)).toBe("₦1,000.00");
        });

        test("should format number to Naira with fraction digits", () => {
            expect(formatNumberToNaira(1000.5, 2)).toBe("₦1,000.50");
        });

        test("should format number to Naira with custom fraction digits", () => {
            expect(formatNumberToNaira(1000.5, 0)).toBe("₦1,001");
        });

        test("should format decimal amounts correctly", () => {
            expect(formatNumberToNaira(1000.567)).toBe("₦1,000.57");
        });
    });

    describe("getNameInitials", () => {
        test("should return initials for two name parts", () => {
            expect(getNameInitials("John Doe")).toBe("JD");
        });

        test("should return initials only from the first two names when extra names exist", () => {
            expect(getNameInitials("John Peter Doe")).toBe("JP");
        });
    });

    describe("copyToClipboard", () => {
        let originalClipboard;

        beforeEach(() => {
            // Save the original clipboard
            originalClipboard = { ...navigator.clipboard };
            // Clear mocks between tests
            jest.clearAllMocks();
        });

        afterEach(() => {
            // Restore the original clipboard
            Object.assign(navigator, { clipboard: originalClipboard });
        });

        test("should copy text and send success feedback", async () => {
            const writeTextMock = jest.fn(() => Promise.resolve());
            Object.assign(navigator, {
                clipboard: {
                    writeText: writeTextMock,
                },
            });

            await copyToClipboard("Hello World");

            expect(writeTextMock).toHaveBeenCalledTimes(1);
            expect(writeTextMock).toHaveBeenCalledWith("Hello World");
            expect(sendFeedback).toHaveBeenCalledTimes(1);
            expect(sendFeedback).toHaveBeenCalledWith("Copied to clipboard", "success");
        });

        test("should propagate error when clipboard fails", async () => {
            const errorMessage = "Copy error";
            const writeTextMock = jest.fn(() => Promise.reject(new Error(errorMessage)));
            Object.assign(navigator, {
                clipboard: {
                    writeText: writeTextMock,
                },
            });

            await expect(copyToClipboard("Hello World")).rejects.toThrow(errorMessage);
            expect(writeTextMock).toHaveBeenCalledTimes(1);
            expect(sendFeedback).not.toHaveBeenCalledWith("Copied to clipboard", "success");
        });
    });

    describe("generateAuthQRCodeValue", () => {
        test("should generate a valid auth QR code value", () => {
            expect(generateAuthQRCodeValue()).toBe("otpauth://totp/CIB?secret=CIB");
        });
    });

    describe("convertCamelCaseToWords", () => {
        test("should convert camel case to words with first letter capitalized", () => {
            expect(convertCamelCaseToWords("helloWorld")).toBe("Hello World");
        });

        test("should convert a longer camelCase string to words", () => {
            expect(convertCamelCaseToWords("helloWorldAgain")).toBe("Hello World Again");
        });
    });

    describe("convertFirstLetterToUppercase", () => {
        test("should convert the first letter to uppercase", () => {
            expect(convertFirstLetterToUppercase("hello world")).toBe("Hello world");
        });
    });

    describe("maskNumber", () => {
        test("should return the original value if it is equal or shorter than default (4 digits)", () => {
            expect(maskNumber(123)).toBe("123");
            expect(maskNumber("1234")).toBe("1234");
        });

        test("should mask all but the last 4 digits by default", () => {
            expect(maskNumber(123456)).toBe("**3456");
            expect(maskNumber("123456789")).toBe("*****6789");
        });

        test("should mask all but the specified number of digits", () => {
            expect(maskNumber(123456, 2)).toBe("****56");
            expect(maskNumber("123456789", 3)).toBe("******789");
        });

        test("should handle non-numeric input", () => {
            expect(maskNumber("abc")).toBe("abc");
            expect(maskNumber("123abc")).toBe("**3abc");
        });
    });

    describe("convertToCamelCase", () => {
        test("should convert snake_case to camelCase", () => {
            expect(convertToCamelCase("hello_world")).toBe("helloWorld");
        });

        test("should handle multiple underscores", () => {
            expect(convertToCamelCase("convert_to_camel_case")).toBe("convertToCamelCase");
        });

        test("should handle empty string", () => {
            expect(convertToCamelCase("")).toBe("");
        });
    });

    describe("enumToCapitalizedPhrase", () => {
        test("should convert enum string to capitalized phrase", () => {
            expect(enumToCapitalizedPhrase("SINGLE_PHASE")).toBe("Single Phase");
            expect(enumToCapitalizedPhrase("MULTI_PHASE")).toBe("Multi Phase");
        });

        test("should return empty string for an empty input", () => {
            expect(enumToCapitalizedPhrase("")).toBe("");
        });

        test("should convert complex enum string to phrase", () => {
            expect(enumToCapitalizedPhrase("MANY_WORDS_IN_ENUM")).toBe("Many Words In Enum");
        });

        test("should throw an error if input is not a string", () => {
            // @ts-ignore: Testing error case for non-string input
            expect(() => enumToCapitalizedPhrase(123)).toThrow("Input must be a string");
        });
    });

    describe("convertDateToMonthYear", () => {
        test("should convert a valid date string to Month, Year format", () => {
            expect(convertDateToMonthYear("2025-04-14")).toBe("April, 2025");
        });

        test("should convert a valid Date object to Month, Year format", () => {
            expect(convertDateToMonthYear(new Date("2025-04-14"))).toBe("April, 2025");
        });

        test("should return an error string for an invalid date input", () => {
            expect(convertDateToMonthYear("invalid-date")).toMatch(/^Error:/);
            expect(convertDateToMonthYear("")).toMatch(/^Error:/);
            expect(convertDateToMonthYear("Not a Date")).toMatch(/^Error:/);
        });
    });

    describe("findOptionByValue", () => {
        const mockOptions = [
            { label: "Option 1", value: "1" },
            { label: "Option 2", value: "2" },
            { label: "Option 3", value: "3" },
        ];

        it("should return the correct option when value exists", () => {
            const result = findOptionByValue("2", mockOptions);
            expect(result).toEqual({ label: "Option 2", value: "2" });
        });

        it("should return null when value does not exist", () => {
            const result = findOptionByValue("4", mockOptions);
            expect(result).toBeNull();
        });

        it("should return null when options array is empty", () => {
            const result = findOptionByValue("1", []);
            expect(result).toBeNull();
        });

        it("should handle cases with duplicate values (returning first match)", () => {
            const optionsWithDuplicates = [...mockOptions, { label: "Option 2 Duplicate", value: "2" }];
            const result = findOptionByValue("2", optionsWithDuplicates);
            expect(result).toEqual({ label: "Option 2", value: "2" });
        });

        it("should be case sensitive when comparing values", () => {
            const result = findOptionByValue("Option 1", mockOptions);
            expect(result).toBeNull();
        });
    });

    describe("convertToSnakeCase", () => {
        it("should convert camelCase to snake_case", () => {
            expect(convertToSnakeCase("camelCaseString")).toBe("camel_case_string");
            expect(convertToSnakeCase("anotherTestExample")).toBe("another_test_example");
            expect(convertToSnakeCase("PDFExport")).toBe("pdf_export");
            expect(convertToSnakeCase("XMLHttpRequest")).toBe("xml_http_request");
        });

        it("should handle spaces, dashes and dots", () => {
            expect(convertToSnakeCase("some string")).toBe("some_string");
            expect(convertToSnakeCase("another-string")).toBe("another_string");
            expect(convertToSnakeCase("file.name")).toBe("file_name");
            expect(convertToSnakeCase("string with spaces-and.dashes")).toBe("string_with_spaces_and_dashes");
        });

        it("should handle edge cases", () => {
            expect(convertToSnakeCase("")).toBe("");
            expect(convertToSnakeCase("   ")).toBe("");
            expect(convertToSnakeCase("__test__")).toBe("test");
            expect(convertToSnakeCase("ALLCAPS")).toBe("allcaps");
            expect(convertToSnakeCase("version2")).toBe("version2");
        });
    });
});
