import { exportAsCSV, exportAsPDF } from "@/functions/exportDocument";
import { sendCatchFeedback } from "@/functions/feedback";
import * as exportToCsv from "export-to-csv";
import jsPDF from "jspdf";

jest.mock("jspdf");
jest.mock("export-to-csv", () => ({
    mkConfig: jest.fn(),
    generateCsv: jest.fn(),
    download: jest.fn(),
}));
jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

const mockData = [
    {
        id: 1,
        name: "Test User",
        timestamp: new Date().toISOString(),
        amount: 2000,
        status: "completed",
    },
    {
        id: 2,
        name: "Another User",
        timestamp: new Date(Date.now() - 86400000).toISOString(), // yesterday
        amount: 3500,
        status: "pending",
    },
];

const mockColumns = [
    { header: "ID", key: "id" },
    { header: "Name", key: "name" },
    { header: "Timestamp", key: "timestamp" },
    { header: "Amount", key: "amount" },
    { header: "Status", key: "status" },
];

describe("Export Utility Functions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("exportAsPDF", () => {
        let saveMock;
        let autoTableMock;

        beforeEach(() => {
            saveMock = jest.fn();
            autoTableMock = jest.fn();

            jsPDF.mockImplementation(() => ({
                setFillColor: jest.fn(),
                rect: jest.fn(),
                setFont: jest.fn().mockReturnThis(),
                setFontSize: jest.fn().mockReturnThis(),
                setTextColor: jest.fn().mockReturnThis(),
                text: jest.fn(),
                autoTable: autoTableMock,
                internal: {
                    pageSize: {
                        width: 297,
                        height: 210,
                    },
                    getNumberOfPages: jest.fn(() => 1),
                },
                save: saveMock,
            }));
        });

        it("generates and saves PDF successfully", () => {
            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                startDate: "2024-01-01",
                endDate: "2024-03-01",
                documentTitle: "Test PDF",
            });

            expect(jsPDF).toHaveBeenCalledWith({
                format: "a4",
                orientation: "landscape",
                unit: "mm",
            });
            expect(autoTableMock).toHaveBeenCalled();
            expect(saveMock).toHaveBeenCalledWith(expect.stringMatching(/test_pdf.*\.pdf$/));
        });

        it("handles error and calls sendCatchFeedback", () => {
            jsPDF.mockImplementation(() => {
                throw new Error("PDF failed");
            });

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Broken PDF",
            });

            expect(sendCatchFeedback).toHaveBeenCalledWith(
                "Error generating PDF. Please try a different format or with fewer rows."
            );
        });

        it("handles empty data array", () => {
            exportAsPDF({
                data: [],
                columns: mockColumns,
                documentTitle: "Empty Data PDF",
            });

            expect(autoTableMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    head: [mockColumns.map((col) => col.header)],
                    body: [],
                })
            );
        });

        it("handles date formatting in rows", () => {
            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Date Test PDF",
            });

            const rows = autoTableMock.mock.calls[0][0].body;
            expect(rows[0][2]).toMatch(/\d{2} \w{3} \d{4} \d{2}:\d{2}/); // Check date format
        });

        it("handles multiple pages", () => {
            // Mock getNumberOfPages to return 2
            jsPDF.mockImplementation(() => ({
                setFillColor: jest.fn(),
                rect: jest.fn(),
                setFont: jest.fn().mockReturnThis(),
                setFontSize: jest.fn().mockReturnThis(),
                setTextColor: jest.fn().mockReturnThis(),
                text: jest.fn(),
                autoTable: autoTableMock,
                internal: {
                    pageSize: {
                        width: 297,
                        height: 210,
                    },
                    getNumberOfPages: jest.fn(() => 2),
                },
                save: saveMock,
            }));

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Multi-page PDF",
            });

            expect(autoTableMock).toHaveBeenCalled();
        });
    });

    describe("exportAsCSV", () => {
        const generateMock = jest.fn(() => "csv-content");
        const downloadMock = jest.fn();

        beforeEach(() => {
            exportToCsv.mkConfig.mockReturnValue({ delimiter: "," });
            exportToCsv.generateCsv.mockReturnValue(generateMock);
            exportToCsv.download.mockReturnValue(downloadMock);
        });

        it("handles empty columns array gracefully", () => {
            exportAsCSV({
                data: mockData,
                columns: [],
                documentTitle: "Empty Columns CSV",
            });

            expect(sendCatchFeedback).not.toHaveBeenCalled();
            expect(exportToCsv.mkConfig).toHaveBeenCalledWith(expect.objectContaining({ columnHeaders: [] }));
        });

        it("generates and downloads CSV successfully", () => {
            exportAsCSV({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Test CSV",
            });

            expect(exportToCsv.mkConfig).toHaveBeenCalledWith(
                expect.objectContaining({ filename: expect.any(String) })
            );
            expect(generateMock).toHaveBeenCalled();
            expect(downloadMock).toHaveBeenCalledWith("csv-content");
        });

        it("handles error and calls sendCatchFeedback", () => {
            exportToCsv.generateCsv.mockImplementation(() => {
                throw new Error("CSV failed");
            });

            exportAsCSV({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Broken CSV",
            });

            expect(sendCatchFeedback).toHaveBeenCalledWith(
                "Error generating CSV. Please try again with a different format."
            );
        });

        it("handles empty data gracefully", () => {
            exportAsCSV({
                data: [],
                columns: mockColumns,
                documentTitle: "Empty CSV",
            });

            expect(generateMock).toHaveBeenCalled();
            expect(exportToCsv.mkConfig).toHaveBeenCalledWith(
                expect.objectContaining({ columnHeaders: mockColumns.map((col) => col.header) })
            );
        });

        it("formats amount and status correctly", () => {
            exportAsCSV({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Formatted CSV",
            });

            const formattedRows = generateMock.mock.calls[0][0];
            expect(formattedRows[0]["Amount"]).toMatch(/₦2,000\.00/);
            expect(formattedRows[0]["Status"]).toBe("COMPLETED");
            expect(formattedRows[1]["Status"]).toBe("PENDING");
        });

        it("handles date formatting in CSV", () => {
            exportAsCSV({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Date CSV",
            });

            const formattedRows = generateMock.mock.calls[0][0];
            expect(formattedRows[0]["Timestamp"]).toMatch(/\d{2} \w{3} \d{4} \d{2}:\d{2}/);
        });
    });

    describe("exportAsPDF didDrawPage callback", () => {
        let saveMock;
        let autoTableMock;
        let docMock;

        beforeEach(() => {
            saveMock = jest.fn();
            autoTableMock = jest.fn();

            docMock = {
                setFillColor: jest.fn(),
                rect: jest.fn(),
                setFont: jest.fn().mockReturnThis(),
                setFontSize: jest.fn().mockReturnThis(),
                setTextColor: jest.fn().mockReturnThis(),
                text: jest.fn(),
                autoTable: autoTableMock,
                internal: {
                    pageSize: {
                        width: 297,
                        height: 210,
                    },
                    getNumberOfPages: jest.fn(),
                },
                save: saveMock,
            };

            jsPDF.mockImplementation(() => docMock);
        });

        it("should add footer with page numbers on first page", () => {
            docMock.internal.getNumberOfPages.mockReturnValue(2);

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Footer Test PDF",
            });

            // Get the didDrawPage callback
            const didDrawPage = autoTableMock.mock.calls[0][0].didDrawPage;

            // Simulate drawing first page
            didDrawPage({
                pageNumber: 1,
                settings: { margin: { left: 14, right: 14 } },
            });

            // Verify footer was added
            expect(docMock.setFontSize).toHaveBeenCalledWith(8);
            expect(docMock.setTextColor).toHaveBeenCalledWith(100, 100, 100);
            expect(docMock.text).toHaveBeenCalledWith("Page 1 of 2", 14, expect.any(Number));
            expect(docMock.text).toHaveBeenCalledWith(
                "FCMB",
                283, // 297 (page width) - 14 (right margin)
                expect.any(Number),
                { align: "right" }
            );

            // Verify no continuation header was added
            expect(docMock.text).not.toHaveBeenCalledWith(
                expect.stringContaining("(Continued)"),
                expect.any(Number),
                expect.any(Number)
            );
        });

        it("should add continuation header on subsequent pages", () => {
            docMock.internal.getNumberOfPages.mockReturnValue(3);

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Continuation Test PDF",
            });

            // Get the didDrawPage callback
            const didDrawPage = autoTableMock.mock.calls[0][0].didDrawPage;

            // Simulate drawing second page
            didDrawPage({
                pageNumber: 2,
                settings: { margin: { left: 14, right: 14 } },
            });

            // Verify continuation header was added
            expect(docMock.setFillColor).toHaveBeenCalledWith(92, 6, 140);
            expect(docMock.rect).toHaveBeenCalledWith(0, 0, 297, 10, "F");
            expect(docMock.setFont).toHaveBeenCalledWith("helvetica", "bold");
            expect(docMock.setFontSize).toHaveBeenCalledWith(10);
            expect(docMock.setTextColor).toHaveBeenCalledWith(255, 255, 255);
            expect(docMock.text).toHaveBeenCalledWith("Continuation Test PDF (Continued)", 14, 7);

            // Verify footer was still added
            expect(docMock.text).toHaveBeenCalledWith("Page 2 of 3", 14, expect.any(Number));
        });

        it("should handle different margin values correctly", () => {
            docMock.internal.getNumberOfPages.mockReturnValue(1);

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Margin Test PDF",
            });

            // Get the didDrawPage callback
            const didDrawPage = autoTableMock.mock.calls[0][0].didDrawPage;

            // Simulate with different margins
            didDrawPage({
                pageNumber: 1,
                settings: { margin: { left: 20, right: 30 } },
            });

            // Verify footer positions use correct margins
            expect(docMock.text).toHaveBeenCalledWith(
                "Page 1 of 1",
                20, // left margin
                expect.any(Number)
            );
            expect(docMock.text).toHaveBeenCalledWith(
                "FCMB",
                267, // 297 (page width) - 30 (right margin)
                expect.any(Number),
                { align: "right" }
            );
        });

        it("should not add continuation header on first page", () => {
            docMock.internal.getNumberOfPages.mockReturnValue(1);

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "First Page Test PDF",
            });

            // Get the didDrawPage callback
            const didDrawPage = autoTableMock.mock.calls[0][0].didDrawPage;

            // Simulate drawing first page
            didDrawPage({
                pageNumber: 1,
                settings: { margin: { left: 14, right: 14 } },
            });

            // Verify no continuation header was added
            expect(docMock.text).not.toHaveBeenCalledWith(
                expect.stringContaining("(Continued)"),
                expect.any(Number),
                expect.any(Number)
            );
        });

        it("should handle single page documents correctly", () => {
            docMock.internal.getNumberOfPages.mockReturnValue(1);

            exportAsPDF({
                data: mockData,
                columns: mockColumns,
                documentTitle: "Single Page Test PDF",
            });

            // Get the didDrawPage callback
            const didDrawPage = autoTableMock.mock.calls[0][0].didDrawPage;

            // Simulate drawing the page
            didDrawPage({
                pageNumber: 1,
                settings: { margin: { left: 14, right: 14 } },
            });

            // Verify footer shows "Page 1 of 1"
            expect(docMock.text).toHaveBeenCalledWith("Page 1 of 1", expect.any(Number), expect.any(Number));
        });
    });
});
