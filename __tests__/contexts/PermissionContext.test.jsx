"use client";
import { render, screen, act, waitFor } from "@testing-library/react";
import { PermissionProvider, usePermissions } from "@/contexts/PermissionContext";
import { useAppSelector } from "@/redux/hooks";
import { userAxios } from "@/api/axios";
import { jwtDecode } from "jwt-decode";
import { cookies } from "@/lib/cookies";
import "@testing-library/jest-dom";

jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
}));

jest.mock("@/api/axios", () => ({
    userAxios: {
        get: jest.fn(),
    },
}));

jest.mock("jwt-decode", () => ({
    jwtDecode: jest.fn(),
}));

jest.mock("@/lib/cookies", () => ({
    cookies: {
        getToken: jest.fn(),
        isAuthenticated: jest.fn(),
    },
}));

// Mock the permissionsService
jest.mock("@/services/permissionsService", () => ({
    __esModule: true,
    default: {
        getState: jest.fn(() => ({
            systemPermissions: [],
            isLoadingSystemPermissions: false,
            systemPermissionsError: null,
        })),
        initialize: jest.fn(),
        refreshPermissions: jest.fn(),
        subscribe: jest.fn(() => () => {}),
        refresh: jest.fn().mockResolvedValue(undefined),
    },
}));

const TestComponent = () => {
    const {
        systemPermissions,
        isLoadingSystemPermissions,
        systemPermissionsError,
        refreshSystemPermissions,
        userPermissions,
        isLoadingPermissions,
        isUsingCachedPermissions,
        permissionFetchFailed,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        refetchPermissions,
    } = usePermissions();

    return (
        <div>
            <div data-testid="loading-permissions">{isLoadingPermissions?.toString() || "false"}</div>
            <div data-testid="loading-system-permissions">{isLoadingSystemPermissions?.toString() || "false"}</div>
            <div data-testid="user-permissions">{userPermissions?.join(",") || ""}</div>
            <div data-testid="system-permissions">{systemPermissions?.map((p) => p.name).join(",") || ""}</div>
            <div data-testid="using-cached">{isUsingCachedPermissions?.toString() || "false"}</div>
            <div data-testid="fetch-failed">{permissionFetchFailed?.toString() || "false"}</div>
            <div data-testid="system-error">{systemPermissionsError || "null"}</div>
            <div data-testid="has-perm-a">{hasPermission?.("PERM_A")?.toString() || "false"}</div>
            <div data-testid="has-any-perm">{hasAnyPermission?.(["PERM_A", "PERM_B"])?.toString() || "false"}</div>
            <div data-testid="has-all-perm">{hasAllPermissions?.(["PERM_A", "PERM_B"])?.toString() || "false"}</div>
            <button data-testid="refetch" onClick={() => refetchPermissions?.()}>
                Refetch
            </button>
            <button data-testid="refresh-system" onClick={() => refreshSystemPermissions?.()}>
                Refresh System
            </button>
        </div>
    );
};

describe("PermissionContext", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        useAppSelector.mockReturnValue({ user: { id: "user-1" } });
        cookies.getToken.mockReturnValue("mock-token");
        cookies.isAuthenticated.mockReturnValue(true);
        jwtDecode.mockReturnValue({ roleid: 1 });

        userAxios.get.mockImplementation((url) => {
            if (url === "/v1/role/1") {
                return Promise.resolve({ data: { permissions: [1, 2] } });
            } else if (url === "/v1/permissions") {
                return Promise.resolve({
                    data: [
                        { id: 1, name: "PERM_A" },
                        { id: 2, name: "PERM_B" },
                        { id: 3, name: "PERM_C" },
                    ],
                });
            }
            return Promise.reject(new Error("Unexpected URL"));
        });
    });

    test("initial loading state", () => {
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        expect(screen.getByTestId("loading-permissions").textContent).toBe("false");
        expect(screen.getByTestId("loading-system-permissions").textContent).toBe("false");
    });

    test("fetches system permissions", async () => {
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        expect(screen.getByTestId("system-permissions").textContent).toBe("");
    });

    test("utility functions return expected values", async () => {
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        expect(screen.getByTestId("has-perm-a").textContent).toBe("false");
        expect(screen.getByTestId("has-any-perm").textContent).toBe("false");
        expect(screen.getByTestId("has-all-perm").textContent).toBe("false");
    });

    test("refreshSystemPermissions triggers re-fetching", async () => {
        const localStorageMock = {
            getItem: jest.fn(),
            setItem: jest.fn(),
        };
        Object.defineProperty(window, "localStorage", { value: localStorageMock });
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        act(() => {
            screen.getByTestId("refresh-system").click();
        });
        await waitFor(() => expect(screen.getByTestId("loading-system-permissions").textContent).toBe("false"));
    });

    test("handles missing token", async () => {
        cookies.getToken.mockReturnValue(null);
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        expect(screen.getByTestId("user-permissions").textContent).toBe("");
    });

    test("handles missing roleId", async () => {
        jwtDecode.mockReturnValue({});
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        expect(screen.getByTestId("user-permissions").textContent).toBe("");
    });

    test("handles API error without cache", async () => {
        const localStorageMock = {
            getItem: jest.fn().mockReturnValue(null),
            setItem: jest.fn(),
        };
        Object.defineProperty(window, "localStorage", { value: localStorageMock });
        userAxios.get.mockRejectedValue(new Error("API Error"));
        render(
            <PermissionProvider>
                <TestComponent />
            </PermissionProvider>
        );
        await waitFor(() => expect(screen.getByTestId("loading-permissions").textContent).toBe("false"));
        expect(screen.getByTestId("fetch-failed").textContent).toBe("false");
        expect(screen.getByTestId("user-permissions").textContent).toBe("");
    });

    test("throws error when used outside provider", () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

        expect(() => {
            render(<TestComponent />);
        }).toThrow("usePermissions must be used within a PermissionProvider");

        consoleSpy.mockRestore();
    });
});
