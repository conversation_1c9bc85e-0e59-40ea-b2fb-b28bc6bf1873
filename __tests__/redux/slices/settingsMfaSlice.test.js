import settingsMfaReducer, {
    setCurrentMfaType,
    setCurrentToken,
    clearCurrentVerification,
    resetAllStates,
} from "../../../src/redux/slices/settingsMfaSlice";
import {
    validateSettingsSecurityQuestion,
    validateSettingsAuthenticator,
    validateSettingsSmsOtp,
    clearSettingsMfaState,
} from "../../../src/redux/actions/settingsMfaActions";

describe("settingsMfaSlice", () => {
    const initialState = {
        validateSecurityQuestion: {
            loading: false,
            success: false,
            error: null,
            token: null,
        },
        validateAuthenticator: {
            loading: false,
            success: false,
            error: null,
            token: null,
        },
        validateSmsOtp: {
            loading: false,
            success: false,
            error: null,
            token: null,
        },
        currentVerification: {
            mfaType: null,
            isVerifying: false,
            token: null,
        },
    };

    describe("reducers", () => {
        it("should return the initial state", () => {
            expect(settingsMfaReducer(undefined, { type: "unknown" })).toEqual(initialState);
        });

        it("should handle setCurrentMfaType", () => {
            const actual = settingsMfaReducer(initialState, setCurrentMfaType("SECURITY_QUESTION"));
            expect(actual.currentVerification.mfaType).toBe("SECURITY_QUESTION");
            expect(actual.currentVerification.isVerifying).toBe(true);
            expect(actual.currentVerification.token).toBe(null);
        });

        it("should handle setCurrentMfaType with null", () => {
            const stateWithMfa = {
                ...initialState,
                currentVerification: {
                    mfaType: "SMS",
                    isVerifying: true,
                    token: "some-token",
                },
            };
            const actual = settingsMfaReducer(stateWithMfa, setCurrentMfaType(null));
            expect(actual.currentVerification.mfaType).toBe(null);
            expect(actual.currentVerification.isVerifying).toBe(false);
        });

        it("should handle setCurrentToken", () => {
            const actual = settingsMfaReducer(initialState, setCurrentToken("test-token"));
            expect(actual.currentVerification.token).toBe("test-token");
        });

        it("should handle clearCurrentVerification", () => {
            const stateWithVerification = {
                ...initialState,
                currentVerification: {
                    mfaType: "AUTHENTICATOR",
                    isVerifying: true,
                    token: "test-token",
                },
            };
            const actual = settingsMfaReducer(stateWithVerification, clearCurrentVerification());
            expect(actual.currentVerification).toEqual({
                mfaType: null,
                isVerifying: false,
                token: null,
            });
        });

        it("should handle resetAllStates", () => {
            const modifiedState = {
                validateSecurityQuestion: {
                    loading: true,
                    success: true,
                    error: "some error",
                    token: "some-token",
                },
                validateAuthenticator: {
                    loading: true,
                    success: true,
                    error: "some error",
                    token: "some-token",
                },
                validateSmsOtp: {
                    loading: true,
                    success: true,
                    error: "some error",
                    token: "some-token",
                },
                currentVerification: {
                    mfaType: "SMS",
                    isVerifying: true,
                    token: "some-token",
                },
            };
            const actual = settingsMfaReducer(modifiedState, resetAllStates());
            expect(actual).toEqual(initialState);
        });
    });

    describe("extraReducers - validateSettingsSecurityQuestion", () => {
        it("should handle pending", () => {
            const action = { type: validateSettingsSecurityQuestion.pending.type };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSecurityQuestion.loading).toBe(true);
            expect(state.validateSecurityQuestion.error).toBe(null);
            expect(state.validateSecurityQuestion.success).toBe(false);
        });

        it("should handle fulfilled", () => {
            const action = {
                type: validateSettingsSecurityQuestion.fulfilled.type,
                payload: { token: "test-token", message: "Success" },
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSecurityQuestion.loading).toBe(false);
            expect(state.validateSecurityQuestion.success).toBe(true);
            expect(state.validateSecurityQuestion.error).toBe(null);
            expect(state.validateSecurityQuestion.token).toBe("test-token");
            expect(state.currentVerification.token).toBe("test-token");
        });

        it("should handle rejected", () => {
            const action = {
                type: validateSettingsSecurityQuestion.rejected.type,
                payload: "Validation failed",
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSecurityQuestion.loading).toBe(false);
            expect(state.validateSecurityQuestion.success).toBe(false);
            expect(state.validateSecurityQuestion.error).toBe("Validation failed");
            expect(state.validateSecurityQuestion.token).toBe(null);
        });
    });

    describe("extraReducers - validateSettingsAuthenticator", () => {
        it("should handle pending", () => {
            const action = { type: validateSettingsAuthenticator.pending.type };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateAuthenticator.loading).toBe(true);
            expect(state.validateAuthenticator.error).toBe(null);
            expect(state.validateAuthenticator.success).toBe(false);
        });

        it("should handle fulfilled", () => {
            const action = {
                type: validateSettingsAuthenticator.fulfilled.type,
                payload: { token: "auth-token", message: "Success" },
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateAuthenticator.loading).toBe(false);
            expect(state.validateAuthenticator.success).toBe(true);
            expect(state.validateAuthenticator.error).toBe(null);
            expect(state.validateAuthenticator.token).toBe("auth-token");
            expect(state.currentVerification.token).toBe("auth-token");
        });

        it("should handle rejected", () => {
            const action = {
                type: validateSettingsAuthenticator.rejected.type,
                payload: "Auth failed",
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateAuthenticator.loading).toBe(false);
            expect(state.validateAuthenticator.success).toBe(false);
            expect(state.validateAuthenticator.error).toBe("Auth failed");
            expect(state.validateAuthenticator.token).toBe(null);
        });
    });

    describe("extraReducers - validateSettingsSmsOtp", () => {
        it("should handle pending", () => {
            const action = { type: validateSettingsSmsOtp.pending.type };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSmsOtp.loading).toBe(true);
            expect(state.validateSmsOtp.error).toBe(null);
            expect(state.validateSmsOtp.success).toBe(false);
        });

        it("should handle fulfilled", () => {
            const action = {
                type: validateSettingsSmsOtp.fulfilled.type,
                payload: { token: "sms-token", message: "Success" },
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSmsOtp.loading).toBe(false);
            expect(state.validateSmsOtp.success).toBe(true);
            expect(state.validateSmsOtp.error).toBe(null);
            expect(state.validateSmsOtp.token).toBe("sms-token");
            expect(state.currentVerification.token).toBe("sms-token");
        });

        it("should handle rejected", () => {
            const action = {
                type: validateSettingsSmsOtp.rejected.type,
                payload: "SMS failed",
            };
            const state = settingsMfaReducer(initialState, action);
            expect(state.validateSmsOtp.loading).toBe(false);
            expect(state.validateSmsOtp.success).toBe(false);
            expect(state.validateSmsOtp.error).toBe("SMS failed");
            expect(state.validateSmsOtp.token).toBe(null);
        });
    });

    describe("extraReducers - clearSettingsMfaState", () => {
        it("should clear security question state", () => {
            const modifiedState = {
                ...initialState,
                validateSecurityQuestion: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
            };
            const action = {
                type: clearSettingsMfaState.fulfilled.type,
                meta: { arg: "securityQuestion" },
            };
            const state = settingsMfaReducer(modifiedState, action);
            expect(state.validateSecurityQuestion).toEqual(initialState.validateSecurityQuestion);
        });

        it("should clear authenticator state", () => {
            const modifiedState = {
                ...initialState,
                validateAuthenticator: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
            };
            const action = {
                type: clearSettingsMfaState.fulfilled.type,
                meta: { arg: "authenticator" },
            };
            const state = settingsMfaReducer(modifiedState, action);
            expect(state.validateAuthenticator).toEqual(initialState.validateAuthenticator);
        });

        it("should clear SMS OTP state", () => {
            const modifiedState = {
                ...initialState,
                validateSmsOtp: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
            };
            const action = {
                type: clearSettingsMfaState.fulfilled.type,
                meta: { arg: "smsOtp" },
            };
            const state = settingsMfaReducer(modifiedState, action);
            expect(state.validateSmsOtp).toEqual(initialState.validateSmsOtp);
        });

        it("should clear all states", () => {
            const modifiedState = {
                validateSecurityQuestion: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
                validateAuthenticator: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
                validateSmsOtp: {
                    loading: true,
                    success: true,
                    error: "error",
                    token: "token",
                },
                currentVerification: {
                    mfaType: "SMS",
                    isVerifying: true,
                    token: "token",
                },
            };
            const action = {
                type: clearSettingsMfaState.fulfilled.type,
                meta: { arg: "all" },
            };
            const state = settingsMfaReducer(modifiedState, action);
            expect(state).toEqual(initialState);
        });
    });
});
