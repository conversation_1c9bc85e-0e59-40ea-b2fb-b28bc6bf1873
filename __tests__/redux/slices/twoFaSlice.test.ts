import twoFaReducer, { clearUpdateMFAState, clearGetTeamMemberState } from "@/redux/slices/twoFaSlice";
import { getTestTeamMember, updatePreferredMFAMethod } from "@/redux/actions/settingsTwoFa";

describe("twoFaSlice", () => {
    // Initial state for tests
    const initialState = {
        preferredMethod: null,
        enabledMethods: {
            AUTHENTICATOR: false,
            SMS: false,
            SECURITY_QUESTION: false,
        },
        teamMember: null,
        updateMFAMethod: {
            loading: false,
            error: null,
            success: false,
        },
        getTeamMember: {
            loading: false,
            error: null,
            success: false,
        },
    };

    it("should return the initial state", () => {
        expect(twoFaReducer(undefined, { type: "unknown" })).toEqual(initialState);
    });

    it("should handle clearUpdateMFAState", () => {
        const modifiedState = {
            ...initialState,
            updateMFAMethod: {
                loading: true,
                error: "Some error",
                success: true,
            },
        };

        expect(twoFaReducer(modifiedState, clearUpdateMFAState())).toEqual({
            ...modifiedState,
            updateMFAMethod: {
                loading: false,
                error: null,
                success: false,
            },
        });
    });

    it("should handle clearGetTeamMemberState", () => {
        const modifiedState = {
            ...initialState,
            getTeamMember: {
                loading: true,
                error: "Some error",
                success: true,
            },
        };

        expect(twoFaReducer(modifiedState, clearGetTeamMemberState())).toEqual({
            ...modifiedState,
            getTeamMember: {
                loading: false,
                error: null,
                success: false,
            },
        });
    });

    it("should handle getTestTeamMember.pending", () => {
        const action = { type: getTestTeamMember.pending.type };
        const state = twoFaReducer(initialState, action);

        expect(state.getTeamMember.loading).toBe(true);
        expect(state.getTeamMember.error).toBe(null);
        expect(state.getTeamMember.success).toBe(false);
    });

    it("should handle getTestTeamMember.fulfilled", () => {
        const mockTeamMember = {
            id: 12,
            corporateId: 1,
            firstName: "ff",
            lastName: "ff",
            email: "<EMAIL>",
            roleId: 1,
            preferredMfaMethod: "AUTHENTICATOR",
            mfaStatus: true,
        };

        const action = {
            type: getTestTeamMember.fulfilled.type,
            payload: mockTeamMember,
        };

        const state = twoFaReducer(initialState, action);

        expect(state.getTeamMember.loading).toBe(false);
        expect(state.getTeamMember.success).toBe(true);
        expect(state.teamMember).toEqual(mockTeamMember);
        expect(state.preferredMethod).toBe("AUTHENTICATOR");
        expect(state.enabledMethods.AUTHENTICATOR).toBe(true);
        expect(state.enabledMethods.SMS).toBe(false);
        expect(state.enabledMethods.SECURITY_QUESTION).toBe(false);
    });

    it("should handle getTestTeamMember.rejected", () => {
        const error = "Failed to fetch team member";
        const action = {
            type: getTestTeamMember.rejected.type,
            payload: error,
        };

        const state = twoFaReducer(initialState, action);

        expect(state.getTeamMember.loading).toBe(false);
        expect(state.getTeamMember.error).toBe(error);
        expect(state.getTeamMember.success).toBe(false);
    });

    it("should handle updatePreferredMFAMethod.pending", () => {
        const action = { type: updatePreferredMFAMethod.pending.type };
        const state = twoFaReducer(initialState, action);

        expect(state.updateMFAMethod.loading).toBe(true);
        expect(state.updateMFAMethod.error).toBe(null);
        expect(state.updateMFAMethod.success).toBe(false);
    });

    it("should handle updatePreferredMFAMethod.fulfilled with response data", () => {
        const mockResponse = {
            id: 12,
            corporateId: 1,
            firstName: "ff",
            lastName: "ff",
            email: "<EMAIL>",
            roleId: 1,
            preferredMfaMethod: "AUTHENTICATOR",
            mfaStatus: true,
        };

        const action = {
            type: updatePreferredMFAMethod.fulfilled.type,
            payload: mockResponse,
            meta: {
                arg: {
                    mfaMethod: "AUTHENTICATOR",
                    email: "<EMAIL>",
                },
            },
        };

        const state = twoFaReducer(initialState, action);

        expect(state.updateMFAMethod.loading).toBe(false);
        expect(state.updateMFAMethod.success).toBe(true);
        expect(state.preferredMethod).toBe("AUTHENTICATOR");
        expect(state.enabledMethods.AUTHENTICATOR).toBe(true);
        expect(state.enabledMethods.SMS).toBe(false);
        expect(state.enabledMethods.SECURITY_QUESTION).toBe(false);
    });

    it("should handle updatePreferredMFAMethod.fulfilled without response data", () => {
        const initialStateWithMethod = {
            ...initialState,
            enabledMethods: {
                AUTHENTICATOR: false,
                SMS: false,
                SECURITY_QUESTION: false,
            },
        };

        const action = {
            type: updatePreferredMFAMethod.fulfilled.type,
            payload: null,
            meta: {
                arg: {
                    mfaMethod: "AUTHENTICATOR",
                    email: "<EMAIL>",
                },
            },
        };

        const state = twoFaReducer(initialStateWithMethod, action);

        expect(state.updateMFAMethod.loading).toBe(false);
        expect(state.updateMFAMethod.success).toBe(true);
        expect(state.enabledMethods.AUTHENTICATOR).toBe(true);
        expect(state.preferredMethod).toBe("AUTHENTICATOR");
    });

    it("should handle updatePreferredMFAMethod.rejected", () => {
        const error = "Failed to update MFA method";
        const action = {
            type: updatePreferredMFAMethod.rejected.type,
            payload: error,
        };

        const state = twoFaReducer(initialState, action);

        expect(state.updateMFAMethod.loading).toBe(false);
        expect(state.updateMFAMethod.error).toBe(error);
        expect(state.updateMFAMethod.success).toBe(false);
    });
});
