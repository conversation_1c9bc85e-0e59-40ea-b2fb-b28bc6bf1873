import { configureStore } from "@reduxjs/toolkit";
import type { AppDispatch } from "../../../src/redux";
import accountsReducer, {
    fetchAccounts,
    setSelectedAccount,
    resetAccounts,
    type Account,
    formatAccountName,
    formatAccountNumber,
    getAccountBalance,
} from "../../../src/redux/features/accounts";
import { acctsAxios } from "../../../src/api/axios";

// Mock the axios instance
jest.mock("../../../src/api/axios", () => ({
    acctsAxios: {
        get: jest.fn(),
    },
}));

// Mock the feedback function
jest.mock("../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

describe("accounts slice", () => {
    const createTestStore = () =>
        configureStore({
            reducer: {
                accounts: accountsReducer,
            },
            middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
        });

    let store: ReturnType<typeof createTestStore>;

    beforeEach(() => {
        store = createTestStore();
        jest.clearAllMocks();
    });

    // Helper to get typed state
    const getAccountsState = () => (store.getState() as { accounts: ReturnType<typeof accountsReducer> }).accounts;

    describe("fetchAccounts", () => {
        const mockAccounts: Account[] = [
            {
                id: 1,
                accountNumber: "**********",
                accountName: "John Doe",
                preferredName: "John",
                currencyCode: "NGN",
                primaryFlag: true,
                hiddenFlag: false,
                status: "ACTIVE",
                schemeType: "SAVINGS",
                schemeCode: "SV",
            },
            {
                id: 2,
                accountNumber: "**********",
                accountName: "Jane Doe",
                preferredName: "Jane",
                currencyCode: "NGN",
                primaryFlag: false,
                hiddenFlag: false,
                status: "ACTIVE",
                schemeType: "CURRENT",
                schemeCode: "CA",
            },
        ];

        it("should fetch accounts successfully", async () => {
            // Mock successful accounts fetch
            (acctsAxios.get as jest.Mock).mockResolvedValueOnce({ data: mockAccounts });

            // Dispatch fetchAccounts
            await (store.dispatch as AppDispatch)(fetchAccounts());

            // Check that accounts were fetched
            const state = getAccountsState();
            expect(state.accounts).toEqual(mockAccounts);
            expect(state.loading).toBe(false);
            expect(state.error).toBe(null);

            // Verify API call was made
            expect(acctsAxios.get).toHaveBeenCalledTimes(1);
            expect(acctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts");
        });

        it("should set selectedAccount to formatted first account when accounts are fetched", async () => {
            (acctsAxios.get as jest.Mock).mockResolvedValueOnce({ data: mockAccounts });

            await (store.dispatch as AppDispatch)(fetchAccounts());

            const state = getAccountsState();
            const firstAccount = mockAccounts[0];
            const expectedSelectedAccount = `${formatAccountName(firstAccount)} ${formatAccountNumber(firstAccount.accountNumber)}`;

            expect(state.selectedAccount).toBe(expectedSelectedAccount);
        });

        it("should handle failed accounts fetch", async () => {
            // Mock failed accounts fetch
            (acctsAxios.get as jest.Mock).mockRejectedValueOnce(new Error("Failed to fetch accounts"));

            // Dispatch fetchAccounts
            await (store.dispatch as AppDispatch)(fetchAccounts());

            // Check error state
            const state = getAccountsState();
            expect(state.accounts).toEqual([]);
            expect(state.loading).toBe(false);
            expect(state.error).toBe("Failed to fetch accounts");
        });

        it("should handle empty accounts array", async () => {
            // Mock successful accounts fetch with empty array
            (acctsAxios.get as jest.Mock).mockResolvedValueOnce({ data: [] });

            // Dispatch fetchAccounts
            await (store.dispatch as AppDispatch)(fetchAccounts());

            // Check state
            const state = getAccountsState();
            expect(state.accounts).toEqual([]);
            expect(state.loading).toBe(false);
            expect(state.error).toBe(null);
            expect(state.selectedAccount).toBe("");
        });

        it("should handle invalid response format", async () => {
            // Mock response with invalid format
            (acctsAxios.get as jest.Mock).mockResolvedValueOnce({ data: null });

            // Dispatch fetchAccounts
            await (store.dispatch as AppDispatch)(fetchAccounts());

            // Check error state
            const state = getAccountsState();
            expect(state.accounts).toEqual([]);
            expect(state.loading).toBe(false);
            expect(state.error).toBe("Invalid response format from accounts API");
        });
    });

    describe("utility functions", () => {
        const mockAccounts: Account[] = [
            {
                id: 1,
                accountNumber: "**********",
                accountName: "John Doe",
                preferredName: "John",
                currencyCode: "NGN",
                primaryFlag: true,
                hiddenFlag: false,
                status: "ACTIVE",
                schemeType: "SAVINGS",
                schemeCode: "SV",
            },
        ];

        describe("formatAccountName", () => {
            it("should return accountName when available", () => {
                const account = mockAccounts[0];
                expect(formatAccountName(account)).toBe("John Doe");
            });

            it("should return formatted scheme type when accountName is not available", () => {
                const account = { ...mockAccounts[0], accountName: "" };
                expect(formatAccountName(account)).toBe("Savings account");
            });
        });

        describe("formatAccountNumber", () => {
            it("should mask account number with last 4 digits", () => {
                expect(formatAccountNumber("**********")).toBe("****7890");
            });

            it("should handle short account numbers", () => {
                expect(formatAccountNumber("123")).toBe("****123");
            });
        });

        describe("getAccountBalance", () => {
            it("should return balance as string when balance is provided and truthy", () => {
                expect(getAccountBalance(1000.5)).toBe("1000.5");
                expect(getAccountBalance(500)).toBe("500");
                expect(getAccountBalance(1)).toBe("1");
            });

            it("should return empty string when balance is 0", () => {
                expect(getAccountBalance(0)).toBe("");
            });

            it("should return empty string when balance is undefined", () => {
                expect(getAccountBalance(undefined)).toBe("");
            });

            it("should return empty string when balance is not provided", () => {
                expect(getAccountBalance()).toBe("");
            });
        });
    });

    describe("actions", () => {
        describe("setSelectedAccount", () => {
            it("should set the selected account", () => {
                const testAccount = "Test Account ****1234";
                store.dispatch(setSelectedAccount(testAccount));

                const state = getAccountsState();
                expect(state.selectedAccount).toBe(testAccount);
            });
        });

        describe("resetAccounts", () => {
            it("should reset selectedAccount to initial state", () => {
                // Set up some state
                store.dispatch(setSelectedAccount("Test Account ****1234"));

                // Reset
                store.dispatch(resetAccounts());

                const state = getAccountsState();
                expect(state.selectedAccount).toBe("");
            });
        });
    });
});
