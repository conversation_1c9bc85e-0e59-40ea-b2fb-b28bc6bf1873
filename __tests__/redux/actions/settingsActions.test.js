import { configureStore } from "@reduxjs/toolkit";
import { changePasswordWithToken } from "../../../src/redux/actions/settingsActions";
import settingsReducer from "../../../src/redux/slices/settingsSlice";
import { userAxios } from "../../../src/api/axios";

// Mock the axios instance
jest.mock("../../../src/api/axios", () => ({
    userAxios: {
        post: jest.fn(),
    },
}));

// Mock feedback functions
jest.mock("../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock session details
jest.mock("../../../src/functions/userSession", () => ({
    getSessionDetails: jest.fn(() => ({
        email: "<EMAIL>",
    })),
}));

describe("Settings Actions - changePasswordWithToken", () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settings: settingsReducer,
                user: () => ({
                    user: { email: "<EMAIL>" },
                }),
                signin: () => ({
                    user: { email: "<EMAIL>" },
                }),
            },
        });
        jest.clearAllMocks();
    });

    describe("changePasswordWithToken", () => {
        const mockRequest = {
            newPassword: "NewPassword123!",
            oldPassword: "OldPassword123!",
            token: "test-mfa-token",
        };

        it("should handle successful password change with token", async () => {
            const mockResponse = {
                data: {
                    status: "success",
                    message: "Password changed successfully",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(changePasswordWithToken(mockRequest));

            expect(result.type).toBe("settings/changePasswordWithToken/fulfilled");
            expect(result.payload).toEqual(mockResponse.data);

            // Verify the URL includes the token as query parameter
            expect(userAxios.post).toHaveBeenCalledWith(
                `/v1/credentials?command=change-password&token=${encodeURIComponent(mockRequest.token)}`,
                {
                    email: "<EMAIL>",
                    newPassword: mockRequest.newPassword,
                    oldPassword: mockRequest.oldPassword,
                }
            );

            const state = store.getState().settings;
            expect(state.changePassword.loading).toBe(false);
            expect(state.changePassword.success).toBe(true);
            expect(state.changePassword.error).toBe(null);
        });

        it("should handle failed password change with token", async () => {
            const mockError = new Error("Invalid token");
            userAxios.post.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(changePasswordWithToken(mockRequest));

            expect(result.type).toBe("settings/changePasswordWithToken/rejected");
            expect(result.payload).toBe("Invalid token");

            const state = store.getState().settings;
            expect(state.changePassword.loading).toBe(false);
            expect(state.changePassword.success).toBe(false);
            expect(state.changePassword.error).toBe("Invalid token");
        });

        it("should use email from user slice", async () => {
            const mockResponse = {
                data: { status: "success", message: "Password changed" },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changePasswordWithToken(mockRequest));

            expect(userAxios.post).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    email: "<EMAIL>",
                })
            );
        });

        it("should fallback to signin email when user email is not available", async () => {
            // Create store without user email
            const storeWithoutUserEmail = configureStore({
                reducer: {
                    settings: settingsReducer,
                    user: () => ({ user: null }),
                    signin: () => ({
                        user: { email: "<EMAIL>" },
                    }),
                },
            });

            const mockResponse = {
                data: { status: "success", message: "Password changed" },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await storeWithoutUserEmail.dispatch(changePasswordWithToken(mockRequest));

            expect(userAxios.post).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    email: "<EMAIL>",
                })
            );
        });

        it("should fallback to settings email when user and signin emails are not available", async () => {
            // Create store without user and signin emails
            const storeWithSettingsEmail = configureStore({
                reducer: {
                    settings: settingsReducer,
                    user: () => ({ user: null }),
                    signin: () => ({ user: null }),
                },
                preloadedState: {
                    settings: {
                        personalInfo: {
                            data: { email: "<EMAIL>" },
                            loading: false,
                            error: null,
                        },
                        businessInfo: {
                            data: null,
                            loading: false,
                            error: null,
                        },
                        changePassword: {
                            loading: false,
                            success: false,
                            error: null,
                        },
                        uploadBusinessLogo: {
                            loading: false,
                            success: false,
                            error: null,
                        },
                        deleteBusinessLogo: {
                            loading: false,
                            success: false,
                            error: null,
                        },
                    },
                },
            });

            const mockResponse = {
                data: { status: "success", message: "Password changed" },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await storeWithSettingsEmail.dispatch(changePasswordWithToken(mockRequest));

            expect(userAxios.post).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    email: "<EMAIL>",
                })
            );
        });

        it("should handle missing email error", async () => {
            // Mock getSessionDetails to return null
            const { getSessionDetails } = require("../../../src/functions/userSession");
            getSessionDetails.mockReturnValueOnce(null);

            // Create store without any email
            const storeWithoutEmail = configureStore({
                reducer: {
                    settings: settingsReducer,
                    user: () => ({ user: null }),
                    signin: () => ({ user: null }),
                },
            });

            const result = await storeWithoutEmail.dispatch(changePasswordWithToken(mockRequest));

            expect(result.type).toBe("settings/changePasswordWithToken/rejected");
            expect(result.payload).toBe("User email not found. Please log in again.");
        });

        it("should properly encode special characters in token", async () => {
            const requestWithSpecialToken = {
                ...mockRequest,
                token: "token+with/special=chars&more",
            };

            const mockResponse = {
                data: { status: "success", message: "Password changed" },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changePasswordWithToken(requestWithSpecialToken));

            expect(userAxios.post).toHaveBeenCalledWith(
                `/v1/credentials?command=change-password&token=${encodeURIComponent(requestWithSpecialToken.token)}`,
                expect.any(Object)
            );
        });

        it("should handle network errors gracefully", async () => {
            const networkError = {
                message: "Network Error",
                response: {
                    data: {
                        message: "Server is unavailable",
                    },
                },
            };
            userAxios.post.mockRejectedValueOnce(networkError);

            const result = await store.dispatch(changePasswordWithToken(mockRequest));

            expect(result.type).toBe("settings/changePasswordWithToken/rejected");
            expect(result.payload).toBe("Network Error");
        });

        it("should use custom message from API response", async () => {
            const mockResponse = {
                data: {
                    status: "success",
                    message: "Custom success message from API",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changePasswordWithToken(mockRequest));

            // Verify that sendFeedback was called with the custom message
            const { sendFeedback } = require("../../../src/functions/feedback");
            expect(sendFeedback).toHaveBeenCalledWith(
                "Custom success message from API",
                "success",
                undefined,
                "Success"
            );
        });

        it("should use default message when API response has no message", async () => {
            const mockResponse = {
                data: {
                    status: "success",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changePasswordWithToken(mockRequest));

            // Verify that sendFeedback was called with the default message
            const { sendFeedback } = require("../../../src/functions/feedback");
            expect(sendFeedback).toHaveBeenCalledWith("Password changed successfully", "success", undefined, "Success");
        });
    });
});
