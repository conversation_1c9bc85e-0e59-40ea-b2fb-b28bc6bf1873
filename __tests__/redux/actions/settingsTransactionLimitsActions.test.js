import { configureStore } from "@reduxjs/toolkit";
import {
    updateTransactionLimitWithToken,
    fetchTransactionLimits,
    updateTransactionLimit,
} from "../../../src/redux/actions/settingsTransactionLimitsActions";
import transactionLimitsReducer from "../../../src/redux/slices/transactionLimitsSlice";
import { userAxios } from "../../../src/api/axios";

// Mock the axios instance
jest.mock("../../../src/api/axios", () => ({
    userAxios: {
        get: jest.fn(),
        put: jest.fn(),
    },
}));

// Mock feedback functions
jest.mock("../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

describe("Settings Transaction Limits Actions", () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                transactionLimits: transactionLimitsReducer,
                corporate: () => ({ corporateId: "test-corp-id" }),
            },
        });
        jest.clearAllMocks();
    });

    describe("updateTransactionLimitWithToken", () => {
        const mockData = {
            limitType: "FCMB_TRANSFER",
            daily: 1000000,
            perTransaction: 500000,
            maximum: 5000000,
        };
        const mockToken = "test-mfa-token";

        it("should handle successful transaction limit update with token", async () => {
            const mockResponse = { data: { message: "Limit updated successfully" } };
            userAxios.put.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: mockToken,
                })
            );

            expect(result.type).toBe("transactionLimits/updateWithToken/fulfilled");
            expect(result.payload).toEqual(mockData);

            // Verify the URL includes the token as query parameter
            expect(userAxios.put).toHaveBeenCalledWith(
                `/v1/transaction-limits?token=${encodeURIComponent(mockToken)}`,
                {
                    type: mockData.limitType,
                    dailyLimit: mockData.daily,
                    perTransactionLimit: mockData.perTransaction,
                    maximumLimit: mockData.maximum,
                }
            );

            const state = store.getState().transactionLimits;
            expect(state.updateLimit.loading).toBe(false);
            expect(state.updateLimit.success).toBe(true);
            expect(state.updateLimit.error).toBe(null);
        });

        it("should handle transaction limit update without token", async () => {
            const mockResponse = { data: { message: "Limit updated successfully" } };
            userAxios.put.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: "",
                })
            );

            expect(result.type).toBe("transactionLimits/updateWithToken/fulfilled");

            // Verify the URL doesn't include token when empty
            expect(userAxios.put).toHaveBeenCalledWith("/v1/transaction-limits", {
                type: mockData.limitType,
                dailyLimit: mockData.daily,
                perTransactionLimit: mockData.perTransaction,
                maximumLimit: mockData.maximum,
            });
        });

        it("should handle failed transaction limit update with token", async () => {
            const mockError = new Error("Update failed");
            userAxios.put.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: mockToken,
                })
            );

            expect(result.type).toBe("transactionLimits/updateWithToken/rejected");
            expect(result.payload).toBe("Failed to update transaction limit");

            const state = store.getState().transactionLimits;
            expect(state.updateLimit.loading).toBe(false);
            expect(state.updateLimit.success).toBe(false);
            expect(state.updateLimit.error).toBe("Failed to update transaction limit");
        });

        it("should update state correctly when limit exists", async () => {
            // Set up initial state with existing limit
            const existingLimit = {
                id: 1,
                type: "FCMB_TRANSFER",
                typeName: "FCMB Transfer",
                dailyLimit: 500000,
                perTransactionLimit: 250000,
                maximumLimit: 2500000,
                lastUpdated: "2023-01-01T00:00:00Z",
            };

            // Manually set the initial state
            store.dispatch({
                type: "transactionLimits/setInitialLimits",
                payload: [existingLimit],
            });

            const mockResponse = { data: { message: "Limit updated successfully" } };
            userAxios.put.mockResolvedValueOnce(mockResponse);

            await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: mockToken,
                })
            );

            const state = store.getState().transactionLimits;
            const updatedLimit = state.limits.data.find((limit) => limit.type === "FCMB_TRANSFER");

            if (updatedLimit) {
                expect(updatedLimit.dailyLimit).toBe(mockData.daily);
                expect(updatedLimit.perTransactionLimit).toBe(mockData.perTransaction);
                expect(updatedLimit.maximumLimit).toBe(mockData.maximum);
                expect(updatedLimit.lastUpdated).toBeDefined();
            }
        });
    });

    describe("Token URL encoding", () => {
        it("should properly encode special characters in token", async () => {
            const mockData = {
                limitType: "FCMB_TRANSFER",
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };
            const specialToken = "token+with/special=chars&more";
            const mockResponse = { data: { message: "Success" } };
            userAxios.put.mockResolvedValueOnce(mockResponse);

            await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: specialToken,
                })
            );

            expect(userAxios.put).toHaveBeenCalledWith(
                `/v1/transaction-limits?token=${encodeURIComponent(specialToken)}`,
                expect.any(Object)
            );
        });
    });

    describe("Error handling", () => {
        it("should handle network errors gracefully", async () => {
            const mockData = {
                limitType: "FCMB_TRANSFER",
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };
            const networkError = {
                message: "Network Error",
                response: {
                    data: {
                        message: "Server is unavailable",
                    },
                },
            };
            userAxios.put.mockRejectedValueOnce(networkError);

            const result = await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: "test-token",
                })
            );

            expect(result.type).toBe("transactionLimits/updateWithToken/rejected");
            expect(result.payload).toBe("Server is unavailable");
        });

        it("should handle errors without response data", async () => {
            const mockData = {
                limitType: "FCMB_TRANSFER",
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };
            const genericError = {
                message: "Something went wrong",
            };
            userAxios.put.mockRejectedValueOnce(genericError);

            const result = await store.dispatch(
                updateTransactionLimitWithToken({
                    data: mockData,
                    token: "test-token",
                })
            );

            expect(result.type).toBe("transactionLimits/updateWithToken/rejected");
            expect(result.payload).toBe("Failed to update transaction limit");
        });
    });
});
