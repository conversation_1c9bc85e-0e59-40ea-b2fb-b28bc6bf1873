import { configureStore } from "@reduxjs/toolkit";
import { changeTransactionPinWithToken } from "../../../src/redux/actions/securityActions";
import securityReducer from "../../../src/redux/slices/securitySlice";
import { transferAxios } from "../../../src/api/axios";

// Mock the axios instance
jest.mock("../../../src/api/axios", () => ({
    transferAxios: {
        post: jest.fn(),
    },
}));

// Mock feedback functions
jest.mock("../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

describe("Security Actions - changeTransactionPinWithToken", () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                security: securityReducer,
            },
        });
        jest.clearAllMocks();
    });

    describe("changeTransactionPinWithToken", () => {
        const mockRequest = {
            oldPin: "123456",
            newPin: "654321",
            token: "test-mfa-token",
        };

        it("should handle successful PIN change with token", async () => {
            const mockResponse = {
                data: {
                    status: "success",
                    message: "PIN changed successfully",
                },
            };
            transferAxios.post.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(changeTransactionPinWithToken(mockRequest));

            expect(result.type).toBe("security/changeTransactionPinWithToken/fulfilled");
            expect(result.payload).toEqual(mockResponse.data);

            // Verify the URL includes the token as query parameter
            expect(transferAxios.post).toHaveBeenCalledWith(
                `/transaction-pin/change?token=${encodeURIComponent(mockRequest.token)}`,
                {
                    oldPin: mockRequest.oldPin,
                    newPin: mockRequest.newPin,
                }
            );

            const state = store.getState().security;
            expect(state.changePin.loading).toBe(false);
            expect(state.changePin.success).toBe(true);
            expect(state.changePin.error).toBe(null);
        });

        it("should handle failed PIN change with token", async () => {
            const mockError = new Error("Invalid PIN");
            transferAxios.post.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(changeTransactionPinWithToken(mockRequest));

            expect(result.type).toBe("security/changeTransactionPinWithToken/rejected");
            expect(result.payload).toEqual({
                message: "Invalid PIN",
            });

            const state = store.getState().security;
            expect(state.changePin.loading).toBe(false);
            expect(state.changePin.success).toBe(false);
            expect(state.changePin.error).toBeDefined();
        });

        it("should properly encode special characters in token", async () => {
            const requestWithSpecialToken = {
                ...mockRequest,
                token: "token+with/special=chars&more",
            };

            const mockResponse = {
                data: { status: "success", message: "PIN changed" },
            };
            transferAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changeTransactionPinWithToken(requestWithSpecialToken));

            expect(transferAxios.post).toHaveBeenCalledWith(
                `/transaction-pin/change?token=${encodeURIComponent(requestWithSpecialToken.token)}`,
                {
                    oldPin: requestWithSpecialToken.oldPin,
                    newPin: requestWithSpecialToken.newPin,
                }
            );
        });

        it("should call sendFeedback on successful PIN change", async () => {
            const mockResponse = {
                data: { status: "success", message: "PIN changed" },
            };
            transferAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changeTransactionPinWithToken(mockRequest));

            const { sendFeedback } = require("../../../src/functions/feedback");
            expect(sendFeedback).toHaveBeenCalledWith("Transaction PIN changed successfully", "success");
        });

        it("should call sendCatchFeedback on error", async () => {
            const mockError = new Error("PIN change failed");
            transferAxios.post.mockRejectedValueOnce(mockError);

            await store.dispatch(changeTransactionPinWithToken(mockRequest));

            const { sendCatchFeedback } = require("../../../src/functions/feedback");
            expect(sendCatchFeedback).toHaveBeenCalledWith(mockError);
        });

        it("should handle network errors gracefully", async () => {
            const networkError = {
                message: "Network Error",
                response: {
                    status: 500,
                    data: {
                        message: "Internal Server Error",
                    },
                },
            };
            transferAxios.post.mockRejectedValueOnce(networkError);

            const result = await store.dispatch(changeTransactionPinWithToken(mockRequest));

            expect(result.type).toBe("security/changeTransactionPinWithToken/rejected");
            expect(result.payload).toEqual({
                message: "Network Error",
            });
        });

        it("should handle errors without message", async () => {
            const errorWithoutMessage = {};
            transferAxios.post.mockRejectedValueOnce(errorWithoutMessage);

            const result = await store.dispatch(changeTransactionPinWithToken(mockRequest));

            expect(result.type).toBe("security/changeTransactionPinWithToken/rejected");
            expect(result.payload).toEqual({
                message: "An error occurred",
            });
        });

        it("should handle pending state correctly", () => {
            const action = { type: changeTransactionPinWithToken.pending.type };
            const state = securityReducer(undefined, action);

            expect(state.changePin.loading).toBe(true);
            expect(state.changePin.error).toBe(null);
            expect(state.changePin.success).toBe(false);
        });

        it("should handle fulfilled state correctly", () => {
            const action = {
                type: changeTransactionPinWithToken.fulfilled.type,
                payload: { status: "success" },
            };
            const state = securityReducer(undefined, action);

            expect(state.changePin.loading).toBe(false);
            expect(state.changePin.success).toBe(true);
            expect(state.changePin.error).toBe(null);
        });

        it("should handle rejected state correctly", () => {
            const action = {
                type: changeTransactionPinWithToken.rejected.type,
                payload: { message: "Error occurred" },
            };
            const state = securityReducer(undefined, action);

            expect(state.changePin.loading).toBe(false);
            expect(state.changePin.success).toBe(false);
            expect(state.changePin.error).toBeDefined();
        });
    });

    describe("Token validation", () => {
        it("should handle empty token", async () => {
            const requestWithEmptyToken = {
                oldPin: "123456",
                newPin: "654321",
                token: "",
            };

            const mockResponse = {
                data: { status: "success" },
            };
            transferAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changeTransactionPinWithToken(requestWithEmptyToken));

            expect(transferAxios.post).toHaveBeenCalledWith("/transaction-pin/change?token=", {
                oldPin: requestWithEmptyToken.oldPin,
                newPin: requestWithEmptyToken.newPin,
            });
        });

        it("should handle very long token", async () => {
            const longToken = "a".repeat(1000);
            const requestWithLongToken = {
                oldPin: "123456",
                newPin: "654321",
                token: longToken,
            };

            const mockResponse = {
                data: { status: "success" },
            };
            transferAxios.post.mockResolvedValueOnce(mockResponse);

            await store.dispatch(changeTransactionPinWithToken(requestWithLongToken));

            expect(transferAxios.post).toHaveBeenCalledWith(
                `/transaction-pin/change?token=${encodeURIComponent(longToken)}`,
                {
                    oldPin: requestWithLongToken.oldPin,
                    newPin: requestWithLongToken.newPin,
                }
            );
        });
    });
});
