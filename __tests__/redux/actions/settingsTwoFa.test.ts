import { getTeamMemberByEmail, getTestTeamMember, updatePreferredMFAMethod } from "@/redux/actions/settingsTwoFa";
import { userAxios } from "@/api/axios";
import { sendFeedback, sendCatchFeedback } from "@/functions/feedback";

// Mock dependencies
jest.mock("@/api/axios", () => ({
    userAxios: {
        get: jest.fn(),
        patch: jest.fn(),
    },
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

describe("settingsTwoFa actions", () => {
    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("getTeamMemberByEmail", () => {
        it("should fetch team member data successfully", async () => {
            // Mock response data
            const mockResponse = {
                data: {
                    id: 12,
                    corporateId: 1,
                    firstName: "<PERSON>",
                    lastName: "Doe",
                    email: "<EMAIL>",
                    roleId: 1,
                    preferredMfaMethod: "AUTHENTICATOR",
                    mfaStatus: true,
                },
            };

            // Mock axios get to return the response
            (userAxios.get as jest.Mock).mockResolvedValue(mockResponse);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = getTeamMemberByEmail();
            await thunk(dispatch, getState, undefined);

            // Check if axios.get was called with the correct URL
            expect(userAxios.get).toHaveBeenCalledWith("/v1/team-members");

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/getTeamMemberByEmail/pending");
            expect(firstCall.meta.arg).toBeUndefined(); // No argument passed

            // Check second call (fulfilled action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/getTeamMemberByEmail/fulfilled");
            expect(secondCall.payload).toEqual(mockResponse.data);
            expect(secondCall.meta.arg).toBeUndefined(); // No argument passed
        });

        it("should handle errors when fetching team member data", async () => {
            // Mock error
            const error = new Error("Network error");
            (userAxios.get as jest.Mock).mockRejectedValue(error);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = getTeamMemberByEmail();
            await thunk(dispatch, getState, undefined);

            // Check if axios.get was called with the correct URL
            expect(userAxios.get).toHaveBeenCalledWith("/v1/team-members");

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/getTeamMemberByEmail/pending");
            expect(firstCall.meta.arg).toBeUndefined(); // No argument passed

            // Check second call (rejected action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/getTeamMemberByEmail/rejected");
            expect(secondCall.payload).toEqual(error);
            expect(secondCall.meta.arg).toBeUndefined(); // No argument passed
            expect(secondCall.meta.rejectedWithValue).toBe(true);
        });
    });

    describe("getTestTeamMember", () => {
        it("should fetch test team member data successfully", async () => {
            // Mock response data
            const mockResponse = {
                data: {
                    id: 12,
                    corporateId: 1,
                    firstName: "ff",
                    lastName: "ff",
                    email: "<EMAIL>",
                    roleId: 1,
                    preferredMfaMethod: "AUTHENTICATOR",
                    mfaStatus: true,
                },
            };

            // Mock axios get to return the response
            (userAxios.get as jest.Mock).mockResolvedValue(mockResponse);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = getTestTeamMember();
            await thunk(dispatch, getState, undefined);

            // Check if axios.get was called with the correct URL
            expect(userAxios.get).toHaveBeenCalledWith("/v1/team-members?emailAddress=<EMAIL>");

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/getTestTeamMember/pending");

            // Check second call (fulfilled action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/getTestTeamMember/fulfilled");
            expect(secondCall.payload).toEqual(mockResponse.data);
        });

        it("should handle errors when fetching test team member data", async () => {
            // Mock error
            const error = new Error("Network error");
            (userAxios.get as jest.Mock).mockRejectedValue(error);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = getTestTeamMember();
            await thunk(dispatch, getState, undefined);

            // Check if axios.get was called with the correct URL
            expect(userAxios.get).toHaveBeenCalledWith("/v1/team-members?emailAddress=<EMAIL>");

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/getTestTeamMember/pending");

            // Check second call (rejected action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/getTestTeamMember/rejected");
            expect(secondCall.payload).toEqual(error);
            expect(secondCall.meta.rejectedWithValue).toBe(true);
        });
    });

    describe("updatePreferredMFAMethod", () => {
        it("should update MFA method successfully", async () => {
            // Mock response data
            const mockResponse = {
                data: {
                    id: 12,
                    corporateId: 1,
                    firstName: "ff",
                    lastName: "ff",
                    email: "<EMAIL>",
                    roleId: 1,
                    preferredMfaMethod: "AUTHENTICATOR",
                    mfaStatus: true,
                },
            };

            // Mock axios patch to return the response
            (userAxios.patch as jest.Mock).mockResolvedValue(mockResponse);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = updatePreferredMFAMethod({
                mfaMethod: "AUTHENTICATOR",
            });
            await thunk(dispatch, getState, undefined);

            // Check if axios.patch was called with the correct URL
            expect(userAxios.patch).toHaveBeenCalledWith("/v1/authenticator-app?mfaMethod=AUTHENTICATOR");

            // Check if sendFeedback was called with the success message
            expect(sendFeedback).toHaveBeenCalledWith(
                "AUTHENTICATOR 2FA authentication updated successfully",
                "success"
            );

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/updatePreferredMFAMethod/pending");
            expect(firstCall.meta.arg).toEqual({ mfaMethod: "AUTHENTICATOR" });

            // Check second call (fulfilled action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/updatePreferredMFAMethod/fulfilled");
            expect(secondCall.payload).toEqual(mockResponse.data);
            expect(secondCall.meta.arg).toEqual({ mfaMethod: "AUTHENTICATOR" });
        });

        it("should handle errors when updating MFA method", async () => {
            // Mock error
            const error = new Error("Network error");
            (userAxios.patch as jest.Mock).mockRejectedValue(error);

            // Create mock dispatch and getState functions
            const dispatch = jest.fn();
            const getState = jest.fn();

            // Call the action creator
            const thunk = updatePreferredMFAMethod({
                mfaMethod: "AUTHENTICATOR",
            });
            await thunk(dispatch, getState, undefined);

            // Check if axios.patch was called with the correct URL
            expect(userAxios.patch).toHaveBeenCalledWith("/v1/authenticator-app?mfaMethod=AUTHENTICATOR");

            // Check if sendCatchFeedback was called with the error
            expect(sendCatchFeedback).toHaveBeenCalledWith(error);

            // Check if dispatch was called with the correct actions
            expect(dispatch).toHaveBeenCalledTimes(2);

            // Check first call (pending action)
            const firstCall = dispatch.mock.calls[0][0];
            expect(firstCall.type).toBe("settings/updatePreferredMFAMethod/pending");
            expect(firstCall.meta.arg).toEqual({ mfaMethod: "AUTHENTICATOR" });

            // Check second call (rejected action)
            const secondCall = dispatch.mock.calls[1][0];
            expect(secondCall.type).toBe("settings/updatePreferredMFAMethod/rejected");
            expect(secondCall.payload).toEqual(error);
            expect(secondCall.meta.arg).toEqual({ mfaMethod: "AUTHENTICATOR" });
            expect(secondCall.meta.rejectedWithValue).toBe(true);
        });
    });
});
