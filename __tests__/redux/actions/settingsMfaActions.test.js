import { configureStore } from "@reduxjs/toolkit";
import {
    validateSettingsSecurityQuestion,
    validateSettingsAuthenticator,
    validateSettingsSmsOtp,
    clearSettingsMfaState,
} from "../../../src/redux/actions/settingsMfaActions";
import settingsMfaReducer from "../../../src/redux/slices/settingsMfaSlice";
import { userAxios } from "../../../src/api/axios";

// Mock the axios instance
jest.mock("../../../src/api/axios", () => ({
    userAxios: {
        post: jest.fn(),
    },
}));

// Mock feedback functions
jest.mock("../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

describe("Settings MFA Actions", () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settingsMfa: settingsMfaReducer,
            },
        });
        jest.clearAllMocks();
    });

    describe("validateSettingsSecurityQuestion", () => {
        const mockRequest = {
            userEmail: "<EMAIL>",
            question: "What is your favorite color?",
            answer: "Blue",
        };

        it("should handle successful security question validation", async () => {
            const mockResponse = {
                data: {
                    token: "mock-security-token",
                    message: "Security question validated successfully",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(validateSettingsSecurityQuestion(mockRequest));

            expect(result.type).toBe("settingsMfa/validateSecurityQuestion/fulfilled");
            expect(result.payload).toEqual(mockResponse.data);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/user-security-questions/validate", mockRequest);

            const state = store.getState().settingsMfa;
            expect(state.validateSecurityQuestion.loading).toBe(false);
            expect(state.validateSecurityQuestion.success).toBe(true);
            expect(state.validateSecurityQuestion.token).toBe("mock-security-token");
            expect(state.currentVerification.token).toBe("mock-security-token");
        });

        it("should handle failed security question validation", async () => {
            const mockError = new Error("Invalid answer");
            userAxios.post.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(validateSettingsSecurityQuestion(mockRequest));

            expect(result.type).toBe("settingsMfa/validateSecurityQuestion/rejected");
            expect(result.payload).toBe("Invalid answer");

            const state = store.getState().settingsMfa;
            expect(state.validateSecurityQuestion.loading).toBe(false);
            expect(state.validateSecurityQuestion.success).toBe(false);
            expect(state.validateSecurityQuestion.error).toBe("Invalid answer");
            expect(state.validateSecurityQuestion.token).toBe(null);
        });
    });

    describe("validateSettingsAuthenticator", () => {
        const mockRequest = {
            username: "<EMAIL>",
            otp: "123456",
        };

        it("should handle successful authenticator validation", async () => {
            const mockResponse = {
                data: {
                    token: "mock-auth-token",
                    message: "Authenticator validated successfully",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(validateSettingsAuthenticator(mockRequest));

            expect(result.type).toBe("settingsMfa/validateAuthenticator/fulfilled");
            expect(result.payload).toEqual(mockResponse.data);
            expect(userAxios.post).toHaveBeenCalledWith(
                "/v1/mfa?command=verify-otp&verificationType=mfa-internal",
                mockRequest
            );

            const state = store.getState().settingsMfa;
            expect(state.validateAuthenticator.loading).toBe(false);
            expect(state.validateAuthenticator.success).toBe(true);
            expect(state.validateAuthenticator.token).toBe("mock-auth-token");
            expect(state.currentVerification.token).toBe("mock-auth-token");
        });

        it("should handle failed authenticator validation", async () => {
            const mockError = new Error("Invalid OTP");
            userAxios.post.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(validateSettingsAuthenticator(mockRequest));

            expect(result.type).toBe("settingsMfa/validateAuthenticator/rejected");
            expect(result.payload).toBe("Invalid OTP");

            const state = store.getState().settingsMfa;
            expect(state.validateAuthenticator.loading).toBe(false);
            expect(state.validateAuthenticator.success).toBe(false);
            expect(state.validateAuthenticator.error).toBe("Invalid OTP");
        });
    });

    describe("validateSettingsSmsOtp", () => {
        const mockRequest = {
            receiver: "+1234567890",
            otp: "123456",
        };

        it("should handle successful SMS OTP validation", async () => {
            const mockResponse = {
                data: {
                    token: "mock-sms-token",
                    message: "SMS OTP validated successfully",
                },
            };
            userAxios.post.mockResolvedValueOnce(mockResponse);

            const result = await store.dispatch(validateSettingsSmsOtp(mockRequest));

            expect(result.type).toBe("settingsMfa/validateSmsOtp/fulfilled");
            expect(result.payload).toEqual(mockResponse.data);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager/validate", mockRequest);

            const state = store.getState().settingsMfa;
            expect(state.validateSmsOtp.loading).toBe(false);
            expect(state.validateSmsOtp.success).toBe(true);
            expect(state.validateSmsOtp.token).toBe("mock-sms-token");
            expect(state.currentVerification.token).toBe("mock-sms-token");
        });

        it("should handle failed SMS OTP validation", async () => {
            const mockError = new Error("Invalid SMS OTP");
            userAxios.post.mockRejectedValueOnce(mockError);

            const result = await store.dispatch(validateSettingsSmsOtp(mockRequest));

            expect(result.type).toBe("settingsMfa/validateSmsOtp/rejected");
            expect(result.payload).toBe("Invalid SMS OTP");

            const state = store.getState().settingsMfa;
            expect(state.validateSmsOtp.loading).toBe(false);
            expect(state.validateSmsOtp.success).toBe(false);
            expect(state.validateSmsOtp.error).toBe("Invalid SMS OTP");
        });
    });

    describe("clearSettingsMfaState", () => {
        it("should clear state successfully", async () => {
            const result = await store.dispatch(clearSettingsMfaState("all"));

            expect(result.type).toBe("settingsMfa/clearState/fulfilled");
        });
    });
});
