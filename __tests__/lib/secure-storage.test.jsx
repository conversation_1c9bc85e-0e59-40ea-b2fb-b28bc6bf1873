import { secureLocalStorage, secureCookies, authTokenManager } from "@/lib/secure-storage";
import Cookies from "js-cookie";
import CryptoJS from "crypto-js";

// Mock dependencies
jest.mock("crypto-js", () => ({
    AES: {
        encrypt: jest.fn().mockReturnValue({
            toString: jest.fn().mockReturnValue("encrypted-value"),
        }),
        decrypt: jest.fn().mockReturnValue({
            toString: jest.fn().mockImplementation((format) => {
                if (format === "test-error") throw new Error("Decryption error");
                return "{'test':'value'}";
            }),
        }),
    },
    enc: {
        Utf8: "utf8-format",
    },
}));

jest.mock("js-cookie", () => ({
    set: jest.fn(),
    get: jest.fn(),
    remove: jest.fn(),
}));

// Mock localStorage
const localStorageMock = (() => {
    let store = {};
    return {
        getItem: jest.fn((key) => store[key] || null),
        setItem: jest.fn((key, value) => {
            store[key] = value;
        }),
        removeItem: jest.fn((key) => {
            delete store[key];
        }),
        clear: jest.fn(() => {
            store = {};
        }),
    };
})();

Object.defineProperty(window, "localStorage", { value: localStorageMock });

// Mock console.error to avoid polluting test output
console.error = jest.fn();

describe("secureLocalStorage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        localStorageMock.clear();
    });

    describe("getItem", () => {
        it("should return null if item does not exist", () => {
            localStorageMock.getItem.mockReturnValueOnce(null);

            const result = secureLocalStorage.getItem("test-key");

            expect(result).toBeNull();
            expect(localStorageMock.getItem).toHaveBeenCalledWith("test-key");
            expect(CryptoJS.AES.decrypt).not.toHaveBeenCalled();
        });

        it("should decrypt and return item if it exists", () => {
            localStorageMock.getItem.mockReturnValueOnce("encrypted-value");

            const result = secureLocalStorage.getItem("test-key");

            expect(result).toEqual("{'test':'value'}");
            expect(localStorageMock.getItem).toHaveBeenCalledWith("test-key");
            expect(CryptoJS.AES.decrypt).toHaveBeenCalled();
        });

        it("should handle decryption errors", () => {
            localStorageMock.getItem.mockReturnValueOnce("encrypted-value");
            CryptoJS.AES.decrypt.mockImplementationOnce(() => {
                throw new Error("Decryption error");
            });

            const result = secureLocalStorage.getItem("test-key");

            expect(result).toBeNull();
            expect(console.error).toHaveBeenCalled();
        });

        it("should handle server-side rendering", () => {
            const originalWindow = global.window;
            // @ts-ignore
            delete global.window;

            const result = secureLocalStorage.getItem("test-key");

            expect(result).toBeNull();

            // Restore window
            global.window = originalWindow;
        });
    });

    describe("setItem", () => {
        it("should encrypt and store item", () => {
            secureLocalStorage.setItem("test-key", { test: "value" });

            expect(CryptoJS.AES.encrypt).toHaveBeenCalled();
            expect(localStorageMock.setItem).toHaveBeenCalledWith("test-key", "encrypted-value");
        });

        it("should handle encryption errors", () => {
            CryptoJS.AES.encrypt.mockImplementationOnce(() => {
                throw new Error("Encryption error");
            });

            secureLocalStorage.setItem("test-key", { test: "value" });

            expect(console.error).toHaveBeenCalled();
            expect(localStorageMock.setItem).not.toHaveBeenCalled();
        });

        it("should handle server-side rendering", () => {
            const originalWindow = global.window;
            // @ts-ignore
            delete global.window;

            secureLocalStorage.setItem("test-key", { test: "value" });

            expect(CryptoJS.AES.encrypt).not.toHaveBeenCalled();
            expect(localStorageMock.setItem).not.toHaveBeenCalled();

            // Restore window
            global.window = originalWindow;
        });
    });

    describe("removeItem", () => {
        it("should remove item from localStorage", () => {
            secureLocalStorage.removeItem("test-key");

            expect(localStorageMock.removeItem).toHaveBeenCalledWith("test-key");
        });

        it("should handle errors", () => {
            localStorageMock.removeItem.mockImplementationOnce(() => {
                throw new Error("Remove error");
            });

            secureLocalStorage.removeItem("test-key");

            expect(console.error).toHaveBeenCalled();
        });

        it("should handle server-side rendering", () => {
            const originalWindow = global.window;
            // @ts-ignore
            delete global.window;

            secureLocalStorage.removeItem("test-key");

            expect(localStorageMock.removeItem).not.toHaveBeenCalled();

            // Restore window
            global.window = originalWindow;
        });
    });

    describe("clear", () => {
        it("should clear localStorage", () => {
            secureLocalStorage.clear();

            expect(localStorageMock.clear).toHaveBeenCalled();
        });

        it("should handle errors", () => {
            localStorageMock.clear.mockImplementationOnce(() => {
                throw new Error("Clear error");
            });

            secureLocalStorage.clear();

            expect(console.error).toHaveBeenCalled();
        });

        it("should handle server-side rendering", () => {
            const originalWindow = global.window;
            // @ts-ignore
            delete global.window;

            // Reset the mock to clear previous calls
            localStorageMock.clear.mockClear();

            secureLocalStorage.clear();

            expect(localStorageMock.clear).not.toHaveBeenCalled();

            // Restore window
            global.window = originalWindow;
        });
    });
});

describe("secureCookies", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("set", () => {
        it("should encrypt and set cookie", () => {
            secureCookies.set("test-cookie", { test: "value" });

            expect(CryptoJS.AES.encrypt).toHaveBeenCalled();
            expect(Cookies.set).toHaveBeenCalledWith("test-cookie", "encrypted-value", expect.any(Object));
        });

        it("should handle encryption errors", () => {
            CryptoJS.AES.encrypt.mockImplementationOnce(() => {
                throw new Error("Encryption error");
            });

            secureCookies.set("test-cookie", { test: "value" });

            expect(console.error).toHaveBeenCalled();
            expect(Cookies.set).not.toHaveBeenCalled();
        });
    });

    describe("get", () => {
        it("should return null if cookie does not exist", () => {
            Cookies.get.mockReturnValueOnce(undefined);

            const result = secureCookies.get("test-cookie");

            expect(result).toBeNull();
            expect(Cookies.get).toHaveBeenCalledWith("test-cookie");
            expect(CryptoJS.AES.decrypt).not.toHaveBeenCalled();
        });

        it("should decrypt and return cookie if it exists", () => {
            Cookies.get.mockReturnValueOnce("encrypted-value");

            const result = secureCookies.get("test-cookie");

            expect(result).toEqual("{'test':'value'}");
            expect(Cookies.get).toHaveBeenCalledWith("test-cookie");
            expect(CryptoJS.AES.decrypt).toHaveBeenCalled();
        });

        it("should handle decryption errors", () => {
            Cookies.get.mockReturnValueOnce("encrypted-value");
            CryptoJS.AES.decrypt.mockImplementationOnce(() => {
                throw new Error("Decryption error");
            });

            const result = secureCookies.get("test-cookie");

            expect(result).toBeNull();
            expect(console.error).toHaveBeenCalled();
        });
    });

    describe("remove", () => {
        it("should remove cookie", () => {
            secureCookies.remove("test-cookie");

            expect(Cookies.remove).toHaveBeenCalledWith("test-cookie", expect.any(Object));
        });

        it("should handle errors", () => {
            Cookies.remove.mockImplementationOnce(() => {
                throw new Error("Remove error");
            });

            secureCookies.remove("test-cookie");

            expect(console.error).toHaveBeenCalled();
        });
    });
});

describe("authTokenManager", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("setToken", () => {
        it("should set token with default expiry", () => {
            authTokenManager.setToken("test-token");

            expect(Cookies.set).toHaveBeenCalled();
        });

        it("should set token with custom expiry", () => {
            authTokenManager.setToken("test-token", 30);

            expect(Cookies.set).toHaveBeenCalled();
        });
    });

    describe("getToken", () => {
        it("should get token", () => {
            Cookies.get.mockReturnValueOnce("encrypted-token");
            CryptoJS.AES.decrypt.mockReturnValueOnce({
                toString: jest.fn().mockReturnValue("test-token"),
            });

            const result = authTokenManager.getToken();

            expect(result).toBe("test-token");
            expect(Cookies.get).toHaveBeenCalled();
        });
    });

    describe("clearToken", () => {
        it("should clear token", () => {
            authTokenManager.clearToken();

            expect(Cookies.remove).toHaveBeenCalled();
        });
    });
});
