import {
    navigateToLoginStep,
    navigateToNextLoginStep,
    navigateToSignupStep,
    navigateToNextSignupStep,
} from "@/lib/navigation-utils";
import { LoginStep, getLoginStepRoute, setLoginProgress, setSignupProgress } from "@/lib/login-utils";
import { SignupStep, getSignupStepRoute } from "@/lib/session-utils";

// Mock the dependencies
jest.mock("@/lib/login-utils", () => ({
    LoginStep: {
        CREDENTIALS: 1,
        MAGIC_CODE: 2,
        MFA_VERIFICATION: 3,
        DEVICE_VERIFICATION: 4,
        TRUST_DEVICE: 5,
        COMPLETED: 6,
    },
    getLoginStepRoute: jest.fn((step) => {
        const routes = {
            1: "/auth/login",
            2: "/auth/login/magic-code",
            3: "/auth/login/two-fa-verification",
            4: "/auth/login/verify-device",
            5: "/auth/login/trust-device",
            6: "/dashboard",
        };
        return routes[step] || "/auth/login";
    }),
    setLoginProgress: jest.fn(),
    clearLoginProgress: jest.fn(),
    getLoginProgress: jest.fn(),
    canAccessLoginStep: jest.fn(),
}));

jest.mock("@/lib/session-utils", () => ({
    SignupStep: {
        PERSONAL_INFO: 1,
        VERIFY_PERSONAL_EMAIL: 2,
        VERIFY_PHONE_NUMBER: 3,
        BUSINESS_INFO: 4,
        VALIDATE_BUSINESS: 5,
        VERIFY_BUSINESS_EMAIL: 6,
        MFA_SETUP: 7,
        COMPLETE: 8,
    },
    getSignupStepRoute: jest.fn((step) => {
        const routes = {
            1: "/auth/sign-up",
            2: "/auth/sign-up/verify-personal-email",
            3: "/auth/sign-up/verify-phone-number",
            4: "/auth/sign-up/business-info",
            5: "/auth/sign-up/validate-business",
            6: "/auth/sign-up/verify-business-email",
            7: "/auth/sign-up/setup-mfa",
            8: "/dashboard",
        };
        return routes[step] || "/auth/sign-up";
    }),
    setSignupProgress: jest.fn(),
    clearSignupProgress: jest.fn(),
    getSignupProgress: jest.fn(),
    canAccessSignupStep: jest.fn(),
}));

describe("navigation-utils", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("navigateToLoginStep", () => {
        it("should navigate to the specified login step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function
            navigateToLoginStep(LoginStep.CREDENTIALS, router);

            // Verify the router was called with the correct route
            expect(router.push).toHaveBeenCalledWith("/auth/login");
            expect(getLoginStepRoute).toHaveBeenCalledWith(LoginStep.CREDENTIALS);
        });

        it("should update the cookie if updateCookie is true", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with updateCookie = true
            navigateToLoginStep(LoginStep.CREDENTIALS, router, true);

            // Verify setLoginProgress was called
            expect(setLoginProgress).toHaveBeenCalledWith(LoginStep.CREDENTIALS);
        });

        it("should not update the cookie if updateCookie is false", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with updateCookie = false
            navigateToLoginStep(LoginStep.CREDENTIALS, router, false);

            // Verify setLoginProgress was not called
            expect(setLoginProgress).not.toHaveBeenCalled();
        });
    });

    describe("navigateToNextLoginStep", () => {
        it("should navigate to the next login step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function
            navigateToNextLoginStep(LoginStep.CREDENTIALS, router);

            // Verify the router was called
            expect(router.push).toHaveBeenCalled();
            // The next step after CREDENTIALS is MAGIC_CODE
            expect(getLoginStepRoute).toHaveBeenCalledWith(LoginStep.MAGIC_CODE);
        });

        it("should skip steps if skipSteps is provided", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with skipSteps = 1
            navigateToNextLoginStep(LoginStep.CREDENTIALS, router, 1);

            // Verify the router was called
            expect(router.push).toHaveBeenCalled();
            // The step after CREDENTIALS + 1 skip is MFA_VERIFICATION
            expect(getLoginStepRoute).toHaveBeenCalledWith(LoginStep.MFA_VERIFICATION);
        });

        it("should not navigate beyond the last step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with the last step
            navigateToNextLoginStep(LoginStep.COMPLETED, router);

            // Verify the router was not called
            expect(router.push).not.toHaveBeenCalled();
        });
    });

    describe("navigateToSignupStep", () => {
        it("should navigate to the specified signup step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function
            navigateToSignupStep(SignupStep.PERSONAL_INFO, router);

            // Verify the router was called
            expect(router.push).toHaveBeenCalledWith("/auth/sign-up");
            expect(getSignupStepRoute).toHaveBeenCalledWith(SignupStep.PERSONAL_INFO);
        });
    });

    describe("navigateToNextSignupStep", () => {
        it("should navigate to the next signup step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function
            navigateToNextSignupStep(SignupStep.PERSONAL_INFO, router);

            // Verify the router was called
            expect(router.push).toHaveBeenCalled();
            // The next step after PERSONAL_INFO is VERIFY_PERSONAL_EMAIL
            expect(getSignupStepRoute).toHaveBeenCalledWith(SignupStep.VERIFY_PERSONAL_EMAIL);
        });

        it("should skip steps if skipSteps is provided", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with skipSteps = 1
            navigateToNextSignupStep(SignupStep.PERSONAL_INFO, router, 1);

            // Verify the router was called
            expect(router.push).toHaveBeenCalled();
            // The step after PERSONAL_INFO + 1 skip is VERIFY_PHONE_NUMBER
            expect(getSignupStepRoute).toHaveBeenCalledWith(SignupStep.VERIFY_PHONE_NUMBER);
        });

        it("should not navigate beyond the last step", () => {
            // Mock the router
            const router = {
                push: jest.fn(),
            };

            // Call the function with the last step
            navigateToNextSignupStep(SignupStep.COMPLETE, router);

            // Verify the router was not called
            expect(router.push).not.toHaveBeenCalled();
        });
    });
});
