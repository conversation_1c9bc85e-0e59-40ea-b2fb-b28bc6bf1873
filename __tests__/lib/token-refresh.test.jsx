import {
    subscribeTokenRefresh,
    onTokenRefreshed,
    logTokenExpiry,
    shouldRefreshToken,
    refreshAccessToken,
    validateAndRefreshToken,
} from "@/lib/token-refresh";
import { cookies } from "@/lib/cookies";
import { jwtDecode } from "jwt-decode";
import { userAxios } from "@/api/axios";
import { getSessionDetails } from "@/functions/userSession";

// Mock dependencies
jest.mock("@/lib/cookies", () => ({
    cookies: {
        getToken: jest.fn(),
        getRefreshToken: jest.fn(),
        setToken: jest.fn(),
        setRefreshToken: jest.fn(),
        clearAllTokens: jest.fn(),
    },
}));

jest.mock("jwt-decode", () => ({
    jwtDecode: jest.fn(),
}));

jest.mock("@/api/axios", () => ({
    userAxios: {
        post: jest.fn(),
    },
}));

jest.mock("@/functions/userSession", () => ({
    getSessionDetails: jest.fn(),
}));

// Mock console methods to avoid noise in tests and test logging
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

beforeAll(() => {
    console.error = jest.fn();
    console.log = jest.fn();
});

afterAll(() => {
    console.error = originalConsoleError;
    console.log = originalConsoleLog;
});

describe("Token Refresh Utilities", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Clear console mocks
        console.error.mockClear();
        console.log.mockClear();

        // Reset module state by calling onTokenRefreshed to clear subscribers
        // This is important to prevent state from leaking between tests
        // onTokenRefreshed("reset-test")
    });

    describe("subscribeTokenRefresh and onTokenRefreshed", () => {
        it("should add callback to subscribers and call them when token is refreshed", () => {
            const callback1 = jest.fn();
            const callback2 = jest.fn();
            const testToken = "new-access-token";

            // Subscribe callbacks
            subscribeTokenRefresh(callback1);
            subscribeTokenRefresh(callback2);

            // Notify subscribers
            onTokenRefreshed(testToken);

            // Both callbacks should be called with the token
            expect(callback1).toHaveBeenCalledWith(testToken);
            expect(callback2).toHaveBeenCalledWith(testToken);
        });

        it("should clear subscribers after notifying them", () => {
            const callback = jest.fn();
            const testToken = "new-access-token";

            // Subscribe callback
            subscribeTokenRefresh(callback);

            // Notify subscribers
            onTokenRefreshed(testToken);

            // Subscribe another callback
            const callback2 = jest.fn();
            subscribeTokenRefresh(callback2);

            // Notify again
            onTokenRefreshed("another-token");

            // First callback should only be called once, second callback once
            expect(callback).toHaveBeenCalledTimes(1);
            expect(callback2).toHaveBeenCalledTimes(1);
            expect(callback2).toHaveBeenCalledWith("another-token");
        });

        it("should handle empty subscribers array", () => {
            // Should not throw error when no subscribers
            expect(() => onTokenRefreshed("test-token")).not.toThrow();
        });
    });

    describe("logTokenExpiry", () => {
        it("should log 'No token available' for null token", () => {
            logTokenExpiry(null);

            expect(console.log).toHaveBeenCalledWith("No token available");
        });

        it("should log 'No token available' for empty token", () => {
            logTokenExpiry("");

            expect(console.log).toHaveBeenCalledWith("No token available");
        });

        it("should log token expiry information for valid token", () => {
            const mockExp = Math.floor(Date.now() / 1000) + 600; // 10 minutes from now
            const mockDecoded = { exp: mockExp };

            jwtDecode.mockReturnValue(mockDecoded);

            logTokenExpiry("valid-token");

            expect(jwtDecode).toHaveBeenCalledWith("valid-token");
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expiry info:"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("seconds until expiry"));
        });

        it("should log token expiry information for token without exp claim", () => {
            const mockDecoded = {}; // No exp claim

            jwtDecode.mockReturnValue(mockDecoded);

            logTokenExpiry("token-without-exp");

            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expiry info:"));
        });

        it("should log error for invalid token", () => {
            const mockError = new Error("Invalid token format");
            jwtDecode.mockImplementation(() => {
                throw mockError;
            });

            logTokenExpiry("invalid-token");

            expect(console.error).toHaveBeenCalledWith("Error decoding token for expiry info:", mockError);
        });

        it("should handle token with past expiry", () => {
            const pastExp = Math.floor(Date.now() / 1000) - 300; // 5 minutes ago
            const mockDecoded = { exp: pastExp };

            jwtDecode.mockReturnValue(mockDecoded);

            logTokenExpiry("expired-token");

            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expiry info:"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("seconds until expiry"));
        });
    });

    describe("shouldRefreshToken", () => {
        it("should return false for null token", () => {
            expect(shouldRefreshToken(null)).toBe(false);
        });

        it("should return false for empty token", () => {
            expect(shouldRefreshToken("")).toBe(false);
        });

        it("should return false for token that expires in more than 5 minutes", () => {
            const futureExp = Math.floor(Date.now() / 1000) + 600; // 10 minutes from now
            jwtDecode.mockReturnValue({ exp: futureExp });

            const result = shouldRefreshToken("valid-token");

            expect(result).toBe(false);
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expires in"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("refresh needed: false"));
        });

        it("should return true for token that expires within 5 minutes", () => {
            const nearExp = Math.floor(Date.now() / 1000) + 200; // 3 minutes from now
            jwtDecode.mockReturnValue({ exp: nearExp });

            const result = shouldRefreshToken("expiring-token");

            expect(result).toBe(true);
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("refresh needed: true"));
        });

        it("should return true for expired token", () => {
            const pastExp = Math.floor(Date.now() / 1000) - 100; // Expired 100 seconds ago
            jwtDecode.mockReturnValue({ exp: pastExp });

            expect(shouldRefreshToken("expired-token")).toBe(true);
        });

        it("should return true for token without exp claim", () => {
            jwtDecode.mockReturnValue({}); // No exp claim

            expect(shouldRefreshToken("no-exp-token")).toBe(true);
        });

        it("should return true and log error for invalid token", () => {
            const mockError = new Error("Invalid token");
            jwtDecode.mockImplementation(() => {
                throw mockError;
            });

            expect(shouldRefreshToken("invalid-token")).toBe(true);
            expect(console.error).toHaveBeenCalledWith("Error checking token expiry:", mockError);
        });

        it("should handle token with exactly 300 seconds until expiry", () => {
            const exactExp = Math.floor(Date.now() / 1000) + 300; // Exactly 5 minutes
            jwtDecode.mockReturnValue({ exp: exactExp });

            expect(shouldRefreshToken("exact-token")).toBe(true);
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expires in"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("refresh needed: true"));
        });

        it("should handle token with 299 seconds until expiry", () => {
            const almostExp = Math.floor(Date.now() / 1000) + 299; // Just under 5 minutes
            jwtDecode.mockReturnValue({ exp: almostExp });

            expect(shouldRefreshToken("almost-expired-token")).toBe(true);
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Token expires in"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("refresh needed: true"));
        });
    });

    describe("refreshAccessToken", () => {
        const mockCurrentToken = "current-token";
        const mockRefreshToken = "refresh-token";
        const mockNewAccessToken = "new-access-token";
        const mockNewRefreshToken = "new-refresh-token";
        const mockSession = { email: "<EMAIL>" };

        beforeEach(() => {
            cookies.getToken.mockReturnValue(mockCurrentToken);
            cookies.getRefreshToken.mockReturnValue(mockRefreshToken);
            getSessionDetails.mockReturnValue(mockSession);
        });

        it("should return current token if refresh is not needed and not forced", async () => {
            // Mock token that doesn't need refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes from now
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                    refresh_token: mockNewRefreshToken,
                },
            });

            const result = await refreshAccessToken(false);

            expect(result).toBe(mockCurrentToken);
            expect(userAxios.post).not.toHaveBeenCalled();
            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining("Token refresh not needed, returning current token")
            );
        });

        it("should refresh token when forced even if not expired", async () => {
            // Mock token that doesn't need refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes from now
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                    refresh_token: mockNewRefreshToken,
                },
            });

            const result = await refreshAccessToken(true);

            expect(result).toBe(mockNewAccessToken);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/refresh", {
                username: mockSession.email,
                refreshToken: mockRefreshToken,
            });
            expect(cookies.setToken).toHaveBeenCalledWith(mockNewAccessToken);
            expect(cookies.setRefreshToken).toHaveBeenCalledWith(mockNewRefreshToken);
            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining("Starting token refresh process - will call API")
            );
            expect(console.log).toHaveBeenCalledWith("Token refresh successful, storing new tokens");
        });

        it("should refresh token when it needs refresh", async () => {
            // Mock token that needs refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200, // 3 minutes from now
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                    refresh_token: mockNewRefreshToken,
                },
            });

            const result = await refreshAccessToken();

            expect(result).toBe(mockNewAccessToken);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/refresh", {
                username: mockSession.email,
                refreshToken: mockRefreshToken,
            });
            expect(console.log).toHaveBeenCalledWith("Calling refresh token API...");
        });

        it("should handle refresh without new refresh token", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                    // No refresh_token in response
                },
            });

            const result = await refreshAccessToken();

            expect(result).toBe(mockNewAccessToken);
            expect(cookies.setToken).toHaveBeenCalledWith(mockNewAccessToken);
            expect(cookies.setRefreshToken).not.toHaveBeenCalled();
        });

        it("should return null and clear tokens when no refresh token available", async () => {
            cookies.getRefreshToken.mockReturnValue(null);
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(cookies.clearAllTokens).toHaveBeenCalled();
            expect(userAxios.post).not.toHaveBeenCalled();
            expect(console.log).toHaveBeenCalledWith("No refresh token available, clearing tokens");
        });

        it("should throw error when no access token in response", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    // No access_token in response
                    refresh_token: mockNewRefreshToken,
                },
            });

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(console.error).toHaveBeenCalledWith("Token refresh failed:", expect.any(Error));
        });

        it("should handle API error and return null", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            const apiError = new Error("API Error");
            userAxios.post.mockRejectedValue(apiError);

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(console.error).toHaveBeenCalledWith("Token refresh failed:", apiError);
        });

        //   it("should handle concurrent refresh requests by queuing", async () => {
        //     jwtDecode.mockReturnValue({
        //       exp: Math.floor(Date.now() / 1000) + 200,
        //     })

        //     // Mock a slow API response
        //     let resolveApiCall
        //     const apiPromise = new Promise((resolve) => {
        //       resolveApiCall = resolve
        //     })

        //     userAxios.post.mockReturnValue(apiPromise)

        //     // Start first refresh
        //     const firstRefreshPromise = refreshAccessToken()

        //     // Start second refresh (should be queued)
        //     const secondRefreshPromise = refreshAccessToken()

        //     expect(console.log).toHaveBeenCalledWith("Token refresh already in progress, subscribing to result")

        //     // Resolve the API call
        //     resolveApiCall({
        //       data: {
        //         access_token: mockNewAccessToken,
        //         refresh_token: mockNewRefreshToken,
        //       },
        //     })

        //     const [firstResult, secondResult] = await Promise.all([firstRefreshPromise, secondResult])

        //     expect(firstResult).toBe(mockNewAccessToken)
        //     expect(secondResult).toBe(mockNewAccessToken)
        //     expect(userAxios.post).toHaveBeenCalledTimes(1) // Only one API call
        //   }, 10000) // Increase timeout

        it("should notify subscribers when token is refreshed", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                    refresh_token: mockNewRefreshToken,
                },
            });

            const subscriber = jest.fn();
            subscribeTokenRefresh(subscriber);

            await refreshAccessToken();

            expect(subscriber).toHaveBeenCalledWith(mockNewAccessToken);
        });

        it("should handle session without email", async () => {
            getSessionDetails.mockReturnValue(null);
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                },
            });

            const result = await refreshAccessToken();

            expect(result).toBe(mockNewAccessToken);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/refresh", {
                username: undefined,
                refreshToken: mockRefreshToken,
            });
        });

        it("should handle session with empty email", async () => {
            getSessionDetails.mockReturnValue({ email: "" });
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                },
            });

            const result = await refreshAccessToken();

            expect(result).toBe(mockNewAccessToken);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/refresh", {
                username: "",
                refreshToken: mockRefreshToken,
            });
        });

        it("should log initial refresh attempt information", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                },
            });

            await refreshAccessToken(true);

            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining("Token refresh attempt - forceRefresh: true")
            );
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("hasToken: true"));
            expect(console.log).toHaveBeenCalledWith(expect.stringContaining("hasSession: true"));
        });

        it("should handle malformed API response", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            // Malformed response
            userAxios.post.mockResolvedValue({
                data: null,
            });

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(console.error).toHaveBeenCalledWith("Token refresh failed:", expect.any(Error));
        });

        it("should handle network timeout during refresh", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            const timeoutError = new Error("Network timeout");
            timeoutError.code = "ECONNABORTED";
            userAxios.post.mockRejectedValue(timeoutError);

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(console.error).toHaveBeenCalledWith("Token refresh failed:", timeoutError);
        });

        it("should handle empty response data", async () => {
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {},
            });

            const result = await refreshAccessToken();

            expect(result).toBe(null);
            expect(console.error).toHaveBeenCalledWith("Token refresh failed:", expect.any(Error));
        });
    });

    describe("validateAndRefreshToken", () => {
        it("should return false when no token exists", async () => {
            cookies.getToken.mockReturnValue(null);

            const result = await validateAndRefreshToken();

            expect(result).toBe(false);
        });

        it("should return false when token is empty string", async () => {
            cookies.getToken.mockReturnValue("");

            const result = await validateAndRefreshToken();

            expect(result).toBe(false);
        });

        it("should return true when token is successfully refreshed", async () => {
            const mockToken = "existing-token";
            const mockNewToken = "new-token";

            cookies.getToken.mockReturnValue(mockToken);
            cookies.getRefreshToken.mockReturnValue("refresh-token");
            getSessionDetails.mockReturnValue({ email: "<EMAIL>" });

            // Mock token that needs refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewToken,
                },
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(true);
        });

        it("should return true when current token is still valid", async () => {
            const mockToken = "valid-token";

            cookies.getToken.mockReturnValue(mockToken);

            // Mock token that doesn't need refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes from now
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(true);
        });

        it("should return false when refresh fails", async () => {
            const mockToken = "existing-token";

            cookies.getToken.mockReturnValue(mockToken);
            cookies.getRefreshToken.mockReturnValue(null); // No refresh token

            // Mock token that needs refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(false);
        });

        it("should return false and log error when refresh throws exception", async () => {
            const mockToken = "existing-token";

            cookies.getToken.mockReturnValue(mockToken);

            // Mock jwtDecode to throw error
            jwtDecode.mockImplementation(() => {
                throw new Error("Invalid token");
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(false);
            expect(console.error).toHaveBeenCalledWith("Error checking token expiry:", expect.any(Error));
        });

        it("should handle refresh returning null", async () => {
            const mockToken = "existing-token";

            cookies.getToken.mockReturnValue(mockToken);
            cookies.getRefreshToken.mockReturnValue("refresh-token");

            // Mock API failure
            userAxios.post.mockRejectedValue(new Error("API Error"));

            // Mock token that needs refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(false);
        });
    });

    describe("Integration Scenarios", () => {
        it("should handle validation flow that doesn't need refresh", async () => {
            const mockValidToken = "valid-token";

            cookies.getToken.mockReturnValue(mockValidToken);

            // Token is still valid
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes from now
            });

            const result = await validateAndRefreshToken();

            expect(result).toBe(true);
            expect(userAxios.post).not.toHaveBeenCalled();
            expect(cookies.setToken).not.toHaveBeenCalled();
        });

        it("should handle complete refresh flow with subscriber notification", async () => {
            const mockCurrentToken = "current-token";
            const mockRefreshToken = "refresh-token";
            const mockNewAccessToken = "new-access-token";
            const mockSession = { email: "<EMAIL>" };

            // Setup mocks
            cookies.getToken.mockReturnValue(mockCurrentToken);
            cookies.getRefreshToken.mockReturnValue(mockRefreshToken);
            getSessionDetails.mockReturnValue(mockSession);

            // Token needs refresh
            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            userAxios.post.mockResolvedValue({
                data: {
                    access_token: mockNewAccessToken,
                },
            });

            // Subscribe to token refresh
            const subscriber = jest.fn();
            subscribeTokenRefresh(subscriber);

            // Perform refresh
            const result = await refreshAccessToken();

            // Verify all interactions
            expect(result).toBe(mockNewAccessToken);
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/refresh", {
                username: mockSession.email,
                refreshToken: mockRefreshToken,
            });
            expect(cookies.setToken).toHaveBeenCalledWith(mockNewAccessToken);
            expect(subscriber).toHaveBeenCalledWith(mockNewAccessToken);
        });

        it("should handle multiple concurrent requests with different outcomes", async () => {
            const mockToken = "test-token";
            cookies.getToken.mockReturnValue(mockToken);
            cookies.getRefreshToken.mockReturnValue("refresh-token");
            getSessionDetails.mockReturnValue({ email: "<EMAIL>" });

            jwtDecode.mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 200,
            });

            // Mock slow API response
            let resolveApiCall;
            const apiPromise = new Promise((resolve) => {
                resolveApiCall = resolve;
            });
            userAxios.post.mockReturnValue(apiPromise);

            // Start multiple concurrent requests
            const promise1 = refreshAccessToken();
            const promise2 = refreshAccessToken();
            const promise3 = validateAndRefreshToken();

            // Resolve API call
            resolveApiCall({
                data: {
                    access_token: "new-token",
                },
            });

            const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);

            expect(result1).toBe("new-token");
            expect(result2).toBe("new-token");
            expect(result3).toBe(true);
            expect(userAxios.post).toHaveBeenCalledTimes(1);
        }, 10000); // Increase timeout
    });
});
