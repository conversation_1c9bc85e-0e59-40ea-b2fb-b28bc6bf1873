import {
    getLoginProgress,
    setLoginProgress,
    clearLoginProgress,
    getLoginStepRoute,
    LoginStep,
} from "@/lib/login-utils";

import Cookies from "js-cookie";

// Mock js-cookie
jest.mock("js-cookie", () => ({
    get: jest.fn(),
    set: jest.fn(),
    remove: jest.fn(),
}));

// Mock localStorage
const localStorageMock = (() => {
    let store = {};
    return {
        getItem: jest.fn((key) => store[key] || null),
        setItem: jest.fn((key, value) => {
            store[key] = value;
        }),
        removeItem: jest.fn((key) => {
            delete store[key];
        }),
        clear: jest.fn(() => {
            store = {};
        }),
    };
})();

Object.defineProperty(window, "localStorage", { value: localStorageMock });

describe("login-utils", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        localStorageMock.clear();
    });

    describe("getLoginProgress", () => {
        it("should return the login progress from cookies", () => {
            // Mock the cookie value
            Cookies.get.mockReturnValue("1");

            // Call the function
            const result = getLoginProgress();

            // Verify the cookie was accessed
            expect(Cookies.get).toHaveBeenCalledWith("login_progress");

            // Verify the result is the parsed login progress
            expect(result).toBe(LoginStep.CREDENTIALS);
        });

        it("should return CREDENTIALS step if no cookie exists", () => {
            // Mock no cookie value
            Cookies.get.mockReturnValue(undefined);

            // Call the function
            const result = getLoginProgress();

            // Verify the cookie was accessed
            expect(Cookies.get).toHaveBeenCalledWith("login_progress");

            // Verify the result is the default step
            expect(result).toBe(LoginStep.CREDENTIALS);
        });

        it("should handle invalid values in cookies", () => {
            // Mock invalid value in cookie
            Cookies.get.mockReturnValue("invalid");

            // Call the function
            const result = getLoginProgress();

            // Verify the cookie was accessed
            expect(Cookies.get).toHaveBeenCalledWith("login_progress");

            // Verify the result is NaN
            expect(isNaN(result)).toBe(true);
        });
    });

    describe("setLoginProgress", () => {
        it("should set the login progress in cookies", () => {
            // Call the function
            setLoginProgress(LoginStep.CREDENTIALS);

            // Verify the cookie was set
            expect(Cookies.set).toHaveBeenCalledWith("login_progress", "1", {
                expires: 1,
                path: "/",
                secure: false,
                sameSite: "strict",
            });
        });

        it("should set the login progress with the correct options", () => {
            // Call the function
            setLoginProgress(LoginStep.MAGIC_CODE);

            // Verify the cookie was set with the correct options
            expect(Cookies.set).toHaveBeenCalledWith("login_progress", "2", {
                expires: 1,
                path: "/",
                secure: false,
                sameSite: "strict",
            });
        });
    });

    describe("clearLoginProgress", () => {
        it("should remove the login progress from cookies", () => {
            // Call the function
            clearLoginProgress();

            // Verify the cookie was removed
            expect(Cookies.remove).toHaveBeenCalledWith("login_progress");
        });
    });

    describe("getLoginStepRoute", () => {
        it("should return the correct route for CREDENTIALS step", () => {
            // Call the function with CREDENTIALS step
            const route = getLoginStepRoute(LoginStep.CREDENTIALS);

            // Verify the result is the correct route
            expect(route).toBe("/auth/login");
        });

        it("should return the correct route for MAGIC_CODE step", () => {
            // Call the function with MAGIC_CODE step
            const route = getLoginStepRoute(LoginStep.MAGIC_CODE);

            // Verify the result is the correct route
            expect(route).toBe("/auth/login/magic-code");
        });

        it("should return the correct route for MFA_VERIFICATION step", () => {
            // Call the function with MFA_VERIFICATION step
            const route = getLoginStepRoute(LoginStep.MFA_VERIFICATION);

            // Verify the result is the correct route
            expect(route).toBe("/auth/login/two-fa-verification");
        });

        it("should return the correct route for COMPLETED step", () => {
            // Call the function with COMPLETED step
            const route = getLoginStepRoute(LoginStep.COMPLETED);

            // Verify the result is the correct route
            expect(route).toBe("/dashboard");
        });

        it("should return the login route for unknown steps", () => {
            // Call the function with an unknown step
            const route = getLoginStepRoute(999);

            // Verify the result is the login route
            expect(route).toBe("/auth/login");
        });
    });
});
