import React from "react";
import { render, screen } from "@testing-library/react";
import ApproverListItem from "@/components/common/approver-list-item";

// Mock the Avatar component
jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children, className, style }) => (
        <div data-testid="avatar" className={className} style={style}>
            {children}
        </div>
    ),
    AvatarFallback: ({ children, className, style }) => (
        <div data-testid="avatar-fallback" className={className} style={style}>
            {children}
        </div>
    ),
}));

// Mock the Badge component
jest.mock("@/components/common/badge", () => {
    const MockBadge = ({ text, size, color, className }) => (
        <div data-testid="badge" className={className}>
            {text}
        </div>
    );
    MockBadge.displayName = "Badge";
    return MockBadge;
});

// Mock data for testing
const mockApprovedApprover = {
    id: 178,
    userId: 117,
    name: "<PERSON><PERSON>",
    role: "BigRole",
    approvalLevel: 1,
    status: "APPROVE",
    date: "23 Nov, 2024 3:00:00 PM",
};

const mockDeclinedApprover = {
    id: 179,
    userId: 110,
    name: "Mojeed Withaccount",
    role: "Super Admin",
    approvalLevel: 2,
    status: "DECLINE",
    date: "23 Nov, 2024 3:00:00 PM",
};

const mockPendingApprover = {
    id: 177,
    userId: 128,
    name: "Vin Petrol",
    role: "BigRole",
    approvalLevel: 3,
    status: null,
    date: null,
};

describe("ApproverListItem Component", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders approved approver with correct styling", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            // Check if name is displayed
            expect(screen.getByText("Lamine Yamal")).toBeInTheDocument();

            // Check if role badge is displayed
            expect(screen.getByText("BigRole")).toBeInTheDocument();

            // Check if status is shown
            expect(screen.getByText("Approved")).toBeInTheDocument();

            // Check if date is displayed
            expect(screen.getByText("23 Nov, 2024 3:00:00 PM")).toBeInTheDocument();

            // Check avatar initials
            expect(screen.getByText("LY")).toBeInTheDocument();
        });

        it("renders declined approver with correct styling", () => {
            render(<ApproverListItem approver={mockDeclinedApprover} />);

            expect(screen.getByText("Mojeed Withaccount")).toBeInTheDocument();
            expect(screen.getByText("Super Admin")).toBeInTheDocument();
            expect(screen.getByText("Declined")).toBeInTheDocument();
            expect(screen.getByText("23 Nov, 2024 3:00:00 PM")).toBeInTheDocument();
            expect(screen.getByText("MW")).toBeInTheDocument();
        });

        it("renders pending approver with correct styling", () => {
            render(<ApproverListItem approver={mockPendingApprover} />);

            expect(screen.getByText("Vin Petrol")).toBeInTheDocument();
            expect(screen.getByText("BigRole")).toBeInTheDocument();
            expect(screen.getByText("Pending approval")).toBeInTheDocument();
            expect(screen.getByText("VP")).toBeInTheDocument();

            // Should not show status text for pending
            expect(screen.queryByText("Approved")).not.toBeInTheDocument();
            expect(screen.queryByText("Declined")).not.toBeInTheDocument();
        });
    });

    describe("Avatar Styling", () => {
        it("applies purple background for approved approvers", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const avatar = screen.getByTestId("avatar");
            expect(avatar).toHaveStyle({ backgroundColor: "#F9F0FE" });
        });

        it("applies purple background for declined approvers", () => {
            render(<ApproverListItem approver={mockDeclinedApprover} />);

            const avatar = screen.getByTestId("avatar");
            expect(avatar).toHaveStyle({ backgroundColor: "#F9F0FE" });
        });

        it("applies gray background for pending approvers", () => {
            render(<ApproverListItem approver={mockPendingApprover} />);

            const avatar = screen.getByTestId("avatar");
            expect(avatar).toHaveStyle({ backgroundColor: "#F9F9FA" });
        });
    });

    describe("Status Colors", () => {
        it("shows green text for approved status", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const statusText = screen.getByText("Approved");
            expect(statusText).toHaveStyle({ color: "#039855" });
        });

        it("shows red text for declined status", () => {
            render(<ApproverListItem approver={mockDeclinedApprover} />);

            const statusText = screen.getByText("Declined");
            expect(statusText).toHaveStyle({ color: "#D92D20" });
        });

        it("shows gray text for pending status", () => {
            render(<ApproverListItem approver={mockPendingApprover} />);

            const statusText = screen.getByText("Pending approval");
            expect(statusText).toHaveStyle({ color: "#3A3A41" });
        });
    });

    describe("Connector Line", () => {
        it("renders connector line when not last item", () => {
            render(<ApproverListItem approver={mockApprovedApprover} isLast={false} />);

            const connectorLine = screen.getByTestId("connector-line");
            expect(connectorLine).toBeInTheDocument();
        });

        it("does not render connector line when last item", () => {
            render(<ApproverListItem approver={mockApprovedApprover} isLast={true} />);

            const connectorLine = screen.queryByTestId("connector-line");
            expect(connectorLine).not.toBeInTheDocument();
        });

        it("applies purple connector line for approved approvers", () => {
            render(<ApproverListItem approver={mockApprovedApprover} isLast={false} />);

            const connectorLine = screen.getByTestId("connector-line");
            expect(connectorLine.querySelector("path")).toHaveAttribute("stroke", "#F9F0FE");
        });

        it("applies gray connector line for declined and pending approvers", () => {
            render(<ApproverListItem approver={mockDeclinedApprover} isLast={false} />);

            const connectorLine = screen.getByTestId("connector-line");
            expect(connectorLine.querySelector("path")).toHaveAttribute("stroke", "#F9F9FA");
        });
    });

    describe("Name Initials", () => {
        it("generates correct initials for single name", () => {
            const singleNameApprover = { ...mockApprovedApprover, name: "John" };
            render(<ApproverListItem approver={singleNameApprover} />);

            expect(screen.getByText("J")).toBeInTheDocument();
        });

        it("generates correct initials for two names", () => {
            const twoNameApprover = { ...mockApprovedApprover, name: "John Doe" };
            render(<ApproverListItem approver={twoNameApprover} />);

            expect(screen.getByText("JD")).toBeInTheDocument();
        });

        it("generates correct initials for three names", () => {
            const threeNameApprover = { ...mockApprovedApprover, name: "John Michael Doe" };
            render(<ApproverListItem approver={threeNameApprover} />);

            expect(screen.getByText("JMD")).toBeInTheDocument();
        });

        it("handles lowercase names correctly", () => {
            const lowercaseNameApprover = { ...mockApprovedApprover, name: "john doe" };
            render(<ApproverListItem approver={lowercaseNameApprover} />);

            expect(screen.getByText("JD")).toBeInTheDocument();
        });
    });

    describe("Special Cases", () => {
        it("handles cancelled transfer by showing all approvers as cancelled", () => {
            render(<ApproverListItem approver={mockApprovedApprover} transferStatus="cancelled" />);

            expect(screen.getByText("Cancelled")).toBeInTheDocument();
            expect(screen.queryByText("Approved")).not.toBeInTheDocument();
        });

        it("applies cancelled styling for cancelled transfers", () => {
            render(<ApproverListItem approver={mockApprovedApprover} transferStatus="cancelled" />);

            const statusText = screen.getByText("Cancelled");
            expect(statusText).toHaveStyle({ color: "#D92D20" });
        });

        it("handles missing date gracefully", () => {
            const noDateApprover = { ...mockApprovedApprover, date: null };
            render(<ApproverListItem approver={noDateApprover} />);

            expect(screen.getByText("Lamine Yamal")).toBeInTheDocument();
            expect(screen.queryByText("23 Nov, 2024 3:00:00 PM")).not.toBeInTheDocument();
        });

        it("handles empty name gracefully", () => {
            const emptyNameApprover = { ...mockApprovedApprover, name: "" };
            render(<ApproverListItem approver={emptyNameApprover} />);

            // Should show placeholder or handle empty name
            expect(screen.getByTestId("avatar")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper ARIA attributes", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const listItem = screen.getByRole("listitem");
            expect(listItem).toBeInTheDocument();
        });

        it("has descriptive text for screen readers", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            // Should have aria-label or aria-describedby for accessibility
            const listItem = screen.getByRole("listitem");
            expect(listItem).toHaveAttribute("aria-label");
        });

        it("has proper contrast for status colors", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const statusText = screen.getByText("Approved");
            // Green color #039855 should have sufficient contrast
            expect(statusText).toHaveStyle({ color: "#039855" });
        });
    });

    describe("Props Validation", () => {
        it("handles required approver prop", () => {
            // Suppress console.error for this test since we expect an error
            const originalError = console.error;
            console.error = jest.fn();

            expect(() => {
                render(<ApproverListItem />);
            }).toThrow("ApproverListItem requires an approver prop");

            // Restore console.error
            console.error = originalError;
        });

        it("handles custom className", () => {
            render(<ApproverListItem approver={mockApprovedApprover} className="custom-class" />);

            const listItem = screen.getByRole("listitem");
            expect(listItem).toHaveClass("custom-class");
        });

        it("handles custom spacing", () => {
            render(<ApproverListItem approver={mockApprovedApprover} spacing="mb-4" />);

            const listItem = screen.getByRole("listitem");
            expect(listItem).toHaveClass("mb-4");
        });
    });

    describe("Layout and Spacing", () => {
        it("maintains proper spacing between elements", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const listItem = screen.getByRole("listitem");
            expect(listItem).toHaveClass("relative", "flex", "flex-row", "items-start", "gap-2");
        });

        it("aligns content properly with avatar", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const avatarColumn = screen.getByTestId("avatar").closest(".w-10");
            expect(avatarColumn).toBeInTheDocument();
        });

        it("positions status on the far right of the list item", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const statusText = screen.getByText("Approved");
            const statusContainer = statusText.closest('[data-testid="status-container"]');

            expect(statusContainer).toBeInTheDocument();
            expect(statusContainer).toHaveClass("ml-auto"); // Should be pushed to the right
        });

        it("maintains proper layout with name, role, and status positioning", () => {
            render(<ApproverListItem approver={mockApprovedApprover} />);

            const contentContainer = screen.getByTestId("content-container");
            expect(contentContainer).toHaveClass("flex", "flex-col", "justify-between", "gap-[3px]", "flex-1");

            const topRow = screen.getByTestId("top-row");
            expect(topRow).toHaveClass("flex", "items-start", "justify-between", "gap-2", "w-full");
        });
    });

    describe("Sorting Behavior", () => {
        const createApproverList = () => {
            const approvers = [mockPendingApprover, mockDeclinedApprover, mockApprovedApprover];
            return approvers;
        };

        it("should expect approved approvers to be rendered first when properly sorted", () => {
            // This test validates that when given a properly sorted list,
            // approved approvers appear first
            const sortedApprovers = [mockApprovedApprover, mockDeclinedApprover, mockPendingApprover];

            const { rerender } = render(
                <ul>
                    {sortedApprovers.map((approver, index) => (
                        <ApproverListItem
                            key={approver.id}
                            approver={approver}
                            isLast={index === sortedApprovers.length - 1}
                        />
                    ))}
                </ul>
            );

            const listItems = screen.getAllByRole("listitem");

            // First item should be approved
            expect(listItems[0]).toHaveTextContent("Lamine Yamal");
            expect(listItems[0]).toHaveTextContent("Approved");

            // Second item should be declined
            expect(listItems[1]).toHaveTextContent("Mojeed Withaccount");
            expect(listItems[1]).toHaveTextContent("Declined");

            // Third item should be pending
            expect(listItems[2]).toHaveTextContent("Vin Petrol");
            expect(listItems[2]).toHaveTextContent("Pending approval");
        });

        it("should render approvers in the correct visual hierarchy regardless of input order", () => {
            // Note: Individual ApproverListItem doesn't sort, but should render correctly
            // when parent component provides proper sorting
            const unsortedApprovers = [mockPendingApprover, mockApprovedApprover, mockDeclinedApprover];

            render(
                <ul>
                    {unsortedApprovers.map((approver, index) => (
                        <ApproverListItem
                            key={approver.id}
                            approver={approver}
                            isLast={index === unsortedApprovers.length - 1}
                        />
                    ))}
                </ul>
            );

            // This test documents current behavior - the component renders in input order
            // The parent component should handle sorting before passing to ApproverListItem
            const listItems = screen.getAllByRole("listitem");

            // Items appear in the order they were passed (unsorted)
            expect(listItems[0]).toHaveTextContent("Vin Petrol"); // pending
            expect(listItems[1]).toHaveTextContent("Lamine Yamal"); // approved
            expect(listItems[2]).toHaveTextContent("Mojeed Withaccount"); // declined
        });
    });
});
