import React from "react";
import { render, screen } from "@testing-library/react";
import PermissionGate from "@/components/common/permission-gate";
import { usePermissions } from "@/contexts/PermissionContext";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import "@testing-library/jest-dom";

// Mock the PermissionContext
jest.mock("@/contexts/PermissionContext", () => ({
    usePermissions: jest.fn(),
}));

// Mock the usePermissionCheck hook
jest.mock("@/hooks/usePermissionCheck", () => ({
    usePermissionCheck: jest.fn(),
}));

describe("PermissionGate Component", () => {
    // Setup mock permission functions
    const mockHasPermission = jest.fn();
    const mockHasAnyPermission = jest.fn();
    const mockHasAllPermissions = jest.fn();
    const mockGetLoadingState = jest.fn();
    const mockGetErrorState = jest.fn();
    const mockPermissionExists = jest.fn();
    const mockIsSystemReady = jest.fn();

    beforeEach(() => {
        // Reset mocks before each test
        mockHasPermission.mockReset();
        mockHasAnyPermission.mockReset();
        mockHasAllPermissions.mockReset();
        mockGetLoadingState.mockReset();
        mockGetErrorState.mockReset();
        mockPermissionExists.mockReset();
        mockIsSystemReady.mockReset();

        // Setup default mock implementation for usePermissions
        usePermissions.mockReturnValue({
            hasPermission: mockHasPermission,
            hasAnyPermission: mockHasAnyPermission,
            hasAllPermissions: mockHasAllPermissions,
        });

        // Setup default mock implementation for usePermissionCheck
        usePermissionCheck.mockReturnValue({
            getLoadingState: mockGetLoadingState,
            getErrorState: mockGetErrorState,
            permissionExists: mockPermissionExists,
            isSystemReady: mockIsSystemReady,
        });

        // Default loading state - not loading
        mockGetLoadingState.mockReturnValue({
            isAnyLoading: false,
        });

        // Default error state - no errors
        mockGetErrorState.mockReturnValue({
            hasAnyError: false,
        });

        // Default permission exists check
        mockPermissionExists.mockReturnValue(true);
    });

    test("renders children when no permissions are specified", () => {
        render(
            <PermissionGate>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(screen.getByTestId("child-content")).toBeInTheDocument();
        expect(screen.getByText("Child Content")).toBeInTheDocument();
    });

    test("renders children when user has the specified permission", () => {
        mockHasPermission.mockReturnValue(true);

        render(
            <PermissionGate permission="TEST_PERMISSION">
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasPermission).toHaveBeenCalledWith("TEST_PERMISSION");
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    test("renders fallback when user doesn't have the specified permission", () => {
        mockHasPermission.mockReturnValue(false);

        render(
            <PermissionGate
                permission="TEST_PERMISSION"
                fallback={<div data-testid="fallback-content">No Permission</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasPermission).toHaveBeenCalledWith("TEST_PERMISSION");
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
    });

    test("renders children when user has any of the specified permissions and requireAll is false", () => {
        mockHasAnyPermission.mockReturnValue(true);

        render(
            <PermissionGate permissions={["PERM_1", "PERM_2"]} requireAll={false}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasAnyPermission).toHaveBeenCalledWith(["PERM_1", "PERM_2"]);
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    test("renders fallback when user doesn't have any of the specified permissions and requireAll is false", () => {
        mockHasAnyPermission.mockReturnValue(false);

        render(
            <PermissionGate
                permissions={["PERM_1", "PERM_2"]}
                requireAll={false}
                fallback={<div data-testid="fallback-content">No Permission</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasAnyPermission).toHaveBeenCalledWith(["PERM_1", "PERM_2"]);
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
    });

    test("renders children when user has all of the specified permissions and requireAll is true", () => {
        mockHasAllPermissions.mockReturnValue(true);

        render(
            <PermissionGate permissions={["PERM_1", "PERM_2"]} requireAll={true}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasAllPermissions).toHaveBeenCalledWith(["PERM_1", "PERM_2"]);
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    test("renders fallback when user doesn't have all of the specified permissions and requireAll is true", () => {
        mockHasAllPermissions.mockReturnValue(false);

        render(
            <PermissionGate
                permissions={["PERM_1", "PERM_2"]}
                requireAll={true}
                fallback={<div data-testid="fallback-content">No Permission</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasAllPermissions).toHaveBeenCalledWith(["PERM_1", "PERM_2"]);
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
    });

    test("renders null as fallback when no fallback is provided", () => {
        mockHasPermission.mockReturnValue(false);

        render(
            <PermissionGate permission="TEST_PERMISSION">
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockHasPermission).toHaveBeenCalledWith("TEST_PERMISSION");
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        // With no fallback, nothing should be rendered
    });

    test("shows loading state when permissions are loading", () => {
        mockGetLoadingState.mockReturnValue({
            isAnyLoading: true,
        });

        const { container } = render(
            <PermissionGate permission="TEST_PERMISSION">
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        // Should show loading indicator instead of content
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        // Look for the loading indicator by class
        const loadingDiv = container.querySelector(".animate-pulse");
        expect(loadingDiv).toBeInTheDocument();
    });

    test("shows error state when there are permission errors", () => {
        mockGetErrorState.mockReturnValue({
            hasAnyError: true,
        });

        render(
            <PermissionGate permission="TEST_PERMISSION">
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        // Should show error message instead of content
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
        expect(screen.getByText("Permission system error")).toBeInTheDocument();
    });

    test("shows custom loading fallback when provided", () => {
        mockGetLoadingState.mockReturnValue({
            isAnyLoading: true,
        });

        render(
            <PermissionGate
                permission="TEST_PERMISSION"
                loadingFallback={<div data-testid="custom-loading">Loading...</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(screen.getByTestId("custom-loading")).toBeInTheDocument();
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
    });

    test("shows custom error fallback when provided", () => {
        mockGetErrorState.mockReturnValue({
            hasAnyError: true,
        });

        render(
            <PermissionGate
                permission="TEST_PERMISSION"
                errorFallback={<div data-testid="custom-error">Custom Error</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(screen.getByTestId("custom-error")).toBeInTheDocument();
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
    });

    test("validates permissions exist in system when validatePermissions is true", () => {
        mockPermissionExists.mockReturnValue(false);

        render(
            <PermissionGate
                permission="INVALID_PERMISSION"
                validatePermissions={true}
                fallback={<div data-testid="fallback-content">No Permission</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockPermissionExists).toHaveBeenCalledWith("INVALID_PERMISSION");
        expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
        expect(screen.queryByTestId("child-content")).not.toBeInTheDocument();
    });

    test("shows development warning for invalid permissions in development mode", () => {
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = "development";

        mockPermissionExists.mockReturnValue(false);

        render(
            <PermissionGate permission="INVALID_PERMISSION" validatePermissions={true}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(screen.getByText(/Invalid permissions:/)).toBeInTheDocument();
        expect(screen.getByText(/INVALID_PERMISSION/)).toBeInTheDocument();

        process.env.NODE_ENV = originalEnv;
    });

    test("validates multiple permissions when validatePermissions is true", () => {
        mockPermissionExists.mockImplementation((perm) => perm === "VALID_PERMISSION");

        render(
            <PermissionGate
                permissions={["VALID_PERMISSION", "INVALID_PERMISSION"]}
                validatePermissions={true}
                fallback={<div data-testid="fallback-content">No Permission</div>}
            >
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockPermissionExists).toHaveBeenCalledWith("VALID_PERMISSION");
        expect(mockPermissionExists).toHaveBeenCalledWith("INVALID_PERMISSION");
        expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
    });

    test("renders children when all permissions are valid and validatePermissions is true", () => {
        mockPermissionExists.mockReturnValue(true);
        mockHasPermission.mockReturnValue(true);

        render(
            <PermissionGate permission="VALID_PERMISSION" validatePermissions={true}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockPermissionExists).toHaveBeenCalledWith("VALID_PERMISSION");
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    test("does not validate permissions when validatePermissions is false", () => {
        mockHasPermission.mockReturnValue(true);
        render(
            <PermissionGate permission="TEST_PERMISSION" validatePermissions={false}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockPermissionExists).not.toHaveBeenCalled();
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    test("handles empty permissions array with validatePermissions", () => {
        render(
            <PermissionGate permissions={[]} validatePermissions={true}>
                <div data-testid="child-content">Child Content</div>
            </PermissionGate>
        );

        expect(mockPermissionExists).not.toHaveBeenCalled();
        expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });
});
