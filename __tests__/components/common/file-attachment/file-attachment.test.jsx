import FileAttachment from "@/components/common/file-attachment";
import { sendFeedback } from "@/functions/feedback";
import "@testing-library/jest-dom";
import { act, fireEvent, render, screen } from "@testing-library/react";

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

describe("FileAttachment", () => {
    const mockOnFilesSelected = jest.fn();
    const mockOnUploadStateChange = jest.fn();
    const defaultProps = {
        onFilesSelected: mockOnFilesSelected,
        onUploadStateChange: mockOnUploadStateChange,
        maxSize: 10,
        acceptedTypes: ["pdf", "jpg"],
    };

    let mockAbort;
    let mockAbortController;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();

        // Setup AbortController mock with signal that properly tracks aborted state
        mockAbort = jest.fn(() => {
            mockAbortController.signal.aborted = true;
        });
        mockAbortController = {
            signal: { aborted: false },
            abort: mockAbort,
        };

        // Create a new AbortController instance for each test that shares the same signal reference
        global.AbortController = jest.fn(() => mockAbortController);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it("renders with default props", () => {
        render(<FileAttachment />);
        expect(screen.getByText("Attachments")).toBeInTheDocument();
        expect(screen.getByText(/We accept PDF, PNG, JPG, CSV files, up to 10MB/i)).toBeInTheDocument();
    });

    it("renders with custom props", () => {
        render(
            <FileAttachment
                headerText="Custom Header"
                descriptionText="Custom Description"
                maxSize={10}
                acceptedTypes={["pdf"]}
            />
        );
        expect(screen.getByText("Custom Header")).toBeInTheDocument();
        expect(screen.getByText("Custom Description")).toBeInTheDocument();
    });

    it("handles file selection through click", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(mockOnFilesSelected).toHaveBeenCalled();
        expect(mockOnFilesSelected.mock.calls[0][0]).toHaveLength(1);
        expect(mockOnFilesSelected.mock.calls[0][0][0]).toBe(file);
    });

    it("handles drag and drop", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const dropZone = screen.getByRole("button", { name: /Upload files/i });

        await act(async () => {
            fireEvent.dragEnter(dropZone);
            fireEvent.dragOver(dropZone);
            fireEvent.drop(dropZone, {
                dataTransfer: {
                    files: [file],
                },
            });
        });

        expect(mockOnFilesSelected).toHaveBeenCalled();
        expect(mockOnFilesSelected.mock.calls[0][0]).toHaveLength(1);
        expect(mockOnFilesSelected.mock.calls[0][0][0]).toBe(file);
    });

    it("shows upload progress based on file size", async () => {
        render(<FileAttachment {...defaultProps} />);

        // Create a 2MB file
        const file = new File([new ArrayBuffer(2 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(screen.getByText("test.pdf")).toBeInTheDocument();
        expect(screen.getByText(/2 sec left/)).toBeInTheDocument();

        // Advance timer by 1 second (10 intervals of 100ms)
        await act(async () => {
            for (let i = 0; i < 10; i++) {
                jest.advanceTimersByTime(100);
            }
        });

        // Should be about halfway through the upload
        expect(screen.getByText(/1 sec left/)).toBeInTheDocument();

        // Complete the upload
        await act(async () => {
            jest.advanceTimersByTime(1000);
        });

        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });

    it("handles small files with minimum upload time", async () => {
        render(<FileAttachment {...defaultProps} />);

        // Create a tiny 10KB file
        const file = new File([new ArrayBuffer(10 * 1024)], "small.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(screen.getByText("small.pdf")).toBeInTheDocument();
        // Should still take minimum 1 second
        expect(screen.getByText(/1 sec left/)).toBeInTheDocument();

        // Complete the upload (minimum 1 second)
        await act(async () => {
            jest.advanceTimersByTime(1000);
        });

        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });

    it("completes upload based on file size", async () => {
        render(<FileAttachment {...defaultProps} />);

        // Create a 10MB file
        const file = new File([new ArrayBuffer(5 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            // Advance time to complete upload (5 seconds for 5MB)
            jest.advanceTimersByTime(5000);
        });

        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });

    it("validates file type", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.txt", { type: "text/plain" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(sendFeedback).toHaveBeenCalledWith("Only pdf, jpg files are supported", "error");
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("validates file size", async () => {
        render(<FileAttachment {...defaultProps} />);

        // Create a file larger than maxSize (10MB)
        const largeFile = new File([new ArrayBuffer(11 * 1024 * 1024)], "large.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [largeFile] } });
        });

        expect(sendFeedback).toHaveBeenCalledWith("File size must be less than 10MB", "error");
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("allows canceling upload", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        const cancelButton = screen.getByLabelText("Cancel upload");
        fireEvent.click(cancelButton);

        expect(screen.queryByText("test.pdf")).not.toBeInTheDocument();
    });

    it("handles keyboard interaction for file selection", async () => {
        render(<FileAttachment {...defaultProps} />);
        const uploadButton = screen.getByRole("button", { name: /Upload files/i });
        const input = screen.getByLabelText(/Choose a file/i);

        // Mock the click handler since we can't trigger the file picker in tests
        const clickSpy = jest.spyOn(input, "click");

        // Test Enter key
        fireEvent.keyDown(uploadButton, { key: "Enter" });
        expect(clickSpy).toHaveBeenCalled();
        clickSpy.mockClear();

        // Test Space key
        fireEvent.keyDown(uploadButton, { key: " " });
        expect(clickSpy).toHaveBeenCalled();

        // Test that other keys don't trigger file selection
        clickSpy.mockClear();
        fireEvent.keyDown(uploadButton, { key: "A" });
        expect(clickSpy).not.toHaveBeenCalled();
    });

    it("aborts upload when cancel is clicked", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            // Let the upload start
            jest.advanceTimersByTime(100);
        });

        const cancelButton = screen.getByLabelText("Cancel upload");

        await act(async () => {
            fireEvent.click(cancelButton);
        });

        expect(mockAbort).toHaveBeenCalled();
        expect(screen.queryByText("test.pdf")).not.toBeInTheDocument();
    });

    it("cleans up upload state when signal is aborted", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            // Let the upload start and create its interval
            jest.advanceTimersByTime(100);
        });

        expect(screen.getByText("test.pdf")).toBeInTheDocument();

        // Trigger abort through the mock function
        mockAbortController.abort();

        // Let the interval detect the aborted signal
        await act(async () => {
            jest.advanceTimersByTime(100);
        });

        // Verify upload state is cleaned up
        expect(screen.queryByText("test.pdf")).not.toBeInTheDocument();
    });

    it("calls onUploadStateChange with correct states during upload", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File([new ArrayBuffer(2 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        // Should be called with false when upload starts
        expect(mockOnUploadStateChange).toHaveBeenCalledWith(false);

        // Complete the upload
        await act(async () => {
            jest.advanceTimersByTime(2000);
        });

        // Should be called with true when upload completes
        expect(mockOnUploadStateChange).toHaveBeenCalledWith(true);
    });

    it("calls onUploadStateChange with false when upload is cancelled", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File([new ArrayBuffer(2 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(100);
        });

        const cancelButton = screen.getByLabelText("Cancel upload");

        await act(async () => {
            fireEvent.click(cancelButton);
        });

        // Should be called with false when upload is cancelled
        expect(mockOnUploadStateChange).toHaveBeenLastCalledWith(false);
    });

    it("cleans up input value when canceling upload", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(100);
        });

        const cancelButton = screen.getByLabelText("Cancel upload");

        await act(async () => {
            fireEvent.click(cancelButton);
        });

        expect(input.value).toBe("");
        expect(mockOnUploadStateChange).toHaveBeenLastCalledWith(false);
        expect(screen.queryByText("test.pdf")).not.toBeInTheDocument();
    });

    // NEW TESTS TO INCREASE BRANCH COVERAGE

    it("handles empty file list", async () => {
        render(<FileAttachment {...defaultProps} />);
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [] } });
        });

        // Should not call onFilesSelected when no files are selected
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("handles file without extension", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "testfile", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(sendFeedback).toHaveBeenCalledWith("Only pdf, jpg files are supported", "error");
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("handles file with empty extension", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "testfile.", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(sendFeedback).toHaveBeenCalledWith("Only pdf, jpg files are supported", "error");
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("aborts existing upload when starting new upload (tests uploadControllerRef.current branch)", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file1 = new File(["test1"], "test1.pdf", { type: "application/pdf" });
        const file2 = new File(["test2"], "test2.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Start first upload
        await act(async () => {
            fireEvent.change(input, { target: { files: [file1] } });
            jest.advanceTimersByTime(100);
        });

        // Verify first upload is in progress
        expect(screen.getByText(/sec left/)).toBeInTheDocument();

        // Start second upload - this should abort the first one
        await act(async () => {
            fireEvent.change(input, { target: { files: [file2] } });
        });

        // Verify that a new upload has started (progress should reset)
        expect(screen.getByText(/sec left/)).toBeInTheDocument();
    });

    it("handles onFileRemoved callback when canceling upload", async () => {
        const mockOnFileRemoved = jest.fn();
        render(<FileAttachment {...defaultProps} onFileRemoved={mockOnFileRemoved} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(100);
        });

        const cancelButton = screen.getByLabelText("Cancel upload");

        await act(async () => {
            fireEvent.click(cancelButton);
        });

        expect(mockOnFileRemoved).toHaveBeenCalled();
    });

    it("handles missing onFilesSelected callback", async () => {
        render(<FileAttachment maxSize={10} acceptedTypes={["pdf"]} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Should not throw error when onFilesSelected is not provided
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        expect(screen.getByText("test.pdf")).toBeInTheDocument();
    });

    it("handles missing onUploadStateChange callback", async () => {
        render(<FileAttachment maxSize={10} acceptedTypes={["pdf"]} onFilesSelected={mockOnFilesSelected} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Should not throw error when onUploadStateChange is not provided
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(1000);
        });

        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });

    it("handles missing onFileRemoved callback", async () => {
        render(<FileAttachment {...defaultProps} onFileRemoved={undefined} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(100);
        });

        const cancelButton = screen.getByLabelText("Cancel upload");

        // Should not throw error when onFileRemoved is not provided
        await act(async () => {
            fireEvent.click(cancelButton);
        });

        expect(screen.queryByText("test.pdf")).not.toBeInTheDocument();
    });

    it("handles null target.files in handleFileChange", async () => {
        render(<FileAttachment {...defaultProps} />);
        const input = screen.getByLabelText(/Choose a file/i);

        // Simulate the case where target.files is null
        await act(async () => {
            fireEvent.change(input, { target: { files: null } });
        });

        // Should not call onFilesSelected when files is null
        expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });

    it("handles setUploadState null branches during upload completion", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Start upload and let it complete normally
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            // Complete the upload to test the completion branch
            jest.advanceTimersByTime(1100);
        });

        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });

    it("tests inputRef.current branch in handleCancel", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Set a value on the input to test the clearing branch
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
            jest.advanceTimersByTime(100);
        });

        // Verify input has a value (file selected)
        expect(input.files).toHaveLength(1);

        const cancelButton = screen.getByLabelText("Cancel upload");
        await act(async () => {
            fireEvent.click(cancelButton);
        });

        // Verify input value was cleared
        expect(input.value).toBe("");
    });

    it("tests uploadControllerRef.current existence by rapid file changes", async () => {
        render(<FileAttachment {...defaultProps} />);

        const file1 = new File(["test1"], "test1.pdf", { type: "application/pdf" });
        const file2 = new File(["test2"], "test2.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        // Start first upload
        await act(async () => {
            fireEvent.change(input, { target: { files: [file1] } });
        });

        // Immediately start second upload before first completes
        // This should trigger the uploadControllerRef.current branch
        await act(async () => {
            fireEvent.change(input, { target: { files: [file2] } });
        });

        // Verify an upload is in progress (this tests that the component handled the rapid change)
        expect(screen.getByText(/sec left/)).toBeInTheDocument();
    });

    describe("Initial File Handling", () => {
        it("renders with initial file in complete state", () => {
            const initialFile = new File(["test"], "initial.pdf", { type: "application/pdf" });
            render(<FileAttachment initialFile={initialFile} />);

            expect(screen.getByText("initial.pdf")).toBeInTheDocument();
            expect(screen.getByText("Upload complete")).toBeInTheDocument();
            expect(screen.getByText("0.0 KB")).toBeInTheDocument();
        });

        it("renders with storedFile when initialFile is not provided", () => {
            const storedFile = { name: "stored.pdf", size: 1024 };
            render(<FileAttachment storedFile={storedFile} />);

            expect(screen.getByText("stored.pdf")).toBeInTheDocument();
            expect(screen.getByText("Upload complete")).toBeInTheDocument();
            expect(screen.getByText("1.0 KB")).toBeInTheDocument();
        });

        it("prioritizes initialFile over storedFile when both are provided", () => {
            const initialFile = new File(["test"], "initial.pdf", { type: "application/pdf" });
            const storedFile = { name: "stored.pdf", size: 1024 };
            render(<FileAttachment initialFile={initialFile} storedFile={storedFile} />);

            expect(screen.getByText("initial.pdf")).toBeInTheDocument();
            expect(screen.queryByText("stored.pdf")).not.toBeInTheDocument();
        });

        it("does not set upload state when uploadState already exists", () => {
            const mockOnUploadStateChange = jest.fn();
            const initialFile = new File(["test"], "initial.pdf", { type: "application/pdf" });

            // First render to establish upload state
            const { rerender } = render(
                <FileAttachment initialFile={initialFile} onUploadStateChange={mockOnUploadStateChange} />
            );

            // Clear the mock to track subsequent calls
            mockOnUploadStateChange.mockClear();

            // Re-render with same props - should not call onUploadStateChange again
            rerender(<FileAttachment initialFile={initialFile} onUploadStateChange={mockOnUploadStateChange} />);

            expect(mockOnUploadStateChange).not.toHaveBeenCalled();
        });
    });

    describe("Style and Layout", () => {
        it("applies custom width", () => {
            render(<FileAttachment width="400px" />);
            // Get the root div element which should have the width style
            const container = screen.getByTestId("file-attachment-root");
            expect(container).toHaveAttribute("style", expect.stringContaining("width: 400px"));
        });

        it("handles custom icons", () => {
            const CustomIcon = () => <div data-testid="custom-icon">Custom</div>;
            const CustomProgressIcon = () => <div data-testid="custom-progress-icon">Progress</div>;

            render(
                <FileAttachment
                    icon={<CustomIcon />}
                    progressIcon={<CustomProgressIcon />}
                    initialFile={new File(["test"], "test.pdf", { type: "application/pdf" })}
                />
            );

            expect(screen.getByTestId("custom-progress-icon")).toBeInTheDocument();
        });
    });

    describe("Drag and Drop", () => {
        it("handles drag and drop interactions correctly", () => {
            render(<FileAttachment {...defaultProps} />);
            const dropZone = screen.getByRole("button", { name: /Upload files/i });

            // Helper function to normalize className string
            const getClasses = (element) => element.className.split(/\s+/).filter(Boolean);

            // Initial state should have bg-white
            expect(getClasses(dropZone)).toContain("bg-white");

            // Test drag enter
            fireEvent.dragEnter(dropZone);
            const dragEnterClasses = getClasses(dropZone);
            expect(dragEnterClasses).toContain("bg-[#F9F9FA]");
            expect(dragEnterClasses).not.toContain("bg-white");

            // Test drag over
            fireEvent.dragOver(dropZone);
            const dragOverClasses = getClasses(dropZone);
            expect(dragOverClasses).toContain("bg-[#F9F9FA]");

            // Test drag leave
            fireEvent.dragLeave(dropZone);
            const dragLeaveClasses = getClasses(dropZone);
            expect(dragLeaveClasses).toContain("bg-white");
            expect(dragLeaveClasses).not.toContain("bg-[#F9F9FA]");
        });
    });

    // Test for correct simulation interval calculation (line 149)
    it("calculates upload simulation interval correctly", async () => {
        render(<FileAttachment {...defaultProps} />);

        // Create a file with exact size to test interval calculation
        const file = new File([new ArrayBuffer(2 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });

            // Let a few intervals pass to verify the increments
            jest.advanceTimersByTime(400); // 4 intervals at 100ms each
        });

        // After 4 updates (0% -> 5% -> 10% -> 15% -> 20%), should be around 20%
        const progressText = screen.getByText(/\d+%/);
        const progressValue = parseInt(progressText.textContent);
        expect(progressValue).toBeGreaterThanOrEqual(18);
        expect(progressValue).toBeLessThanOrEqual(22);
    });

    // Specific test for the updateInterval and progress calculation (line 162)
    it("handles progress updates and completion correctly", async () => {
        jest.spyOn(global, "setInterval");
        jest.spyOn(global, "clearInterval");

        render(<FileAttachment {...defaultProps} />);

        const file = new File([new ArrayBuffer(1 * 1024 * 1024)], "test.pdf", { type: "application/pdf" });
        const input = screen.getByLabelText(/Choose a file/i);

        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });

        // Check that setInterval was called with the correct interval
        expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 100);

        // Complete the upload and verify clearInterval is called
        await act(async () => {
            jest.advanceTimersByTime(1100); // Full upload time + a bit more
        });

        expect(clearInterval).toHaveBeenCalled();
        expect(screen.getByText("Upload complete")).toBeInTheDocument();
    });
});
