/* eslint-disable quotes */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import TermsCheckbox from "@/components/common/terms-checkbox";

// Mock React.useId for consistent testing
jest.mock("react", () => ({
    ...jest.requireActual("react"),
    useId: () => "test-id",
}));

describe("TermsCheckbox", () => {
    // Test that the component renders correctly
    it("renders the checkbox with terms and privacy policy links", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        // Check if checkbox is rendered by data-testid
        expect(screen.getByTestId("checkbox")).toBeInTheDocument();

        // Check if the text content is present
        expect(screen.getByText(/I agree to FCMB's/)).toBeInTheDocument();

        // Check if links are present
        const termsLink = screen.getByRole("link", { name: "Terms of Service" });
        const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

        expect(termsLink).toBeInTheDocument();
        expect(privacyLink).toBeInTheDocument();

        // Check if links have correct attributes - THIS SHOULD FAIL INITIALLY
        // Terms of Service should use local PDF, not external URL
        expect(termsLink).toHaveAttribute(
            "href",
            "/documents/FCMB Corporate Internet Banking – User Terms and Conditions.pdf"
        );
        expect(termsLink).toHaveAttribute("target", "_blank");
        expect(privacyLink).toHaveAttribute("href", "/documents/FCMB-Corporate-Internet-Banking-Privacy-Policy.pdf");
        expect(privacyLink).toHaveAttribute("target", "_blank");
    });

    // Test checkbox state changes when clicking the checkbox
    it("calls onCheckedChange when checkbox is clicked", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        const checkbox = screen.getByTestId("checkbox");
        fireEvent.click(checkbox);

        expect(mockOnChange).toHaveBeenCalledWith(true);
    });

    // Test checkbox state changes when clicking the input element directly
    it("calls onCheckedChange when clicking the checkbox input directly", () => {
        const mockOnChange = jest.fn();
        const { container } = render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        const checkboxInput = container.querySelector('input[type="checkbox"]');
        fireEvent.click(checkboxInput);

        expect(mockOnChange).toHaveBeenCalledWith(true);
    });

    // Test checkbox state changes when clicking the label text
    it("calls onCheckedChange when clicking the label text", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        const labelText = screen.getByText(/I agree to FCMB's/);
        fireEvent.click(labelText);

        expect(mockOnChange).toHaveBeenCalledWith(true);
    });

    // Test checkbox toggles correctly
    it("toggles checkbox state correctly on multiple clicks", () => {
        const mockOnChange = jest.fn();
        const { rerender } = render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        // First click - should call with true
        const checkbox = screen.getByTestId("checkbox");
        fireEvent.click(checkbox);
        expect(mockOnChange).toHaveBeenLastCalledWith(true);

        // Simulate state update
        rerender(<TermsCheckbox checked={true} onCheckedChange={mockOnChange} />);

        // Second click - should call with false
        fireEvent.click(checkbox);
        expect(mockOnChange).toHaveBeenLastCalledWith(false);
    });

    // Test that clicking links doesn't toggle the checkbox
    it("does not toggle checkbox when clicking on links", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        const termsLink = screen.getByRole("link", { name: "Terms of Service" });
        const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

        // Click on terms link
        fireEvent.click(termsLink);
        expect(mockOnChange).not.toHaveBeenCalled();

        // Click on privacy link
        fireEvent.click(privacyLink);
        expect(mockOnChange).not.toHaveBeenCalled();
    });

    // Test error state
    it("displays error message when error prop is true", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} error={true} />);

        expect(screen.getByText("You must agree to the terms to continue")).toBeInTheDocument();
    });

    // Test that error message is not shown when error is false
    it("does not display error message when error prop is false", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} error={false} />);

        expect(screen.queryByText("You must agree to the terms to continue")).not.toBeInTheDocument();
    });

    // Test checked state
    it("reflects the checked state correctly", () => {
        const mockOnChange = jest.fn();
        const { rerender, container } = render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        // Get the actual input element by type
        const checkbox = container.querySelector('input[type="checkbox"]');
        expect(checkbox).not.toBeChecked();

        rerender(<TermsCheckbox checked={true} onCheckedChange={mockOnChange} />);
        expect(checkbox).toBeChecked();
    });

    // Test className prop
    it("applies custom className", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} className="custom-class" />);

        // Get the outermost container div
        const container = screen.getByTestId("checkbox").closest(".flex")?.parentElement;
        expect(container).toHaveClass("custom-class");
    });

    // Test link styling
    it("renders links with correct styling", () => {
        const mockOnChange = jest.fn();
        render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

        const termsLink = screen.getByRole("link", { name: "Terms of Service" });
        const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

        expect(termsLink).toHaveClass("text-primary", "underline", "hover:no-underline");
        expect(privacyLink).toHaveClass("text-primary", "underline", "hover:no-underline");
    });

    // NEW TESTS FOR PDF NAVIGATION BEHAVIOR - THESE SHOULD INITIALLY FAIL
    describe("PDF Navigation Tests", () => {
        // Test that Terms of Service link doesn't trigger navigation to login page
        it("should not cause navigation to login page when clicking Terms of Service link", () => {
            // Mock window.open to prevent actual navigation during test
            const originalOpen = window.open;
            const mockOpen = jest.fn();
            window.open = mockOpen;

            // Mock useRouter to track navigation attempts
            const mockPush = jest.fn();
            const mockReplace = jest.fn();
            jest.mock("next/navigation", () => ({
                useRouter: () => ({
                    push: mockPush,
                    replace: mockReplace,
                    back: jest.fn(),
                    forward: jest.fn(),
                    refresh: jest.fn(),
                }),
            }));

            const mockOnChange = jest.fn();
            render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

            const termsLink = screen.getByRole("link", { name: "Terms of Service" });

            // Click the Terms of Service link
            fireEvent.click(termsLink);

            // Verify that no navigation to login occurred
            expect(mockPush).not.toHaveBeenCalledWith(expect.stringContaining("/auth/login"));
            expect(mockReplace).not.toHaveBeenCalledWith(expect.stringContaining("/auth/login"));

            // Restore original window.open
            window.open = originalOpen;
        });

        // Test that Privacy Policy link doesn't trigger navigation to login page
        it("should not cause navigation to login page when clicking Privacy Policy link", () => {
            // Mock window.open to prevent actual navigation during test
            const originalOpen = window.open;
            const mockOpen = jest.fn();
            window.open = mockOpen;

            // Mock useRouter to track navigation attempts
            const mockPush = jest.fn();
            const mockReplace = jest.fn();
            jest.mock("next/navigation", () => ({
                useRouter: () => ({
                    push: mockPush,
                    replace: mockReplace,
                    back: jest.fn(),
                    forward: jest.fn(),
                    refresh: jest.fn(),
                }),
            }));

            const mockOnChange = jest.fn();
            render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

            const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

            // Click the Privacy Policy link
            fireEvent.click(privacyLink);

            // Verify that no navigation to login occurred
            expect(mockPush).not.toHaveBeenCalledWith(expect.stringContaining("/auth/login"));
            expect(mockReplace).not.toHaveBeenCalledWith(expect.stringContaining("/auth/login"));

            // Restore original window.open
            window.open = originalOpen;
        });

        // Test that PDFs open in new tab without affecting current page navigation
        it("should open Terms of Service PDF in new tab without affecting current page", () => {
            const mockOnChange = jest.fn();
            render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

            const termsLink = screen.getByRole("link", { name: "Terms of Service" });

            // Verify the link has target="_blank" to open in new tab
            expect(termsLink).toHaveAttribute("target", "_blank");
            expect(termsLink).toHaveAttribute("rel", "noopener noreferrer");

            // Verify the href points to local PDF
            expect(termsLink).toHaveAttribute(
                "href",
                "/documents/FCMB Corporate Internet Banking – User Terms and Conditions.pdf"
            );
        });

        // Test that Privacy Policy PDF opens in new tab without affecting current page navigation
        it("should open Privacy Policy PDF in new tab without affecting current page", () => {
            const mockOnChange = jest.fn();
            render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

            const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

            // Verify the link has target="_blank" to open in new tab
            expect(privacyLink).toHaveAttribute("target", "_blank");
            expect(privacyLink).toHaveAttribute("rel", "noopener noreferrer");

            // Verify the href points to local PDF
            expect(privacyLink).toHaveAttribute(
                "href",
                "/documents/FCMB-Corporate-Internet-Banking-Privacy-Policy.pdf"
            );
        });

        // Test that clicking links doesn't interfere with form state
        it("should not interfere with form state when clicking PDF links", () => {
            const mockOnChange = jest.fn();
            render(<TermsCheckbox checked={false} onCheckedChange={mockOnChange} />);

            const termsLink = screen.getByRole("link", { name: "Terms of Service" });
            const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });

            // Click both links
            fireEvent.click(termsLink);
            fireEvent.click(privacyLink);

            // Verify checkbox state hasn't changed
            expect(mockOnChange).not.toHaveBeenCalled();
        });
    });
});
