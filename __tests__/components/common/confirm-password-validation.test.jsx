import React from "react";
import { render, screen } from "@testing-library/react";
import ConfirmPasswordValidation from "@/components/common/confirm-password-validation";

// Mock the Tick icon component
jest.mock("@/components/icons/bill-payment-icons", () => ({
    Tick: ({ color }) => (
        <div data-testid="tick-icon" style={{ color }}>
            ✓
        </div>
    ),
}));

describe("ConfirmPasswordValidation Component", () => {
    it("renders nothing when isVisible is false", () => {
        render(<ConfirmPasswordValidation password="test123" confirmPassword="test123" isVisible={false} />);
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("renders nothing when confirmPassword is empty", () => {
        render(<ConfirmPasswordValidation password="test123" confirmPassword="" isVisible={true} />);
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("shows success message when passwords match", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass123!" confirmPassword="StrongPass123!" isVisible={true} />
        );

        expect(screen.getByText("Passwords match")).toBeInTheDocument();
        expect(screen.getByTestId("tick-icon")).toBeInTheDocument();
        // Check that the parent div has the correct classes
        const successDiv = screen.getByText("Passwords match").closest("div");
        expect(successDiv).toHaveClass("text-[#039855]", "font-medium", "text-sm");
    });

    it("shows nothing when passwords do not match", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass123!" confirmPassword="DifferentPass123!" isVisible={true} />
        );

        expect(screen.queryByText("Passwords must match")).not.toBeInTheDocument();
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("handles case sensitivity correctly", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass123!" confirmPassword="strongpass123!" isVisible={true} />
        );

        expect(screen.queryByText("Passwords must match")).not.toBeInTheDocument();
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("handles whitespace differences correctly", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass123!" confirmPassword=" StrongPass123! " isVisible={true} />
        );

        expect(screen.queryByText("Passwords must match")).not.toBeInTheDocument();
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("shows success for exact match with special characters", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass@123!" confirmPassword="StrongPass@123!" isVisible={true} />
        );

        expect(screen.getByText("Passwords match")).toBeInTheDocument();
        expect(screen.getByTestId("tick-icon")).toBeInTheDocument();
        // Check that the parent div has the correct classes
        const successDiv = screen.getByText("Passwords match").closest("div");
        expect(successDiv).toHaveClass("text-[#039855]", "font-medium", "text-sm");
    });

    it("shows nothing for partial match", () => {
        render(
            <ConfirmPasswordValidation password="StrongPass123!" confirmPassword="StrongPass123" isVisible={true} />
        );

        expect(screen.queryByText("Passwords must match")).not.toBeInTheDocument();
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("handles empty password with non-empty confirm password", () => {
        render(<ConfirmPasswordValidation password="" confirmPassword="test123" isVisible={true} />);

        expect(screen.queryByText("Passwords must match")).not.toBeInTheDocument();
        expect(screen.queryByText("Passwords match")).not.toBeInTheDocument();
    });

    it("handles very long passwords correctly", () => {
        const longPassword = "a".repeat(100) + "A1!";
        render(<ConfirmPasswordValidation password={longPassword} confirmPassword={longPassword} isVisible={true} />);

        expect(screen.getByText("Passwords match")).toBeInTheDocument();
        expect(screen.getByTestId("tick-icon")).toBeInTheDocument();
        // Check that the parent div has the correct classes
        const successDiv = screen.getByText("Passwords match").closest("div");
        expect(successDiv).toHaveClass("text-[#039855]", "font-medium", "text-sm");
    });

    it("handles passwords with unicode characters", () => {
        render(
            <ConfirmPasswordValidation
                password="StrongPass123!🚀"
                confirmPassword="StrongPass123!🚀"
                isVisible={true}
            />
        );

        expect(screen.getByText("Passwords match")).toBeInTheDocument();
        expect(screen.getByTestId("tick-icon")).toBeInTheDocument();
        // Check that the parent div has the correct classes
        const successDiv = screen.getByText("Passwords match").closest("div");
        expect(successDiv).toHaveClass("text-[#039855]", "font-medium", "text-sm");
    });
});
