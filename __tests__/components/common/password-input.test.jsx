import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Formik } from "formik";
import * as yup from "yup";
import PasswordInput from "@/components/common/password-input";

// Mock the validation components
jest.mock("@/components/common/password-validation", () => {
    return function MockPasswordValidation({ password, isVisible }) {
        if (!isVisible) return null;
        return (
            <div data-testid="password-validation">
                <span>Password validation for: {password}</span>
            </div>
        );
    };
});

jest.mock("@/components/common/confirm-password-validation", () => {
    return function MockConfirmPasswordValidation({ password, confirmPassword, isVisible }) {
        if (!isVisible) return null;
        return (
            <div data-testid="confirm-password-validation">
                <span>
                    Confirm validation: {password} vs {confirmPassword}
                </span>
            </div>
        );
    };
});

// Mock LabelInput component
jest.mock("@/components/common/label-input", () => {
    return function MockLabelInput({ formik, name, label, onFocus, onBlur, ...props }) {
        return (
            <div>
                <label htmlFor={name}>{label}</label>
                <input
                    id={name}
                    name={name}
                    value={formik?.values[name] || ""}
                    onChange={(e) => formik?.setFieldValue(name, e.target.value)}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    {...props}
                />
            </div>
        );
    };
});

const validationSchema = yup.object({
    password: yup.string().required("Password is required"),
    confirmPassword: yup.string().required("Confirm password is required"),
});

const TestWrapper = ({ children }) => (
    <Formik
        initialValues={{ password: "", confirmPassword: "" }}
        validationSchema={validationSchema}
        onSubmit={() => {}}
    >
        {children}
    </Formik>
);

describe("PasswordInput Component", () => {
    it("renders password input with label", () => {
        render(
            <TestWrapper>
                {(formik) => <PasswordInput formik={formik} passwordFieldName="password" label="Test Password" />}
            </TestWrapper>
        );

        expect(screen.getByLabelText("Test Password")).toBeInTheDocument();
    });

    it("renders confirm password input when confirmPasswordFieldName is provided", () => {
        render(
            <TestWrapper>
                {(formik) => (
                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                        confirmLabel="Confirm Test Password"
                    />
                )}
            </TestWrapper>
        );

        expect(screen.getByLabelText("Password")).toBeInTheDocument();
        expect(screen.getByLabelText("Confirm Test Password")).toBeInTheDocument();
    });

    it("does not render confirm password input when confirmPasswordFieldName is not provided", () => {
        render(<TestWrapper>{(formik) => <PasswordInput formik={formik} passwordFieldName="password" />}</TestWrapper>);

        expect(screen.getByLabelText("Password")).toBeInTheDocument();
        expect(screen.queryByLabelText("Confirm password")).not.toBeInTheDocument();
    });

    it("shows password validation when password input is focused", () => {
        render(<TestWrapper>{(formik) => <PasswordInput formik={formik} passwordFieldName="password" />}</TestWrapper>);

        const passwordInput = screen.getByLabelText("Password");
        fireEvent.focus(passwordInput);

        expect(screen.getByTestId("password-validation")).toBeInTheDocument();
    });

    it("shows password validation when password has content", () => {
        render(<TestWrapper>{(formik) => <PasswordInput formik={formik} passwordFieldName="password" />}</TestWrapper>);

        const passwordInput = screen.getByLabelText("Password");
        fireEvent.change(passwordInput, { target: { value: "test123" } });

        expect(screen.getByTestId("password-validation")).toBeInTheDocument();
    });

    it("shows confirm password validation when confirm password input is focused", () => {
        render(
            <TestWrapper>
                {(formik) => (
                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                    />
                )}
            </TestWrapper>
        );

        const confirmPasswordInput = screen.getByLabelText("Confirm password");
        fireEvent.focus(confirmPasswordInput);

        expect(screen.getByTestId("confirm-password-validation")).toBeInTheDocument();
    });

    it("shows confirm password validation when confirm password has content", () => {
        render(
            <TestWrapper>
                {(formik) => (
                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                    />
                )}
            </TestWrapper>
        );

        const confirmPasswordInput = screen.getByLabelText("Confirm password");
        fireEvent.change(confirmPasswordInput, { target: { value: "test123" } });

        expect(screen.getByTestId("confirm-password-validation")).toBeInTheDocument();
    });

    it("hides password validation when password input loses focus and has no content", () => {
        render(<TestWrapper>{(formik) => <PasswordInput formik={formik} passwordFieldName="password" />}</TestWrapper>);

        const passwordInput = screen.getByLabelText("Password");
        fireEvent.focus(passwordInput);
        expect(screen.getByTestId("password-validation")).toBeInTheDocument();

        fireEvent.blur(passwordInput);
        expect(screen.queryByTestId("password-validation")).not.toBeInTheDocument();
    });

    it("keeps password validation visible when password input loses focus but has content", () => {
        render(<TestWrapper>{(formik) => <PasswordInput formik={formik} passwordFieldName="password" />}</TestWrapper>);

        const passwordInput = screen.getByLabelText("Password");
        fireEvent.change(passwordInput, { target: { value: "test123" } });
        fireEvent.focus(passwordInput);
        fireEvent.blur(passwordInput);

        expect(screen.getByTestId("password-validation")).toBeInTheDocument();
    });

    it("disables validation when showValidation is false", () => {
        render(
            <TestWrapper>
                {(formik) => (
                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                        showValidation={false}
                    />
                )}
            </TestWrapper>
        );

        const passwordInput = screen.getByLabelText("Password");
        const confirmPasswordInput = screen.getByLabelText("Confirm password");

        fireEvent.focus(passwordInput);
        fireEvent.focus(confirmPasswordInput);

        expect(screen.queryByTestId("password-validation")).not.toBeInTheDocument();
        expect(screen.queryByTestId("confirm-password-validation")).not.toBeInTheDocument();
    });

    it("passes correct password value to validation components", () => {
        render(
            <TestWrapper>
                {(formik) => (
                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                    />
                )}
            </TestWrapper>
        );

        const passwordInput = screen.getByLabelText("Password");
        const confirmPasswordInput = screen.getByLabelText("Confirm password");

        fireEvent.change(passwordInput, { target: { value: "test123" } });
        fireEvent.change(confirmPasswordInput, { target: { value: "test456" } });

        fireEvent.focus(passwordInput);
        fireEvent.focus(confirmPasswordInput);

        expect(screen.getByText("Password validation for: test123")).toBeInTheDocument();
        expect(screen.getByText("Confirm validation: test123 vs test456")).toBeInTheDocument();
    });
});
