import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuCheckboxItem,
    DropdownMenuRadioItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuGroup,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuRadioGroup,
} from "@/components/common/dropdown-menu";

// Mock the cn utility function
jest.mock("@/lib/utils", () => ({
    cn: (...classes) => classes.filter(Boolean).join(" "),
}));

describe("DropdownMenu Components", () => {
    describe("Basic DropdownMenu", () => {
        it("renders dropdown menu with trigger and content", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>Item 1</DropdownMenuItem>
                        <DropdownMenuItem>Item 2</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            expect(screen.getByText("Open Menu")).toBeInTheDocument();

            // Click to open the dropdown
            await user.click(screen.getByText("Open Menu"));

            await waitFor(() => {
                expect(screen.getByText("Item 1")).toBeInTheDocument();
                expect(screen.getByText("Item 2")).toBeInTheDocument();
            });
        });
    });

    describe("DropdownMenuItem", () => {
        it("renders menu item with custom className", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem className="custom-class">Custom Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const menuItem = screen.getByText("Custom Item");
                expect(menuItem).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(menuItem.closest('[class*="custom-class"]')).toBeInTheDocument();
            });
        });

        it("renders menu item with inset prop", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem inset>Inset Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const menuItem = screen.getByText("Inset Item");
                expect(menuItem).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(menuItem.closest('[class*="pl-8"]')).toBeInTheDocument();
            });
        });

        it("handles click events", async () => {
            const handleClick = jest.fn();
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem onClick={handleClick}>Clickable Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                expect(screen.getByText("Clickable Item")).toBeInTheDocument();
            });

            await user.click(screen.getByText("Clickable Item"));
            expect(handleClick).toHaveBeenCalledTimes(1);
        });
    });

    describe("DropdownMenuCheckboxItem", () => {
        it("renders checkbox item with check indicator when checked", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuCheckboxItem checked>Checked Item</DropdownMenuCheckboxItem>
                        <DropdownMenuCheckboxItem checked={false}>Unchecked Item</DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const checkedItem = screen.getByText("Checked Item");
                const uncheckedItem = screen.getByText("Unchecked Item");

                expect(checkedItem).toBeInTheDocument();
                expect(uncheckedItem).toBeInTheDocument();

                // Check that the checked item has the checkbox indicator
                // eslint-disable-next-line quotes
                expect(checkedItem.closest('[data-state="checked"]')).toBeInTheDocument();
            });
        });

        it("handles checkbox state changes", async () => {
            const user = userEvent.setup();
            const handleCheckedChange = jest.fn();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuCheckboxItem checked={false} onCheckedChange={handleCheckedChange}>
                            Toggle Item
                        </DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                expect(screen.getByText("Toggle Item")).toBeInTheDocument();
            });

            await user.click(screen.getByText("Toggle Item"));
            expect(handleCheckedChange).toHaveBeenCalledWith(true);
        });
    });

    describe("DropdownMenuRadioItem", () => {
        it("renders radio item with radio indicator", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuRadioItem value="option1">Option 1</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="option2">Option 2</DropdownMenuRadioItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                expect(screen.getByText("Option 1")).toBeInTheDocument();
                expect(screen.getByText("Option 2")).toBeInTheDocument();
            });
        });

        it("works with DropdownMenuRadioGroup", async () => {
            const user = userEvent.setup();
            const handleValueChange = jest.fn();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuRadioGroup value="option1" onValueChange={handleValueChange}>
                            <DropdownMenuRadioItem value="option1">Option 1</DropdownMenuRadioItem>
                            <DropdownMenuRadioItem value="option2">Option 2</DropdownMenuRadioItem>
                        </DropdownMenuRadioGroup>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                expect(screen.getByText("Option 1")).toBeInTheDocument();
                expect(screen.getByText("Option 2")).toBeInTheDocument();
            });

            await user.click(screen.getByText("Option 2"));
            expect(handleValueChange).toHaveBeenCalledWith("option2");
        });
    });

    describe("DropdownMenuLabel", () => {
        it("renders label with custom className", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuLabel className="custom-label">Section Label</DropdownMenuLabel>
                        <DropdownMenuItem>Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const label = screen.getByText("Section Label");
                expect(label).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(label.closest('[class*="custom-label"]')).toBeInTheDocument();
            });
        });

        it("renders label with inset prop", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuLabel inset>Inset Label</DropdownMenuLabel>
                        <DropdownMenuItem>Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const label = screen.getByText("Inset Label");
                expect(label).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(label.closest('[class*="pl-8"]')).toBeInTheDocument();
            });
        });
    });

    describe("DropdownMenuSeparator", () => {
        it("renders separator between menu items", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>Item 1</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>Item 2</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                expect(screen.getByText("Item 1")).toBeInTheDocument();
                expect(screen.getByText("Item 2")).toBeInTheDocument();
                // Separator should be present as a divider element
                // eslint-disable-next-line quotes
                expect(document.querySelector('[class*="h-px bg-muted"]')).toBeInTheDocument();
            });
        });
    });

    describe("DropdownMenuShortcut", () => {
        it("renders shortcut text", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>
                            Copy
                            <DropdownMenuShortcut>⌘C</DropdownMenuShortcut>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                expect(screen.getByText("Copy")).toBeInTheDocument();
                expect(screen.getByText("⌘C")).toBeInTheDocument();
            });
        });

        it("renders shortcut with custom className", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>
                            Action
                            <DropdownMenuShortcut className="custom-shortcut">⌘A</DropdownMenuShortcut>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const shortcut = screen.getByText("⌘A");
                expect(shortcut).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(shortcut.closest('[class*="custom-shortcut"]')).toBeInTheDocument();
            });
        });
    });

    describe("DropdownMenuSub", () => {
        it("renders submenu with trigger and content", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuSub>
                            <DropdownMenuSubTrigger>More Options</DropdownMenuSubTrigger>
                            <DropdownMenuSubContent>
                                <DropdownMenuItem>Sub Item 1</DropdownMenuItem>
                                <DropdownMenuItem>Sub Item 2</DropdownMenuItem>
                            </DropdownMenuSubContent>
                        </DropdownMenuSub>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                expect(screen.getByText("More Options")).toBeInTheDocument();
            });

            await user.click(screen.getByText("More Options"));
            await waitFor(() => {
                expect(screen.getByText("Sub Item 1")).toBeInTheDocument();
                expect(screen.getByText("Sub Item 2")).toBeInTheDocument();
            });
        });

        it("renders submenu trigger with inset prop", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuSub>
                            <DropdownMenuSubTrigger inset>Inset Submenu</DropdownMenuSubTrigger>
                            <DropdownMenuSubContent>
                                <DropdownMenuItem>Sub Item</DropdownMenuItem>
                            </DropdownMenuSubContent>
                        </DropdownMenuSub>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                const subTrigger = screen.getByText("Inset Submenu");
                expect(subTrigger).toBeInTheDocument();
                // eslint-disable-next-line quotes
                expect(subTrigger.closest('[class*="pl-8"]')).toBeInTheDocument();
            });
        });
    });

    describe("DropdownMenuGroup", () => {
        it("groups menu items together", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuGroup>
                            <DropdownMenuItem>Group Item 1</DropdownMenuItem>
                            <DropdownMenuItem>Group Item 2</DropdownMenuItem>
                        </DropdownMenuGroup>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                expect(screen.getByText("Group Item 1")).toBeInTheDocument();
                expect(screen.getByText("Group Item 2")).toBeInTheDocument();
            });
        });
    });

    describe("Accessibility", () => {
        it("supports keyboard navigation", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>Item 1</DropdownMenuItem>
                        <DropdownMenuItem>Item 2</DropdownMenuItem>
                        <DropdownMenuItem>Item 3</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            // Focus the trigger
            screen.getByText("Open").focus();

            // Open with Enter key
            await user.keyboard("{Enter}");

            await waitFor(() => {
                expect(screen.getByText("Item 1")).toBeInTheDocument();
            });

            // Navigate with arrow keys
            await user.keyboard("{ArrowDown}");
            expect(screen.getByText("Item 2")).toHaveFocus();

            await user.keyboard("{ArrowDown}");
            expect(screen.getByText("Item 3")).toHaveFocus();

            // Close with Escape
            await user.keyboard("{Escape}");

            await waitFor(() => {
                expect(screen.queryByText("Item 1")).not.toBeInTheDocument();
            });
        });

        it("has proper ARIA attributes", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem>Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            const trigger = screen.getByText("Open");
            expect(trigger).toHaveAttribute("aria-haspopup", "menu");

            await user.click(trigger);

            await waitFor(() => {
                // eslint-disable-next-line quotes
                const content = document.querySelector('[role="menu"]');
                expect(content).toBeInTheDocument();
                expect(trigger).toHaveAttribute("aria-expanded", "true");
            });
        });
    });

    describe("Disabled states", () => {
        it("renders disabled menu items", async () => {
            const user = userEvent.setup();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem disabled>Disabled Item</DropdownMenuItem>
                        <DropdownMenuItem>Enabled Item</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));

            await waitFor(() => {
                const disabledItem = screen.getByText("Disabled Item");
                expect(disabledItem).toBeInTheDocument();
                // Check for the disabled attribute on the element itself
                expect(disabledItem).toHaveAttribute("aria-disabled", "true");
            });
        });

        it("prevents interaction with disabled items", async () => {
            const user = userEvent.setup();
            const handleClick = jest.fn();

            render(
                <DropdownMenu>
                    <DropdownMenuTrigger>Open</DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem disabled onClick={handleClick}>
                            Disabled Item
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );

            await user.click(screen.getByText("Open"));
            await waitFor(() => {
                expect(screen.getByText("Disabled Item")).toBeInTheDocument();
            });

            // Check that the disabled item has the correct attributes
            const disabledItem = screen.getByText("Disabled Item");
            expect(disabledItem).toHaveAttribute("aria-disabled", "true");

            // The disabled item should not be clickable, but Radix UI still allows the click event
            // to bubble up. We'll test that the item is visually disabled instead.
            expect(disabledItem).toHaveClass("data-[disabled]:pointer-events-none");
        });
    });
});
