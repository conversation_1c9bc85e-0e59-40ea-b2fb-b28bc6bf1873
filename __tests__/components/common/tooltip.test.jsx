// components/Tooltip.test.jsx
import { fireEvent, render, screen } from "@testing-library/react";
import Tooltip from "../../../src/components/common/tooltip";

describe("Tooltip Component", () => {
    const defaultProps = {
        label: "Tooltip text",
        children: <button>Hover tooltip</button>,
    };

    it("renders children correctly", () => {
        render(<Tooltip {...defaultProps} />);
        expect(screen.getByText("Hover tooltip")).toBeInTheDocument();
    });

    it("does not show tooltip initially", () => {
        render(<Tooltip {...defaultProps} />);
        expect(screen.queryByText("Tooltip text")).not.toBeInTheDocument();
    });

    it("shows tooltip on mouse enter", () => {
        render(<Tooltip {...defaultProps} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        expect(screen.getByText("Tooltip text")).toBeInTheDocument();
    });

    it("hides tooltip on mouse leave", () => {
        render(<Tooltip {...defaultProps} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        fireEvent.mouseLeave(trigger);
        expect(screen.queryByText("Tooltip text")).not.toBeInTheDocument();
    });

    it("applies correct classes for top position (default)", () => {
        render(<Tooltip {...defaultProps} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip).toHaveClass("bottom-full");
        expect(tooltip).toHaveClass("mb-2");
        expect(tooltip).toHaveClass("left-1/2");
        expect(tooltip).toHaveClass("-translate-x-1/2");
    });

    it("applies correct classes for bottom position", () => {
        render(<Tooltip {...defaultProps} position="bottom" />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip).toHaveClass("top-full");
        expect(tooltip).toHaveClass("mt-2");
        expect(tooltip).toHaveClass("left-1/2");
        expect(tooltip).toHaveClass("-translate-x-1/2");
    });

    it("applies correct classes for left position", () => {
        render(<Tooltip {...defaultProps} position="left" />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip).toHaveClass("right-full");
        expect(tooltip).toHaveClass("mr-2");
        expect(tooltip).toHaveClass("top-1/2");
        expect(tooltip).toHaveClass("-translate-y-1/2");
    });

    it("applies correct classes for right position", () => {
        render(<Tooltip {...defaultProps} position="right" />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip).toHaveClass("left-full");
        expect(tooltip).toHaveClass("ml-2");
        expect(tooltip).toHaveClass("top-1/2");
        expect(tooltip).toHaveClass("-translate-y-1/2");
    });

    it("renders with different label text", () => {
        const customLabel = "Custom tooltip";
        render(<Tooltip {...defaultProps} label={customLabel} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        expect(screen.getByText(customLabel)).toBeInTheDocument();
    });

    it("renders different children correctly", () => {
        const customChild = <span>Custom child</span>;
        render(<Tooltip {...defaultProps} children={customChild} />);
        expect(screen.getByText("Custom child")).toBeInTheDocument();
    });

    it("applies default class when an invalid position is provided", () => {
        render(<Tooltip {...defaultProps} position={"invalid"} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip.className).not.toContain("bottom-full");
        expect(tooltip.className).not.toContain("top-full");
        expect(tooltip.className).not.toContain("right-full");
        expect(tooltip.className).not.toContain("left-full");
        expect(tooltip.className).toBe(
            "absolute z-50 w-max max-w-full min-w-[300px] p-3 font-semibold text-sm text-white bg-[#212126] rounded-md shadow-sm "
        );
    });

    it("applies common classes for all positions", () => {
        render(<Tooltip {...defaultProps} />);
        const trigger = screen.getByText("Hover tooltip");
        fireEvent.mouseEnter(trigger);
        const tooltip = screen.getByText("Tooltip text");
        expect(tooltip).toHaveClass("absolute");
        expect(tooltip).toHaveClass("z-50");
        expect(tooltip).toHaveClass("w-max");
        expect(tooltip).toHaveClass("max-w-full");
        expect(tooltip).toHaveClass("min-w-[300px]");
        expect(tooltip).toHaveClass("p-3");
        expect(tooltip).toHaveClass("font-semibold");
        expect(tooltip).toHaveClass("text-sm");
        expect(tooltip).toHaveClass("text-white");
        expect(tooltip).toHaveClass("bg-[#212126]");
        expect(tooltip).toHaveClass("rounded-md");
        expect(tooltip).toHaveClass("shadow-sm");
    });
});
