import React from "react";
import { render, screen } from "@testing-library/react";
import PasswordValidation from "@/components/common/password-validation";

describe("PasswordValidation Component", () => {
    it("renders nothing when isVisible is false", () => {
        render(<PasswordValidation password="test123" isVisible={false} />);
        expect(screen.queryByText("A capital letter")).not.toBeInTheDocument();
    });

    it("renders all criteria when isVisible is true", () => {
        render(<PasswordValidation password="test123" isVisible={true} />);

        expect(screen.getByText("A capital letter")).toBeInTheDocument();
        expect(screen.getByText("A number")).toBeInTheDocument();
        expect(screen.getByText("A special character (e.g., @, #, $, %)")).toBeInTheDocument();
        expect(screen.getByText("At least 14 characters")).toBeInTheDocument();
    });

    it("shows red exclamation icons for unmet criteria", () => {
        render(<PasswordValidation password="weak" isVisible={true} />);

        // Check for red text color on all criteria
        const criteriaTexts = [
            "A capital letter",
            "A number",
            "A special character (e.g., @, #, $, %)",
            "At least 14 characters",
        ];

        criteriaTexts.forEach((text) => {
            expect(screen.getByText(text)).toHaveClass("text-sm", " text-sm text-black");
        });
    });

    it("shows green check icons for met criteria", () => {
        render(<PasswordValidation password="StrongPass123!" isVisible={true} />);

        // Check for green text color on all criteria
        const criteriaTexts = [
            "A capital letter",
            "A number",
            "A special character (e.g., @, #, $, %)",
            "At least 14 characters",
        ];

        criteriaTexts.forEach((text) => {
            expect(screen.getByText(text)).toHaveClass("text-sm", "text-black");
        });
    });

    it("shows mixed icons for partially met criteria", () => {
        render(<PasswordValidation password="StrongPass123" isVisible={true} />);

        // Capital, number should be green
        expect(screen.getByText("A capital letter")).toHaveClass("text-sm", "text-black");
        expect(screen.getByText("A number")).toHaveClass("text-sm", "text-black");

        // Length should be red (less than 14 characters)
        expect(screen.getByText("At least 14 characters")).toHaveClass("text-sm", "text-black");

        // Special character should be red
        expect(screen.getByText("A special character (e.g., @, #, $, %)")).toHaveClass("text-sm", "text-black");
    });

    it("correctly validates capital letter requirement", () => {
        const { rerender } = render(<PasswordValidation password="lowercase123!" isVisible={true} />);

        // Should show red for capital letter
        expect(screen.getByText("A capital letter")).toHaveClass("text-sm", "text-black");

        rerender(<PasswordValidation password="Uppercase123!" isVisible={true} />);

        // Should show green for capital letter
        expect(screen.getByText("A capital letter")).toHaveClass("text-sm", "text-black");
    });

    it("correctly validates number requirement", () => {
        const { rerender } = render(<PasswordValidation password="NoNumbers!" isVisible={true} />);

        // Should show red for number
        expect(screen.getByText("A number")).toHaveClass("text-sm", "text-black");

        rerender(<PasswordValidation password="HasNumbers123!" isVisible={true} />);

        // Should show green for number
        expect(screen.getByText("A number")).toHaveClass("text-sm", "text-black");
    });

    it("correctly validates special character requirement", () => {
        const { rerender } = render(<PasswordValidation password="NoSpecial123" isVisible={true} />);

        // Should show red for special character
        expect(screen.getByText("A special character (e.g., @, #, $, %)")).toHaveClass("text-sm", "text-black");

        rerender(<PasswordValidation password="HasSpecial@123" isVisible={true} />);

        // Should show green for special character
        expect(screen.getByText("A special character (e.g., @, #, $, %)")).toHaveClass("text-sm", "text-black");
    });

    it("correctly validates length requirement", () => {
        const { rerender } = render(<PasswordValidation password="Short1!" isVisible={true} />);

        // Should show red for length (less than 14)
        expect(screen.getByText("At least 14 characters")).toHaveClass("text-sm", "text-black");

        rerender(<PasswordValidation password="LongEnoughPassword123!" isVisible={true} />);

        // Should show green for length (14 or more)
        expect(screen.getByText("At least 14 characters")).toHaveClass("text-sm", "text-black");
    });

    it("handles empty password correctly", () => {
        render(<PasswordValidation password="" isVisible={true} />);

        // All criteria should be red for empty password
        const criteriaTexts = [
            "A capital letter",
            "A number",
            "A special character (e.g., @, #, $, %)",
            "At least 14 characters",
        ];

        criteriaTexts.forEach((text) => {
            expect(screen.getByText(text)).toHaveClass("text-sm", "text-black");
        });
    });

    it("handles various special characters correctly", () => {
        const specialCharacters = [
            "@",
            "#",
            "$",
            "%",
            "!",
            "&",
            "*",
            "(",
            ")",
            ",",
            ".",
            "?",
            // eslint-disable-next-line quotes
            '"',
            ":",
            "{",
            "}",
            "|",
            "<",
            ">",
        ];

        specialCharacters.forEach((char) => {
            const { unmount } = render(<PasswordValidation password={`Test123${char}`} isVisible={true} />);

            expect(screen.getByText("A special character (e.g., @, #, $, %)")).toHaveClass("text-sm", "text-black");
            unmount();
        });
    });
});
