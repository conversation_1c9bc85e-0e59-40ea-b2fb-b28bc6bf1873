import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import {
    PermissionFallback,
    withPermissionFallback,
    PermissionHealthIndicator,
    EmergencyAccess,
} from "@/components/common/permission-fallback";

jest.mock("@/hooks/usePermissionCheck", () => ({
    usePermissionCheck: jest.fn(() => ({
        getErrorState: () => ({ hasAnyError: false, systemPermissionsError: null, dynamicPermissionsError: null }),
        getLoadingState: () => ({ isAnyLoading: false }),
    })),
}));

jest.mock("@/services/permissionsService", () => ({
    __esModule: true,
    default: {
        refresh: jest.fn(),
        getState: jest.fn(() => ({
            isLoaded: true,
            error: null,
            permissionsList: [{ id: 1 }],
            lastFetched: Date.now(),
        })),
        subscribe: jest.fn(() => () => {}),
    },
}));

describe("PermissionFallback", () => {
    it("renders children when no error", () => {
        render(
            <PermissionFallback>
                <div data-testid="child">Child</div>
            </PermissionFallback>
        );
        expect(screen.getByTestId("child")).toBeInTheDocument();
    });

    it("renders fallback on error", () => {
        jest.spyOn(require("@/hooks/usePermissionCheck"), "usePermissionCheck").mockReturnValue({
            getErrorState: () => ({ hasAnyError: true, systemPermissionsError: "fail", dynamicPermissionsError: null }),
            getLoadingState: () => ({ isAnyLoading: false }),
        });
        render(
            <PermissionFallback>
                <div>Child</div>
            </PermissionFallback>
        );
        expect(screen.getByText(/Permission System Unavailable/i)).toBeInTheDocument();
        expect(screen.getByText(/fail/)).toBeInTheDocument();
    });

    it("calls retry on fallback button click", () => {
        jest.spyOn(require("@/hooks/usePermissionCheck"), "usePermissionCheck").mockReturnValue({
            getErrorState: () => ({ hasAnyError: true, systemPermissionsError: "fail", dynamicPermissionsError: null }),
            getLoadingState: () => ({ isAnyLoading: false }),
        });
        const { getByText } = render(
            <PermissionFallback>
                <div>Child</div>
            </PermissionFallback>
        );
        fireEvent.click(getByText(/Retry/i));
        expect(require("@/services/permissionsService").default.refresh).toHaveBeenCalled();
    });
});

describe("withPermissionFallback", () => {
    it("wraps a component and renders fallback on error", () => {
        jest.spyOn(require("@/hooks/usePermissionCheck"), "usePermissionCheck").mockReturnValue({
            getErrorState: () => ({ hasAnyError: true, systemPermissionsError: "fail", dynamicPermissionsError: null }),
            getLoadingState: () => ({ isAnyLoading: false }),
        });
        const Dummy = () => <div>Dummy</div>;
        const Wrapped = withPermissionFallback(Dummy);
        render(<Wrapped />);
        expect(screen.getByText(/Permission System Unavailable/i)).toBeInTheDocument();
    });
});

describe("PermissionHealthIndicator", () => {
    it("renders healthy status", () => {
        render(<PermissionHealthIndicator />);
        expect(screen.getByText(/Healthy/)).toBeInTheDocument();
    });
});

describe("EmergencyAccess", () => {
    it("renders children and reason", () => {
        render(
            <EmergencyAccess emergencyPermissions={["PERM"]}>
                <div>Emergency</div>
            </EmergencyAccess>
        );
        expect(screen.getByText("Emergency")).toBeInTheDocument();
        expect(screen.getByText(/Emergency access granted/)).toBeInTheDocument();
    });
});
