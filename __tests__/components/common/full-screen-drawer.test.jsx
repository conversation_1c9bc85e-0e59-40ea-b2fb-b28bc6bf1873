import "@testing-library/jest-dom";
import { fireEvent, render, screen } from "@testing-library/react";
import FullScreenDrawer from "../../../src/components/common/full-screen-drawer";
import { Provider } from "react-redux";
import { store } from "../../../src/redux";

// Rule 1: Mock all components being used
jest.mock("../../../src/components/common/delete-confirmation", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onConfirm }) => {
        if (!isOpen) return null;
        return (
            <div data-testid="exit-confirmation-modal">
                <button
                    onClick={() => {
                        onClose();
                    }}
                    data-testid="cancel-exit"
                >
                    Cancel
                </button>
                <button
                    onClick={() => {
                        onConfirm();
                    }}
                    data-testid="confirm-exit"
                >
                    Confirm
                </button>
            </div>
        );
    },
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, ...props }) => (
        <button onClick={onClick} {...props}>
            {children}
        </button>
    ),
}));

jest.mock("lucide-react", () => ({
    X: () => <span>×</span>,
}));

jest.mock("@/components/icons/bill-payment-icons", () => ({
    SupportIcon: () => <span>Support</span>,
}));

// Mock Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
    useAppSelector: jest.fn(() => ({})),
}));

// Import the mocked hooks
import { useAppDispatch } from "@/redux/hooks";

// Rule 2: Only test important functionality
describe("FullScreenDrawer Component", () => {
    const mockOnClose = jest.fn();
    const mockOnConfirmExit = jest.fn();
    const mockOnCancelExit = jest.fn();
    const mockDispatch = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        useAppDispatch.mockReturnValue(mockDispatch);
    });

    // Helper function to render with Redux provider
    const renderWithProvider = (component) => {
        return render(<Provider store={store}>{component}</Provider>);
    };

    // Rule 3: Use data-testid
    test("renders drawer content and controls when open", () => {
        renderWithProvider(
            <FullScreenDrawer isOpen={true} onClose={mockOnClose} title="Test Drawer" data-testid="drawer">
                <div>Content</div>
            </FullScreenDrawer>
        );

        expect(screen.getByTestId("modal-title")).toHaveTextContent("Test Drawer");
        expect(screen.getByText("Content")).toBeInTheDocument();
        expect(screen.getByTestId("close-button")).toBeInTheDocument();
    });

    // Test support button functionality
    test("support button dispatches openSupportDialog action when clicked", () => {
        renderWithProvider(
            <FullScreenDrawer isOpen={true} onClose={mockOnClose} showSupport={true}>
                <div>Content</div>
            </FullScreenDrawer>
        );

        const supportButton = screen.getByText("Support");
        expect(supportButton).toBeInTheDocument();

        fireEvent.click(supportButton);
        expect(mockDispatch).toHaveBeenCalledTimes(1);
    });

    // Rule 4: Don't test implementation details
    describe("exit behavior", () => {
        test("closes directly when showExitConfirmation is false", () => {
            renderWithProvider(
                <FullScreenDrawer isOpen={true} onClose={mockOnClose} showExitConfirmation={false}>
                    <div>Content</div>
                </FullScreenDrawer>
            );

            fireEvent.click(screen.getByTestId("close-button"));
            expect(mockOnClose).toHaveBeenCalledTimes(1);
            expect(screen.queryByTestId("exit-confirmation-modal")).not.toBeInTheDocument();
        });

        test("shows confirmation dialog and handles confirmation flow", () => {
            renderWithProvider(
                <FullScreenDrawer
                    isOpen={true}
                    onClose={mockOnClose}
                    showExitConfirmation={true}
                    onConfirmExit={mockOnConfirmExit}
                >
                    <div>Content</div>
                </FullScreenDrawer>
            );

            fireEvent.click(screen.getByTestId("close-button"));
            expect(screen.getByTestId("exit-confirmation-modal")).toBeInTheDocument();

            mockOnClose.mockClear();

            fireEvent.click(screen.getByTestId("confirm-exit"));
            expect(mockOnConfirmExit).toHaveBeenCalledTimes(1);
            expect(mockOnClose).not.toHaveBeenCalled();
        });

        test("handles cancellation flow correctly", () => {
            renderWithProvider(
                <FullScreenDrawer
                    isOpen={true}
                    onClose={mockOnClose}
                    showExitConfirmation={true}
                    onCancelExit={mockOnCancelExit}
                >
                    <div>Content</div>
                </FullScreenDrawer>
            );

            fireEvent.click(screen.getByTestId("close-button"));
            expect(screen.getByTestId("exit-confirmation-modal")).toBeInTheDocument();

            mockOnClose.mockClear();

            fireEvent.click(screen.getByTestId("cancel-exit"));
            expect(mockOnCancelExit).toHaveBeenCalledTimes(1);
            expect(mockOnClose).not.toHaveBeenCalled();
        });

        test("falls back to onClose when onConfirmExit is not provided", () => {
            renderWithProvider(
                <FullScreenDrawer isOpen={true} onClose={mockOnClose} showExitConfirmation={true}>
                    <div>Content</div>
                </FullScreenDrawer>
            );

            fireEvent.click(screen.getByTestId("close-button"));
            fireEvent.click(screen.getByTestId("confirm-exit"));
            expect(mockOnClose).toHaveBeenCalledTimes(1);
        });
    });

    test("supports optional features", () => {
        const { rerender } = renderWithProvider(
            <FullScreenDrawer isOpen={true} onClose={mockOnClose} showSupport={true} disablePadding={true}>
                <div>Content</div>
            </FullScreenDrawer>
        );

        expect(screen.getByText("Support")).toBeInTheDocument();

        // Test content area padding
        const contentArea = screen.getByText("Content").parentElement;
        expect(contentArea).not.toHaveClass("px-14", "py-8");

        // Test support button visibility
        rerender(
            <Provider store={store}>
                <FullScreenDrawer isOpen={true} onClose={mockOnClose} showSupport={false}>
                    <div>Content</div>
                </FullScreenDrawer>
            </Provider>
        );
        expect(screen.queryByText("Support")).not.toBeInTheDocument();
    });

    test("calls onCancelExit when not provided and defaults to closing", () => {
        renderWithProvider(
            <FullScreenDrawer isOpen={true} onClose={mockOnClose} showExitConfirmation={true}>
                <div>Content</div>
            </FullScreenDrawer>
        );

        fireEvent.click(screen.getByTestId("close-button"));
        fireEvent.click(screen.getByTestId("cancel-exit"));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
});
