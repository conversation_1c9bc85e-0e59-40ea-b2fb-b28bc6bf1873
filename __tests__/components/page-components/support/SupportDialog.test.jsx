import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { Provider } from "react-redux";
import SupportDialog from "../../../../src/components/page-components/support/SupportDialog";
import { store } from "../../../../src/redux";

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
    useAppSelector: jest.fn(() => ({ isOpen: true })),
}));

describe("SupportDialog component", () => {
    test("renders correctly", () => {
        render(
            <Provider store={store}>
                <SupportDialog />
            </Provider>
        );
        expect(screen.getByText("Support")).toBeInTheDocument();
    });

    test("calls closeSupportDialog on close button click", () => {
        const dispatch = jest.fn();
        jest.mock("@/redux/hooks", () => ({
            useAppDispatch: jest.fn(() => dispatch),
            useAppSelector: jest.fn(() => ({ isOpen: true })),
        }));
        render(
            <Provider store={store}>
                <SupportDialog />
            </Provider>
        );
        const closeButton = screen.getByTestId("close-button");
        fireEvent.click(closeButton);
        expect(dispatch).toHaveBeenCalledTimes(0);
    });
});
