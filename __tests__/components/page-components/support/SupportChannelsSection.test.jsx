import { render, screen } from "@testing-library/react";
import React from "react";
import SupportChannelsSection from "../../../../src/components/page-components/support/SupportChannelsSection";
import { supportChannels } from "../../../../src/components/page-components/support/data";

describe("SupportChannelsSection component", () => {
    test("renders title", () => {
        render(<SupportChannelsSection />);
        expect(screen.getByText("Support channels")).toBeInTheDocument();
    });

    test("renders support channels", () => {
        render(<SupportChannelsSection />);
        supportChannels.forEach((channel) => {
            expect(screen.getByText(channel.channel)).toBeInTheDocument();
        });
    });
    test("renders channel names and durations", () => {
        render(<SupportChannelsSection />);
        supportChannels.forEach((channel) => {
            expect(screen.getByText(channel.channel)).toBeInTheDocument();
        });
    });

    test("renders TimeIcon", () => {
        render(<SupportChannelsSection />);
        expect(screen.getAllByTestId("TimeIcon")).toHaveLength(supportChannels.length);
    });

    test("renders buttons with correct text and icons", () => {
        render(<SupportChannelsSection />);
        supportChannels.forEach((channel, index) => {
            const button = screen.getAllByRole("button")[index];
            expect(button).toHaveTextContent(channel.actionText);
        });
    });

    test("renders disabled buttons", () => {
        render(<SupportChannelsSection />);
        supportChannels.forEach((channel, index) => {
            const button = screen.getAllByRole("button")[index];
            if (channel.disabled) {
                expect(button).toHaveAttribute("disabled");
            } else {
                expect(button).not.toHaveAttribute("disabled");
            }
        });
    });
});
