import {
    accountOptions,
    bankDocumentTypeOptions,
    checkSizeOptions,
    collectionBranchOptions,
    FAQs,
    feedbackCategoryOptions,
    supportChannels,
} from "../../../../src/components/page-components/support/data";

describe("supportChannels array", () => {
    test("has correct length", () => {
        expect(supportChannels).toHaveLength(4);
    });

    test("has correct channel names", () => {
        const channelNames = supportChannels.map((channel) => channel.channel);
        expect(channelNames).toEqual(["Live chat", "Call", "Whatsapp", "Email"]);
    });

    test("has correct durations", () => {
        const durations = supportChannels.map((channel) => channel.duration);
        expect(durations).toEqual(["1 min", "1 min", "2 min", "1 min"]);
    });

    test("has correct action texts", () => {
        const actionTexts = supportChannels.map((channel) => channel.actionText);
        expect(actionTexts).toEqual(["Chat now", "***********", "Chat now", "Send an email"]);
    });

    test("has correct actions", () => {
        const actions = supportChannels.map((channel) => channel.action);
        actions.forEach((action) => expect(typeof action).toBe("function"));
    });

    test("actions perform correct operations", () => {
        const originalWindowOpen = window.open;
        const spy = jest.fn();
        window.open = spy;

        supportChannels[0].action(); // Live chat action
        expect(spy).not.toHaveBeenCalled();

        supportChannels[1].action(); // Call action
        expect(spy).toHaveBeenCalledTimes(1);
        expect(spy).toHaveBeenCalledWith("tel:***********", "WindowName", "noopener");

        supportChannels[2].action(); // Whatsapp action
        expect(spy).toHaveBeenCalledTimes(2);
        expect(spy).toHaveBeenCalledWith("https://wa.me/+2349099999815", "WindowName", "noopener");

        supportChannels[3].action(); // Email action
        expect(spy).toHaveBeenCalledTimes(3);
        expect(spy).toHaveBeenCalledWith("mailto:<EMAIL>", "WindowName", "noopener");

        window.open = originalWindowOpen;
    });
});

describe("FAQs", () => {
    test("should have the correct number of FAQs", () => {
        expect(FAQs.length).toBe(6);
    });

    test("should have unique questions", () => {
        const questions = FAQs.map((faq) => faq.question);
        expect(new Set(questions).size).toBe(5); // Note: There's a duplicate question
    });

    test("should have non-empty questions and answers", () => {
        FAQs.forEach((faq) => {
            expect(faq.question).not.toBe("");
            expect(faq.answer).not.toBe("");
        });
    });

    test("should have the correct data for each FAQ", () => {
        FAQs.forEach((faq, index) => {
            expect(faq.question).toBe(
                [
                    "I'm not getting a verification email",
                    "I can't set up 2FA",
                    "My verification code isn't working",
                    "I'm having issues logging in",
                    "Forgot my security question",
                    "I'm not getting a verification email",
                ][index]
            );

            expect(faq.answer).toBe(
                "Be sure to check your spam folder. Important emails can get lost there sometimes."
            );
        });
    });
});

describe("Options arrays", () => {
    test("bankDocumentTypeOptions has correct length", () => {
        expect(bankDocumentTypeOptions).toHaveLength(3);
    });

    test("bankDocumentTypeOptions has correct values", () => {
        expect(bankDocumentTypeOptions).toEqual([
            { label: "Cheque book", value: "Cheque book" },
            { label: "Reference letter", value: "Reference letter" },
            { label: "Letter of indebtedness", value: "Letter of indebtedness" },
        ]);
    });

    test("accountOptions has correct length", () => {
        expect(accountOptions).toHaveLength(2);
    });

    test("accountOptions has correct values", () => {
        expect(accountOptions).toEqual([
            { label: "Main account - 01******9245", value: "Main account - 01******9245" },
            { label: "Sub account - 01******7890", value: "Sub account - 01******7890" },
        ]);
    });

    test("collectionBranchOptions has correct length", () => {
        expect(collectionBranchOptions).toHaveLength(1);
    });

    test("collectionBranchOptions has correct values", () => {
        expect(collectionBranchOptions).toEqual([
            { label: "FCMB, Lekki Admiralty way", value: "FCMB, Lekki Admiralty way" },
        ]);
    });

    test("checkSizeOptions has correct length", () => {
        expect(checkSizeOptions).toHaveLength(3);
    });

    test("checkSizeOptions has correct values", () => {
        expect(checkSizeOptions).toEqual([
            { label: "50 leaves", value: "50 leaves" },
            { label: "100 leaves", value: "100 leaves" },
            { label: "200 leaves", value: "200 leaves" },
        ]);
    });
});

describe("feedbackCategoryOptions", () => {
    test("is not empty", () => {
        expect(feedbackCategoryOptions.length).toBeGreaterThan(0);
    });

    test("has correct shape", () => {
        expect(feedbackCategoryOptions[0]).toHaveProperty("label");
        expect(feedbackCategoryOptions[0]).toHaveProperty("value");
    });

    test("values are correct", () => {
        expect(feedbackCategoryOptions[0].label).toBe("Category 1");
        expect(feedbackCategoryOptions[0].value).toBe("Category 1");
    });

    test("is not frozen", () => {
        expect(Object.isFrozen(feedbackCategoryOptions)).toBe(false);
    });
});
