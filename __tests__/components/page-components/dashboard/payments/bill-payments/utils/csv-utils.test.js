/**
 * @file csv-utils.test.js
 * @purpose Comprehensive test suite for CSV utility functions in the bill payments module.
 *
 * @functionality This test file validates the CSV handling utilities used in bulk payment processing:
 * - parseCSV: Tests CSV content parsing from strings to structured data objects
 * - validateCsvFile: Tests file validation including size limits, required columns, and data integrity
 * - convertCsvToEntries: Tests conversion of CSV data to BulkAirtimeEntry objects for Redux store
 * The tests ensure data quality, error handling, and proper validation for user-uploaded CSV files.
 *
 * @dependencies Jest testing framework, React Testing Library, CSV utility functions from csv-utils.ts
 *
 * @usage Run with `npm test csv-utils.test` to validate CSV functionality. Tests cover core functionality,
 * edge cases, error conditions, and data validation to ensure robust CSV file processing in the application.
 */

import {
    parseCSV,
    validateCsvFile,
    convertCsvToEntries,
} from "@/components/page-components/dashboard/bill-payments/utils/csv-utils";

describe("CSV Utilities", () => {
    describe("parseCSV", () => {
        it("should parse valid CSV content correctly", () => {
            const csvContent = "Phone number,Network,Amount\n0801234567,MTN,500\n0908765432,Airtel,1000";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(2);
            expect(result[0]["Phone number"]).toBe("0801234567");
            expect(result[0]["Network"]).toBe("MTN");
            expect(result[0]["Amount"]).toBe("500");
            expect(result[1]["Phone number"]).toBe("0908765432");
            expect(result[1]["Network"]).toBe("Airtel");
            expect(result[1]["Amount"]).toBe("1000");
        });

        it("should handle CSV with BOM marker", () => {
            const csvContent = "\uFEFFPhone number,Network,Amount\n0801234567,MTN,500";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(1);
            expect(result[0]["Phone number"]).toBe("0801234567");
        });

        it("should handle CRLF line endings", () => {
            const csvContent = "Phone number,Network,Amount\r\n0801234567,MTN,500";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(1);
            expect(result[0]["Phone number"]).toBe("0801234567");
        });

        it("should return empty array for CSV without data rows", () => {
            const csvContent = "Phone number,Network,Amount";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(0);
        });

        it("should handle missing values in rows", () => {
            const csvContent = "Phone number,Network,Amount\n0801234567,MTN,\n,Airtel,1000";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(2);
            expect(result[0]["Amount"]).toBe("");
            expect(result[1]["Phone number"]).toBe("");
        });

        it("should trim headers and values", () => {
            const csvContent = " Phone number , Network , Amount \n 0801234567 , MTN , 500 ";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(1);
            expect(result[0]["Phone number"]).toBe("0801234567");
            expect(result[0]["Network"]).toBe("MTN");
            expect(result[0]["Amount"]).toBe("500");
        });

        it("should return empty array when parsing fails", () => {
            const originalSplit = String.prototype.split;
            String.prototype.split = function () {
                throw new Error("Parsing error");
            };

            const csvContent = "Phone number,Network,Amount\n0801234567,MTN,500";
            const result = parseCSV(csvContent);

            expect(result).toHaveLength(0);

            String.prototype.split = originalSplit;
        });
    });

    describe("validateCsvFile", () => {
        let originalConsoleError;

        beforeEach(() => {
            originalConsoleError = console.error;
            console.error = jest.fn();
        });

        afterEach(() => {
            console.error = originalConsoleError;
        });

        it("should validate a correct CSV file", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount\n0801234567,MTN,500" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const csvContent = "Phone number,Network,Amount\n0801234567,MTN,500";
            const file = new File([csvContent], "test.csv", { type: "text/csv" });

            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(true);
            expect(result.data).toHaveLength(1);
        });

        it("should reject a file larger than 10MB", async () => {
            const file = {
                size: 11 * 1024 * 1024,
                name: "large.csv",
            };

            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("File is too large");
        });

        it("should reject an empty CSV file", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(() => this.onload({ target: { result: "" } }), 0);
                }
                readAsText() {}
            };

            const file = new File([""], "empty.csv", { type: "text/csv" });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toBe("CSV file is empty");
        });

        it("should reject a CSV file with no data rows", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount"], "header-only.csv", { type: "text/csv" });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toBe("CSV file contains no data rows");
        });

        it("should reject a CSV file missing required columns", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Amount\n0801234567,500" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Amount\n0801234567,500"], "missing-column.csv", { type: "text/csv" });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("missing required columns");
            expect(result.errorMessage).toContain("Network");
        });

        it("should reject a CSV file with invalid phone numbers", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount\nabc,MTN,500" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\nabc,MTN,500"], "invalid-phone.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("Invalid data found in rows");
            expect(result.errorMessage).toContain("phone number format");
        });

        it("should reject a CSV file with invalid amounts", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount\n0801234567,MTN,abc" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\n0801234567,MTN,abc"], "invalid-amount.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("Invalid data found in rows");
            expect(result.errorMessage).toContain("amount");
        });

        it("should handle FileReader error", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(() => this.onerror(), 0);
                }
                readAsText() {}
            };

            const file = new File(["corrupted data"], "corrupted.csv", { type: "text/csv" });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("Error reading the CSV file");
        });

        it("should reject a CSV file with empty network providers", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount\n0801234567,,500" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\n0801234567,,500"], "empty-network.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("Invalid data found in rows");
            expect(result.errorMessage).toContain("network provider");
        });

        it("should handle multiple validation errors", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: { result: "Phone number,Network,Amount\nabc,,xyz" },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\nabc,,xyz"], "multiple-errors.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(false);
            expect(result.errorMessage).toContain("Invalid data found in rows");
            expect(result.errorMessage).toContain("phone number format");
            expect(result.errorMessage).toContain("network provider");
            expect(result.errorMessage).toContain("amount");
        });

        it("should accept phone numbers with special characters", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: {
                                    result: "Phone number,Network,Amount\n+234-************,MTN,500\n(0908) 765-4321,Airtel,1000",
                                },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\n+234-************,MTN,500"], "special-chars.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(true);
            expect(result.data).toHaveLength(2);
        });

        it("should validate amounts with decimal values", async () => {
            global.FileReader = class {
                constructor() {
                    setTimeout(
                        () =>
                            this.onload({
                                target: {
                                    result: "Phone number,Network,Amount\n0801234567,MTN,500.50\n0908765432,Airtel,1000.99",
                                },
                            }),
                        0
                    );
                }
                readAsText() {}
            };

            const file = new File(["Phone number,Network,Amount\n0801234567,MTN,500.50"], "decimal-amounts.csv", {
                type: "text/csv",
            });
            const result = await validateCsvFile(file);

            expect(result.isValid).toBe(true);
            expect(result.data).toHaveLength(2);
        });
    });

    describe("convertCsvToEntries", () => {
        it("should convert valid CSV data to BulkAirtimeEntry format", () => {
            const csvData = [
                { "Phone number": "0801234567", Network: "MTN", Amount: "500" },
                { "Phone number": "0908765432", Network: "Airtel", Amount: "1000" },
            ];

            const result = convertCsvToEntries(csvData);

            expect(result).toHaveLength(2);
            expect(result[0].phoneNumber).toBe("0801234567");
            expect(result[0].network).toBe("MTN");
            expect(result[0].amount).toBe(500);
            expect(result[0].status).toBe("Valid");

            expect(result[1].phoneNumber).toBe("0908765432");
            expect(result[1].network).toBe("Airtel");
            expect(result[1].amount).toBe(1000);
            expect(result[1].status).toBe("Valid");

            expect(result[0].id).toBeDefined();
            expect(result[1].id).toBeDefined();
            expect(result[0].id).not.toBe(result[1].id);
        });

        it("should mark entries with invalid phone numbers as Invalid", () => {
            const csvData = [{ "Phone number": "abc", Network: "MTN", Amount: "500" }];

            const result = convertCsvToEntries(csvData);

            expect(result[0].status).toBe("Invalid");
        });

        it("should mark entries with invalid amounts as Invalid", () => {
            const csvData = [{ "Phone number": "0801234567", Network: "MTN", Amount: "abc" }];

            const result = convertCsvToEntries(csvData);

            expect(result[0].status).toBe("Invalid");
            expect(result[0].amount).toBe(0);
        });

        it("should mark entries with empty networks as Invalid", () => {
            const csvData = [{ "Phone number": "0801234567", Network: "", Amount: "500" }];

            const result = convertCsvToEntries(csvData);

            expect(result[0].status).toBe("Invalid");
        });

        it("should mark entries with non-positive amounts as Invalid", () => {
            const csvData = [
                { "Phone number": "0801234567", Network: "MTN", Amount: "0" },
                { "Phone number": "0908765432", Network: "Airtel", Amount: "-10" },
            ];

            const result = convertCsvToEntries(csvData);

            expect(result[0].status).toBe("Invalid");
            expect(result[1].status).toBe("Invalid");
        });

        it("should handle missing fields in CSV data", () => {
            const csvData = [
                { "Phone number": undefined, Network: "MTN", Amount: "500" },
                { "Phone number": "0801234567", Network: undefined, Amount: "500" },
                { "Phone number": "0801234567", Network: "MTN", Amount: undefined },
            ];

            const result = convertCsvToEntries(csvData);

            expect(result[0].phoneNumber).toBe("");
            expect(result[0].status).toBe("Invalid");

            expect(result[1].network).toBe("");
            expect(result[1].status).toBe("Invalid");

            expect(result[2].amount).toBe(0);
            expect(result[2].status).toBe("Invalid");
        });

        it("should handle phone numbers with special characters", () => {
            const csvData = [
                { "Phone number": "+234-************", Network: "MTN", Amount: "500" },
                { "Phone number": "(0908) 765-4321", Network: "Airtel", Amount: "1000" },
            ];

            const result = convertCsvToEntries(csvData);

            expect(result[0].phoneNumber).toBe("+234-************");
            expect(result[0].status).toBe("Valid");

            expect(result[1].phoneNumber).toBe("(0908) 765-4321");
            expect(result[1].status).toBe("Valid");
        });

        it("should generate unique IDs for each entry", () => {
            const csvData = [
                { "Phone number": "0801234567", Network: "MTN", Amount: "500" },
                { "Phone number": "0801234567", Network: "MTN", Amount: "500" },
                { "Phone number": "0801234567", Network: "MTN", Amount: "500" },
            ];

            const result = convertCsvToEntries(csvData);

            const ids = result.map((entry) => entry.id);
            const uniqueIds = new Set(ids);

            expect(uniqueIds.size).toBe(3);
            expect(ids[0]).toMatch(/^csv-0-\d+$/);
            expect(ids[1]).toMatch(/^csv-1-\d+$/);
            expect(ids[2]).toMatch(/^csv-2-\d+$/);
        });

        it("should handle decimal amounts", () => {
            const csvData = [
                { "Phone number": "0801234567", Network: "MTN", Amount: "500.50" },
                { "Phone number": "0908765432", Network: "Airtel", Amount: "1000.99" },
            ];

            const result = convertCsvToEntries(csvData);

            expect(result[0].amount).toBe(500.5);
            expect(result[0].status).toBe("Valid");

            expect(result[1].amount).toBe(1000.99);
            expect(result[1].status).toBe("Valid");
        });

        it("should handle empty CSV data array", () => {
            const csvData = [];

            const result = convertCsvToEntries(csvData);

            expect(result).toHaveLength(0);
        });
    });
});
