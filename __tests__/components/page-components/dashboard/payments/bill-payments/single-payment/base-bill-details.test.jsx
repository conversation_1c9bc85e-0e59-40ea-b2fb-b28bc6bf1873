import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { BaseBillDetails } from "@/components/page-components/dashboard/bill-payments/single-payment/base-bill-details";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useExitHandlers } from "@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers";
import { useCustomerValidation } from "@/components/page-components/dashboard/bill-payments/single-payment/hooks/use-customer-validation";
import * as Yup from "yup";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { resetCustomerValidation } from "@/redux/slices/billPaymentSlice";

// Mock dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/hooks/use-customer-validation", () => ({
    useCustomerValidation: jest.fn(),
}));

// Mock child components
jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, title, onClose }) =>
        isOpen ? (
            <div data-testid="drawer">
                <button data-testid="close-drawer" onClick={onClose}>
                    Close
                </button>
                <h1>{title}</h1>
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper">
            {steps.map((step, index) => (
                <div key={step.id} data-current={index === currentStep}>
                    {step.label}
                </div>
            ))}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common", () => ({
    BillDetailsForm: ({ formik, categoryId, onPackageSelect, children }) => (
        <div data-testid="bill-details-form">
            <div data-testid="form-children">{children}</div>
            <button
                data-testid="mock-package-select"
                onClick={() =>
                    onPackageSelect({
                        amount: "5000",
                        amountFixed: true,
                        paymentCode: "TEST123",
                    })
                }
            >
                Select Package
            </button>
        </div>
    ),
}));

jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({
        value,
        onChange,
        onBlur,
        name,
        label,
        placeholder,
        disabled,
        showError,
        error,
        "data-testid": testId,
    }) => (
        <div data-testid={testId || `input-${name}`}>
            <label>{label}</label>
            <input
                value={value || ""}
                onChange={onChange}
                onBlur={onBlur}
                name={name}
                placeholder={placeholder}
                disabled={disabled}
                data-testid={`input-field-${name}`}
            />
            {showError && error && <div data-testid={`error-${name}`}>{error}</div>}
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, type, onClick, disabled }) => (
        <button data-testid="submit-button" type={type} onClick={onClick} disabled={disabled}>
            {children}
        </button>
    ),
}));

jest.mock("@/redux/slices/singleBillPayment", () => ({
    resetSingleBillPayment: jest.fn().mockReturnValue({ type: "singleBillPayment/resetSingleBillPayment" }),
    updateSelectedPackage: jest.fn().mockReturnValue({ type: "singleBillPayment/updateSelectedPackage" }),
}));

jest.mock("@/redux/slices/billPaymentSlice", () => ({
    resetCustomerValidation: jest.fn().mockReturnValue({ type: "billPaymentSlice/resetCustomerValidation" }),
}));

describe("BaseBillDetails", () => {
    // Common test values and props
    const mockSteps = [
        { id: "details", label: "Details" },
        { id: "payment", label: "Payment" },
        { id: "review", label: "Review" },
    ];

    const mockDefaultValues = {
        serviceProvider: "TestProvider",
        package: "TestPackage",
        amount: "1000",
        customerDetails: "",
        paymentCode: "TEST123",
        meterId: "", // Custom field for testing
    };

    const mockValidationSchema = (biller) =>
        Yup.object({
            serviceProvider: Yup.string().required("Service provider is required"),
            package: Yup.string().required("Package is required"),
            amount: Yup.string().required("Amount is required"),
            meterId: Yup.string().required("Meter ID is required"),
        });

    const mockValidateCustomerId = jest.fn((value) => value.length >= 5);
    const mockOnSubmit = jest.fn();
    const mockDispatch = jest.fn();

    // Mock Redux state
    const mockReduxState = {
        billPayments: {
            biller: {
                data: [
                    {
                        billerId: 1,
                        billerName: "TestProvider",
                    },
                ],
            },
        },
        singleBillPayment: {
            selectedPackage: {
                amount: "1000",
                amountFixed: true,
                paymentCode: "TEST123",
            },
        },
    };

    // Setup exit handlers mock
    const mockExitHandlers = {
        isOpen: true,
        showExitConfirmation: false,
        handleClose: jest.fn(),
        handleConfirmExit: jest.fn(),
        handleCancelExit: jest.fn(),
    };

    // Setup customer validation mock - enhancing with a working debouncedValidate implementation
    const mockCustomerValidation = {
        customerDetails: { name: "", description: "" },
        validationLoading: false,
        debouncedValidate: jest.fn((customerId, paymentCode) => {
            // This implementation will be replaced in specific tests as needed
        }),
        resetCustomerDetails: jest.fn(),
        isValidationSuccessful: false,
        validationAttempted: false,
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup mocks
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
        useExitHandlers.mockReturnValue(mockExitHandlers);
        useCustomerValidation.mockReturnValue(mockCustomerValidation);
    });

    it("renders the component correctly", () => {
        // Arrange & Act
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Assert - check key elements are rendered
        expect(screen.getByText("Test Bill Details")).toBeInTheDocument();
        expect(screen.getByText("Bill details")).toBeInTheDocument();
        expect(screen.getByTestId("drawer")).toBeInTheDocument();
        expect(screen.getByTestId("stepper")).toBeInTheDocument();
        expect(screen.getByTestId("bill-details-form")).toBeInTheDocument();
        expect(screen.getByTestId("meterId-input")).toBeInTheDocument();
        expect(screen.getByText("Next")).toBeInTheDocument();
    });

    it("calls onSubmit when form is submitted with valid data", async () => {
        // Arrange - Set up mock with successful validation
        const validCustomerValidation = {
            ...mockCustomerValidation,
            customerDetails: { name: "John Doe", description: "Test Customer" },
            validationLoading: false,
            isValidationSuccessful: true,
            validationAttempted: true,
        };
        useCustomerValidation.mockReturnValue(validCustomerValidation);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - fill in the meterId field
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        // Submit the form - using the submit button click to trigger handleNextClick
        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - check that onSubmit was called with the right values
        expect(mockOnSubmit).toHaveBeenCalledWith({
            serviceProvider: "TestProvider",
            package: "TestPackage",
            amount: 1000,
            customerDetails: "John Doe, Test Customer",
            paymentCode: "TEST123",
            meterId: "12345678",
        });
    });

    it("handles package selection and triggers customer validation", async () => {
        // Arrange - prepare a mock implementation for debouncedValidate that will be called
        const enhancedMockCustomerValidation = {
            ...mockCustomerValidation,
            debouncedValidate: jest.fn(),
        };
        useCustomerValidation.mockReturnValue(enhancedMockCustomerValidation);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - fill the meterId field first
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        // Then trigger package selection
        await act(async () => {
            const packageSelectButton = getByTestId("mock-package-select");
            fireEvent.click(packageSelectButton);
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalled();
        expect(enhancedMockCustomerValidation.resetCustomerDetails).toHaveBeenCalled();
    });

    it("handles customer ID input change and validation", async () => {
        // Arrange
        const enhancedMockCustomerValidation = {
            ...mockCustomerValidation,
            debouncedValidate: jest.fn(),
        };
        useCustomerValidation.mockReturnValue(enhancedMockCustomerValidation);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - input a valid meter ID
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        // Assert - resetCustomerDetails should be called but not debouncedValidate
        expect(enhancedMockCustomerValidation.resetCustomerDetails).toHaveBeenCalled();
        expect(enhancedMockCustomerValidation.debouncedValidate).not.toHaveBeenCalled();
    });

    it("shows validation loading and customer details when available", () => {
        // Arrange - Set up mock with validation loading
        useCustomerValidation.mockReturnValue({
            ...mockCustomerValidation,
            validationLoading: true,
        });

        // Act
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Assert - should show loading indicator
        expect(screen.getByText("Validating...")).toBeInTheDocument();

        // Cleanup
        useCustomerValidation.mockReturnValue(mockCustomerValidation);
    });

    it("shows customer name when validation is complete", () => {
        // Arrange - Set up mock with customer details
        useCustomerValidation.mockReturnValue({
            ...mockCustomerValidation,
            customerDetails: { name: "John Doe", description: "" },
            validationLoading: false,
        });

        // Act
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Assert - should show customer name
        expect(screen.getByText("John Doe")).toBeInTheDocument();

        // Cleanup
        useCustomerValidation.mockReturnValue(mockCustomerValidation);
    });

    it("uses dynamic labels when useDynamicLabels is true", () => {
        // Arrange
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel=""
                customerIdPlaceholder=""
                validateCustomerId={mockValidateCustomerId}
                useDynamicLabels={true}
            />
        );

        // Assert - with useDynamicLabels true and selected biller, should show dynamic label
        expect(screen.getByText("TestProvider number")).toBeInTheDocument();
    });

    it("handles drawer close correctly", () => {
        // Arrange
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act
        const closeButton = screen.getByTestId("close-drawer");
        fireEvent.click(closeButton);

        // Assert
        expect(mockExitHandlers.handleClose).toHaveBeenCalled();
    });

    it("dispatches reset actions when exit is confirmed", () => {
        // Arrange - Create a real onExit function that we'll capture and call
        const mockExitHandlersWithConfirmation = {
            ...mockExitHandlers,
            showExitConfirmation: true,
        };

        // Mock implementation that captures the onExit function
        let capturedOnExit;
        useExitHandlers.mockImplementation(({ onExit }) => {
            capturedOnExit = onExit;
            return mockExitHandlersWithConfirmation;
        });

        // Render to capture the onExit function
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - explicitly call the onExit function that was passed to useExitHandlers
        capturedOnExit();

        // Assert - verify resetSingleBillPayment and resetCustomerValidation were dispatched
        expect(resetSingleBillPayment).toHaveBeenCalled();
        expect(resetCustomerValidation).toHaveBeenCalled();
        expect(mockDispatch).toHaveBeenCalledWith({ type: "singleBillPayment/resetSingleBillPayment" });
        expect(mockDispatch).toHaveBeenCalledWith({ type: "billPaymentSlice/resetCustomerValidation" });

        // Cleanup
        useExitHandlers.mockReturnValue(mockExitHandlers);
    });

    it("disables the meterId input when package is invalid", () => {
        // Setup Redux state with no selected package to make package invalid
        const invalidPackageState = {
            ...mockReduxState,
            singleBillPayment: {
                selectedPackage: null, // No selected package
            },
        };
        useAppSelector.mockImplementation((selector) => selector(invalidPackageState));

        // Test component with an empty package which should be invalid
        const invalidPackageProps = {
            ...mockDefaultValues,
            package: "", // Empty package to make it invalid
        };

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={invalidPackageProps}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Assert - Check if the input is disabled
        const input = screen.getByTestId("input-field-meterId");
        expect(input).toBeDisabled();

        // Reset the mock for other tests
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
    });

    it("uses customerIdPlaceholder when provided, even with useDynamicLabels", () => {
        // Arrange - Explicitly provide both props
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Custom Label"
                customerIdPlaceholder="Custom Placeholder" // Explicitly provided
                validateCustomerId={mockValidateCustomerId}
                useDynamicLabels={true} // Even with dynamic labels enabled
            />
        );

        // Assert - Explicit placeholder should be used, not dynamic one
        const input = screen.getByTestId("input-field-meterId");
        expect(input).toHaveAttribute("placeholder", "Custom Placeholder");

        // Also check that the label uses the explicit value
        expect(screen.getByText("Custom Label")).toBeInTheDocument();
    });

    it("uses dynamic label and placeholder when customerIdLabel and customerIdPlaceholder are empty", () => {
        // Arrange - Empty props with useDynamicLabels
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="" // Empty to trigger dynamic label
                customerIdPlaceholder="" // Empty to trigger dynamic placeholder
                validateCustomerId={mockValidateCustomerId}
                useDynamicLabels={true}
            />
        );

        // Assert - Check for dynamic label and placeholder
        expect(screen.getByText("TestProvider number")).toBeInTheDocument(); // Dynamic label

        const input = screen.getByTestId("input-field-meterId");
        expect(input).toHaveAttribute("placeholder", "Enter TestProvider number"); // Dynamic placeholder
    });

    it("uses default label and placeholder when no biller is selected", () => {
        // Arrange - Setup with no selected biller
        const emptyStateWithoutBiller = {
            billPayments: {
                biller: {
                    data: [],
                },
            },
            singleBillPayment: {
                selectedPackage: null,
            },
        };
        useAppSelector.mockImplementation((selector) => selector(emptyStateWithoutBiller));

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={{
                    ...mockDefaultValues,
                    serviceProvider: "", // No service provider selected
                }}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="" // Empty to trigger fallback
                customerIdPlaceholder="" // Empty to trigger fallback
                validateCustomerId={mockValidateCustomerId}
                useDynamicLabels={true}
            />
        );

        // Assert - Check for default values
        expect(screen.getByText("Number")).toBeInTheDocument(); // Default label

        const input = screen.getByTestId("input-field-meterId");
        expect(input).toHaveAttribute("placeholder", "Enter number"); // Default placeholder

        // Reset the mock for other tests
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
    });

    it("handles validation errors correctly", () => {
        // Mock Yup validation to throw an error
        jest.spyOn(Yup, "object").mockImplementationOnce(() => ({
            validateSync: jest.fn().mockImplementation(() => {
                const error = new Yup.ValidationError("Validation error");
                error.inner = [
                    { path: "testField", message: "Test error message" },
                    { path: "anotherField", message: "Another error" },
                ];
                throw error;
            }),
        }));

        // Custom validation schema that will use our mocked Yup.object
        const mockErrorValidationSchema = () => Yup.object();

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockErrorValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Trigger form validation by attempting to submit the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // We expect the validation errors to be processed correctly
        // Hard to directly test the return value, but we can check if formik handles the validation
        // This test will pass if the validation error handling code path is exercised without errors
    });

    it("prevents form submission when customer validation is in progress", async () => {
        // Arrange - Set validation loading to true
        const validationLoadingState = {
            ...mockCustomerValidation,
            validationLoading: true,
            validationAttempted: true,
            customerDetails: { name: "", description: "" },
        };
        useCustomerValidation.mockReturnValue(validationLoadingState);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Set a valid meter ID to pass initial validation
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        // Act - Click the submit button to trigger handleNextClick
        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - Check that the button is disabled when validation is in progress
        const submitButton = getByTestId("submit-button");
        expect(submitButton).toBeDisabled();

        // The onSubmit prop should not have been called because validation is in progress
        expect(mockOnSubmit).not.toHaveBeenCalled();

        // The button should show loading state
        expect(screen.getByText("Verifying details...")).toBeInTheDocument();
    });

    it("formats customer details correctly with name and description", async () => {
        // Arrange - Set up with successful validation and both name and description
        const customerValidationWithDetails = {
            ...mockCustomerValidation,
            customerDetails: {
                name: "John Doe",
                description: "Premium Customer",
            },
            validationLoading: false,
            isValidationSuccessful: true,
            validationAttempted: true,
        };
        useCustomerValidation.mockReturnValue(customerValidationWithDetails);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - fill in the meterId field and submit
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - check that onSubmit was called with combined customer details string
        expect(mockOnSubmit).toHaveBeenCalledWith(
            expect.objectContaining({
                customerDetails: "John Doe, Premium Customer",
            })
        );
    });

    it("formats customer details correctly with name only", async () => {
        // Arrange - Set up with successful validation and name only
        const customerValidationWithNameOnly = {
            ...mockCustomerValidation,
            customerDetails: {
                name: "John Doe",
                description: "", // Empty description
            },
            validationLoading: false,
            isValidationSuccessful: true,
            validationAttempted: true,
        };
        useCustomerValidation.mockReturnValue(customerValidationWithNameOnly);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - fill in the meterId field and submit
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - check that onSubmit was called with name only as customer details
        expect(mockOnSubmit).toHaveBeenCalledWith(
            expect.objectContaining({
                customerDetails: "John Doe",
            })
        );
    });

    it("handles null selectedBiller correctly in formik validation", () => {
        // Arrange - Mock Redux state with empty billers array
        const emptyBillersState = {
            billPayments: {
                biller: {
                    data: [], // No billers available
                },
            },
            singleBillPayment: {
                selectedPackage: null,
            },
        };

        // Apply the empty state
        useAppSelector.mockImplementation((selector) => selector(emptyBillersState));

        // Create a spy on the validation schema to check if it's called with null
        const validationSchemaSpy = jest.fn().mockImplementation(mockValidationSchema);

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={validationSchemaSpy}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Trigger validation by submitting the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The validation schema should have been called with null biller
        expect(validationSchemaSpy).toHaveBeenCalledWith(null);

        // Reset the mock for other tests
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
    });

    it("handles null billerForValidation correctly in formik validation", () => {
        // Arrange - Mock Redux state with billers that don't match serviceProvider
        const nonMatchingBillersState = {
            billPayments: {
                biller: {
                    data: [
                        {
                            billerId: 1,
                            billerName: "DifferentProvider", // Different from the one in defaultValues
                        },
                    ],
                },
            },
            singleBillPayment: {
                selectedPackage: {
                    amount: "1000",
                    amountFixed: true,
                    paymentCode: "TEST123",
                },
            },
        };

        useAppSelector.mockImplementation((selector) => selector(nonMatchingBillersState));

        // Create a spy on the validation schema to check if it's called with null
        const validationSchemaSpy = jest.fn().mockImplementation(mockValidationSchema);

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues} // Has serviceProvider: "TestProvider"
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={validationSchemaSpy}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Trigger validation by submitting the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The validation schema should have been called with null biller
        // This is because the selected biller name doesn't match any in the state
        expect(validationSchemaSpy).toHaveBeenCalledWith(null);

        // Reset the mock for other tests
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
    });

    it("handles non-ValidationError exceptions in formik validation", () => {
        // Arrange - Create a validation schema that throws a regular Error
        const errorThrowingSchema = () => ({
            validateSync: jest.fn().mockImplementation(() => {
                throw new Error("Regular error, not a ValidationError");
            }),
        });

        // Render component with the error-throwing schema
        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={errorThrowingSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Trigger validation by trying to submit the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The form should not throw an uncaught exception when a regular Error is thrown
        // If we get here without an error, the test passes
        // We can't easily check the return value of validate function, but we can check
        // that the form didn't submit successfully
        expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it("skips validation when already attempted with same customer ID", async () => {
        // Arrange - Set up mock with validation already attempted and same customer ID
        const validationAlreadyAttemptedState = {
            ...mockCustomerValidation,
            validationAttempted: true, // Validation was already attempted
            isValidationSuccessful: false, // But not successful
            validationLoading: false,
            debouncedValidate: jest.fn(), // Spy to ensure it's NOT called
        };
        useCustomerValidation.mockReturnValue(validationAlreadyAttemptedState);

        // Use the same customer ID as in defaultValues to trigger the "no action needed" path
        const sameCustomerIdValues = {
            ...mockDefaultValues,
            meterId: "12345678", // This will be the same as what we set as default
        };

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={sameCustomerIdValues} // defaultValues has this customer ID
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - Click Next button with the same customer ID as in defaultValues
        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - debouncedValidate should NOT be called because validation was already attempted
        // with the same customer ID (no action needed)
        expect(validationAlreadyAttemptedState.debouncedValidate).not.toHaveBeenCalled();

        // And form submission should proceed since no validation is needed
        expect(mockOnSubmit).toHaveBeenCalled();
    });

    it("handles biller search with billername property", () => {
        // Arrange - Mock Redux state with billers that have billername property (different from billerName)
        const billersWithBillername = {
            billPayments: {
                biller: {
                    data: [
                        {
                            billerid: 1,
                            billername: "TestProvider", // Using billername instead of billerName
                        },
                    ],
                },
            },
            singleBillPayment: {
                selectedPackage: {
                    amount: "1000",
                    amountFixed: true,
                    paymentCode: "TEST123",
                },
            },
        };

        useAppSelector.mockImplementation((selector) => selector(billersWithBillername));

        // Create a spy on the validation schema to verify it's called with the correct biller
        const validationSchemaSpy = jest.fn().mockImplementation(mockValidationSchema);

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={validationSchemaSpy}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Trigger validation by submitting the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The validation schema should have been called with the correct biller
        // This tests the billername property path in the find logic
        expect(validationSchemaSpy).toHaveBeenCalledWith({
            billerId: 1,
            billerName: "TestProvider",
        });

        // Reset the mock for other tests
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
    });

    it("returns empty validation errors when no service provider is selected", () => {
        // Arrange - Set up formik with no service provider to trigger early return
        const noServiceProviderValues = {
            ...mockDefaultValues,
            serviceProvider: "", // No service provider selected
        };

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={noServiceProviderValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Click submit to trigger validation
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The component should render without errors
        // This tests the early return path when no service provider is selected
        expect(screen.getByText("Test Bill Details")).toBeInTheDocument();
    });

    it("includes validation errors for serviceProvider field even when not touched", () => {
        // Arrange - Create validation schema that throws serviceProvider error
        const validationSchemaWithServiceProviderError = () => ({
            validateSync: jest.fn().mockImplementation(() => {
                const error = new ValidationError("Service provider validation error");
                error.inner = [
                    { path: "serviceProvider", message: "Service provider is required" },
                    { path: "meterId", message: "Meter ID is required" },
                ];
                throw error;
            }),
        });

        render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={validationSchemaWithServiceProviderError}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
            />
        );

        // Act - Trigger validation by submitting the form
        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        // Assert - The validation should include serviceProvider error
        // This tests the conditional logic in error processing
        expect(screen.getByText("Test Bill Details")).toBeInTheDocument();
    });

    it("triggers debouncedValidate when validation is needed and not already attempted", async () => {
        // Arrange - Set up mock that needs validation
        const validationNeededState = {
            ...mockCustomerValidation,
            validationAttempted: false, // Validation not attempted yet
            isValidationSuccessful: false, // Not successful
            validationLoading: false,
            debouncedValidate: jest.fn(), // Spy to ensure it's called
        };
        useCustomerValidation.mockReturnValue(validationNeededState);

        const { getByTestId } = render(
            <BaseBillDetails
                onSubmit={mockOnSubmit}
                defaultValues={mockDefaultValues}
                categoryId={5}
                title="Test Bill Details"
                steps={mockSteps}
                currentStep={0}
                validationSchema={mockValidationSchema}
                customerIdField="meterId"
                customerIdLabel="Meter ID"
                customerIdPlaceholder="Enter meter ID"
                validateCustomerId={mockValidateCustomerId}
                categoryName="test-category"
            />
        );

        // Act - Enter a valid customer ID and click Next
        await act(async () => {
            const meterIdInput = getByTestId("input-field-meterId");
            fireEvent.change(meterIdInput, { target: { value: "12345678" } });
        });

        await act(async () => {
            const submitButton = getByTestId("submit-button");
            fireEvent.click(submitButton);
        });

        // Assert - debouncedValidate should be called with the correct parameters
        expect(validationNeededState.debouncedValidate).toHaveBeenCalledWith("12345678", "TEST123");
    });
});
