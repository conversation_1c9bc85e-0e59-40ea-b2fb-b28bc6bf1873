import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import SingleReviewBill, {
    handlePaymentError,
} from "@/components/page-components/dashboard/bill-payments/single-payment/review-bill";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { usePaymentConfirmation } from "@/components/page-components/dashboard/bill-payments/single-payment/utils/use-payment-confirmation";
import { useExitHandlers } from "@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers";
import { sendCatchFeedback } from "@/functions/feedback";
import { formatAccountName, formatAccountNumber } from "@/redux/features/accounts";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";

// Mock dependencies
jest.mock("@/redux/features/accounts", () => ({
    formatAccountName: jest.fn(() => "Current account"),
    formatAccountNumber: jest.fn((number) => number),
}));

// Mock useAppSelector and useAppDispatch
const mockDispatch = jest.fn();
const mockSelector = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: (selector) => mockSelector(selector),
}));

// Set default mock state
const setMockState = (overrides = {}) => {
    const defaultState = {
        accounts: {
            selectedAccount: "Current account **********",
            accounts: [
                {
                    accountNumber: "**********",
                    accountName: "Test Account",
                    schemeType: "current",
                },
            ],
        },
        corporate: { corporateId: "corp123" },
        security: { verifyPin: { open: false } },
        transferMfaSlice: {
            getTeamMemberDetails: { loading: false, success: false },
            teamMember: null,
        },
        ...overrides,
    };
    mockSelector.mockImplementation((selector) => selector(defaultState));
};

jest.mock("@/redux/slices/singleBillPayment", () => ({
    resetSingleBillPayment: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/utils/use-payment-confirmation", () => ({
    usePaymentConfirmation: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock MFA-related actions and slices
jest.mock("@/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(() => ({ type: "transferMfa/getTeamMemberDetails/pending" })),
}));

jest.mock("@/redux/slices/settingsMfaSlice", () => ({
    resetAllStates: jest.fn(() => ({ type: "RESET_ALL_STATES" })),
}));

// Mock SettingsMfaVerification component
jest.mock("@/components/page-components/dashboard/settings/components/settings-mfa-verification", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onVerified, userMfaType, email, phoneNumber }) =>
        isOpen ? (
            <div data-testid="settings-mfa-verification-modal">
                <div data-testid="mfa-type">{userMfaType}</div>
                <div data-testid="mfa-email">{email}</div>
                <div data-testid="mfa-phone">{phoneNumber}</div>
                <button data-testid="close-mfa-verification" onClick={onClose}>
                    Close MFA
                </button>
                <button data-testid="verify-mfa" onClick={() => onVerified("test-mfa-token")}>
                    Verify MFA
                </button>
            </div>
        ) : null,
}));

// Mock child components to simplify testing
jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper-mock">
            Steps: {steps.length}, Current: {currentStep}
        </div>
    ),
}));

jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({
        children,
        isOpen,
        onClose,
        title,
        showExitConfirmation,
        onConfirmExit,
        onCancelExit,
        showSupport,
        disablePadding,
    }) => (
        <div data-testid="full-screen-drawer-mock" data-is-open={isOpen}>
            <h1>{title}</h1>
            <button data-testid="drawer-close-button" onClick={onClose}>
                Close
            </button>
            {showExitConfirmation && (
                <div data-testid="exit-confirmation">
                    <button
                        data-testid="confirm-exit-button"
                        onClick={(e) => {
                            onConfirmExit(e);
                        }}
                    >
                        Confirm Exit
                    </button>
                    <button data-testid="cancel-exit-button" onClick={onCancelExit}>
                        Cancel Exit
                    </button>
                </div>
            )}
            {children}
        </div>
    ),
}));

// Create mock function for BillDetails
const mockBillDetails = jest.fn();

jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/components", () => ({
    RecipientSection: ({ billType, identifier, details, onChangeBillDetails, categoryName }) => (
        <div data-testid="recipient-section-mock">
            <div data-testid="bill-type">{billType}</div>
            <div data-testid="identifier">{identifier}</div>
            <div data-testid="details">{details}</div>
            <button data-testid="change-bill-details-button" onClick={onChangeBillDetails}>
                Change Details
            </button>
        </div>
    ),
    BillDetails: (props) => {
        mockBillDetails(props);
        const {
            billType,
            getBillTypeDisplay,
            getBillerValue,
            identificationNumber,
            smartCardNumber,
            getFullCustomerDetails,
            amount,
            selectedAccount,
        } = props;
        return (
            <div data-testid="bill-details-mock">
                <div data-testid="bill-type-display">{getBillTypeDisplay(billType)}</div>
                <div data-testid="biller-value">{getBillerValue()}</div>
                <div data-testid="identification-number">{identificationNumber}</div>
                <div data-testid="customer-details">{getFullCustomerDetails()}</div>
                <div data-testid="amount">₦{amount.toLocaleString()}</div>
                <div data-testid="selected-account">{selectedAccount}</div>
            </div>
        );
    },
    ActionButtons: ({ onBack, handleConfirm, isProcessing }) => (
        <div data-testid="action-buttons-mock">
            <button data-testid="back-button" onClick={onBack}>
                Back
            </button>
            <button data-testid="confirm-button" onClick={handleConfirm} disabled={isProcessing}>
                {isProcessing ? "Processing..." : "Confirm"}
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/multi-party-approval", () => ({
    __esModule: true,
    default: ({ isOpen, amount, type }) => (
        <div data-testid="multi-party-approval-mock" data-is-open={isOpen}>
            Amount: ₦{amount}, Type: {type}
        </div>
    ),
}));

// Mock the helper functions with original implementations to improve coverage
jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/display-helpers", () => {
    const originalModule = jest.requireActual(
        "@/components/page-components/dashboard/bill-payments/single-payment/display-helpers"
    );
    return {
        ...originalModule,
        // Use original implementations for better coverage
        getBillIdentifier: jest.fn(originalModule.getBillIdentifier),
        getCustomerDetails: jest.fn(originalModule.getCustomerDetails),
        getBillTypeDisplay: jest.fn(originalModule.getBillTypeDisplay),
        getBillerValue: jest.fn(originalModule.getBillerValue),
        getFullCustomerDetails: jest.fn(originalModule.getFullCustomerDetails),
        getServiceId: jest.fn(originalModule.getServiceId),
    };
});

describe("SingleReviewBill", () => {
    // Common props for all tests
    const defaultProps = {
        billType: "airtime",
        phoneNumber: "***********",
        amount: 1000,
        networkProvider: "MTN",
        onBack: jest.fn(),
        onConfirm: jest.fn(),
        paymentInfo: { accountNumber: "**********", narration: "Airtime purchase" },
        onChangeBillDetails: jest.fn(),
    };

    // Mock hook returns for default case
    const mockHandleConfirm = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        setMockState(); // Set default mock state

        // Default mock implementations
        usePaymentConfirmation.mockReturnValue({
            isProcessing: false,
            currentStep: 3,
            handleConfirm: mockHandleConfirm,
        });

        useExitHandlers.mockReturnValue({
            isOpen: true,
            showExitConfirmation: false,
            handleClose: jest.fn(),
            handleConfirmExit: jest.fn(),
            handleCancelExit: jest.fn(),
        });
    });

    test("renders with airtime bill type correctly", () => {
        render(<SingleReviewBill {...defaultProps} />);

        // Check title
        expect(screen.getByText("Airtime bill payment")).toBeInTheDocument();

        // Check that components are rendered
        expect(screen.getByTestId("full-screen-drawer-mock")).toBeInTheDocument();
        expect(screen.getByTestId("stepper-mock")).toBeInTheDocument();
        expect(screen.getByTestId("recipient-section-mock")).toBeInTheDocument();
        expect(screen.getByTestId("bill-details-mock")).toBeInTheDocument();
        expect(screen.getByTestId("action-buttons-mock")).toBeInTheDocument();
        expect(screen.getByTestId("multi-party-approval-mock")).toBeInTheDocument();

        // Check bill type specific data
        expect(screen.getByTestId("bill-type")).toHaveTextContent("airtime");
        expect(screen.getByTestId("identifier")).toHaveTextContent("***********");
        expect(screen.getByTestId("details")).toHaveTextContent("MTN • ₦1,000.00");
    });

    test("renders with electricity bill type correctly", () => {
        const electricityProps = {
            ...defaultProps,
            billType: "electricity",
            identificationNumber: "12345678",
            serviceProvider: "EKEDC",
            customerDetails: "John Doe, 123 Main St",
            phoneNumber: undefined,
            networkProvider: undefined,
        };

        render(<SingleReviewBill {...electricityProps} />);

        // Check title
        expect(screen.getByText("Electricity bill payment")).toBeInTheDocument();

        // Check electricity specific data
        expect(screen.getByTestId("bill-type")).toHaveTextContent("electricity");
        expect(screen.getByTestId("identifier")).toHaveTextContent("12345678");
        expect(screen.getByTestId("details")).toHaveTextContent("John Doe • ₦1,000.00");
    });

    test("renders with cable-tv bill type correctly", () => {
        const cableTvProps = {
            ...defaultProps,
            billType: "cable-tv",
            smartCardNumber: "87654321",
            serviceProvider: "DSTV",
            package: "Premium",
            customerDetails: "Jane Smith, Active Subscription",
            phoneNumber: undefined,
            networkProvider: undefined,
        };

        render(<SingleReviewBill {...cableTvProps} />);

        // Check title - the helper function formats cable-tv as "Cable Tv"
        expect(screen.getByText("Cable Tv bill payment")).toBeInTheDocument();

        // Check cable-tv specific data
        expect(screen.getByTestId("bill-type")).toHaveTextContent("cable-tv");
        expect(screen.getByTestId("identifier")).toHaveTextContent("87654321");
        expect(screen.getByTestId("details")).toHaveTextContent("Jane Smith • ₦1,000.00");
        expect(screen.getByTestId("biller-value")).toHaveTextContent("DSTV, Premium");
    });

    test("uses categoryName for title when provided", () => {
        const propsWithCategory = {
            ...defaultProps,
            categoryName: "Mobile Data",
        };

        render(<SingleReviewBill {...propsWithCategory} />);

        // Check custom category name is used in title
        expect(screen.getByText("Mobile Data bill payment")).toBeInTheDocument();
    });

    test("handles back button click", () => {
        render(<SingleReviewBill {...defaultProps} />);

        fireEvent.click(screen.getByTestId("back-button"));

        expect(defaultProps.onBack).toHaveBeenCalledTimes(1);
    });

    test("handles confirm button click", () => {
        render(<SingleReviewBill {...defaultProps} />);

        fireEvent.click(screen.getByTestId("confirm-button"));

        // Should dispatch getTeamMemberDetails action for 2FA flow
        expect(mockDispatch).toHaveBeenCalledWith(
            expect.objectContaining({
                type: expect.stringContaining("getTeamMemberDetails"),
            })
        );
    });

    test("handles change bill details button click", () => {
        render(<SingleReviewBill {...defaultProps} />);

        fireEvent.click(screen.getByTestId("change-bill-details-button"));

        expect(defaultProps.onChangeBillDetails).toHaveBeenCalledTimes(1);
    });

    test("displays process indicator when processing", () => {
        // Override the default mock to indicate processing
        usePaymentConfirmation.mockReturnValue({
            isProcessing: true,
            currentStep: 4,
            handleConfirm: mockHandleConfirm,
        });

        render(<SingleReviewBill {...defaultProps} />);

        // Check processing state
        expect(screen.getByTestId("confirm-button")).toBeDisabled();
        expect(screen.getByTestId("confirm-button")).toHaveTextContent("Processing...");
    });

    test("displays processing when pin modal is open", () => {
        // Mock security state with pin modal open
        setMockState({
            security: { verifyPin: { open: true } },
        });

        render(<SingleReviewBill {...defaultProps} />);

        // Check processing state due to pin modal
        expect(screen.getByTestId("confirm-button")).toBeDisabled();
        expect(screen.getByTestId("confirm-button")).toHaveTextContent("Processing...");
    });

    test("handles exit drawer flow correctly", () => {
        const mockHandleClose = jest.fn();
        const mockHandleConfirmExit = jest.fn();
        const mockHandleCancelExit = jest.fn();

        // Override the default mock to test exit flow
        useExitHandlers.mockReturnValue({
            isOpen: true,
            showExitConfirmation: true,
            handleClose: mockHandleClose,
            handleConfirmExit: mockHandleConfirmExit,
            handleCancelExit: mockHandleCancelExit,
        });

        render(<SingleReviewBill {...defaultProps} />);

        // Check exit confirmation is shown
        expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();

        // Test close button
        fireEvent.click(screen.getByTestId("drawer-close-button"));
        expect(mockHandleClose).toHaveBeenCalledTimes(1);

        // Test confirm exit
        fireEvent.click(screen.getByTestId("confirm-exit-button"));
        expect(mockHandleConfirmExit).toHaveBeenCalledTimes(1);

        // Test cancel exit
        fireEvent.click(screen.getByTestId("cancel-exit-button"));
        expect(mockHandleCancelExit).toHaveBeenCalledTimes(1);
    });

    test("gets account number correctly from selected account", () => {
        render(<SingleReviewBill {...defaultProps} />);

        // The account number should be taken from the found account
        expect(usePaymentConfirmation).toHaveBeenCalledWith(
            expect.objectContaining({
                accountNumber: "**********",
            })
        );
    });

    test("uses fallback account number when account not found", () => {
        // Mock different account selection
        setMockState({
            accounts: {
                selectedAccount: "Non-existent Account",
                accounts: [], // Empty accounts array will cause fallback
            },
        });

        render(<SingleReviewBill {...defaultProps} />);

        // The account number should fall back to paymentInfo.accountNumber
        expect(usePaymentConfirmation).toHaveBeenCalledWith(
            expect.objectContaining({
                accountNumber: "**********",
            })
        );
    });

    test("handles account fallback when paymentInfo accountNumber exists", () => {
        setMockState({
            accounts: {
                selectedAccount: "Non-existent Account",
                accounts: [
                    {
                        accountNumber: "**********",
                        accountName: "Fallback Account",
                        schemeType: "savings",
                    },
                ],
            },
        });

        render(<SingleReviewBill {...defaultProps} />);

        // Should find the account by number in paymentInfo
        expect(usePaymentConfirmation).toHaveBeenCalledWith(
            expect.objectContaining({
                accountNumber: "**********",
            })
        );
    });

    test("handles account fallback when no paymentInfo accountNumber", () => {
        const propsWithoutAccountNumber = {
            ...defaultProps,
            paymentInfo: { narration: "Test payment" },
        };

        setMockState({
            accounts: {
                selectedAccount: "Non-existent Account",
                accounts: [
                    {
                        accountNumber: "**********",
                        accountName: "First Account",
                        schemeType: "current",
                    },
                ],
            },
        });

        render(<SingleReviewBill {...propsWithoutAccountNumber} />);

        // Should use first available account
        expect(usePaymentConfirmation).toHaveBeenCalledWith(
            expect.objectContaining({
                accountNumber: "**********",
            })
        );
    });

    test("handles no accounts available fallback", () => {
        const propsWithoutAccountNumber = {
            ...defaultProps,
            paymentInfo: { narration: "Test payment" },
        };

        setMockState({
            accounts: {
                selectedAccount: "Non-existent Account",
                accounts: [],
            },
        });

        render(<SingleReviewBill {...propsWithoutAccountNumber} />);

        // Should use empty string when no accounts available
        expect(usePaymentConfirmation).toHaveBeenCalledWith(
            expect.objectContaining({
                accountNumber: "",
            })
        );
    });
});

// 2FA Integration Tests
describe("2FA Integration Tests", () => {
    const mockHandleConfirm = jest.fn();

    const defaultProps = {
        billType: "airtime",
        phoneNumber: "***********",
        amount: 1000,
        networkProvider: "MTN",
        onBack: jest.fn(),
        onConfirm: jest.fn(),
        paymentInfo: { accountNumber: "**********", narration: "Airtime purchase" },
        onChangeBillDetails: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        setMockState(); // Reset to default state

        usePaymentConfirmation.mockReturnValue({
            isProcessing: false,
            currentStep: 3,
            handleConfirm: mockHandleConfirm,
        });

        useExitHandlers.mockReturnValue({
            isOpen: true,
            showExitConfirmation: false,
            handleClose: jest.fn(),
            handleConfirmExit: jest.fn(),
            handleCancelExit: jest.fn(),
        });
    });

    describe("2FA Flow Initiation", () => {
        test("should dispatch getTeamMemberDetails when confirm button is clicked and user has MFA enabled", () => {
            // State with MFA-enabled team member already fetched
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Click confirm button - this should trigger 2FA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Should dispatch getTeamMemberDetails action
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "transferMfa/getTeamMemberDetails/pending",
                })
            );

            // MFA modal should be shown when team member has MFA enabled
            expect(screen.getByTestId("settings-mfa-verification-modal")).toBeInTheDocument();
            expect(screen.getByTestId("mfa-type")).toHaveTextContent("SMS");
            expect(screen.getByTestId("mfa-email")).toHaveTextContent("<EMAIL>");
            expect(screen.getByTestId("mfa-phone")).toHaveTextContent("+**********");
        });

        test("should handle MFA flow when team member loading state changes", () => {
            // Start with loading state
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: true, success: false },
                    teamMember: null,
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Click confirm should trigger the flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            // No modal should be shown yet during loading
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();

            // Verify that the getTeamMemberDetails action was dispatched
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "transferMfa/getTeamMemberDetails/pending",
                })
            );
        });

        test("should dispatch getTeamMemberDetails first, then proceed directly when user has no MFA enabled", () => {
            // State with team member with NO MFA
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: false,
                        preferredMfaMethod: null,
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Click confirm button - this should trigger MFA check FIRST
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Should dispatch getTeamMemberDetails action
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "transferMfa/getTeamMemberDetails/pending",
                })
            );

            // Should not show MFA verification modal
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();

            // Should proceed directly to payment
            expect(mockHandleConfirm).toHaveBeenCalled();
        });

        test("should not show MFA modal when team member data is not available", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: false },
                    teamMember: null,
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
        });
    });

    describe("MFA Verification Process", () => {
        test("should handle successful MFA verification with AUTHENTICATOR method", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "AUTHENTICATOR",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow by clicking confirm
            fireEvent.click(screen.getByTestId("confirm-button"));

            // MFA modal should appear
            expect(screen.getByTestId("settings-mfa-verification-modal")).toBeInTheDocument();
            expect(screen.getByTestId("mfa-type")).toHaveTextContent("AUTHENTICATOR");

            // Verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // Should close MFA modal and proceed to payment
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
            expect(mockHandleConfirm).toHaveBeenCalled();
        });

        test("should handle successful MFA verification with SMS method", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            expect(screen.getByTestId("mfa-type")).toHaveTextContent("SMS");

            // Verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // Should close MFA modal and proceed to payment
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
            expect(mockHandleConfirm).toHaveBeenCalled();
        });

        test("should handle successful MFA verification with SECURITY_QUESTION method", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SECURITY_QUESTION",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            expect(screen.getByTestId("mfa-type")).toHaveTextContent("SECURITY_QUESTION");

            // Verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // Should close MFA modal and proceed to payment
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
            expect(mockHandleConfirm).toHaveBeenCalled();
        });

        test("should handle MFA verification cancellation", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow first
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Verify MFA modal is shown
            expect(screen.getByTestId("settings-mfa-verification-modal")).toBeInTheDocument();

            // Close MFA modal
            fireEvent.click(screen.getByTestId("close-mfa-verification"));

            // Should close MFA modal without proceeding to payment
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
            expect(mockHandleConfirm).not.toHaveBeenCalled();
        });
    });

    describe("Payment API Integration with MFA Token", () => {
        test("should pass MFA token to payment API when verification succeeds", () => {
            const mockHandleConfirmWithToken = jest.fn();
            usePaymentConfirmation.mockReturnValue({
                isProcessing: false,
                currentStep: 3,
                handleConfirm: mockHandleConfirmWithToken,
            });

            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Verify MFA first
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // Should call handleConfirm after MFA verification
            expect(mockHandleConfirmWithToken).toHaveBeenCalledTimes(1);
        });

        test("should pass empty token to payment API when user has no MFA", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: false,
                        preferredMfaMethod: null,
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Click confirm payment directly (no MFA required)
            fireEvent.click(screen.getByTestId("confirm-button"));

            expect(mockHandleConfirm).toHaveBeenCalledTimes(1);
        });
    });

    describe("Error Handling for MFA", () => {
        test("should handle API failure when fetching team member details", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: false, error: "API Error" },
                    teamMember: null,
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Should not show MFA modal on API failure
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
        });

        test("should handle loading state when fetching team member details", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: true, success: false },
                    teamMember: null,
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Should not show MFA modal while loading
            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
        });
    });

    describe("Button States and Loading", () => {
        test("should disable confirm button when MFA modal is open", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow to show modal
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Confirm button should be disabled when MFA modal is open
            expect(screen.getByTestId("confirm-button")).toBeDisabled();
        });

        test("should enable confirm button after successful MFA verification", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // Confirm button should be enabled after MFA verification
            expect(screen.getByTestId("confirm-button")).not.toBeDisabled();
        });
    });

    describe("State Cleanup", () => {
        test("should reset MFA state on successful payment completion", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa"));

            // resetAllStates should be called due to useEffect cleanup
            expect(resetAllStates).toHaveBeenCalled();
        });

        test("should reset MFA state on component unmount", () => {
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                    },
                },
            });

            const { unmount } = render(<SingleReviewBill {...defaultProps} />);

            // Trigger MFA flow to set state that will trigger cleanup
            fireEvent.click(screen.getByTestId("confirm-button"));

            // Unmount component
            unmount();

            // Should clean up MFA state on unmount
            expect(resetAllStates).toHaveBeenCalled();
        });

        test("should handle showApprovals state correctly", () => {
            render(<SingleReviewBill {...defaultProps} />);

            // Multi-party approval should be shown by default
            const approvalElement = screen.getByTestId("multi-party-approval-mock");
            expect(approvalElement).toBeInTheDocument();
            expect(approvalElement).toHaveAttribute("data-is-open", "true");
        });

        test("should test all helper functions with various inputs", () => {
            const {
                getBillIdentifier,
                getCustomerDetails,
                getBillTypeDisplay,
                getBillerValue,
                getFullCustomerDetails,
                getServiceId,
            } = require("@/components/page-components/dashboard/bill-payments/single-payment/display-helpers");

            // Test getBillIdentifier
            expect(getBillIdentifier("electricity", "ELEC123")).toBe("ELEC123");
            expect(getBillIdentifier("cable-tv", undefined, "CARD456")).toBe("CARD456");
            expect(getBillIdentifier("airtime", undefined, undefined, "***********")).toBe("***********");
            expect(getBillIdentifier("electricity")).toBe("N/A");
            expect(getBillIdentifier("data", undefined, undefined, "08098765432")).toBe("08098765432");

            // Test getCustomerDetails
            expect(getCustomerDetails("electricity", "John Doe, Lagos")).toBe("John Doe");
            expect(getCustomerDetails("cable-tv", "Jane Smith, Active")).toBe("Jane Smith");
            expect(getCustomerDetails("airtime", "Simple Name")).toBe("Simple Name");
            expect(getCustomerDetails("electricity")).toBe("N/A");
            expect(getCustomerDetails("data", "  Trimmed Name  ")).toBe("Trimmed Name");

            // Test getBillTypeDisplay
            expect(getBillTypeDisplay("airtime", "Mobile Airtime")).toBe("Mobile Airtime");
            expect(getBillTypeDisplay("cable-tv")).toBe("Cable Tv");
            expect(getBillTypeDisplay("electricity")).toBe("Electricity");
            expect(getBillTypeDisplay("multi-word-type")).toBe("Multi Word Type");

            // Test getBillerValue
            expect(getBillerValue("electricity", "EKEDC", "Prepaid")).toBe("EKEDC, Prepaid");
            expect(getBillerValue("cable-tv", "DSTV", "Premium")).toBe("DSTV, Premium");
            expect(getBillerValue("electricity", "EKEDC")).toBe("EKEDC");
            expect(getBillerValue("airtime", undefined, undefined, "MTN")).toBe("MTN");
            expect(getBillerValue("data", undefined, undefined, "Airtel")).toBe("Airtel");

            // Test getFullCustomerDetails
            expect(getFullCustomerDetails("electricity", "Full Details Here")).toBe("Full Details Here");
            expect(getFullCustomerDetails("airtime", "  Spaced Details  ")).toBe("Spaced Details");
            expect(getFullCustomerDetails("cable-tv")).toBe("N/A");

            // Test getServiceId
            expect(getServiceId("electricity", "ELEC123")).toBe("ELEC123");
            expect(getServiceId("cable-tv", undefined, "CARD456")).toBe("CARD456");
            expect(getServiceId("airtime", undefined, undefined, "MTN")).toBe("MTN");
            expect(getServiceId("data", undefined, undefined, "Glo")).toBe("Glo");
        });

        test("should handle edge cases in helper functions", () => {
            const {
                getBillIdentifier,
                getCustomerDetails,
                getBillerValue,
                getServiceId,
            } = require("@/components/page-components/dashboard/bill-payments/single-payment/display-helpers");

            // Edge cases for getBillIdentifier
            expect(getBillIdentifier("unknown-type", "id", "card", "phone")).toBe("phone");
            expect(getBillIdentifier("electricity", "", "card", "phone")).toBe("");

            // Edge cases for getCustomerDetails
            expect(getCustomerDetails("electricity", "")).toBe("N/A");
            expect(getCustomerDetails("cable-tv", "OnlyName")).toBe("OnlyName");
            expect(getCustomerDetails("electricity", "Name,")).toBe("Name");

            // Edge cases for getBillerValue
            expect(getBillerValue("electricity", "", "")).toBe("");
            expect(getBillerValue("unknown", "", "", "")).toBe("");

            // Edge cases for getServiceId
            expect(getServiceId("electricity", "")).toBe("");
            expect(getServiceId("unknown-type")).toBe("");
        });

        test("should test component state changes and callbacks", () => {
            const mockHandleClose = jest.fn();
            const mockHandleConfirmExit = jest.fn();

            useExitHandlers.mockReturnValue({
                isOpen: true,
                showExitConfirmation: false,
                handleClose: mockHandleClose,
                handleConfirmExit: mockHandleConfirmExit,
                handleCancelExit: jest.fn(),
            });

            render(<SingleReviewBill {...defaultProps} />);

            // Trigger close
            fireEvent.click(screen.getByTestId("drawer-close-button"));
            expect(mockHandleClose).toHaveBeenCalled();
        });

        test("should handle all account selection scenarios", () => {
            // Test when account is found by formatted string but fallback logic is still exercised
            setMockState({
                accounts: {
                    selectedAccount: "Current account **********",
                    accounts: [
                        {
                            accountNumber: "**********",
                            accountName: "Test Account",
                            schemeType: "current",
                        },
                        {
                            accountNumber: "**********",
                            accountName: "Another Account",
                            schemeType: "savings",
                        },
                    ],
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            expect(usePaymentConfirmation).toHaveBeenCalledWith(
                expect.objectContaining({
                    accountNumber: "**********",
                })
            );
        });

        test("should handle formatAccountName and formatAccountNumber calls", () => {
            const { formatAccountName, formatAccountNumber } = require("@/redux/features/accounts");

            // These functions should be called when determining the account
            render(<SingleReviewBill {...defaultProps} />);

            expect(formatAccountName).toHaveBeenCalled();
            expect(formatAccountNumber).toHaveBeenCalled();
        });

        test("should cover all display helper edge cases", () => {
            const {
                getBillerValue,
            } = require("@/components/page-components/dashboard/bill-payments/single-payment/display-helpers");

            // Test the specific uncovered branches in getBillerValue
            expect(getBillerValue("electricity", "Provider", "")).toBe("Provider");
            expect(getBillerValue("cable-tv", "Provider", "")).toBe("Provider");
            expect(getBillerValue("electricity", "", "Package")).toBe(", Package");
        });

        test("should test specific component lifecycle scenarios", () => {
            // Test component with minimal MFA state changes
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: false },
                    teamMember: null,
                },
            });

            const { rerender } = render(<SingleReviewBill {...defaultProps} />);

            // Change to MFA state and rerender
            setMockState({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        mfaStatus: false,
                        preferredMfaMethod: null,
                    },
                },
            });

            rerender(<SingleReviewBill {...defaultProps} />);

            // Test different account scenarios to cover more branches
            const noAccountProps = {
                ...defaultProps,
                paymentInfo: { narration: "Test" }, // No accountNumber
            };

            setMockState({
                accounts: {
                    selectedAccount: "Unknown Account",
                    accounts: [],
                },
            });

            rerender(<SingleReviewBill {...noAccountProps} />);
        });
    });
});

describe("handlePaymentError function", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        console.error = jest.fn();
    });

    test("logs error and calls sendCatchFeedback with Error instance", () => {
        const mockSetCurrentStep = jest.fn();
        const testError = new Error("Test error message");

        handlePaymentError(testError, mockSetCurrentStep);

        expect(sendCatchFeedback).toHaveBeenCalledWith(testError);
        expect(mockSetCurrentStep).toHaveBeenCalledWith(3);
    });

    test("creates new Error when non-Error is passed", () => {
        const mockSetCurrentStep = jest.fn();
        const nonErrorObject = { message: "Not an Error instance" };

        handlePaymentError(nonErrorObject, mockSetCurrentStep);

        expect(sendCatchFeedback).toHaveBeenCalledWith(
            expect.objectContaining({
                message: "Payment failed. Please try again",
            })
        );
        expect(mockSetCurrentStep).toHaveBeenCalledWith(3);
    });

    describe("Pay From Field Display", () => {
        const defaultProps = {
            billType: "electricity",
            identificationNumber: "**********1",
            amount: 5000,
            serviceProvider: "AEDC",
            package: "Prepaid",
            onBack: jest.fn(),
            onConfirm: jest.fn(),
            paymentInfo: { narration: "Test payment", accountNumber: "**********" },
            categoryName: "Electricity",
        };

        beforeEach(() => {
            jest.clearAllMocks();
            setMockState(); // Set default mock state

            // Mock usePaymentConfirmation
            usePaymentConfirmation.mockReturnValue({
                isProcessing: false,
                currentStep: 3,
                handleConfirm: jest.fn(),
            });

            // Mock useExitHandlers
            useExitHandlers.mockReturnValue({
                isOpen: true,
                showExitConfirmation: false,
                handleClose: jest.fn(),
                handleConfirmExit: jest.fn(),
                handleCancelExit: jest.fn(),
            });
        });

        it("displays selected account in Pay from field when account is manually selected", () => {
            // Mock state with manually selected account
            setMockState({
                accounts: {
                    selectedAccount: "Current Account ****7890", // Manually selected formatted account
                    accounts: [
                        {
                            accountNumber: "**********",
                            accountName: "Current Account",
                            schemeType: "current",
                        },
                        {
                            accountNumber: "**********",
                            accountName: "Savings Account",
                            schemeType: "savings",
                        },
                    ],
                },
            });

            render(<SingleReviewBill {...defaultProps} />);

            // The BillDetails component should receive the selectedAccount
            expect(screen.getByTestId("bill-details-mock")).toBeInTheDocument();
            // The selectedAccount should be passed to BillDetails
            expect(mockBillDetails).toHaveBeenCalledWith(
                expect.objectContaining({
                    selectedAccount: "Current Account ****7890",
                })
            );
        });

        it("displays selected account in Pay from field when account is auto-selected", () => {
            // Mock state simulating auto-selected account (first account with formatted display)
            setMockState({
                accounts: {
                    selectedAccount: "Current Account ****7890", // Auto-selected formatted account
                    accounts: [
                        {
                            accountNumber: "**********",
                            accountName: "Current Account",
                            schemeType: "current",
                        },
                    ],
                },
            });

            const propsWithAutoSelectedAccount = {
                ...defaultProps,
                paymentInfo: {
                    narration: "Test payment",
                    accountNumber: "**********", // Auto-selected account number
                },
            };

            render(<SingleReviewBill {...propsWithAutoSelectedAccount} />);

            // The BillDetails component should receive the selectedAccount
            expect(screen.getByTestId("bill-details-mock")).toBeInTheDocument();
            // The selectedAccount should be passed to BillDetails
            expect(mockBillDetails).toHaveBeenCalledWith(
                expect.objectContaining({
                    selectedAccount: "Current Account ****7890",
                })
            );
        });

        it("handles fallback to paymentInfo.accountNumber when selectedAccount is empty but account exists", () => {
            // Mock state where selectedAccount is empty but paymentInfo has accountNumber
            setMockState({
                accounts: {
                    selectedAccount: "", // Empty selected account (auto-selection didn't work)
                    accounts: [
                        {
                            accountNumber: "**********",
                            accountName: "Current Account",
                            schemeType: "current",
                        },
                    ],
                },
            });

            const propsWithAccountNumber = {
                ...defaultProps,
                paymentInfo: {
                    narration: "Test payment",
                    accountNumber: "**********", // Account number exists in paymentInfo
                },
            };

            render(<SingleReviewBill {...propsWithAccountNumber} />);

            // The BillDetails component should receive the empty selectedAccount
            // (the component fallback logic will handle this)
            expect(screen.getByTestId("bill-details-mock")).toBeInTheDocument();
            expect(mockBillDetails).toHaveBeenCalledWith(
                expect.objectContaining({
                    selectedAccount: "", // Empty selectedAccount, fallback logic in component will handle
                })
            );
        });

        it("displays account consistently regardless of selection method", () => {
            const testAccountNumber = "**********";
            const testFormattedAccount = "Test Account ****7890";

            // Test auto-selection scenario
            setMockState({
                accounts: {
                    selectedAccount: testFormattedAccount,
                    accounts: [
                        {
                            accountNumber: testAccountNumber,
                            accountName: "Test Account",
                            schemeType: "current",
                        },
                    ],
                },
            });

            const { rerender } = render(<SingleReviewBill {...defaultProps} />);

            // Verify account display for auto-selected account
            expect(mockBillDetails).toHaveBeenCalledWith(
                expect.objectContaining({
                    selectedAccount: testFormattedAccount,
                })
            );

            // Clear previous calls
            mockBillDetails.mockClear();

            // Test manual selection scenario (same account, same formatted display)
            const manualSelectionProps = {
                ...defaultProps,
                paymentInfo: {
                    narration: "Test payment",
                    accountNumber: testAccountNumber, // Manually selected account
                },
            };

            rerender(<SingleReviewBill {...manualSelectionProps} />);

            // Verify account display for manually selected account (should be the same)
            expect(mockBillDetails).toHaveBeenCalledWith(
                expect.objectContaining({
                    selectedAccount: testFormattedAccount,
                })
            );
        });
    });
});
