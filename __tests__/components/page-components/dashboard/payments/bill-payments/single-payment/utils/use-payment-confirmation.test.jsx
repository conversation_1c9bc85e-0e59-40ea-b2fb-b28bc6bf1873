import { renderHook, act } from "@testing-library/react";
import { usePaymentConfirmation } from "@/components/page-components/dashboard/bill-payments/single-payment/utils/use-payment-confirmation";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { openVerifyPinModal } from "@/redux/slices/securitySlice";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { fetchPaymentItems, initiateBillPayment } from "@/redux/actions/billPaymentThunks";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import * as paymentUtils from "@/components/page-components/dashboard/bill-payments/single-payment/utils/payment-utils";
import React from "react";

// Mock dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/slices/securitySlice", () => ({
    openVerifyPinModal: jest.fn(),
}));

jest.mock("@/redux/slices/singleBillPayment", () => ({
    resetSingleBillPayment: jest.fn(),
}));

jest.mock("@/redux/actions/billPaymentThunks", () => ({
    fetchPaymentItems: jest.fn(),
    initiateBillPayment: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock the payment utils
jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/utils/payment-utils", () => ({
    createPaymentParams: jest.fn(() => ({ mock: "params" })),
    validatePaymentParams: jest.fn(() => []),
}));

// Mock console.error
console.error = jest.fn();

describe("usePaymentConfirmation", () => {
    // Common test props
    const defaultProps = {
        billType: "airtime",
        amount: 1000,
        accountNumber: "**********",
        corporateId: "CORP123",
        phoneNumber: "***********",
        paymentInfo: { narration: "Airtime recharge" },
        networkProvider: "MTN",
        categoryName: "Airtime",
        onConfirm: jest.fn(),
        refetchRecentBills: jest.fn().mockResolvedValue(),
    };

    // Mock dispatch function
    const mockDispatch = jest.fn();
    const mockUnwrap = jest.fn();

    // Mock state setter for currentStep
    let mockSetCurrentStep;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Create a fresh mock for setCurrentStep
        mockSetCurrentStep = jest.fn();

        // Setup default mock implementations
        mockDispatch.mockImplementation((action) => {
            if (typeof action === "function") {
                return action(mockDispatch);
            }
            return { unwrap: mockUnwrap };
        });

        mockUnwrap.mockResolvedValue({ success: true });

        useAppDispatch.mockReturnValue(mockDispatch);

        // Default useAppSelector implementation
        useAppSelector.mockImplementation((selector) => {
            // Mock state structure
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: null,
                        success: false,
                        open: false,
                    },
                },
            };

            return selector(mockState);
        });

        // Mock initiateBillPayment to return an object with unwrap method
        initiateBillPayment.mockReturnValue({
            unwrap: mockUnwrap,
        });

        // Reset payment utils mocks
        paymentUtils.createPaymentParams.mockReturnValue({ mock: "params" });
        paymentUtils.validatePaymentParams.mockReturnValue([]);
    });

    test("should initialize with correct default values", () => {
        const { result } = renderHook(() => usePaymentConfirmation(defaultProps));

        expect(result.current.isProcessing).toBe(false);
        expect(result.current.currentStep).toBe(3);
    });

    test("should find biller ID when networkProvider changes for non-electricity/cable-tv bills", () => {
        renderHook(() => usePaymentConfirmation(defaultProps));

        expect(fetchPaymentItems).toHaveBeenCalledWith({
            billerId: "123",
        });
    });

    // Test specifically targeting the biller matching logic in lines 94-95
    test("should find biller using billername property", () => {
        // Set up biller data with billername property but no shortName
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN", // Using billername property
                                // No shortName property
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: null,
                        success: false,
                        open: false,
                    },
                },
            };

            return selector(mockState);
        });

        renderHook(() => usePaymentConfirmation(defaultProps));

        // Verify billername was used to match and fetch items
        expect(fetchPaymentItems).toHaveBeenCalledWith({
            billerId: "123",
        });
    });

    test("should find biller using billerName property", () => {
        // Set up biller data with billerName property (camelCase version) but no other matching properties
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerId: "123", // Note: using billerId instead of billerid
                                billerName: "MTN", // Using camelCase billerName property
                                // No shortName or billername properties
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: null,
                        success: false,
                        open: false,
                    },
                },
            };

            return selector(mockState);
        });

        renderHook(() => usePaymentConfirmation(defaultProps));

        // Verify billerName was used to match and fetch items
        expect(fetchPaymentItems).toHaveBeenCalledWith({
            billerId: "123",
        });
    });

    test("should not fetch payment items for electricity billType", () => {
        renderHook(() =>
            usePaymentConfirmation({
                ...defaultProps,
                billType: "electricity",
            })
        );

        expect(fetchPaymentItems).not.toHaveBeenCalled();
    });

    test("should open PIN verification modal when handleConfirm is called", () => {
        const { result } = renderHook(() => usePaymentConfirmation(defaultProps));

        act(() => {
            result.current.handleConfirm();
        });

        expect(openVerifyPinModal).toHaveBeenCalled();
    });

    test("should reset state when PIN modal is closed without verification", async () => {
        // First render hook and initiate PIN verification
        const { result, rerender } = renderHook(() => usePaymentConfirmation(defaultProps));

        act(() => {
            result.current.handleConfirm();
        });

        // Now update the selector to simulate modal closed without verification
        useAppSelector.mockImplementation((selector) => {
            return {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: null,
                        success: false,
                        open: false, // Modal is now closed
                    },
                },
            };
        });

        // Rerender to trigger the useEffect
        rerender();

        // Verify state is reset
        expect(result.current.isProcessing).toBe(false);
    });

    // Test for handlePaymentError with a real Error object - triggering the error path directly
    test("should handle payment errors properly when Error object is thrown", async () => {
        // Mock the error that will be thrown during payment processing
        const errorToThrow = new Error("Test payment failed");
        mockUnwrap.mockRejectedValue(errorToThrow);

        // Setup the security state for a verified PIN and initiated verification
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: "1234", // PIN is already verified
                        success: true, // Verification was successful
                        open: false, // Modal is closed
                    },
                },
            };

            return selector(mockState);
        });

        // Render the hook with the mocked state
        let hookResult;

        // We need to set up the React setState mock for these tests
        // by spying on useState
        const originalUseState = React.useState;
        jest.spyOn(React, "useState").mockImplementation((initialValue) => {
            // For currentStep state
            if (initialValue === 3) {
                return [3, mockSetCurrentStep];
            }

            // For other useState calls, use the original
            return originalUseState(initialValue);
        });

        try {
            hookResult = renderHook(() => usePaymentConfirmation(defaultProps));

            // First initiate PIN verification
            act(() => {
                hookResult.result.current.handleConfirm();
            });

            // Allow time for the async error handling to complete
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Verify error handling behavior

            expect(sendCatchFeedback).toHaveBeenCalledWith(errorToThrow);
        } finally {
            // Restore original useState
            React.useState.mockRestore();
        }
    });

    // Test for handlePaymentError with a non-Error object
    test("should handle payment errors properly when non-Error object is thrown", async () => {
        // Mock a non-Error object that will be thrown during payment
        const nonErrorObject = { status: 500, message: "API Error" };
        mockUnwrap.mockRejectedValue(nonErrorObject);

        // Setup the state for payment processing
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: "1234", // PIN is verified
                        success: true,
                        open: false,
                    },
                },
            };

            return selector(mockState);
        });

        // We need to set up the React useState mock for these tests
        const originalUseState = React.useState;
        jest.spyOn(React, "useState").mockImplementation((initialValue) => {
            // For currentStep state
            if (initialValue === 3) {
                return [3, mockSetCurrentStep];
            }
            // For other useState calls, use the original
            return originalUseState(initialValue);
        });

        try {
            // Render the hook
            const { result } = renderHook(() => usePaymentConfirmation(defaultProps));

            // Initiate payment that will fail
            act(() => {
                result.current.handleConfirm();
            });

            // Allow time for the async operations to complete
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Verify error handling behavior for non-Error objects

            expect(sendCatchFeedback).toHaveBeenCalledWith(expect.any(Error));
        } finally {
            // Restore original useState
            React.useState.mockRestore();
        }
    });

    // Test for validation errors in payment params
    test("should handle validation errors before initiating payment", async () => {
        // Configure validatePaymentParams to return validation errors
        const validationError = "Phone number is required";
        paymentUtils.validatePaymentParams.mockReturnValue([validationError]);

        // Setup the state for payment processing
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: "1234", // PIN is verified
                        success: true,
                        open: false,
                    },
                },
            };

            return selector(mockState);
        });

        // We need to set up the React useState mock for these tests
        const originalUseState = React.useState;
        jest.spyOn(React, "useState").mockImplementation((initialValue) => {
            // For currentStep state
            if (initialValue === 3) {
                return [3, mockSetCurrentStep];
            }
            // For other useState calls, use the original
            return originalUseState(initialValue);
        });

        try {
            // Render the hook with props that will trigger validation error
            const { result } = renderHook(() =>
                usePaymentConfirmation({
                    ...defaultProps,
                    phoneNumber: "", // Empty phone number that should trigger validation error
                })
            );

            // Initiate payment that will be validated and fail
            act(() => {
                result.current.handleConfirm();
            });

            // Allow time for validation and error handling
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Verify validation errors are caught and handled
            expect(sendCatchFeedback).toHaveBeenCalledWith(expect.any(Error));
            expect(initiateBillPayment).not.toHaveBeenCalled(); // Payment should not proceed
        } finally {
            // Restore original useState
            React.useState.mockRestore();
        }
    });

    // Test for successful payment flow including refetchRecentBills, feedback, and onConfirm
    test("should complete successful payment process and call required actions", async () => {
        // Mock successful payment result
        mockUnwrap.mockResolvedValue({ success: true, reference: "REF123456" });

        // Mock refetchRecentBills
        const mockRefetchRecentBills = jest.fn().mockResolvedValue();

        // Setup state with PIN already verified
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: "1234", // PIN is verified
                        success: true, // Verification was successful
                        open: false, // Modal is closed
                    },
                },
            };

            return selector(mockState);
        });

        // Instead of modifying React.useState, we'll set up the hook directly
        const { result, rerender } = renderHook(() =>
            usePaymentConfirmation({
                ...defaultProps,
                refetchRecentBills: mockRefetchRecentBills,
            })
        );

        // Call handleConfirm to start the process
        act(() => {
            result.current.handleConfirm();
        });

        // Allow time for the async payment processing to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 100));
        });

        // Verify successful path execution
        expect(initiateBillPayment).toHaveBeenCalled();
        expect(mockRefetchRecentBills).toHaveBeenCalled();
        expect(resetSingleBillPayment).toHaveBeenCalled();
        expect(sendFeedback).toHaveBeenCalledWith("Payment initiated successfully", "success");
        expect(defaultProps.onConfirm).toHaveBeenCalled();
    });

    // Test case for when result is falsy and should throw an error
    test("should throw error when payment result is falsy", async () => {
        // Mock unsuccessful payment result (falsy)
        mockUnwrap.mockResolvedValue(null);

        // Setup state with PIN already verified
        useAppSelector.mockImplementation((selector) => {
            const mockState = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billerid: "123",
                                billername: "MTN",
                                shortName: "MTN",
                            },
                        ],
                        loading: false,
                        error: null,
                    },
                    paymentItems: {
                        data: [{ id: "1", name: "Airtime", amount: "500" }],
                        loading: false,
                        error: null,
                    },
                },
                security: {
                    verifyPin: {
                        pin: "1234", // PIN is verified
                        success: true, // Verification was successful
                        open: false, // Modal is closed
                    },
                },
            };

            return selector(mockState);
        });

        // Render hook and call handleConfirm
        const { result } = renderHook(() => usePaymentConfirmation(defaultProps));

        // Call handleConfirm to start the process
        act(() => {
            result.current.handleConfirm();
        });

        // Allow time for the async payment processing to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 100));
        });

        // Verify error path was taken
        expect(sendCatchFeedback).toHaveBeenCalledWith(expect.any(Error));
        expect(sendCatchFeedback.mock.calls[0][0].message).toBe("Payment failed. Please try again.");
        expect(resetSingleBillPayment).not.toHaveBeenCalled();
        expect(sendFeedback).not.toHaveBeenCalled();
        expect(defaultProps.onConfirm).not.toHaveBeenCalled();
    });
});
