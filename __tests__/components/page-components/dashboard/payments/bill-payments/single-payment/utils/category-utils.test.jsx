/**
 * Tests for Category Utils
 *
 * Purpose: Test category utility functions for bill payment categorization
 * Coverage: shouldSkipValidation function logic, getCustomerIdLabel function
 *
 * @jest-environment jsdom
 */

import {
    shouldSkipValidation,
    getCustomerIdLabel,
} from "@/components/page-components/dashboard/bill-payments/single-payment/utils/category-utils";

describe("Category Utils", () => {
    describe("shouldSkipValidation", () => {
        test('should return true for categories containing both "mobile" and "recharge"', () => {
            expect(shouldSkipValidation("mobile recharge")).toBe(true);
            expect(shouldSkipValidation("Mobile Recharge")).toBe(true); // Case insensitive
            expect(shouldSkipValidation("recharge mobile")).toBe(true); // Order doesn't matter
            expect(shouldSkipValidation("mobile data recharge")).toBe(true); // With additional words
            expect(shouldSkipValidation("prepaid recharge mobile")).toBe(true); // With additional words
        });

        test('should return false for categories containing only "mobile"', () => {
            expect(shouldSkipValidation("mobile")).toBe(false);
            expect(shouldSkipValidation("mobile data")).toBe(false);
            expect(shouldSkipValidation("mobile airtime")).toBe(false);
            expect(shouldSkipValidation("prepaid mobile")).toBe(false);
        });

        test('should return false for categories containing only "recharge"', () => {
            expect(shouldSkipValidation("recharge")).toBe(false);
            expect(shouldSkipValidation("data recharge")).toBe(false);
            expect(shouldSkipValidation("prepaid recharge")).toBe(false);
        });

        test('should return false for categories containing neither "mobile" nor "recharge"', () => {
            expect(shouldSkipValidation("utility")).toBe(false);
            expect(shouldSkipValidation("cable tv")).toBe(false);
            expect(shouldSkipValidation("electricity")).toBe(false);
            expect(shouldSkipValidation("insurance")).toBe(false);
            expect(shouldSkipValidation("airtime")).toBe(false);
            expect(shouldSkipValidation("data")).toBe(false);
            expect(shouldSkipValidation("prepaid")).toBe(false);
            expect(shouldSkipValidation("postpaid")).toBe(false);
        });

        test("should handle empty string", () => {
            expect(shouldSkipValidation("")).toBe(false);
        });
    });

    describe("getCustomerIdLabel", () => {
        test('should return "Smart Card Number" for cable-tv bill type', () => {
            expect(getCustomerIdLabel("any category", "cable-tv")).toBe("Smart Card Number");
        });

        test('should return "Phone number" for mobile recharge categories', () => {
            expect(getCustomerIdLabel("mobile recharge", "")).toBe("Phone number");
            expect(getCustomerIdLabel("Mobile Recharge", "")).toBe("Phone number");
            expect(getCustomerIdLabel("recharge mobile", "")).toBe("Phone number");
        });

        test('should return "Customer ID" for other categories', () => {
            expect(getCustomerIdLabel("utility", "")).toBe("Customer ID");
            expect(getCustomerIdLabel("electricity", "")).toBe("Customer ID");
            expect(getCustomerIdLabel("insurance", "")).toBe("Customer ID");
            expect(getCustomerIdLabel("mobile", "")).toBe("Customer ID"); // Only mobile, no recharge
            expect(getCustomerIdLabel("recharge", "")).toBe("Customer ID"); // Only recharge, no mobile
        });

        test("should prioritize bill type over category name", () => {
            // Cable TV bill type should override mobile recharge category
            expect(getCustomerIdLabel("mobile recharge", "cable-tv")).toBe("Smart Card Number");
        });
    });
});
