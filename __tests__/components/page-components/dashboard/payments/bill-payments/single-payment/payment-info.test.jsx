import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import PaymentInfo from "@/components/page-components/dashboard/bill-payments/single-payment/payment-info";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useExitHandlers } from "@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers";
import { updatePaymentInfo, resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { resetAccounts, setSelectedAccount } from "@/redux/features/accounts";

// Mock shared utilities
jest.mock("@/components/page-components/dashboard/bill-payments/common/account-utils", () => ({
    formatAccountName: jest.fn((account) => account.accountName || "Savings Account"),
    formatAccountNumber: jest.fn((accountNumber) => {
        const lastFourDigits = accountNumber.slice(-4);
        return `****${lastFourDigits}`;
    }),
}));

// Mock dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(),
}));

// Fix mock action creators to return proper structure
jest.mock("@/redux/slices/singleBillPayment", () => ({
    updatePaymentInfo: jest.fn((payload) => ({
        type: "singleBillPayment/updatePaymentInfo",
        payload,
    })),
    resetSingleBillPayment: jest.fn(() => ({ type: "singleBillPayment/resetSingleBillPayment" })),
}));

jest.mock("@/redux/features/accounts", () => ({
    resetAccounts: jest.fn(() => ({ type: "accounts/resetAccounts" })),
    setSelectedAccount: jest.fn((payload) => ({
        type: "accounts/setSelectedAccount",
        payload,
    })),
}));

// Mock child components - Ensure original functions are called for proper coverage
jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, title, onClose, showExitConfirmation, onConfirmExit, onCancelExit }) =>
        isOpen ? (
            <div data-testid="drawer">
                <h1 data-testid="drawer-title">{title}</h1>
                <button data-testid="close-drawer" onClick={() => onClose && onClose()}>
                    Close
                </button>
                {showExitConfirmation && (
                    <div data-testid="exit-confirmation">
                        <button data-testid="confirm-exit" onClick={() => onConfirmExit && onConfirmExit()}>
                            Confirm Exit
                        </button>
                        <button data-testid="cancel-exit" onClick={() => onCancelExit && onCancelExit()}>
                            Cancel Exit
                        </button>
                    </div>
                )}
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper">
            Step {currentStep + 1} of {steps.length}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/account-selector", () => ({
    __esModule: true,
    default: ({ selectedAccount, onAccountChange }) => {
        return (
            <div data-testid="account-selector">
                <select
                    data-testid="account-select"
                    value={selectedAccount || ""}
                    onChange={(e) => {
                        // Call original function for proper coverage
                        if (onAccountChange) {
                            onAccountChange(e.target.value);
                        }
                    }}
                >
                    <option value="">Select Account</option>
                    <option value="**********">Account ********** - ₦50,000.00</option>
                    <option value="**********">Account ********** - ₦25,000.00</option>
                </select>
            </div>
        );
    },
}));

jest.mock("@/components/common/text-area", () => ({
    __esModule: true,
    default: ({ value, onChange, name, label, placeholder, minHeight, maxHeight, ...props }) => (
        <div data-testid={`textarea-${name}`}>
            <label>{label}</label>
            <textarea
                data-testid={`textarea-field-${name}`}
                value={value || ""}
                onChange={(e) => {
                    // Call original function for proper coverage
                    if (onChange) {
                        onChange(e);
                    }
                }}
                placeholder={placeholder}
                style={{
                    minHeight: minHeight,
                    maxHeight: maxHeight,
                }}
                {...props}
            />
        </div>
    ),
}));

// Fix button mock to properly handle disabled state
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, type, variant, ...props }) => (
        <button
            data-testid={`button-${variant || "default"}`}
            onClick={(e) => {
                // Only call onClick if not disabled
                if (onClick && !disabled) {
                    onClick(e);
                }
            }}
            disabled={disabled}
            type={type}
            {...props}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/auth", () => ({
    InfoIcon: (props) => (
        <div data-testid="info-icon" {...props}>
            !
        </div>
    ),
}));

describe("PaymentInfo (Single Payment)", () => {
    const mockDispatch = jest.fn();
    const mockOnBack = jest.fn();
    const mockOnContinue = jest.fn();
    const mockOnFieldChange = jest.fn();

    // Mock Redux state
    const mockReduxState = {
        singleBillPayment: {
            paymentInfo: {
                narration: "",
                accountNumber: "",
            },
        },
        account: {
            accounts: [
                { accountNumber: "**********", balance: 50000 },
                { accountNumber: "**********", balance: 25000 },
            ],
            loadingStatus: "idle",
        },
    };

    // Mock exit handlers
    const mockExitHandlers = {
        isOpen: true,
        showExitConfirmation: false,
        handleClose: jest.fn(),
        handleConfirmExit: jest.fn(),
        handleCancelExit: jest.fn(),
    };

    // Default props
    const defaultProps = {
        onBack: mockOnBack,
        onContinue: mockOnContinue,
        billDetails: {
            type: "electricity",
            amount: 5000,
            serviceProvider: "EKEDC",
            package: "Prepaid",
            identificationNumber: "**********1",
        },
        defaultValues: {
            narration: "",
            accountNumber: "",
        },
        onFieldChange: mockOnFieldChange,
        categoryName: "Electricity",
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Clear all timers before each test
        jest.clearAllTimers();
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
        useExitHandlers.mockReturnValue(mockExitHandlers);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe("Component Rendering", () => {
        it("renders the component correctly with proper title", async () => {
            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            expect(screen.getByTestId("drawer")).toBeInTheDocument();
            expect(screen.getByTestId("drawer-title")).toHaveTextContent("Electricity bill payment");
            expect(screen.getByText("Payment information")).toBeInTheDocument();
            expect(screen.getByTestId("stepper")).toBeInTheDocument();
            expect(screen.getByTestId("account-selector")).toBeInTheDocument();
            expect(screen.getByTestId("textarea-narration")).toBeInTheDocument();
        });

        it("uses default title when no category name is provided", async () => {
            const propsWithoutCategory = {
                ...defaultProps,
                categoryName: undefined,
            };

            await act(async () => {
                render(<PaymentInfo {...propsWithoutCategory} />);
            });

            expect(screen.getByTestId("drawer-title")).toHaveTextContent("Bill payment");
        });
    });

    describe("State Initialization", () => {
        it("initializes state from persisted Redux values or falls back to defaults", async () => {
            const stateWithPersistedData = {
                ...mockReduxState,
                singleBillPayment: {
                    paymentInfo: {
                        narration: "Persisted narration",
                        accountNumber: "**********",
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithPersistedData));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const narrationField = screen.getByTestId("textarea-field-narration");
            const accountSelect = screen.getByTestId("account-select");

            expect(narrationField.value).toBe("Persisted narration");
            expect(accountSelect.value).toBe("**********");
        });
    });

    describe("Account Management", () => {
        it("auto-selects first account and handles account changes", async () => {
            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            // Should auto-select first account
            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));
            });

            const accountSelect = screen.getByTestId("account-select");

            // Test account change
            await act(async () => {
                fireEvent.change(accountSelect, { target: { value: "**********" } });
            });

            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));
            expect(mockOnFieldChange).toHaveBeenCalledWith("accountNumber", "**********");
        });
    });

    describe("Narration Handling", () => {
        beforeEach(() => {
            jest.useFakeTimers();
        });

        it("handles narration changes with debouncing", async () => {
            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const narrationField = screen.getByTestId("textarea-field-narration");

            // Test rapid changes - should only dispatch final value
            await act(async () => {
                fireEvent.change(narrationField, { target: { value: "First" } });
                fireEvent.change(narrationField, { target: { value: "Final" } });
            });

            // Fast forward timers to trigger debounced update
            await act(async () => {
                jest.advanceTimersByTime(300);
            });

            await waitFor(() => {
                const finalUpdateCalls = mockDispatch.mock.calls.filter(
                    (call) =>
                        call[0] &&
                        call[0].type === "singleBillPayment/updatePaymentInfo" &&
                        call[0].payload &&
                        call[0].payload.narration === "Final"
                );
                expect(finalUpdateCalls.length).toBeGreaterThan(0);
            });
        });
    });

    describe("Form Validation", () => {
        it("validates required fields and prevents submission with errors", async () => {
            const stateWithEmptyAccount = {
                ...mockReduxState,
                account: {
                    accounts: [],
                    loadingStatus: "idle",
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithEmptyAccount));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const narrationField = screen.getByTestId("textarea-field-narration");
            const continueButton = screen.getByTestId("button-primary");

            // Test empty narration
            await act(async () => {
                fireEvent.change(narrationField, { target: { value: "" } });
                fireEvent.click(continueButton);
            });

            expect(mockOnContinue).not.toHaveBeenCalled();

            // Test whitespace-only narration
            await act(async () => {
                fireEvent.change(narrationField, { target: { value: "   " } });
                fireEvent.click(continueButton);
            });

            expect(mockOnContinue).not.toHaveBeenCalled();
        });
    });

    describe("Form Submission", () => {
        it("submits form successfully and prevents multiple submissions", async () => {
            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const narrationField = screen.getByTestId("textarea-field-narration");
            const continueButton = screen.getByTestId("button-primary");

            // Set valid narration
            await act(async () => {
                fireEvent.change(narrationField, { target: { value: "Valid narration" } });
            });

            // Submit form
            await act(async () => {
                fireEvent.click(continueButton);
            });

            expect(mockOnContinue).toHaveBeenCalledWith({
                narration: "Valid narration",
                accountNumber: "**********",
            });

            // Button should be disabled after submission
            expect(continueButton.disabled).toBe(true);
        });
    });

    describe("Button States", () => {
        it("handles button states correctly", async () => {
            // Test with loading accounts
            const stateWithLoadingAccounts = {
                ...mockReduxState,
                account: {
                    accounts: [],
                    loadingStatus: "loading",
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithLoadingAccounts));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const continueButton = screen.getByTestId("button-primary");
            const backButton = screen.getByTestId("button-outline");

            expect(continueButton.disabled).toBe(true);

            // Test back button
            await act(async () => {
                fireEvent.click(backButton);
            });

            expect(mockOnBack).toHaveBeenCalled();
        });
    });

    describe("Exit Handling", () => {
        it("handles exit functionality", async () => {
            // Test with exit confirmation
            const exitHandlersWithConfirmation = {
                ...mockExitHandlers,
                showExitConfirmation: true,
            };

            useExitHandlers.mockReturnValue(exitHandlersWithConfirmation);

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();

            const confirmButton = screen.getByTestId("confirm-exit");
            const cancelButton = screen.getByTestId("cancel-exit");

            await act(async () => {
                fireEvent.click(confirmButton);
                fireEvent.click(cancelButton);
            });

            expect(mockExitHandlers.handleConfirmExit).toHaveBeenCalled();
            expect(mockExitHandlers.handleCancelExit).toHaveBeenCalled();
        });
    });

    describe("Edge Cases and Coverage", () => {
        it("handles edge cases and function coverage", async () => {
            // Test without onFieldChange callback
            const propsWithoutFieldChange = {
                ...defaultProps,
                onFieldChange: undefined,
            };

            await act(async () => {
                render(<PaymentInfo {...propsWithoutFieldChange} />);
            });

            const accountSelect = screen.getByTestId("account-select");

            await act(async () => {
                fireEvent.change(accountSelect, { target: { value: "**********" } });
            });

            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));
        });

        it("tests onExit callback from useExitHandlers", async () => {
            let capturedOnExit;
            useExitHandlers.mockImplementation(({ onExit }) => {
                capturedOnExit = onExit;
                return mockExitHandlers;
            });

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            // Call the onExit function that was passed to useExitHandlers
            if (capturedOnExit) {
                capturedOnExit();
            }

            expect(mockDispatch).toHaveBeenCalledWith(resetSingleBillPayment());
            expect(mockDispatch).toHaveBeenCalledWith(resetAccounts());
        });

        it("tests narration effect early return condition", async () => {
            const stateWithSameNarration = {
                ...mockReduxState,
                singleBillPayment: {
                    paymentInfo: {
                        narration: "Same value",
                        accountNumber: "**********",
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithSameNarration));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const narrationField = screen.getByTestId("textarea-field-narration");

            // Clear any previous dispatch calls
            mockDispatch.mockClear();

            // Change to the same value - this should trigger the early return
            await act(async () => {
                fireEvent.change(narrationField, { target: { value: "Same value" } });
            });

            // Should not dispatch because of early return (same value)
            expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ narration: "Same value" }));
        });
    });

    describe("Account Auto-Selection", () => {
        it("should auto-select first account and update both payment info and accounts.selectedAccount when no account is selected", async () => {
            const mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            // Mock state with accounts but no selected account
            const stateWithAccountsNoSelection = {
                ...mockReduxState,
                account: {
                    accounts: [
                        { accountNumber: "**********", accountName: "Savings Account", balance: 50000 },
                        { accountNumber: "**********", accountName: "Current Account", balance: 25000 },
                    ],
                    loadingStatus: "idle",
                },
                singleBillPayment: {
                    paymentInfo: {
                        narration: "",
                        accountNumber: "", // No account selected initially
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithAccountsNoSelection));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            // Should dispatch updatePaymentInfo with first account number
            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));

            // Should dispatch setSelectedAccount with formatted display string
            expect(mockDispatch).toHaveBeenCalledWith(setSelectedAccount("Savings Account ****7890"));

            // Auto-selection should have occurred with proper formatting
        });

        it("should not auto-select when account is already selected", async () => {
            const mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            // Mock state with accounts and already selected account
            const stateWithSelectedAccount = {
                ...mockReduxState,
                account: {
                    accounts: [
                        { accountNumber: "**********", accountName: "Savings Account", balance: 50000 },
                        { accountNumber: "**********", accountName: "Current Account", balance: 25000 },
                    ],
                    loadingStatus: "idle",
                },
                singleBillPayment: {
                    paymentInfo: {
                        narration: "",
                        accountNumber: "**********", // Account already selected
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithSelectedAccount));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            // Should not dispatch auto-selection since account is already selected
            expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));
            expect(mockDispatch).not.toHaveBeenCalledWith(setSelectedAccount(expect.any(String)));
        });

        it("should not auto-select when no accounts are available", async () => {
            const mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            // Mock state with no accounts
            const stateWithNoAccounts = {
                ...mockReduxState,
                account: {
                    accounts: [], // No accounts available
                    loadingStatus: "idle",
                },
                singleBillPayment: {
                    paymentInfo: {
                        narration: "",
                        accountNumber: "",
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNoAccounts));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            // Should not dispatch auto-selection when no accounts available
            expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: expect.any(String) }));
            expect(mockDispatch).not.toHaveBeenCalledWith(setSelectedAccount(expect.any(String)));
        });

        it("should handle manual account selection and update accounts.selectedAccount", async () => {
            const mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            useAppSelector.mockImplementation((selector) => selector(mockReduxState));

            await act(async () => {
                render(<PaymentInfo {...defaultProps} />);
            });

            const accountSelect = screen.getByTestId("account-select");

            // Clear previous dispatch calls from auto-selection
            mockDispatch.mockClear();

            // Simulate manual account selection
            await act(async () => {
                fireEvent.change(accountSelect, { target: { value: "**********" } });
            });

            // Should update payment info with selected account
            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountNumber: "**********" }));

            // Should call onFieldChange callback
            expect(mockOnFieldChange).toHaveBeenCalledWith("accountNumber", "**********");
        });
    });
});
