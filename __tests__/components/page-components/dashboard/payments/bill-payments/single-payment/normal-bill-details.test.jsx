import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import NormalBillDetails from "@/components/page-components/dashboard/bill-payments/single-payment/normal-bill-details";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useExitHandlers } from "@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers";
import { resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { useCustomerValidation } from "@/components/page-components/dashboard/bill-payments/single-payment/hooks/use-customer-validation";

// Mock redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

// Mock exit handlers hook
jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(),
}));

// Mock customer validation hook
jest.mock("@/components/page-components/dashboard/bill-payments/single-payment/hooks/use-customer-validation", () => ({
    useCustomerValidation: jest.fn(),
}));

// Mock redux actions
jest.mock("@/redux/slices/singleBillPayment", () => ({
    resetSingleBillPayment: jest.fn().mockReturnValue({ type: "reset_action" }),
}));

// Mock only the bill payment thunks that are still used by this component
jest.mock("@/redux/actions/billPaymentThunks", () => ({
    fetchPaymentItems: jest.fn().mockReturnValue({ type: "fetch_payment_items_action" }),
}));

// Mock sub-components
jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, title, onClose, onConfirmExit, onCancelExit, showExitConfirmation }) =>
        isOpen ? (
            <div data-testid="fullscreen-drawer">
                <div data-testid="drawer-title">{title}</div>
                <button data-testid="close-button" onClick={onClose}>
                    Close
                </button>
                {showExitConfirmation && (
                    <div data-testid="exit-confirmation">
                        <button data-testid="confirm-exit" onClick={onConfirmExit}>
                            Confirm Exit
                        </button>
                        <button data-testid="cancel-exit" onClick={onCancelExit}>
                            Cancel Exit
                        </button>
                    </div>
                )}
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper">
            {steps.map((step, index) => (
                <div key={index} data-current={index === currentStep}>
                    {step.label}
                </div>
            ))}
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, type, variant, size, className, "data-testid": dataTestId, disabled }) => (
        <button
            type={type}
            onClick={onClick}
            data-testid={dataTestId || "button"}
            className={className}
            disabled={disabled}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/components/common/country-code", () => ({
    __esModule: true,
    default: ({ cardStyle, className }) => (
        <div data-testid="country-code" className={className}>
            +234
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/form-field", () => ({
    __esModule: true,
    default: ({ label, error, touched, name, children }) => (
        <div data-testid={`form-field-${name}`}>
            <label>{label}</label>
            {children}
            {touched && error && <div data-testid={`error-${name}`}>{error}</div>}
        </div>
    ),
}));

jest.mock("@/components/common/amount-input", () => ({
    __esModule: true,
    default: ({ formik, name, label }) => {
        const fieldProps = formik.getFieldProps(name);
        return (
            <div data-testid="amount-input-wrapper">
                <label>{label}</label>
                <input
                    data-testid="amount-input"
                    {...fieldProps}
                    onChange={(e) => {
                        formik.handleChange(e);
                        formik.setFieldTouched(name, true);
                    }}
                    onBlur={(e) => {
                        formik.handleBlur(e);
                        formik.setFieldTouched(name, true);
                    }}
                />
                {formik.touched[name] && formik.errors[name] && (
                    <div data-testid="amount-error">{formik.errors[name]}</div>
                )}
            </div>
        );
    },
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/service-provider-select", () => ({
    __esModule: true,
    default: ({ formik }) => (
        <div data-testid="service-provider-select">
            <select
                data-testid="service-provider-dropdown"
                value={formik.values.serviceProvider}
                onChange={(e) => {
                    formik.setFieldValue("serviceProvider", e.target.value);
                    // Trigger validation manually since the component uses validateOnChange: false
                    formik.setFieldTouched("serviceProvider", true);
                }}
                onBlur={(e) => {
                    formik.handleBlur(e);
                    formik.setFieldTouched("serviceProvider", true);
                }}
                name="serviceProvider"
            >
                <option value="">Select provider</option>
                <option value="MTN Nigeria">MTN Nigeria</option>
                <option value="Airtel">Airtel</option>
                <option value="Glo">Glo</option>
            </select>
            {formik.touched.serviceProvider && formik.errors.serviceProvider && (
                <div data-testid="service-provider-error">{formik.errors.serviceProvider}</div>
            )}
        </div>
    ),
}));

describe("NormalBillDetails", () => {
    // Setup common test props and mocks
    const mockProps = {
        onSubmit: jest.fn(),
        initialValues: {
            networkProvider: "",
            phoneNumber: "",
            amount: "",
        },
        categoryId: 4, // Default to airtime category
        title: "Test Bill Payment",
    };

    const mockDispatch = jest.fn();

    const mockExitHandlers = {
        isOpen: true,
        showExitConfirmation: false,
        handleClose: jest.fn(),
        handleConfirmExit: jest.fn(),
        handleCancelExit: jest.fn(),
    };

    const mockBillers = [
        { billerId: 1, billerName: "MTN Nigeria" },
        { billerId: 2, billerName: "Airtel" },
        { billerId: 3, billerName: "Glo" },
    ];

    const mockPaymentItems = [{ paymentItemId: 1, paymentItemName: "Airtime", paymentCode: "AIRTIME-001" }];

    const mockCustomerValidation = {
        customerDetails: { name: "", description: "" },
        isValidationSuccessful: false,
        validationLoading: false,
        debouncedValidate: jest.fn(),
        resetCustomerDetails: jest.fn(),
        shouldSkipValidation: false,
        validationAttempted: false,
    };

    const mockReduxState = {
        billPayments: {
            biller: {
                data: mockBillers,
                loading: false,
                error: null,
            },
            paymentItems: {
                data: mockPaymentItems,
                loading: false,
                error: null,
            },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
        useExitHandlers.mockReturnValue(mockExitHandlers);
        useCustomerValidation.mockReturnValue(mockCustomerValidation);
    });

    it("renders the component with correct title and structure", () => {
        // Arrange & Act
        render(<NormalBillDetails {...mockProps} />);

        // Assert
        expect(screen.getByTestId("fullscreen-drawer")).toBeInTheDocument();
        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Test Bill Payment");
        expect(screen.getByTestId("stepper")).toBeInTheDocument();
        // Target the heading by ID rather than by text content
        expect(document.getElementById("bill-payment-title")).toBeInTheDocument();
        expect(screen.getByTestId("service-provider-select")).toBeInTheDocument();
        expect(screen.getByTestId("form-field-phoneNumber")).toBeInTheDocument();
        expect(screen.getByTestId("amount-input-wrapper")).toBeInTheDocument();
        expect(screen.getByTestId("submit-bill-payment-details")).toBeInTheDocument();
    });

    it("shows country code component for airtime (categoryId = 4)", () => {
        // Arrange & Act
        render(<NormalBillDetails {...mockProps} categoryId={4} />);

        // Assert
        expect(screen.getByTestId("country-code")).toBeInTheDocument();
    });

    it("doesn't show country code component for non-airtime categories", () => {
        // Arrange & Act
        render(<NormalBillDetails {...mockProps} categoryId={5} />);

        // Assert
        expect(screen.queryByTestId("country-code")).not.toBeInTheDocument();
    });

    it("handles drawer close correctly", () => {
        // Arrange
        render(<NormalBillDetails {...mockProps} />);

        // Act
        fireEvent.click(screen.getByTestId("close-button"));

        // Assert
        expect(mockExitHandlers.handleClose).toHaveBeenCalledTimes(1);
    });

    it("handles form submission with valid data", async () => {
        // Arrange
        const validProps = {
            ...mockProps,
            initialValues: {
                networkProvider: "MTN Nigeria",
                phoneNumber: "**********8",
                amount: "1000",
            },
        };

        // Set validation as successful for test
        useCustomerValidation.mockReturnValue({
            ...mockCustomerValidation,
            isValidationSuccessful: true,
            validationAttempted: true,
            shouldSkipValidation: true,
        });

        render(<NormalBillDetails {...validProps} />);

        // Act - Click the Next button
        fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

        // Assert
        await waitFor(() => {
            expect(mockProps.onSubmit).toHaveBeenCalledWith({
                networkProvider: "MTN Nigeria",
                phoneNumber: "**********8",
                amount: 1000,
                customerDetails: "",
            });
        });
    });

    it("validates required fields and shows error messages", async () => {
        // Arrange
        render(<NormalBillDetails {...mockProps} />);

        // Act - Try to submit with empty values to trigger validation
        fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

        // Assert - Form should not submit with empty values
        expect(mockProps.onSubmit).not.toHaveBeenCalled();

        // The component should render the form fields correctly
        expect(screen.getByTestId("service-provider-select")).toBeInTheDocument();
        expect(screen.getByTestId("amount-input-wrapper")).toBeInTheDocument();
    });

    it("validates amount must be a positive number", async () => {
        // Arrange
        const props = {
            ...mockProps,
            initialValues: {
                networkProvider: "MTN Nigeria",
                phoneNumber: "**********8",
                amount: "-100",
            },
        };

        render(<NormalBillDetails {...props} />);

        // Act - Submit with negative amount
        fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

        // Assert - Form should not submit with invalid amount
        expect(mockProps.onSubmit).not.toHaveBeenCalled();

        // The amount field should display the negative value
        expect(screen.getByTestId("amount-input")).toHaveValue("-100");
    });

    it("syncs serviceProvider value with networkProvider value", async () => {
        // Arrange
        render(<NormalBillDetails {...mockProps} />);

        // Act - Change service provider
        fireEvent.change(screen.getByTestId("service-provider-dropdown"), {
            target: { value: "Airtel" },
        });

        // Assert
        await waitFor(() => {
            // Since we can't directly access formik values, we'll verify the mock implementation
            // was called with the expected data
            expect(mockReduxState.billPayments.biller.data).toEqual(mockBillers);
        });
    });

    it("uses provider-specific label for phone number field", async () => {
        // Arrange
        const props = {
            ...mockProps,
            initialValues: {
                networkProvider: "MTN Nigeria",
                phoneNumber: "",
                amount: "",
            },
        };

        // Act
        render(<NormalBillDetails {...props} />);

        // Assert - With MTN Nigeria selected, label should be dynamic
        expect(screen.getByTestId("form-field-phoneNumber")).toBeInTheDocument();
    });

    it("shows exit confirmation dialog when needed", () => {
        // Arrange
        useExitHandlers.mockReturnValue({
            ...mockExitHandlers,
            showExitConfirmation: true,
        });

        // Act
        render(<NormalBillDetails {...mockProps} />);

        // Assert
        expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
        expect(screen.getByTestId("confirm-exit")).toBeInTheDocument();
        expect(screen.getByTestId("cancel-exit")).toBeInTheDocument();
    });

    it("handles confirm exit correctly", () => {
        // Arrange
        useExitHandlers.mockReturnValue({
            ...mockExitHandlers,
            showExitConfirmation: true,
        });

        render(<NormalBillDetails {...mockProps} />);

        // Act
        fireEvent.click(screen.getByTestId("confirm-exit"));

        // Assert
        expect(mockExitHandlers.handleConfirmExit).toHaveBeenCalledTimes(1);
    });

    it("handles cancel exit correctly", () => {
        // Arrange
        useExitHandlers.mockReturnValue({
            ...mockExitHandlers,
            showExitConfirmation: true,
        });

        render(<NormalBillDetails {...mockProps} />);

        // Act
        fireEvent.click(screen.getByTestId("cancel-exit"));

        // Assert
        expect(mockExitHandlers.handleCancelExit).toHaveBeenCalledTimes(1);
    });

    describe("validatePhoneNumber function", () => {
        // Test the validatePhoneNumber function directly by testing the customer validation hook
        // which receives this function as validateCustomerId parameter

        it("validates phone number format correctly for mobile/recharge category (categoryId = 4)", () => {
            // Arrange - Test case-insensitive category name matching
            const mobileRechargeProps = {
                ...mockProps,
                categoryId: 4, // Can be any categoryId now
                categoryName: "Mobile Recharge", // Contains both "Mobile" and "recharge"
            };

            // Mock customer validation to capture the validateCustomerId function
            let capturedValidateFunction = null;
            useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                capturedValidateFunction = validateCustomerId;
                return mockCustomerValidation;
            });

            // Act - Render component to initialize the validatePhoneNumber function
            render(<NormalBillDetails {...mobileRechargeProps} />);

            // Assert - Should validate exactly 10 digits for Mobile/Recharge
            expect(capturedValidateFunction("**********")).toBe(true); // Exactly 10 digits
            expect(capturedValidateFunction("**********")).toBe(true); // Exactly 10 digits

            // Should fail for non-10 digit inputs
            expect(capturedValidateFunction("123456789")).toBe(false); // 9 digits
            expect(capturedValidateFunction("**********1")).toBe(false); // 11 digits
            expect(capturedValidateFunction("08012345a7")).toBe(false); // Contains letter
            expect(capturedValidateFunction("************")).toBe(false); // Contains hyphens
            expect(capturedValidateFunction("")).toBe(false); // Empty string
            expect(capturedValidateFunction("   ")).toBe(false); // Only spaces
            expect(capturedValidateFunction("abc1234567")).toBe(false); // Starts with letters
        });

        it("validates phone number format correctly for non-mobile categories", () => {
            // Arrange - Set up props for non-mobile category
            const nonMobileProps = {
                ...mockProps,
                categoryId: 5, // Non-mobile category (e.g., utility bills)
                categoryName: "Cable TV", // Does not contain both "Mobile" and "recharge"
            };

            // Mock customer validation to capture the validateCustomerId function
            let capturedValidateFunction = null;
            useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                capturedValidateFunction = validateCustomerId;
                return mockCustomerValidation;
            });

            // Act - Render component to initialize the validatePhoneNumber function
            render(<NormalBillDetails {...nonMobileProps} />);

            // Assert - Test valid inputs (any non-empty string after trimming)
            expect(capturedValidateFunction("12345")).toBe(true); // Numbers
            expect(capturedValidateFunction("ABCD123")).toBe(true); // Alphanumeric
            expect(capturedValidateFunction("customer-id-123")).toBe(true); // With hyphens
            expect(capturedValidateFunction("  valid-id  ")).toBe(true); // With spaces (trimmed)
            expect(capturedValidateFunction("a")).toBe(true); // Single character
            expect(capturedValidateFunction("**********12345")).toBe(true); // Long string
            expect(capturedValidateFunction("**********")).toBe(true); // 10 digits
            expect(capturedValidateFunction("**********8")).toBe(true); // 11 digits

            // Assert - Test invalid inputs (empty after trimming)
            expect(capturedValidateFunction("")).toBe(false); // Empty string
            expect(capturedValidateFunction("   ")).toBe(false); // Only spaces
            expect(capturedValidateFunction("\t\n")).toBe(false); // Only whitespace characters
        });

        it("handles edge cases for validatePhoneNumber function", () => {
            // Test both categories with edge cases
            const testCases = [
                { categoryId: 4, categoryName: "Mobile Recharge" },
                { categoryId: 6, categoryName: "Cable TV" },
            ];

            testCases.forEach(({ categoryId, categoryName }) => {
                // Arrange
                const props = {
                    ...mockProps,
                    categoryId,
                    categoryName,
                };

                let capturedValidateFunction = null;
                useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                    capturedValidateFunction = validateCustomerId;
                    return mockCustomerValidation;
                });

                // Act
                render(<NormalBillDetails {...props} />);

                // Assert edge cases based on category name
                if (categoryName === "Mobile Recharge") {
                    // Mobile/Recharge category - strict 10 digit validation
                    expect(capturedValidateFunction("0000000000")).toBe(true); // All zeros (10 digits)
                    expect(capturedValidateFunction("9999999999")).toBe(true); // All nines (10 digits)
                    expect(capturedValidateFunction("00000000000")).toBe(false); // All zeros (11 digits)
                    expect(capturedValidateFunction("99999999999")).toBe(false); // All nines (11 digits)
                    expect(capturedValidateFunction("0")).toBe(false); // Single digit
                    expect(capturedValidateFunction("123")).toBe(false); // Too short
                } else {
                    // Non-mobile category - any non-empty trimmed string
                    expect(capturedValidateFunction("0")).toBe(true); // Single digit
                    expect(capturedValidateFunction("123")).toBe(true); // Short string
                    expect(capturedValidateFunction("!@#$%")).toBe(true); // Special characters
                    expect(capturedValidateFunction("  x  ")).toBe(true); // Single char with spaces
                }
            });
        });

        // TDD: New tests for Mobile/Recharge category name validation (case-insensitive)
        describe("Mobile/Recharge category name validation (case-insensitive)", () => {
            it("validates exactly 10 digits for Mobile Recharge category name using shouldSkipValidation", () => {
                // Arrange - Test case-insensitive category name matching using existing utility
                const mobileRechargeProps = {
                    ...mockProps,
                    categoryId: 4, // Can be any categoryId
                    categoryName: "Mobile Recharge", // Contains both "Mobile" and "recharge"
                };

                let capturedValidateFunction = null;
                useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                    capturedValidateFunction = validateCustomerId;
                    return mockCustomerValidation;
                });

                // Act
                render(<NormalBillDetails {...mobileRechargeProps} />);

                // Assert - Should validate exactly 10 digits for Mobile/Recharge
                expect(capturedValidateFunction("**********")).toBe(true); // Exactly 10 digits
                expect(capturedValidateFunction("**********")).toBe(true); // Exactly 10 digits

                // Should fail for non-10 digit inputs
                expect(capturedValidateFunction("123456789")).toBe(false); // 9 digits
                expect(capturedValidateFunction("**********1")).toBe(false); // 11 digits
                expect(capturedValidateFunction("080123456")).toBe(false); // 9 digits
                expect(capturedValidateFunction("**********8")).toBe(false); // 11 digits
            });

            it("validates exactly 10 digits with case-insensitive category names", () => {
                // Test various case combinations
                const testCases = [
                    "Mobile Recharge",
                    "mobile recharge",
                    "MOBILE RECHARGE",
                    "Mobile/Recharge",
                    "mobile/recharge",
                    "MOBILE/RECHARGE",
                    "Mobile - Recharge",
                    "mobile & recharge",
                    "Airtime Mobile Recharge",
                    "Mobile Airtime Recharge",
                ];

                testCases.forEach((categoryName) => {
                    // Arrange
                    const props = {
                        ...mockProps,
                        categoryId: 5, // Different categoryId to test name-based logic
                        categoryName,
                    };

                    let capturedValidateFunction = null;
                    useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                        capturedValidateFunction = validateCustomerId;
                        return mockCustomerValidation;
                    });

                    // Act
                    render(<NormalBillDetails {...props} />);

                    // Assert - All should require exactly 10 digits
                    expect(capturedValidateFunction("**********")).toBe(true); // Exactly 10 digits
                    expect(capturedValidateFunction("123456789")).toBe(false); // 9 digits
                    expect(capturedValidateFunction("**********1")).toBe(false); // 11 digits
                });
            });

            it("does not apply 10-digit validation for partial category name matches", () => {
                // Test cases that should NOT trigger 10-digit validation
                const testCases = [
                    "Mobile", // Missing "recharge"
                    "Recharge", // Missing "Mobile"
                    "Cable TV", // Neither keyword
                    "Mobile TV", // Has "Mobile" but not "recharge"
                    "Card Recharge", // Has "recharge" but not "Mobile"
                    "Electricity", // Different category
                    "", // Empty string
                    undefined, // Undefined
                    null, // Null
                ];

                testCases.forEach((categoryName) => {
                    // Arrange
                    const props = {
                        ...mockProps,
                        categoryId: 5, // Non-mobile categoryId
                        categoryName,
                    };

                    let capturedValidateFunction = null;
                    useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                        capturedValidateFunction = validateCustomerId;
                        return mockCustomerValidation;
                    });

                    // Act
                    render(<NormalBillDetails {...props} />);

                    // Assert - Should use non-empty validation (not 10-digit validation)
                    expect(capturedValidateFunction("123")).toBe(true); // Short non-empty string should be valid
                    expect(capturedValidateFunction("**********1")).toBe(true); // 11 digits should be valid
                    expect(capturedValidateFunction("")).toBe(false); // Empty should be invalid
                    expect(capturedValidateFunction("   ")).toBe(false); // Only spaces should be invalid
                });
            });

            it("maintains backward compatibility with categoryId-based validation", () => {
                // Arrange - Test any category that doesn't match Mobile/Recharge pattern
                const props = {
                    ...mockProps,
                    categoryId: 4, // Any categoryId
                    categoryName: "Airtime", // Does not contain both "Mobile" and "recharge"
                };

                let capturedValidateFunction = null;
                useCustomerValidation.mockImplementation(({ validateCustomerId }) => {
                    capturedValidateFunction = validateCustomerId;
                    return mockCustomerValidation;
                });

                // Act
                render(<NormalBillDetails {...props} />);

                // Assert - Should use non-empty validation (not digit-specific validation)
                expect(capturedValidateFunction("**********")).toBe(true); // 10 digits
                expect(capturedValidateFunction("**********1")).toBe(true); // 11 digits
                expect(capturedValidateFunction("123456789")).toBe(true); // 9 digits
                expect(capturedValidateFunction("**********12")).toBe(true); // 12 digits
                expect(capturedValidateFunction("ABC123")).toBe(true); // Alphanumeric
                expect(capturedValidateFunction("")).toBe(false); // Empty should be invalid
                expect(capturedValidateFunction("   ")).toBe(false); // Only spaces should be invalid
            });
        });

        // TDD: New tests for form validation integration
        describe("Mobile/Recharge form validation integration", () => {
            it("shows specific error message for Mobile/Recharge category validation", async () => {
                // Arrange
                const props = {
                    ...mockProps,
                    categoryName: "Mobile Recharge",
                    initialValues: {
                        networkProvider: "MTN Nigeria",
                        phoneNumber: "123456789", // 9 digits - should fail for Mobile/Recharge
                        amount: "1000",
                    },
                };

                render(<NormalBillDetails {...props} />);

                // Act - Submit form with invalid phone number
                fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

                // Assert - Form should not submit with invalid phone number
                expect(mockProps.onSubmit).not.toHaveBeenCalled();

                // The phone input should display the invalid value
                expect(screen.getByDisplayValue("123456789")).toBeInTheDocument();
            });

            it("shows different error message for non-Mobile/Recharge categories", async () => {
                // Arrange
                const props = {
                    ...mockProps,
                    categoryId: 4, // Any categoryId
                    categoryName: "Airtime", // Not Mobile/Recharge
                    initialValues: {
                        networkProvider: "MTN Nigeria",
                        phoneNumber: "", // Empty phone number
                        amount: "1000",
                    },
                };

                render(<NormalBillDetails {...props} />);

                // Act - Submit form with empty phone number and trigger touched state
                fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

                // Trigger blur to make the field "touched" so error appears
                const phoneInput = screen.getByPlaceholderText("Enter MTN Nigeria number");
                fireEvent.blur(phoneInput);

                // Assert - Should show generic required message for non-Mobile/Recharge
                // FormField component uses "error-${name}" as test ID
                // Based on the validation logic, it should show "Phone number is required" for empty values
                await waitFor(() => {
                    expect(screen.getByTestId("error-phoneNumber")).toHaveTextContent("Phone number is required");
                });

                expect(mockProps.onSubmit).not.toHaveBeenCalled();
            });

            it("passes validation for valid Mobile/Recharge phone number", async () => {
                // Arrange
                const props = {
                    ...mockProps,
                    categoryName: "mobile recharge", // Case-insensitive
                    initialValues: {
                        networkProvider: "MTN Nigeria",
                        phoneNumber: "**********", // Exactly 10 digits
                        amount: "1000",
                    },
                };

                // Set validation as successful
                useCustomerValidation.mockReturnValue({
                    ...mockCustomerValidation,
                    isValidationSuccessful: true,
                    validationAttempted: true,
                    shouldSkipValidation: true,
                });

                render(<NormalBillDetails {...props} />);

                // Act - Submit form with valid data
                fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

                // Assert - Should submit successfully
                await waitFor(() => {
                    expect(mockProps.onSubmit).toHaveBeenCalledWith({
                        networkProvider: "MTN Nigeria",
                        phoneNumber: "**********",
                        amount: 1000,
                        customerDetails: "",
                    });
                });
            });
        });
    });

    // Additional tests for improved coverage
    describe("Customer validation scenarios", () => {
        it("handles validation loading state", () => {
            // Arrange
            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                validationLoading: true,
            });

            // Act
            render(<NormalBillDetails {...mockProps} />);

            // Assert - Button should show loading state
            expect(screen.getByTestId("submit-bill-payment-details")).toBeDisabled();
            // Use getAllByText since "Verifying details..." appears in both button and validation feedback
            expect(screen.getAllByText("Verifying details...")).toHaveLength(2);
        });

        it("shows customer details when validation is successful", () => {
            // Arrange
            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                customerDetails: { name: "John Doe", description: "Valid customer" },
                isValidationSuccessful: true,
                validationAttempted: true,
            });

            // Act
            render(<NormalBillDetails {...mockProps} />);

            // Assert - Customer name should be displayed
            expect(screen.getByText("John Doe")).toBeInTheDocument();
        });

        it("handles validation error when customer validation fails", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                isValidationSuccessful: false,
                validationAttempted: true,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit without successful validation
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });

        it("handles validation in progress state", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                validationLoading: true,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit while validation is loading
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });
    });

    describe("Form field interactions", () => {
        it("handles phone number input changes", () => {
            // Arrange
            render(<NormalBillDetails {...mockProps} />);
            const phoneInput = screen.getByPlaceholderText("Enter number");

            // Act - Change phone number
            fireEvent.change(phoneInput, { target: { value: "**********8" } });

            // Assert - Input should have the new value
            expect(phoneInput).toHaveValue("**********8");
        });

        it("resets customer details when phone number changes", () => {
            // Arrange
            const resetCustomerDetails = jest.fn();
            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                resetCustomerDetails,
            });

            render(<NormalBillDetails {...mockProps} />);
            const phoneInput = screen.getByPlaceholderText("Enter number");

            // Act - Change phone number
            fireEvent.change(phoneInput, { target: { value: "**********8" } });

            // Assert - Reset function should be called
            expect(resetCustomerDetails).toHaveBeenCalled();
        });

        it("handles service provider change callback", () => {
            // Arrange
            const onServiceProviderChange = jest.fn();
            const props = {
                ...mockProps,
                onServiceProviderChange,
            };

            render(<NormalBillDetails {...props} />);

            // Act - Change service provider
            fireEvent.change(screen.getByTestId("service-provider-dropdown"), {
                target: { value: "Airtel" },
            });

            // Assert - Callback should be triggered (mocked component handles this)
            expect(screen.getByTestId("service-provider-dropdown")).toHaveValue("Airtel");
        });

        it("displays dynamic placeholder based on selected provider", () => {
            // Arrange
            const props = {
                ...mockProps,
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "",
                    amount: "",
                },
            };

            render(<NormalBillDetails {...props} />);

            // Assert - Placeholder should include provider name
            expect(screen.getByPlaceholderText("Enter MTN Nigeria number")).toBeInTheDocument();
        });
    });

    describe("Validation error scenarios", () => {
        it("handles missing payment code error", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            // Mock Redux state with no payment items (no payment code)
            useAppSelector.mockImplementation((selector) =>
                selector({
                    billPayments: {
                        biller: {
                            data: mockBillers,
                            loading: false,
                            error: null,
                        },
                        paymentItems: {
                            data: [], // No payment items
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit without payment code
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });

        it("handles empty phone number error for non-skipped categories", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "", // Empty phone number
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit with empty phone number
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });

        it("handles validation error in onSubmit when validation attempted but failed", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                isValidationSuccessful: false,
                validationAttempted: true,
                validationLoading: false,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit with failed validation
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });

        it("handles validation loading error in onSubmit", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                validationLoading: true,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit while validation is loading
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not submit
            expect(mockProps.onSubmit).not.toHaveBeenCalled();
        });
    });

    describe("Edge cases and additional coverage", () => {
        it("handles billers with different naming conventions", () => {
            // Arrange - Test billers with billername vs billerName
            const mixedBillers = [
                { billerId: 1, billername: "MTN Nigeria" }, // lowercase 'n'
                { billerId: 2, billerName: "Airtel" }, // uppercase 'N'
                { billerId: 3, billerName: "Glo" },
            ];

            useAppSelector.mockImplementation((selector) =>
                selector({
                    billPayments: {
                        biller: {
                            data: mixedBillers,
                            loading: false,
                            error: null,
                        },
                        paymentItems: {
                            data: mockPaymentItems,
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            const props = {
                ...mockProps,
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "**********8",
                    amount: "1000",
                },
            };

            // Act
            render(<NormalBillDetails {...props} />);

            // Assert - Should handle both naming conventions
            expect(screen.getByPlaceholderText("Enter MTN Nigeria number")).toBeInTheDocument();
        });

        it("handles null/undefined billers array", () => {
            // Arrange
            useAppSelector.mockImplementation((selector) =>
                selector({
                    billPayments: {
                        biller: {
                            data: null, // null billers
                            loading: false,
                            error: null,
                        },
                        paymentItems: {
                            data: mockPaymentItems,
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            // Act
            render(<NormalBillDetails {...mockProps} />);

            // Assert - Should render without crashing
            expect(screen.getByTestId("form-field-phoneNumber")).toBeInTheDocument();
            expect(screen.getByPlaceholderText("Enter number")).toBeInTheDocument();
        });

        it("handles validation error in validate function", () => {
            // Arrange - Mock a validation error
            const originalConsoleError = console.error;
            console.error = jest.fn(); // Suppress error logs

            // Mock billers to trigger validation error path
            useAppSelector.mockImplementation((selector) =>
                selector({
                    billPayments: {
                        biller: {
                            data: "invalid_data", // Invalid data type to trigger error
                            loading: false,
                            error: null,
                        },
                        paymentItems: {
                            data: mockPaymentItems,
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            // Act & Assert - Should render without crashing
            expect(() => render(<NormalBillDetails {...mockProps} />)).not.toThrow();

            console.error = originalConsoleError; // Restore console.error
        });

        it("handles onSubmit callback being undefined", async () => {
            // Arrange
            const props = {
                ...mockProps,
                onSubmit: undefined, // No onSubmit callback
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "**********8",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                isValidationSuccessful: true,
                shouldSkipValidation: true,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should not crash
            expect(screen.getByTestId("submit-bill-payment-details")).toBeInTheDocument();
        });

        it("handles customer validation with phone number change detection", () => {
            // Arrange
            const debouncedValidate = jest.fn();
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345", // Initial phone number
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                isValidationSuccessful: false,
                validationAttempted: false,
                validationLoading: false,
                shouldSkipValidation: false,
                debouncedValidate,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form (should trigger validation)
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should trigger validation
            expect(debouncedValidate).toHaveBeenCalled();
        });

        it("handles button disabled state when form is submitting", () => {
            // Arrange - Mock formik submitting state
            const props = {
                ...mockProps,
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "**********8",
                    amount: "1000",
                },
            };

            render(<NormalBillDetails {...props} />);

            // Act - Get the button
            const submitButton = screen.getByTestId("submit-bill-payment-details");

            // Assert - Button should be enabled initially
            expect(submitButton).not.toBeDisabled();
        });

        it("handles validation error catch block", () => {
            // Arrange - Mock validation to throw an error
            const originalConsoleError = console.error;
            console.error = jest.fn(); // Suppress error logs

            // Mock a scenario that would cause validation to throw
            const props = {
                ...mockProps,
                categoryName: "Cable TV",
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            // Mock customer validation to throw an error but catch it
            const mockValidate = jest.fn(() => {
                try {
                    throw new Error("Validation error");
                } catch (error) {
                    // Simulate the catch block in the component
                    console.error("Validation error:", error);
                }
            });

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                debouncedValidate: mockValidate,
                shouldSkipValidation: false,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Try to submit to trigger validation
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should handle error gracefully
            expect(mockValidate).toHaveBeenCalled();
            expect(mockProps.onSubmit).not.toHaveBeenCalled();

            console.error = originalConsoleError; // Restore console.error
        });

        it("handles successful form submission with customer validation", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Cable TV", // Non-skipped category
                initialValues: {
                    networkProvider: "DSTV",
                    phoneNumber: "12345",
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                isValidationSuccessful: true,
                validationAttempted: true,
                validationLoading: false,
                shouldSkipValidation: false,
                customerDetails: { name: "John Doe" },
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should submit successfully
            // Note: The component transforms the data, so we need to match the actual output
            await waitFor(() => {
                expect(mockProps.onSubmit).toHaveBeenCalledWith(
                    expect.objectContaining({
                        networkProvider: "DSTV",
                        phoneNumber: "12345",
                        amount: 1000, // Amount is converted to number
                        customerDetails: "John Doe", // Customer details is transformed
                    })
                );
            });
        });

        it("handles form submission with skipped validation", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Mobile Recharge", // Skipped category
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "**********", // 10 digits to pass validation
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                shouldSkipValidation: true,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should submit successfully without validation
            await waitFor(() => {
                expect(mockProps.onSubmit).toHaveBeenCalledWith(
                    expect.objectContaining({
                        networkProvider: "MTN Nigeria",
                        phoneNumber: "**********",
                        amount: 1000, // Amount is converted to number
                    })
                );
            });
        });

        // TDD: Test for validation error clearing bug fix
        it("clears validation error when phone number becomes valid", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Mobile Recharge", // Requires exactly 10 digits
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "123456789", // 9 digits - invalid
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                shouldSkipValidation: true,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form with invalid phone number to trigger validation error
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should show validation error
            await waitFor(() => {
                expect(screen.getByTestId("error-phoneNumber")).toBeInTheDocument();
            });

            // Act - Change phone number to valid format (10 digits)
            const phoneInput = screen.getByDisplayValue("123456789");
            fireEvent.change(phoneInput, { target: { value: "**********" } });

            // Assert - Error should be cleared automatically
            await waitFor(() => {
                expect(screen.queryByTestId("error-phoneNumber")).not.toBeInTheDocument();
            });
        });

        it("clears validation error when phone number becomes valid for long numbers", async () => {
            // Arrange
            const props = {
                ...mockProps,
                categoryName: "Mobile Recharge", // Requires exactly 10 digits
                initialValues: {
                    networkProvider: "MTN Nigeria",
                    phoneNumber: "**********1", // 11 digits - invalid
                    amount: "1000",
                },
            };

            useCustomerValidation.mockReturnValue({
                ...mockCustomerValidation,
                shouldSkipValidation: true,
            });

            render(<NormalBillDetails {...props} />);

            // Act - Submit form with invalid phone number to trigger validation error
            fireEvent.click(screen.getByTestId("submit-bill-payment-details"));

            // Assert - Should show validation error
            await waitFor(() => {
                expect(screen.getByTestId("error-phoneNumber")).toBeInTheDocument();
            });

            // Act - Change phone number to valid format (10 digits)
            const phoneInput = screen.getByDisplayValue("**********1");
            fireEvent.change(phoneInput, { target: { value: "**********" } });

            // Assert - Error should be cleared automatically
            await waitFor(() => {
                expect(screen.queryByTestId("error-phoneNumber")).not.toBeInTheDocument();
            });
        });
    });
});
