import React from "react";
import { renderHook, act } from "@testing-library/react";
import { useFileRestoration } from "@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setFileInfo } from "@/redux/features/bulkAirtime";
import * as fileStorageUtils from "@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-storage";

// Mock the Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

// Mock the Redux actions
jest.mock("@/redux/features/bulkAirtime", () => ({
    setFileInfo: jest.fn(),
}));

// Mock file storage utilities
jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-storage", () => ({
    FILE_STORAGE_KEY: "bulk_airtime_file",
    base64ToFile: jest.fn(),
    fileToBase64: jest.fn(),
    isFileMatchingInfo: jest.fn(),
}));

// Mock localStorage
const localStorageMock = (() => {
    let store = {};
    return {
        getItem: jest.fn((key) => store[key] || null),
        setItem: jest.fn((key, value) => {
            store[key] = value;
        }),
        removeItem: jest.fn((key) => {
            delete store[key];
        }),
        clear: jest.fn(() => {
            store = {};
        }),
    };
})();

Object.defineProperty(window, "localStorage", {
    value: localStorageMock,
});

// Console mock to reduce noise in tests
global.console = {
    ...console,
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
};

describe("useFileRestoration", () => {
    // Common test variables
    const mockDispatch = jest.fn();
    const mockFile = new File(["test content"], "test.csv", { type: "text/csv" });
    const mockFileInfo = {
        name: "test.csv",
        size: mockFile.size,
        type: "text/csv",
        lastModified: mockFile.lastModified,
    };
    const mockBase64Data = "data:text/csv;base64,dGVzdCBjb250ZW50";

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Setup default mock implementations
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockReturnValue(null);
        fileStorageUtils.fileToBase64.mockResolvedValue(mockBase64Data);
        fileStorageUtils.base64ToFile.mockResolvedValue(mockFile);
        fileStorageUtils.isFileMatchingInfo.mockReturnValue(true);

        // Clear localStorage for each test
        localStorageMock.clear();
    });

    test("should initialize with null file and upload not complete", () => {
        // Arrange & Act
        const { result } = renderHook(() => useFileRestoration());

        // Assert
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should store file successfully", async () => {
        // Arrange
        const { result } = renderHook(() => useFileRestoration());

        // Act
        await act(async () => {
            await result.current.storeFile(mockFile);
        });

        // Assert
        expect(result.current.uploadedFile).toEqual(mockFile);
        expect(result.current.isUploadComplete).toBe(true);
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(mockFileInfo));
        expect(fileStorageUtils.fileToBase64).toHaveBeenCalledWith(mockFile);
        expect(localStorageMock.setItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY, mockBase64Data);
    });

    test("should handle errors when storing file", async () => {
        // Arrange
        const storageError = new Error("Storage error");
        fileStorageUtils.fileToBase64.mockRejectedValueOnce(storageError);
        const { result } = renderHook(() => useFileRestoration());

        // Act & Assert
        await expect(
            act(async () => {
                await result.current.storeFile(mockFile);
            })
        ).rejects.toThrow(storageError);

        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should clear file data successfully", () => {
        // Arrange
        const { result } = renderHook(() => useFileRestoration());

        // Act
        act(() => {
            result.current.clearFile();
        });

        // Assert
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY);
    });

    test("should restore file from localStorage when fileInfo exists", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);

        // Act
        const { result, rerender } = renderHook(() => useFileRestoration());

        // Wait for rehydration and effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(fileStorageUtils.base64ToFile).toHaveBeenCalledWith(
            mockBase64Data,
            mockFileInfo.name,
            mockFileInfo.type
        );
        expect(fileStorageUtils.isFileMatchingInfo).toHaveBeenCalledWith(mockFile, mockFileInfo);
        expect(result.current.uploadedFile).toEqual(mockFile);
        expect(result.current.isUploadComplete).toBe(true);
    });

    test("should handle metadata mismatch when restoring file", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);
        fileStorageUtils.isFileMatchingInfo.mockReturnValue(false);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for rehydration and effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle file data in localStorage but no fileInfo in Redux", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(null);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for rehydration and effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle fileInfo in Redux but no file data in localStorage", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(null);
        useAppSelector.mockReturnValue(mockFileInfo);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for rehydration and effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle errors when restoring file", async () => {
        // Arrange
        const restoreError = new Error("Restore error");
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);
        fileStorageUtils.base64ToFile.mockRejectedValueOnce(restoreError);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for rehydration and effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle server-side rendering conditions by mocking window checks", async () => {
        // Since we can't easily mock typeof window checks in Jest, we'll test the scenario
        // by ensuring localStorage operations are properly guarded

        // Arrange - Test case when storeFile should work normally (browser environment)
        const { result } = renderHook(() => useFileRestoration());

        // Act
        await act(async () => {
            await result.current.storeFile(mockFile);
        });

        // Assert - Normal browser behavior
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(mockFileInfo));
        expect(fileStorageUtils.fileToBase64).toHaveBeenCalledWith(mockFile);
        expect(localStorageMock.setItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY, mockBase64Data);
        expect(result.current.uploadedFile).toEqual(mockFile);
        expect(result.current.isUploadComplete).toBe(true);
    });

    test("should handle clearFile with localStorage operations", () => {
        // Arrange
        const { result } = renderHook(() => useFileRestoration());

        // Act
        act(() => {
            result.current.clearFile();
        });

        // Assert - Normal browser behavior
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY);
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should prevent multiple restoration attempts", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);

        const { result } = renderHook(() => useFileRestoration());

        // First restoration attempt
        await act(async () => {
            await result.current.restoreFile();
        });

        // Clear mocks to track second call
        jest.clearAllMocks();

        // Act - Second restoration attempt
        await act(async () => {
            await result.current.restoreFile();
        });

        // Assert - Should not attempt restoration again
        expect(fileStorageUtils.base64ToFile).not.toHaveBeenCalled();
        expect(localStorageMock.getItem).not.toHaveBeenCalled();
    });

    test("should handle manual restoreFile call", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);
        const { result } = renderHook(() => useFileRestoration());

        // Clear any automatic restoration that happened
        jest.clearAllMocks();

        // Reset restoration attempted flag by creating a new hook instance
        const { result: newResult } = renderHook(() => useFileRestoration());

        // Mock the selector to return fileInfo for manual call
        useAppSelector.mockReturnValue(mockFileInfo);

        // Act - Manual restore call
        await act(async () => {
            await newResult.current.restoreFile();
        });

        // Assert
        expect(fileStorageUtils.base64ToFile).toHaveBeenCalledWith(
            mockBase64Data,
            mockFileInfo.name,
            mockFileInfo.type
        );
        expect(newResult.current.uploadedFile).toEqual(mockFile);
        expect(newResult.current.isUploadComplete).toBe(true);
    });

    test("should clear fileInfo when file restoration fails due to metadata mismatch", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);
        fileStorageUtils.isFileMatchingInfo.mockReturnValue(false);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle error in restoreFileFromData and clear fileInfo", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);
        fileStorageUtils.base64ToFile.mockRejectedValueOnce(new Error("Base64 conversion failed"));

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle localStorage access during restoration", async () => {
        // Arrange - Test normal restoration flow with localStorage access
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration attempt
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert - Should attempt localStorage operations in browser environment
        expect(localStorageMock.getItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY);
        expect(result.current.uploadedFile).toEqual(mockFile);
        expect(result.current.isUploadComplete).toBe(true);
    });

    test("should handle error cleanup in restoreFile catch block including localStorage", async () => {
        // Arrange - Simulate an error that triggers the catch block
        useAppSelector.mockReturnValue(mockFileInfo);
        localStorageMock.getItem.mockImplementation(() => {
            throw new Error("localStorage access error");
        });

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration attempt
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert - Should clear fileInfo and localStorage on error
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY);
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should not attempt restoration when file already exists", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);

        const { result } = renderHook(() => useFileRestoration());

        // First store a file to set uploadedFile
        await act(async () => {
            await result.current.storeFile(mockFile);
        });

        // Clear mocks to track restoration calls
        jest.clearAllMocks();

        // Act - Try to restore when file already exists
        await act(async () => {
            await result.current.restoreFile();
        });

        // Assert - Should not attempt restoration
        expect(localStorageMock.getItem).not.toHaveBeenCalled();
        expect(fileStorageUtils.base64ToFile).not.toHaveBeenCalled();
    });

    test("should handle useEffect when isRestorationAttempted changes", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        let mockFileInfoValue = null;
        useAppSelector.mockImplementation(() => mockFileInfoValue);

        const { result, rerender } = renderHook(() => useFileRestoration());

        // Act - Update fileInfo to trigger useEffect
        mockFileInfoValue = mockFileInfo;
        useAppSelector.mockReturnValue(mockFileInfo);

        rerender();

        // Wait for effect to run
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(fileStorageUtils.base64ToFile).toHaveBeenCalledWith(
            mockBase64Data,
            mockFileInfo.name,
            mockFileInfo.type
        );
        expect(result.current.uploadedFile).toEqual(mockFile);
        expect(result.current.isUploadComplete).toBe(true);
    });

    test("should not trigger restoration when fileInfo exists but restoration already attempted", async () => {
        // Arrange
        localStorageMock.getItem.mockReturnValue(mockBase64Data);
        useAppSelector.mockReturnValue(mockFileInfo);

        const { result, rerender } = renderHook(() => useFileRestoration());

        // Wait for initial restoration
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Clear mocks after initial restoration
        jest.clearAllMocks();

        // Act - Re-render the same hook instance (not creating a new one)
        rerender();

        // Wait a bit to see if restoration is attempted again
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 50));
        });

        // Assert - Should not attempt restoration again
        expect(fileStorageUtils.base64ToFile).not.toHaveBeenCalled();
        expect(localStorageMock.getItem).not.toHaveBeenCalled();
    });

    test("should handle corrupted localStorage data in error scenario", async () => {
        // Arrange
        const corruptedData = "invalid-base64-data";
        localStorageMock.getItem.mockReturnValue(corruptedData);
        useAppSelector.mockReturnValue(mockFileInfo);
        fileStorageUtils.base64ToFile.mockRejectedValueOnce(new Error("Invalid base64 data"));

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });

    test("should handle edge case with different localStorage access error", async () => {
        // Arrange - Different error scenario from the previous test
        useAppSelector.mockReturnValue(mockFileInfo);
        localStorageMock.getItem.mockImplementation(() => {
            throw new Error("localStorage quota exceeded");
        });

        // Act
        const { result } = renderHook(() => useFileRestoration());

        // Wait for restoration attempt
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 150));
        });

        // Assert
        expect(mockDispatch).toHaveBeenCalledWith(setFileInfo(null));
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(fileStorageUtils.FILE_STORAGE_KEY);
        expect(result.current.uploadedFile).toBeNull();
        expect(result.current.isUploadComplete).toBe(false);
    });
});
