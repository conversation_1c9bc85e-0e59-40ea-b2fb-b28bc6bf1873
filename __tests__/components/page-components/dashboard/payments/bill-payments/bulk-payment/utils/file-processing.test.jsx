/**
 * @file file-processing.test.jsx
 * @purpose Unit tests for the CSV file processing utility functions in the bulk payment flow.
 *
 * @functionality This file provides comprehensive test coverage for the file processing utilities:
 * - validateAndProcessFile: Tests handling of valid and invalid CSV files, empty data, and base64 conversion failures
 * - isProcessingSuccessful: Tests determination of processing success based on valid entry counts
 * - getProcessingFeedback: Tests generation of appropriate feedback messages based on processing results
 * The tests verify that the utilities correctly validate input files, process CSV data, and provide appropriate user feedback.
 *
 * @dependencies
 * - Jest for testing framework
 * - Mock implementations of validateCsvFile, convertCsvToEntries, and fileToBase64 functions
 *
 * @usage These tests verify that the file processing utilities work correctly and handle
 * various edge cases such as invalid files, empty data, and conversion failures. They ensure
 * the reliability of the bulk payment file upload functionality.
 */

import {
    validateAndProcessFile,
    isProcessingSuccessful,
    getProcessingFeedback,
} from "@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-processing";

// Mock the dependencies
jest.mock("@/components/page-components/dashboard/bill-payments/utils/csv-utils", () => ({
    validateCsvFile: jest.fn(),
    convertCsvToEntries: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-storage", () => ({
    fileToBase64: jest.fn(),
}));

// Import the mocked functions
import {
    validateCsvFile,
    convertCsvToEntries,
} from "@/components/page-components/dashboard/bill-payments/utils/csv-utils";
import { fileToBase64 } from "@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-storage";

describe("File Processing Utilities", () => {
    // Reset all mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("validateAndProcessFile", () => {
        const mockFile = new File(["test content"], "test.csv", { type: "text/csv" });

        it("should process a valid CSV file successfully", async () => {
            // Setup mocks with a valid CSV file response
            const mockData = [{ "Phone number": "1234567890", Network: "MTN", Amount: "1000" }];
            validateCsvFile.mockImplementation(() =>
                Promise.resolve({
                    isValid: true,
                    data: mockData,
                })
            );

            const mockEntries = [{ id: "1", phoneNumber: "1234567890", network: "MTN", amount: 1000, status: "Valid" }];
            convertCsvToEntries.mockReturnValue(mockEntries);
            fileToBase64.mockResolvedValue("data:text/csv;base64,test123");

            // Execute
            const result = await validateAndProcessFile(mockFile);

            // Verify
            expect(validateCsvFile).toHaveBeenCalledWith(mockFile);
            expect(convertCsvToEntries).toHaveBeenCalledWith(mockData);
            expect(fileToBase64).toHaveBeenCalledWith(mockFile);

            expect(result).toEqual({
                entries: mockEntries,
                fileInfo: {
                    name: "test.csv",
                    size: expect.any(Number),
                    type: "text/csv",
                    lastModified: expect.any(Number),
                },
                base64Data: "data:text/csv;base64,test123",
                validCount: 1,
                totalCount: 1,
            });
        });

        it("should handle validation failure", async () => {
            // Setup mocks with an invalid CSV file error
            const errorMessage = "CSV file contains no data rows";
            validateCsvFile.mockImplementation(() =>
                Promise.resolve({
                    isValid: false,
                    errorMessage: errorMessage,
                })
            );

            // Execute and verify
            await expect(validateAndProcessFile(mockFile)).rejects.toThrow(errorMessage);
            expect(validateCsvFile).toHaveBeenCalledWith(mockFile);
            expect(convertCsvToEntries).not.toHaveBeenCalled();
        });

        it("should handle empty data", async () => {
            // Setup mocks with empty data array
            validateCsvFile.mockImplementation(() =>
                Promise.resolve({
                    isValid: true,
                    data: [],
                })
            );

            // Execute and verify
            await expect(validateAndProcessFile(mockFile)).rejects.toThrow("No valid data found in the CSV file");
            expect(validateCsvFile).toHaveBeenCalledWith(mockFile);
            expect(convertCsvToEntries).not.toHaveBeenCalled();
        });

        it("should continue processing if base64 conversion fails", async () => {
            // Setup mocks
            const mockData = [{ "Phone number": "1234567890", Network: "MTN", Amount: "1000" }];
            validateCsvFile.mockImplementation(() =>
                Promise.resolve({
                    isValid: true,
                    data: mockData,
                })
            );

            const mockEntries = [{ id: "1", phoneNumber: "1234567890", network: "MTN", amount: 1000, status: "Valid" }];
            convertCsvToEntries.mockReturnValue(mockEntries);
            fileToBase64.mockImplementation(() => Promise.reject(new Error("Base64 conversion failed")));

            // Enable spying on console.error
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

            // Execute
            const result = await validateAndProcessFile(mockFile);

            // Verify
            expect(validateCsvFile).toHaveBeenCalledWith(mockFile);
            expect(convertCsvToEntries).toHaveBeenCalledWith(mockData);
            expect(fileToBase64).toHaveBeenCalledWith(mockFile);

            expect(result).toEqual({
                entries: mockEntries,
                fileInfo: {
                    name: "test.csv",
                    size: expect.any(Number),
                    type: "text/csv",
                    lastModified: expect.any(Number),
                },
                base64Data: undefined, // Should be undefined when conversion fails
                validCount: 1,
                totalCount: 1,
            });

            consoleSpy.mockRestore();
        });
    });

    describe("isProcessingSuccessful", () => {
        it("should return true when there are valid entries", () => {
            const result = {
                entries: [{ status: "Valid" }, { status: "Invalid" }],
                fileInfo: {},
                validCount: 1,
                totalCount: 2,
            };

            expect(isProcessingSuccessful(result)).toBe(true);
        });

        it("should return false when there are no valid entries", () => {
            const result = {
                entries: [{ status: "Invalid" }, { status: "Invalid" }],
                fileInfo: {},
                validCount: 0,
                totalCount: 2,
            };

            expect(isProcessingSuccessful(result)).toBe(false);
        });

        it("should return false when there are no entries", () => {
            const result = {
                entries: [],
                fileInfo: {},
                validCount: 0,
                totalCount: 0,
            };

            expect(isProcessingSuccessful(result)).toBe(false);
        });
    });

    describe("getProcessingFeedback", () => {
        it("should return success message when all entries are valid", () => {
            const result = {
                entries: [{ status: "Valid" }, { status: "Valid" }],
                fileInfo: {},
                validCount: 2,
                totalCount: 2,
            };

            expect(getProcessingFeedback(result)).toEqual({
                message: "File processed successfully. All 2 entries are valid.",
                type: "success",
            });
        });

        it("should return warning message when some entries are invalid", () => {
            const result = {
                entries: [{ status: "Valid" }, { status: "Invalid" }],
                fileInfo: {},
                validCount: 1,
                totalCount: 2,
            };

            expect(getProcessingFeedback(result)).toEqual({
                message: "File processed successfully. 1 out of 2 entries are valid.",
                type: "warning",
            });
        });

        it("should return warning message when no entries are valid", () => {
            const result = {
                entries: [{ status: "Invalid" }, { status: "Invalid" }],
                fileInfo: {},
                validCount: 0,
                totalCount: 2,
            };

            expect(getProcessingFeedback(result)).toEqual({
                message: "File uploaded, but no valid entries were found. Please add entries manually.",
                type: "warning",
            });
        });
    });
});
