import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import PaymentInfo from "@/components/page-components/dashboard/bill-payments/bulk-payment/payment-info";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useExitHandlers } from "@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers";
import { updatePaymentInfo, resetBulkAirtime } from "@/redux/features/bulkAirtime";
import { setSelectedAccount } from "@/redux/features/accounts";
import { useFormik } from "formik";

// Mock shared utilities
jest.mock("@/components/page-components/dashboard/bill-payments/common/account-utils", () => ({
    formatAccountName: jest.fn((account) => account.accountName || "Account"),
    formatAccountNumber: jest.fn((accountNumber) => {
        const lastFourDigits = accountNumber.slice(-4);
        return `****${lastFourDigits}`;
    }),
}));

// Mock dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(),
}));

jest.mock("@/redux/features/bulkAirtime", () => ({
    updatePaymentInfo: jest.fn().mockReturnValue({ type: "bulkAirtime/updatePaymentInfo" }),
    resetBulkAirtime: jest.fn().mockReturnValue({ type: "bulkAirtime/resetBulkAirtime" }),
}));

jest.mock("@/redux/features/accounts", () => ({
    setSelectedAccount: jest.fn((payload) => ({
        type: "accounts/setSelectedAccount",
        payload,
    })),
}));

// Mock child components
jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, title, onClose, showExitConfirmation, onConfirmExit, onCancelExit }) =>
        isOpen ? (
            <div data-testid="drawer">
                <h1 data-testid="drawer-title">{title}</h1>
                <button data-testid="close-drawer" onClick={onClose}>
                    Close
                </button>
                {showExitConfirmation && (
                    <div data-testid="exit-confirmation">
                        <button data-testid="confirm-exit" onClick={onConfirmExit}>
                            Confirm Exit
                        </button>
                        <button data-testid="cancel-exit" onClick={onCancelExit}>
                            Cancel Exit
                        </button>
                    </div>
                )}
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper">
            Step {currentStep + 1} of {steps.length}
        </div>
    ),
}));

jest.mock("@/components/common/summary-card", () => ({
    SummaryCard: ({ items }) => (
        <div data-testid="summary-card">
            {items.map((item, index) => (
                <div key={index} data-testid={`summary-item-${index}`}>
                    <span data-testid={`summary-label-${index}`}>{item.label}</span>
                    <span data-testid={`summary-value-${index}`}>{item.value}</span>
                </div>
            ))}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/account-selector", () => ({
    __esModule: true,
    default: ({ selectedAccount, onAccountChange }) => {
        return (
            <div data-testid="account-selector">
                <select
                    data-testid="account-select"
                    value={selectedAccount || ""}
                    onChange={(e) => onAccountChange && onAccountChange(e.target.value)}
                >
                    <option value="">Select Account</option>
                    <option value="**********">Account ********** - ₦50,000.00</option>
                    <option value="**********">Account ********** - ₦25,000.00</option>
                </select>
            </div>
        );
    },
}));

jest.mock("@/components/common/text-area", () => ({
    __esModule: true,
    default: ({ value, onChange, name, label, placeholder }) => (
        <div data-testid={`textarea-${name}`}>
            <label>{label}</label>
            <textarea
                data-testid={`textarea-field-${name}`}
                value={value || ""}
                onChange={onChange}
                placeholder={placeholder}
            />
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, type, variant }) => (
        <button data-testid={`button-${variant || "default"}`} onClick={onClick} disabled={disabled} type={type}>
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/auth", () => ({
    InfoIcon: () => <div data-testid="info-icon">ℹ</div>,
}));

// Mock formik - we'll control this dynamically in tests
const mockFormik = {
    values: { narration: "Test narration" },
    errors: {},
    touched: {},
    handleSubmit: jest.fn(),
    setFieldValue: jest.fn(),
    setFieldTouched: jest.fn(),
    validateForm: jest.fn(() => Promise.resolve({})),
};

jest.mock("formik", () => ({
    useFormik: jest.fn(() => mockFormik),
}));

describe("PaymentInfo (Bulk Payment)", () => {
    const mockDispatch = jest.fn();
    const mockOnBack = jest.fn();
    const mockOnContinue = jest.fn();

    // Mock Redux state
    const mockReduxState = {
        bulkAirtime: {
            paymentInfo: {
                narration: "Test narration",
                accountFrom: "**********", // Set a default selected account
            },
            entries: [
                { status: "Valid", amount: 1000 },
                { status: "Valid", amount: 2000 },
                { status: "Invalid", amount: 500 },
            ],
        },
        account: {
            accounts: [
                { accountNumber: "**********", balance: 50000 },
                { accountNumber: "**********", balance: 25000 },
            ],
            loadingStatus: "idle",
        },
    };

    // Mock exit handlers
    const mockExitHandlers = {
        isOpen: true,
        showExitConfirmation: false,
        handleClose: jest.fn(),
        handleConfirmExit: jest.fn(),
        handleCancelExit: jest.fn(),
    };

    // Default props
    const defaultProps = {
        onBack: mockOnBack,
        onContinue: mockOnContinue,
        validEntriesCount: 2,
        totalValidAmount: 3000,
        categoryName: "Airtime",
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));
        useExitHandlers.mockReturnValue(mockExitHandlers);

        // Reset mockFormik to default state
        mockFormik.values = { narration: "Test narration" };
        mockFormik.errors = {};
        mockFormik.touched = {};
        mockFormik.validateForm = jest.fn(() => Promise.resolve({}));
        mockFormik.setFieldValue = jest.fn();
        mockFormik.setFieldTouched = jest.fn();
    });

    it("renders the component correctly with summary information", () => {
        render(<PaymentInfo {...defaultProps} />);

        expect(screen.getByTestId("drawer")).toBeInTheDocument();
        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Airtime bulk bill payment");
        expect(screen.getByText("Payment information")).toBeInTheDocument();
        expect(screen.getByTestId("stepper")).toBeInTheDocument();
        expect(screen.getByTestId("summary-card")).toBeInTheDocument();
        expect(screen.getByTestId("account-selector")).toBeInTheDocument();
        expect(screen.getByTestId("textarea-narration")).toBeInTheDocument();
    });

    it("displays correct summary information", () => {
        render(<PaymentInfo {...defaultProps} />);

        expect(screen.getByTestId("summary-label-0")).toHaveTextContent("Valid phone numbers");
        expect(screen.getByTestId("summary-value-0")).toHaveTextContent("2");
        expect(screen.getByTestId("summary-label-1")).toHaveTextContent("Total valid amount");
        expect(screen.getByTestId("summary-value-1")).toHaveTextContent("₦3,000");
    });

    it("calculates summary from Redux state when props not provided", () => {
        const propsWithoutSummary = {
            onBack: mockOnBack,
            onContinue: mockOnContinue,
            categoryName: "Airtime",
        };

        render(<PaymentInfo {...propsWithoutSummary} />);

        // Should calculate from entries in Redux state: 2 valid entries totaling 3000
        expect(screen.getByTestId("summary-value-0")).toHaveTextContent("2");
        expect(screen.getByTestId("summary-value-1")).toHaveTextContent("₦3,000");
    });

    it("handles account change correctly", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const accountSelect = screen.getByTestId("account-select");

        await act(async () => {
            fireEvent.change(accountSelect, { target: { value: "**********" } });
        });

        expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountFrom: "**********" }));
    });

    it("handles narration change", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Test narration" } });
        });

        expect(narrationField.value).toBe("Test narration");
    });

    it("disables continue button when accounts are loading", () => {
        const loadingState = {
            ...mockReduxState,
            account: {
                ...mockReduxState.account,
                loadingStatus: "loading",
            },
        };

        useAppSelector.mockImplementation((selector) => selector(loadingState));

        render(<PaymentInfo {...defaultProps} />);

        const continueButton = screen.getByTestId("button-primary");
        expect(continueButton).toBeDisabled();
    });

    it("disables continue button when no accounts are available", () => {
        const noAccountsState = {
            ...mockReduxState,
            account: {
                accounts: [],
                loadingStatus: "idle",
            },
        };

        useAppSelector.mockImplementation((selector) => selector(noAccountsState));

        render(<PaymentInfo {...defaultProps} />);

        const continueButton = screen.getByTestId("button-primary");
        expect(continueButton).toBeDisabled();
    });

    it("disables continue button when no account is selected", () => {
        const noSelectedAccountState = {
            ...mockReduxState,
            bulkAirtime: {
                ...mockReduxState.bulkAirtime,
                paymentInfo: {
                    narration: "Test narration",
                    accountFrom: "",
                },
            },
        };

        useAppSelector.mockImplementation((selector) => selector(noSelectedAccountState));

        render(<PaymentInfo {...defaultProps} />);

        const continueButton = screen.getByTestId("button-primary");
        expect(continueButton).toBeDisabled();
    });

    it("handles back button click", () => {
        render(<PaymentInfo {...defaultProps} />);

        const backButton = screen.getByTestId("button-outline");
        fireEvent.click(backButton);

        expect(mockOnBack).toHaveBeenCalled();
    });

    it("handles drawer close correctly", () => {
        render(<PaymentInfo {...defaultProps} />);

        const closeButton = screen.getByTestId("close-drawer");
        fireEvent.click(closeButton);

        expect(mockExitHandlers.handleClose).toHaveBeenCalled();
    });

    it("dispatches reset action when exit is confirmed", () => {
        let capturedOnExit;
        useExitHandlers.mockImplementation(({ onExit }) => {
            capturedOnExit = onExit;
            return mockExitHandlers;
        });

        render(<PaymentInfo {...defaultProps} />);

        // Call the onExit function that was passed to useExitHandlers
        capturedOnExit();

        expect(resetBulkAirtime).toHaveBeenCalled();
        expect(mockDispatch).toHaveBeenCalledWith({ type: "bulkAirtime/resetBulkAirtime" });
    });

    it("shows exit confirmation modal when showExitConfirmation is true", () => {
        const exitConfirmationHandlers = {
            ...mockExitHandlers,
            showExitConfirmation: true,
        };

        useExitHandlers.mockReturnValue(exitConfirmationHandlers);

        render(<PaymentInfo {...defaultProps} />);

        expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
        expect(screen.getByTestId("confirm-exit")).toBeInTheDocument();
        expect(screen.getByTestId("cancel-exit")).toBeInTheDocument();
    });

    it("handles exit confirmation buttons correctly", () => {
        const exitConfirmationHandlers = {
            ...mockExitHandlers,
            showExitConfirmation: true,
        };

        useExitHandlers.mockReturnValue(exitConfirmationHandlers);

        render(<PaymentInfo {...defaultProps} />);

        // Test confirm exit
        fireEvent.click(screen.getByTestId("confirm-exit"));
        expect(exitConfirmationHandlers.handleConfirmExit).toHaveBeenCalled();

        // Test cancel exit
        fireEvent.click(screen.getByTestId("cancel-exit"));
        expect(exitConfirmationHandlers.handleCancelExit).toHaveBeenCalled();
    });

    it("uses default category name when not provided", () => {
        const propsWithoutCategory = {
            onBack: mockOnBack,
            onContinue: mockOnContinue,
            validEntriesCount: 2,
            totalValidAmount: 3000,
        };

        render(<PaymentInfo {...propsWithoutCategory} />);

        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Airtime bulk bill payment");
    });

    it("persists narration from Redux state", () => {
        const stateWithNarration = {
            ...mockReduxState,
            bulkAirtime: {
                ...mockReduxState.bulkAirtime,
                paymentInfo: {
                    narration: "Persisted narration",
                    accountFrom: "**********",
                },
            },
        };

        useAppSelector.mockImplementation((selector) => selector(stateWithNarration));

        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");
        expect(narrationField.value).toBe("Persisted narration");
    });

    it("submits form when continue button is clicked", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const continueButton = screen.getByTestId("button-primary");

        await act(async () => {
            fireEvent.click(continueButton);
        });

        expect(mockOnContinue).toHaveBeenCalled();
    });

    // Test form validation error handling (lines 81-93)
    it("handles form validation errors during formik onSubmit", async () => {
        const mockValidationErrors = { narration: "Narration is required" };
        mockFormik.validateForm = jest.fn(() => Promise.resolve(mockValidationErrors));

        render(<PaymentInfo {...defaultProps} />);

        const continueButton = screen.getByTestId("button-primary");

        await act(async () => {
            fireEvent.click(continueButton);
        });

        expect(mockFormik.validateForm).toHaveBeenCalled();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("narration", true);
        expect(mockOnContinue).not.toHaveBeenCalled();
    });

    // Test account change optimization logic (lines 131-132)
    it("skips account change when value hasn't changed", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const accountSelect = screen.getByTestId("account-select");

        // First change to establish current value
        await act(async () => {
            fireEvent.change(accountSelect, { target: { value: "**********" } });
        });

        mockDispatch.mockClear();

        // Second change to same value should be skipped
        await act(async () => {
            fireEvent.change(accountSelect, { target: { value: "**********" } });
        });

        expect(mockDispatch).not.toHaveBeenCalled();
    });

    // Test narration change optimization and debouncing (lines 143-161)
    it("skips narration change when value hasn't changed", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        // First change to establish current value
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Test narration" } });
        });

        mockDispatch.mockClear();

        // Second change to same value should be skipped
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Test narration" } });
        });

        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("debounces narration changes and clears existing timeout", async () => {
        jest.useFakeTimers();

        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "First change" } });
        });

        // Change again before timeout completes
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Second change" } });
        });

        // Fast forward time to trigger debounced dispatch
        act(() => {
            jest.advanceTimersByTime(300);
        });

        expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ narration: "Second change" }));
        expect(mockDispatch).toHaveBeenCalledTimes(1); // Should only dispatch once due to debouncing

        jest.useRealTimers();
    });

    // Test cleanup timeout on component unmount (lines 167-168)
    it("cleans up timeout on component unmount", async () => {
        jest.useFakeTimers();
        const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");

        const { unmount } = render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Test timeout cleanup" } });
        });

        // Unmount component before timeout completes
        unmount();

        expect(clearTimeoutSpy).toHaveBeenCalled();

        clearTimeoutSpy.mockRestore();
        jest.useRealTimers();
    });

    // Test form validation in handleFormSubmit (lines 180-185)
    it("handles form validation errors in handleFormSubmit", async () => {
        const mockValidationErrors = { narration: "Narration is required" };
        mockFormik.validateForm = jest.fn(() => Promise.resolve(mockValidationErrors));

        render(<PaymentInfo {...defaultProps} />);

        const form = screen.getByTestId("payment-form");

        await act(async () => {
            fireEvent.submit(form);
        });

        expect(mockFormik.validateForm).toHaveBeenCalled();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("narration", true);
        expect(mockOnContinue).not.toHaveBeenCalled();
    });

    it("proceeds with form submission when validation passes", async () => {
        mockFormik.validateForm = jest.fn(() => Promise.resolve({}));

        render(<PaymentInfo {...defaultProps} />);

        const form = screen.getByTestId("payment-form");

        await act(async () => {
            fireEvent.submit(form);
        });

        expect(mockFormik.validateForm).toHaveBeenCalled();
        expect(mockOnContinue).toHaveBeenCalled();
    });

    // Additional test to ensure validation error display
    it("displays validation error when narration is invalid", () => {
        mockFormik.errors = { narration: "Narration is required" };
        mockFormik.touched = { narration: true };

        render(<PaymentInfo {...defaultProps} />);

        expect(screen.getByText("Narration is required")).toBeInTheDocument();
        expect(screen.getByTestId("info-icon")).toBeInTheDocument();
    });

    describe("Account Auto-Selection", () => {
        it("auto-selects first account and updates both payment info and accounts.selectedAccount when no account is selected", () => {
            const stateWithNoSelectedAccount = {
                ...mockReduxState,
                bulkAirtime: {
                    ...mockReduxState.bulkAirtime,
                    paymentInfo: {
                        narration: "Test narration",
                        accountFrom: "", // No account selected initially
                    },
                },
                account: {
                    accounts: [
                        { accountNumber: "**********", accountName: "Savings Account", balance: 50000 },
                        { accountNumber: "**********", accountName: "Current Account", balance: 25000 },
                    ],
                    loadingStatus: "idle",
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNoSelectedAccount));

            render(<PaymentInfo {...defaultProps} />);

            // Should dispatch updatePaymentInfo with first account number
            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountFrom: "**********" }));

            // Should dispatch setSelectedAccount with formatted display string
            expect(mockDispatch).toHaveBeenCalledWith(setSelectedAccount("Savings Account ****7890"));

            // Auto-selection should have occurred
        });

        it("doesn't auto-select account when one is already selected", () => {
            render(<PaymentInfo {...defaultProps} />);

            // mockDispatch should not be called for auto-selection since account is already selected
            expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ accountFrom: "**********" }));
            expect(mockDispatch).not.toHaveBeenCalledWith(setSelectedAccount(expect.any(String)));
        });

        it("doesn't auto-select when no accounts are available", () => {
            const stateWithNoAccounts = {
                ...mockReduxState,
                bulkAirtime: {
                    ...mockReduxState.bulkAirtime,
                    paymentInfo: {
                        narration: "Test narration",
                        accountFrom: "",
                    },
                },
                account: {
                    accounts: [], // No accounts available
                    loadingStatus: "idle",
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNoAccounts));

            render(<PaymentInfo {...defaultProps} />);

            // Should not dispatch auto-selection when no accounts available
            expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ accountFrom: expect.any(String) }));
            expect(mockDispatch).not.toHaveBeenCalledWith(setSelectedAccount(expect.any(String)));
        });

        it("handles manual account selection correctly", async () => {
            render(<PaymentInfo {...defaultProps} />);

            const accountSelect = screen.getByTestId("account-select");

            // Clear previous dispatch calls
            mockDispatch.mockClear();

            // Simulate manual account selection
            await act(async () => {
                fireEvent.change(accountSelect, { target: { value: "**********" } });
            });

            // Should update payment info with selected account
            expect(mockDispatch).toHaveBeenCalledWith(updatePaymentInfo({ accountFrom: "**********" }));

            // Should also update accounts.selectedAccount with formatted display string - need to find the account first
            const selectedAccount = mockReduxState.account.accounts.find((acc) => acc.accountNumber === "**********");
            const expectedFormattedDisplay = selectedAccount ? "Account ****4321" : "Account ****4321";
            expect(mockDispatch).toHaveBeenCalledWith(setSelectedAccount(expectedFormattedDisplay));
        });
    });

    // Test formik onSubmit with validation errors (lines 81-93)
    it("handles formik onSubmit with validation errors", async () => {
        const mockValidationErrors = { narration: "Narration is required" };
        mockFormik.validateForm = jest.fn(() => Promise.resolve(mockValidationErrors));

        // Mock the formik onSubmit to be called directly
        const mockOnSubmit = jest.fn();
        mockFormik.handleSubmit = mockOnSubmit;

        render(<PaymentInfo {...defaultProps} />);

        // Directly call the onSubmit function to trigger formik validation
        const { onSubmit } = useFormik.mock.calls[0][0];
        await onSubmit();

        expect(mockFormik.validateForm).toHaveBeenCalled();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("narration", true);
        expect(mockOnContinue).not.toHaveBeenCalled();
    });

    // Test formik onSubmit without validation errors (lines 91-93)
    it("handles formik onSubmit without validation errors", async () => {
        mockFormik.validateForm = jest.fn(() => Promise.resolve({}));

        render(<PaymentInfo {...defaultProps} />);

        // Directly call the onSubmit function to trigger formik validation
        const { onSubmit } = useFormik.mock.calls[0][0];
        await onSubmit();

        expect(mockFormik.validateForm).toHaveBeenCalled();
        expect(mockOnContinue).toHaveBeenCalled();
    });

    // Test narration change with same value (lines 145-146)
    it("handles narration change with same value at component start", async () => {
        const stateWithSameNarration = {
            ...mockReduxState,
            bulkAirtime: {
                ...mockReduxState.bulkAirtime,
                paymentInfo: {
                    narration: "Same narration",
                    accountFrom: "**********",
                },
            },
        };

        useAppSelector.mockImplementation((selector) => selector(stateWithSameNarration));

        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        // Try to change to the same value that's already in the state
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Same narration" } });
        });

        // Should not dispatch since it's the same value
        expect(mockDispatch).not.toHaveBeenCalledWith(updatePaymentInfo({ narration: "Same narration" }));
    });

    // Test early return in narration change (lines 145-146)
    it("triggers early return in narration change when value is unchanged", async () => {
        render(<PaymentInfo {...defaultProps} />);

        const narrationField = screen.getByTestId("textarea-field-narration");

        // Set initial value
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Initial value" } });
        });

        // Wait for debounce
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 350));
        });

        mockDispatch.mockClear();

        // Try to change to the same value again
        await act(async () => {
            fireEvent.change(narrationField, { target: { value: "Initial value" } });
        });

        // Should not dispatch since it's the same value (early return)
        expect(mockDispatch).not.toHaveBeenCalled();
    });
});
