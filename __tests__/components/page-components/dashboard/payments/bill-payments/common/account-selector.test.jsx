/**
 * @file account-selector.test.jsx
 *
 * @purpose Test suite for the AccountSelector component used in bill payments.
 *
 * @functionality This test file verifies that the AccountSelector component correctly
 * renders account options, handles account selection, displays account balances, and
 * works with the useAccountSelection hook. It tests various component states including loading, error
 * handling, and normal operation with different account scenarios.
 */

import { render, screen, fireEvent } from "@testing-library/react";
import AccountSelector from "@/components/page-components/dashboard/bill-payments/common/account-selector";
import "@testing-library/jest-dom";

// Mock shared utilities
jest.mock("@/components/page-components/dashboard/bill-payments/common/account-utils", () => ({
    formatAccountName: jest.fn((account) => account.accountName || "Savings account"),
    formatAccountNumber: jest.fn((accountNumber) => {
        const lastFourDigits = accountNumber.slice(-4);
        return `****${lastFourDigits}`;
    }),
    formatAccountDisplay: jest.fn(
        (account) => `${account.accountName || "Savings account"} ****${account.accountNumber.slice(-4)}`
    ),
    getAccountByNumber: jest.fn((accounts, accountNumber) =>
        accounts.find((acc) => acc.accountNumber === accountNumber)
    ),
}));

// Mock the useAccountSelection hook
jest.mock("@/components/page-components/dashboard/bill-payments/common/useAccountSelection", () => ({
    useAccountSelection: jest.fn(() => ({
        accounts: [
            {
                id: 1,
                accountNumber: "**********",
                accountName: "SAVINGS ACCOUNT",
                preferredName: "My Savings",
                currencyCode: "NGN",
                primaryFlag: true,
                hiddenFlag: false,
                status: "ACTIVE",
                schemeType: "SAVINGS",
                schemeCode: "SV",
            },
            {
                id: 2,
                accountNumber: "**********",
                accountName: "CURRENT ACCOUNT",
                preferredName: "Business Account",
                currencyCode: "NGN",
                primaryFlag: false,
                hiddenFlag: false,
                status: "A",
                schemeType: "CHECKING",
                schemeCode: "CA",
            },
        ],
        loading: false,
        balances: { **********: 1250000.75, "**********": 500000 },
        currentAccount: undefined,
        handleAccountChange: jest.fn(),
    })),
}));

// Mock loading indicator
jest.mock("@/components/common/loading-indicator", () => ({
    __esModule: true,
    default: ({ size, align }) => (
        <div data-testid="loading-indicator" data-size={size} data-align={align}>
            Loading...
        </div>
    ),
}));

// Mock number formatter
jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((number) => {
        if (typeof number === "number") {
            return `₦ ${number.toLocaleString()}`;
        }
        return "₦ -";
    }),
}));

// Mock Select component that preserves original function calls
jest.mock("@/components/common/select", () => {
    return {
        Select: ({ children, onValueChange, value }) => (
            <div
                data-testid="select-component"
                data-value={value}
                onClick={() => onValueChange && onValueChange("**********")}
            >
                {children}
            </div>
        ),
        SelectContent: ({ children }) => <div data-testid="select-content">{children}</div>,
        SelectItem: ({ children, value, disabled }) => (
            <div
                data-testid="select-item"
                data-value={value}
                data-disabled={disabled}
                onClick={() => console.log("SelectItem clicked:", value)}
            >
                {children}
            </div>
        ),
        SelectTrigger: ({ children, id, className, ...props }) => (
            <div data-testid="account-selector-trigger" id={id} className={className} {...props}>
                {children}
            </div>
        ),
    };
});

describe("AccountSelector Component", () => {
    // Import the mocked useAccountSelection
    const {
        useAccountSelection,
    } = require("@/components/page-components/dashboard/bill-payments/common/useAccountSelection");

    const mockAccounts = [
        {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            preferredName: "My Savings",
            currencyCode: "NGN",
            primaryFlag: true,
            hiddenFlag: false,
            status: "ACTIVE",
            schemeType: "SAVINGS",
            schemeCode: "SV",
        },
        {
            id: 2,
            accountNumber: "**********",
            accountName: "CURRENT ACCOUNT",
            preferredName: "Business Account",
            currencyCode: "NGN",
            primaryFlag: false,
            hiddenFlag: false,
            status: "A",
            schemeType: "CHECKING",
            schemeCode: "CA",
        },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        // Reset the mock to default state
        useAccountSelection.mockReturnValue({
            accounts: mockAccounts,
            loading: false,
            balances: { **********: 1250000.75, "**********": 500000 },
            currentAccount: undefined,
            handleAccountChange: jest.fn(),
        });
    });

    test("renders loading state when accounts are being fetched", () => {
        useAccountSelection.mockReturnValue({
            accounts: [],
            loading: true,
            balances: {},
            currentAccount: undefined,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector />);

        expect(screen.getByText("Pay from")).toBeInTheDocument();
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    test("renders no accounts message when no accounts are available", () => {
        useAccountSelection.mockReturnValue({
            accounts: [],
            loading: false,
            balances: {},
            currentAccount: undefined,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector />);

        expect(screen.getByText("No accounts available")).toBeInTheDocument();
    });

    test("renders accounts from useAccountSelection hook", () => {
        // Uses default mock setup from beforeEach
        render(<AccountSelector />);

        expect(screen.getByTestId("account-selector-trigger")).toBeInTheDocument();
        expect(screen.getByTestId("select-content")).toBeInTheDocument();
        expect(screen.getAllByTestId("select-item")).toHaveLength(2);
    });

    test("calls onAccountChange when account is selected", () => {
        const onAccountChangeMock = jest.fn();
        const mockHandleAccountChange = jest.fn((accountNumber) => {
            // Simulate what the actual hook does - call onAccountChange
            onAccountChangeMock(accountNumber);
        });

        useAccountSelection.mockReturnValue({
            accounts: [
                {
                    id: 1,
                    accountNumber: "**********",
                    accountName: "SAVINGS ACCOUNT",
                    schemeType: "SAVINGS",
                },
            ],
            loading: false,
            balances: { **********: 1250000.75 },
            currentAccount: undefined,
            handleAccountChange: mockHandleAccountChange,
        });

        render(<AccountSelector onAccountChange={onAccountChangeMock} />);

        // Simulate clicking the select component
        fireEvent.click(screen.getByTestId("select-component"));

        expect(onAccountChangeMock).toHaveBeenCalledWith("**********");
        expect(mockHandleAccountChange).toHaveBeenCalledWith("**********");
    });

    test("displays custom label when provided", () => {
        render(<AccountSelector label="Select Account" />);

        expect(screen.getByText("Select Account")).toBeInTheDocument();
    });

    test("displays account information when account is selected", () => {
        const selectedAccount = {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            schemeType: "SAVINGS",
        };

        useAccountSelection.mockReturnValue({
            accounts: [selectedAccount],
            loading: false,
            balances: { **********: 1250000.75 },
            currentAccount: selectedAccount,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toHaveTextContent("SAVINGS ACCOUNT");
        expect(trigger).toHaveTextContent("****7890");
    });

    test("applies custom className when provided", () => {
        render(<AccountSelector className="custom-class" />);

        const container = screen.getByTestId("select-component").parentElement;
        expect(container.className).toContain("custom-class");
    });

    test("handles account selection when passed as prop", () => {
        const selectedAccount = {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            schemeType: "SAVINGS",
        };

        useAccountSelection.mockReturnValue({
            accounts: [selectedAccount],
            loading: false,
            balances: { **********: 1250000.75 },
            currentAccount: selectedAccount,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toBeInTheDocument();
        expect(trigger).toHaveTextContent("SAVINGS ACCOUNT");
        expect(trigger).toHaveTextContent("****7890");
    });

    test("renders Select an account when no account is selected", () => {
        render(<AccountSelector />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toHaveTextContent("Select an account");
    });

    test("displays default Pay from label when no label provided", () => {
        render(<AccountSelector />);

        expect(screen.getByText("Pay from")).toBeInTheDocument();
    });

    test("handles account change properly", () => {
        const onAccountChangeMock = jest.fn();
        const mockHandleAccountChange = jest.fn((accountNumber) => {
            // Simulate what the actual hook does - call onAccountChange
            onAccountChangeMock(accountNumber);
        });

        useAccountSelection.mockReturnValue({
            accounts: mockAccounts,
            loading: false,
            balances: { **********: 1250000.75, "**********": 500000 },
            currentAccount: undefined,
            handleAccountChange: mockHandleAccountChange,
        });

        render(<AccountSelector onAccountChange={onAccountChangeMock} />);

        fireEvent.click(screen.getByTestId("select-component"));

        expect(onAccountChangeMock).toHaveBeenCalledWith("**********");
        expect(mockHandleAccountChange).toHaveBeenCalledWith("**********");
    });

    test("renders all account options in dropdown", () => {
        render(<AccountSelector />);

        const selectItems = screen.getAllByTestId("select-item");
        expect(selectItems).toHaveLength(2);

        // Check first account
        expect(selectItems[0]).toHaveTextContent("SAVINGS ACCOUNT");
        expect(selectItems[0]).toHaveTextContent("****7890");

        // Check second account
        expect(selectItems[1]).toHaveTextContent("CURRENT ACCOUNT");
        expect(selectItems[1]).toHaveTextContent("****4321");
    });

    test("uses correct value prop for Select component", () => {
        const selectedAccount = {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            schemeType: "SAVINGS",
        };

        useAccountSelection.mockReturnValue({
            accounts: [selectedAccount],
            loading: false,
            balances: { **********: 1250000.75 },
            currentAccount: selectedAccount,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const selectComponent = screen.getByTestId("select-component");
        expect(selectComponent).toHaveAttribute("data-value", "**********");
    });

    test("handles empty selected account gracefully", () => {
        render(<AccountSelector selectedAccount="" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toHaveTextContent("Select an account");
    });

    test("shows loading indicator in balance area when balance is loading", () => {
        const selectedAccount = {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            schemeType: "SAVINGS",
        };

        useAccountSelection.mockReturnValue({
            accounts: [selectedAccount],
            loading: false,
            balances: {}, // No balance loaded yet
            currentAccount: selectedAccount,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        // Balance should show loading indicator initially
        expect(trigger.querySelector('[data-testid="loading-indicator"]')).toBeInTheDocument();
    });

    test("handles missing account gracefully", () => {
        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toHaveTextContent("Select an account");
    });

    test("handles account without accountName using schemeType fallback", () => {
        const accountsWithoutName = [
            {
                id: 1,
                accountNumber: "**********",
                accountName: "", // Empty accountName to trigger fallback
                preferredName: "My Account",
                currencyCode: "NGN",
                primaryFlag: true,
                hiddenFlag: false,
                status: "ACTIVE",
                schemeType: "CHECKING",
                schemeCode: "CHK",
            },
        ];

        useAccountSelection.mockReturnValue({
            accounts: accountsWithoutName,
            loading: false,
            balances: { **********: 1250000.75 },
            currentAccount: accountsWithoutName[0],
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toHaveTextContent("Savings account");
        expect(trigger).toHaveTextContent("****7890");
    });

    test("displays balance when balance data is available", () => {
        const selectedAccount = {
            id: 1,
            accountNumber: "**********",
            accountName: "SAVINGS ACCOUNT",
            schemeType: "SAVINGS",
        };

        useAccountSelection.mockReturnValue({
            accounts: [selectedAccount],
            loading: false,
            balances: { **********: 500000 },
            currentAccount: selectedAccount,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector selectedAccount="**********" />);

        const trigger = screen.getByTestId("account-selector-trigger");
        expect(trigger).toBeInTheDocument();
    });

    test("formats balance correctly in dropdown items when balance is available", () => {
        useAccountSelection.mockReturnValue({
            accounts: mockAccounts,
            loading: false,
            balances: {},
            currentAccount: undefined,
            handleAccountChange: jest.fn(),
        });

        render(<AccountSelector />);

        const selectItems = screen.getAllByTestId("select-item");
        expect(selectItems).toHaveLength(2);

        // Items should show loading indicators initially since balance fetch is async
        expect(selectItems[0].querySelector('[data-testid="loading-indicator"]')).toBeInTheDocument();
        expect(selectItems[1].querySelector('[data-testid="loading-indicator"]')).toBeInTheDocument();
    });
});
