/**
 * Test file for account utility functions
 */

import {
    formatAccountName,
    formatAccountNumber,
    getAccountByNumber,
    formatAccountDisplay,
} from "@/components/page-components/dashboard/bill-payments/common/account-utils";

describe("Account Utils", () => {
    const mockAccount = {
        accountNumber: "**********",
        accountName: "SAVINGS ACCOUNT",
        schemeType: "SAVINGS",
    };

    describe("formatAccountName", () => {
        it("returns accountName when available", () => {
            expect(formatAccountName(mockAccount)).toBe("SAVINGS ACCOUNT");
        });

        it("formats schemeType when accountName is not available", () => {
            const accountWithoutName = { ...mockAccount, accountName: "" };
            expect(formatAccountName(accountWithoutName)).toBe("Savings account");
        });

        it("returns fallback when both accountName and schemeType are empty", () => {
            const accountWithoutBoth = { ...mockAccount, accountName: "", schemeType: "" };
            expect(formatAccountName(accountWithoutBoth)).toBe("Account");
        });
    });

    describe("formatAccountNumber", () => {
        it("formats account number correctly", () => {
            expect(formatAccountNumber("**********")).toBe("****7890");
        });
    });

    describe("getAccountByNumber", () => {
        const accounts = [mockAccount, { accountNumber: "**********", accountName: "CURRENT ACCOUNT" }];

        it("finds account by number", () => {
            expect(getAccountByNumber(accounts, "**********")).toBe(mockAccount);
        });

        it("returns undefined for non-existent account", () => {
            expect(getAccountByNumber(accounts, "**********")).toBeUndefined();
        });
    });

    describe("formatAccountDisplay", () => {
        it("combines name and number formatting", () => {
            expect(formatAccountDisplay(mockAccount)).toBe("SAVINGS ACCOUNT ****7890");
        });
    });
});
