/**
 * @file useAccountSelection.test.js
 * @purpose Test for useAccountSelection hook to prevent infinite API calls
 */

import { renderHook, act, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import {
    useAccountSelection,
    clearGlobalBalanceCache,
} from "@/components/page-components/dashboard/bill-payments/common/useAccountSelection";
import useGetAccountDetails from "@/hooks/useGetAccount";

// Mock the dependencies
jest.mock("@/hooks/useGetAccount");
jest.mock("@/components/page-components/dashboard/bill-payments/common/account-utils", () => ({
    formatAccountName: jest.fn((account) => account.accountName || "Savings account"),
    formatAccountNumber: jest.fn((accountNumber) => {
        const lastFourDigits = accountNumber.slice(-4);
        return `****${lastFourDigits}`;
    }),
    formatAccountDisplay: jest.fn(
        (account) => `${account.accountName || "Savings account"} ****${account.accountNumber.slice(-4)}`
    ),
    getAccountByNumber: jest.fn((accounts, accountNumber) =>
        accounts.find((acc) => acc.accountNumber === accountNumber)
    ),
}));
jest.mock("@/redux/features/accounts", () => ({
    setSelectedAccount: jest.fn((payload) => ({
        type: "accounts/setSelectedAccount",
        payload,
    })),
}));

const mockUseGetAccountDetails = useGetAccountDetails;
const mockGetDetails = jest.fn();

// Mock Redux store
const createMockStore = (initialState = {}) =>
    configureStore({
        reducer: {
            account: (
                state = {
                    accounts: [],
                    loadingStatus: "idle",
                },
                action
            ) => state,
        },
        preloadedState: initialState,
    });

// Test wrapper component
const createWrapper =
    (store) =>
    ({ children }) => <Provider store={store}>{children}</Provider>;

describe("useAccountSelection Hook - Infinite Calls Prevention", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Clear the global cache between tests to prevent interference
        clearGlobalBalanceCache();
        mockUseGetAccountDetails.mockReturnValue({
            getDetails: mockGetDetails,
            loading: false,
            details: undefined,
        });
    });

    describe("Infinite API Calls Prevention", () => {
        test("should not make infinite API calls when getDetails function changes", async () => {
            const mockAccounts = [
                { accountNumber: "**********", accountName: "Test Account 1" },
                { accountNumber: "**********", accountName: "Test Account 2" },
            ];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValue({ balance: 1000 });

            const { rerender } = renderHook(() => useAccountSelection({ selectedAccount: "**********" }), {
                wrapper: createWrapper(store),
            });

            // Wait for initial fetch
            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(2); // Called once per account
            });

            const initialCallCount = mockGetDetails.mock.calls.length;

            // Simulate re-render (which would normally cause getDetails to be recreated)
            rerender();
            rerender();
            rerender();

            // Wait a bit to ensure no additional calls are made
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Should not have made additional calls after re-renders
            expect(mockGetDetails).toHaveBeenCalledTimes(initialCallCount);
        });

        test("should only fetch balances once when accounts list is stable", async () => {
            const mockAccounts = [{ accountNumber: "**********", accountName: "Account 1" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValue({ balance: 500 });

            renderHook(() => useAccountSelection({ selectedAccount: "**********" }), { wrapper: createWrapper(store) });

            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(1);
            });

            // Wait additional time to ensure no extra calls
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 200));
            });

            expect(mockGetDetails).toHaveBeenCalledTimes(1);
        });

        test("should cache balance results and not refetch on subsequent renders", async () => {
            const mockAccounts = [{ accountNumber: "**********", accountName: "Cached Account" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValue({ balance: 750 });

            const { result, rerender } = renderHook(() => useAccountSelection({ selectedAccount: "**********" }), {
                wrapper: createWrapper(store),
            });

            await waitFor(() => {
                expect(result.current.balances["**********"]).toBe(750);
            });

            const initialCallCount = mockGetDetails.mock.calls.length;

            // Multiple re-renders should not cause additional API calls
            rerender();
            rerender();

            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            expect(mockGetDetails).toHaveBeenCalledTimes(initialCallCount);
            expect(result.current.balances["**********"]).toBe(750);
        });
    });

    describe("Proper Functionality After Fix", () => {
        test("should fetch balances for all accounts on initial render", async () => {
            const mockAccounts = [
                { accountNumber: "1111", accountName: "Account 1" },
                { accountNumber: "2222", accountName: "Account 2" },
                { accountNumber: "3333", accountName: "Account 3" },
            ];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails
                .mockResolvedValueOnce({ balance: 100 })
                .mockResolvedValueOnce({ balance: 200 })
                .mockResolvedValueOnce({ balance: 300 });

            const { result } = renderHook(() => useAccountSelection({ selectedAccount: "1111" }), {
                wrapper: createWrapper(store),
            });

            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(3);
                expect(mockGetDetails).toHaveBeenCalledWith("1111");
                expect(mockGetDetails).toHaveBeenCalledWith("2222");
                expect(mockGetDetails).toHaveBeenCalledWith("3333");
            });

            await waitFor(() => {
                expect(result.current.balances).toEqual({
                    1111: 100,
                    2222: 200,
                    3333: 300,
                });
            });
        });

        test("should handle API errors gracefully without infinite retries", async () => {
            const mockAccounts = [{ accountNumber: "4444", accountName: "Error Account" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockRejectedValue(new Error("API Error"));

            const { result } = renderHook(() => useAccountSelection({ selectedAccount: "4444" }), {
                wrapper: createWrapper(store),
            });

            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(1);
            });

            // Wait to ensure no retry attempts
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 200));
            });

            expect(mockGetDetails).toHaveBeenCalledTimes(1);
            expect(result.current.balances["4444"]).toBe(0); // Should default to 0
        });

        test("should not fetch balances when accounts array is empty", async () => {
            const store = createMockStore({
                account: {
                    accounts: [],
                    loadingStatus: "idle",
                },
            });

            renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            expect(mockGetDetails).not.toHaveBeenCalled();
        });

        test("should fetch balances when accounts are added after initial render", async () => {
            let storeState = {
                account: {
                    accounts: [],
                    loadingStatus: "idle",
                },
            };

            const store = createMockStore(storeState);
            mockGetDetails.mockResolvedValue({ balance: 999 });

            const { result, rerender } = renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            // Initially no calls
            expect(mockGetDetails).not.toHaveBeenCalled();

            // Simulate accounts being loaded
            storeState.account.accounts = [{ accountNumber: "5555", accountName: "New Account" }];

            rerender();

            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledWith("5555");
            });

            await waitFor(() => {
                expect(result.current.balances["5555"]).toBe(999);
            });
        });
    });

    describe("Loading States", () => {
        test("should reflect loading state from Redux store", () => {
            const store = createMockStore({
                account: {
                    accounts: [],
                    loadingStatus: "loading",
                },
            });

            const { result } = renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            expect(result.current.loading).toBe(true);
        });

        test("should reflect idle state from Redux store", () => {
            const store = createMockStore({
                account: {
                    accounts: [],
                    loadingStatus: "idle",
                },
            });

            const { result } = renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            expect(result.current.loading).toBe(false);
        });
    });

    describe("Account Selection Handling", () => {
        test("should handle account selection without causing additional API calls", async () => {
            const mockAccounts = [{ accountNumber: "6666", accountName: "Selectable Account" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            const mockOnAccountChange = jest.fn();
            mockGetDetails.mockResolvedValue({ balance: 1500 });

            const { result } = renderHook(() => useAccountSelection({ onAccountChange: mockOnAccountChange }), {
                wrapper: createWrapper(store),
            });

            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(1);
            });

            const initialCallCount = mockGetDetails.mock.calls.length;

            // Simulate account selection
            act(() => {
                result.current.handleAccountChange("6666");
            });

            expect(mockOnAccountChange).toHaveBeenCalledWith("6666");

            // Should not trigger additional API calls
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            expect(mockGetDetails).toHaveBeenCalledTimes(initialCallCount);
        });
    });

    describe("Caching Functionality", () => {
        test("should cache balance results and avoid redundant API calls within TTL", async () => {
            const mockAccounts = [
                { accountNumber: "**********", accountName: "Cached Account" },
                { accountNumber: "**********", accountName: "Another Account" },
            ];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValueOnce({ balance: 1000 }).mockResolvedValueOnce({ balance: 2000 });

            const { result, rerender } = renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            // Wait for initial balance fetch
            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(2);
            });

            expect(result.current.balances["**********"]).toBe(1000);
            expect(result.current.balances["**********"]).toBe(2000);

            // Clear mock calls to track subsequent calls
            mockGetDetails.mockClear();

            // Re-render component (simulating prop change or parent re-render)
            rerender();

            // Wait for any potential API calls (should be none due to caching)
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Should not make additional API calls as results are cached
            expect(mockGetDetails).not.toHaveBeenCalled();
            expect(result.current.balances["**********"]).toBe(1000);
            expect(result.current.balances["**********"]).toBe(2000);
        });

        test("should expose invalidateCache function for manual cache clearing", () => {
            const mockAccounts = [{ accountNumber: "**********", accountName: "Test Account" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValue({ balance: 500 });

            const { result } = renderHook(() => useAccountSelection({}), { wrapper: createWrapper(store) });

            // Should expose invalidateCache function
            expect(typeof result.current.invalidateCache).toBe("function");

            // Function should be callable without errors
            act(() => {
                result.current.invalidateCache("**********");
                result.current.invalidateCache(); // Clear all cache
            });
        });

        test("should share cache between different component instances (simulating navigation)", async () => {
            const mockAccounts = [{ accountNumber: "**********", accountName: "Shared Account" }];

            const store = createMockStore({
                account: {
                    accounts: mockAccounts,
                    loadingStatus: "idle",
                },
            });

            mockGetDetails.mockResolvedValue({ balance: 1500 });

            // First component instance (like payment-info page)
            const { result: firstInstance, unmount: unmountFirst } = renderHook(() => useAccountSelection({}), {
                wrapper: createWrapper(store),
            });

            // Wait for first instance to fetch and cache the balance
            await waitFor(() => {
                expect(mockGetDetails).toHaveBeenCalledTimes(1);
                expect(firstInstance.current.balances["**********"]).toBe(1500);
            });

            // Clear mock to track new calls
            mockGetDetails.mockClear();

            // Unmount first instance (simulating navigation away from page)
            unmountFirst();

            // Second component instance (like review-bill page, then back to payment-info)
            const { result: secondInstance } = renderHook(() => useAccountSelection({}), {
                wrapper: createWrapper(store),
            });

            // Wait a bit for any potential API calls
            await act(async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
            });

            // Should NOT make additional API calls as balance is cached globally
            expect(mockGetDetails).not.toHaveBeenCalled();

            // Wait for state to be populated from cache
            await waitFor(() => {
                expect(secondInstance.current.balances["**********"]).toBe(1500);
            });
        });
    });
});
