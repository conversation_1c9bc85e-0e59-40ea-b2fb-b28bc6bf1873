/* eslint-disable @typescript-eslint/no-require-imports */
import {
    TransactionBadge,
    TransactionDetails,
} from "@/components/page-components/dashboard/transactions/transaction-details";
import { formatDate } from "@/functions/date";
import { convertCamelCaseToWords, formatNumberToNaira, getNameInitials } from "@/functions/stringManipulations";
import sendMoneyReducer from "@/redux/slices/sendMoneySlice";
import transactionReducer from "@/redux/slices/transactionSlice";
import { configureStore } from "@reduxjs/toolkit";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useSearchParams: jest.fn(),
}));

// Mock the components used by TransactionDetails
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, className }) =>
        isOpen ? (
            <div data-testid="side-drawer" className={className}>
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children, className }) => (
        <div data-testid="avatar" className={className}>
            {children}
        </div>
    ),
    AvatarImage: () => <img data-testid="avatar-image" alt="avatar-image" />,
    AvatarFallback: ({ children }) => <div data-testid="avatar-fallback">{children}</div>,
}));

jest.mock("@/components/common/tab-switch", () => ({
    __esModule: true,
    default: ({ tabs, panels }) => (
        <div data-testid="tab-switch">
            {tabs.map((tab, index) => (
                <div key={tab} data-testid={`tab-${index}`}>
                    {tab}
                    {panels[index]}
                </div>
            ))}
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, loading }) => (
        <button data-testid="button" onClick={onClick} disabled={loading}>
            {loading ? "Loading..." : children}
        </button>
    ),
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn(),
    getNameInitials: jest.fn(),
    convertCamelCaseToWords: jest.fn(),
}));

jest.mock("@/functions/date", () => ({
    formatDate: jest.fn(),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn(),
}));

jest.mock("@/redux/slices/transactionSlice", () => ({
    transactionActions: {
        getTransaction: jest.fn(),
    },
}));

const mockTransaction = {
    transactionType: "Credit",
    counterpartyType: "Dangote Farms",
    amount: 7300000,
    narration: "Fertilizer supplies",
    createdDate: "2024-12-10T00:04:00Z",
    transactionId: "TRX-240123-4567",
};

const createMockStore = (initialState = {}) =>
    configureStore({
        reducer: {
            transaction: transactionReducer,
            sendMoney: sendMoneyReducer,
        },
        preloadedState: {
            transaction: {
                transactions: [mockTransaction],
                get: {
                    loading: false,
                    error: null,
                },
                transaction: mockTransaction,
                ...initialState.transaction,
            },
            sendMoney: {
                getTransferApprovers: {
                    data: [],
                },
                ...initialState.sendMoney,
            },
        },
    });

const renderWithProvider = (ui, { store = createMockStore(), ...renderOptions } = {}) => {
    const Wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;
    return render(ui, { wrapper: Wrapper, ...renderOptions });
};

describe("TransactionDetails Component", () => {
    const mockProps = {
        transactionId: "TRX-240123-4567",
        isOpen: true,
        handleCloseDetails: jest.fn(),
    };

    const mockDispatch = jest.fn();
    const { useAppDispatch, useAppSelector } = require("@/redux/hooks");
    const { downloadReceipt } = require("@/redux/actions/transferActions");
    const { transactionActions } = require("@/redux/slices/transactionSlice");

    beforeEach(() => {
        jest.clearAllMocks();
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) =>
            selector({
                transaction: {
                    get: { loading: false, error: null },
                    transaction: mockTransaction,
                },
                transfer: { downloadReceiptLoading: false },
            })
        );
        formatNumberToNaira.mockImplementation((amount) => `₦${amount.toLocaleString()}.00`);
        getNameInitials.mockImplementation((name) => name.slice(0, 2).toUpperCase());
        convertCamelCaseToWords.mockImplementation((key) =>
            key.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/^./, (str) => str.toUpperCase())
        );
        formatDate.mockImplementation((date) => new Date(date).toLocaleString());
    });

    it("renders correctly when open", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);

        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.getByTestId("side-drawer")).toHaveClass("!max-w-[488px]");
        expect(screen.getAllByText("Dangote Farms")[0]).toBeInTheDocument();
        expect(screen.getAllByText("Fertilizer supplies")[0]).toBeInTheDocument();
        expect(screen.getAllByText("+ ₦7,300,000.00")[0]).toBeInTheDocument();
    });

    it("doesn't render when closed", () => {
        renderWithProvider(<TransactionDetails {...mockProps} isOpen={false} />);
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    it("calls handleCloseDetails when close button is clicked", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);
        fireEvent.click(screen.getByTestId("close-btn"));
        expect(mockProps.handleCloseDetails).toHaveBeenCalledTimes(1);
    });

    it("renders only download receipt button", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);
        expect(screen.getByRole("button", { name: /download receipt/i })).toBeInTheDocument();
        expect(screen.queryByRole("button", { name: /report transaction/i })).not.toBeInTheDocument();
    });

    it("renders nothing when transaction is loading", () => {
        useAppSelector.mockImplementation((selector) =>
            selector({
                transaction: {
                    get: { loading: true, error: null },
                    transaction: null,
                },
                transfer: { downloadReceiptLoading: false },
            })
        );
        renderWithProvider(<TransactionDetails {...mockProps} />);
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.queryByText("Dangote Farms")).not.toBeInTheDocument();
    });

    it("renders nothing when there is an error", () => {
        useAppSelector.mockImplementation((selector) =>
            selector({
                transaction: {
                    get: { loading: false, error: "Failed to fetch" },
                    transaction: null,
                },
                transfer: { downloadReceiptLoading: false },
            })
        );
        renderWithProvider(<TransactionDetails {...mockProps} />);
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.queryByText("Dangote Farms")).not.toBeInTheDocument();
    });

    it("dispatches getTransaction action on mount when transactionId is provided", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);
        expect(transactionActions.getTransaction).toHaveBeenCalledWith(mockProps.transactionId);
    });

    it("uses selectedTransaction when provided", () => {
        const selectedTransaction = {
            ...mockTransaction,
            counterpartyType: "Acme Corp",
            amount: 1000000,
        };
        renderWithProvider(<TransactionDetails {...mockProps} selectedTransaction={selectedTransaction} />);
        expect(screen.getAllByText("Acme Corp")[0]).toBeInTheDocument();
        expect(screen.getAllByText("+ ₦1,000,000.00")[0]).toBeInTheDocument();
    });

    it("calls handleReportDownload when download button is clicked", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);
        const button = screen.getByRole("button", { name: /download receipt/i });
        fireEvent.click(button);
        expect(downloadReceipt).toHaveBeenCalledWith({
            transactionId: mockProps.transactionId,
            isUserInitiated: true,
        });
        expect(mockDispatch).toHaveBeenCalled();
    });

    it("renders TabDetails for credit transactions", () => {
        renderWithProvider(<TransactionDetails {...mockProps} />);
        expect(screen.getByText("Details")).toBeInTheDocument();
        expect(screen.queryByTestId("tab-switch")).not.toBeInTheDocument();
    });
});

describe("TransactionBadge Component", () => {
    it("renders debit transaction type correctly", () => {
        render(<TransactionBadge transactionType="Debit" />);

        const badge = screen.getByText("Debit");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass(
            "rounded-full py-[6px] px-[10px] max-w-max capitalize text-[14px] leading-[18px] font-medium"
        );
        expect(badge).toHaveStyle({
            backgroundColor: "#FEF3F2",
            color: "#D92D20",
        });
    });

    it("renders credit transaction type correctly", () => {
        render(<TransactionBadge transactionType="Credit" />);

        const badge = screen.getByText("Credit");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass(
            "rounded-full py-[6px] px-[10px] max-w-max capitalize text-[14px] leading-[18px] font-medium"
        );
        expect(badge).toHaveStyle({
            backgroundColor: "#ECFDF3",
            color: "#039855",
        });
    });

    it("renders unknown transaction type with default styles", () => {
        render(<TransactionBadge transactionType="Unknown" />);

        const badge = screen.getByText("Unknown");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass(
            "rounded-full py-[6px] px-[10px] max-w-max capitalize text-[14px] leading-[18px] font-medium"
        );
        expect(badge).toHaveStyle({
            backgroundColor: "",
            color: "",
        });
    });

    it("applies correct capitalization to transaction type", () => {
        render(<TransactionBadge transactionType="Credit" />);

        const badge = screen.getByText("Credit");
        expect(badge).toHaveClass("capitalize");
        expect(badge.textContent).toBe("Credit");
    });

    it("has correct font properties", () => {
        render(<TransactionBadge transactionType="Debit" />);

        const badge = screen.getByText("Debit");
        expect(badge).toHaveClass("text-[14px] leading-[18px] font-medium");
    });

    it("has correct padding and max-width", () => {
        render(<TransactionBadge transactionType="Credit" />);

        const badge = screen.getByText("Credit");
        expect(badge).toHaveClass("py-[6px] px-[10px] max-w-max");
    });

    it("has rounded corners", () => {
        render(<TransactionBadge transactionType="Debit" />);

        const badge = screen.getByText("Debit");
        expect(badge).toHaveClass("rounded-full");
    });
});
