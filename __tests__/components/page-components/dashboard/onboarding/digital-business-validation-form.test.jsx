// DigitalBusinessValidationForm.test.jsx
import DigitalBusinessValidationForm from "@/components/page-components/dashboard/onboarding/digital-business-validation-form";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { businessValidationForm } from "@/redux/actions/digitalOnboardingAction";
import { useAppDispatch } from "@/redux/hooks";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

// --- Mocks ---
jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ isOpen, title, children, onClose, showExitConfirmation, onCancelExit, onConfirmExit }) =>
        isOpen ? (
            <div data-testid="drawer">
                <h1>{title}</h1>
                <button onClick={onClose} data-testid="drawer-close">
                    Close
                </button>
                {showExitConfirmation && (
                    <div data-testid="exit-confirmation">
                        <button onClick={onCancelExit} data-testid="cancel-exit">
                            Cancel
                        </button>
                        <button onClick={onConfirmExit} data-testid="confirm-exit">
                            Confirm Exit
                        </button>
                    </div>
                )}
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({ formik, name, label, type, onKeyDown, maxLength, showError }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <input
                id={name}
                name={name}
                type={type}
                value={formik.values[name]}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                onKeyDown={onKeyDown}
                maxLength={maxLength}
                data-show-error={showError}
            />
            {formik.touched[name] && formik.errors[name] && (
                <div data-testid={`${name}-error`}>{formik.errors[name]}</div>
            )}
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, type, disabled, loading, variant, size, className }) => (
        <button
            type={type}
            disabled={disabled}
            data-loading={loading ? "true" : "false"}
            onClick={onClick}
            data-variant={variant}
            data-size={size}
            className={className}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
}));

jest.mock("@/redux/actions/digitalOnboardingAction", () => ({
    businessValidationForm: jest.fn(),
}));

jest.mock("@/redux/features/uiDialogSlice", () => ({
    openDigitalBusinessInformationFormDialog: jest.fn(() => ({ type: "OPEN_INFO_DIALOG" })),
}));

jest.mock("@/redux/slices/onboardingSlice", () => ({
    setBusinessIdentifiers: jest.fn((payload) => ({ type: "SET_IDENTIFIERS", payload })),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

describe("DigitalBusinessValidationForm", () => {
    let mockDispatch;
    const mockOnClose = jest.fn();
    const mockHandlePrev = jest.fn();

    beforeEach(() => {
        mockDispatch = jest.fn((action) => action);
        useAppDispatch.mockReturnValue(mockDispatch);
        jest.clearAllMocks();
    });

    describe("Component Rendering", () => {
        it("renders title, inputs, and disabled Continue button", () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            expect(screen.getByTestId("drawer")).toBeInTheDocument();
            expect(screen.getByText("Account Application")).toBeInTheDocument();
            expect(screen.getByTestId("digital-business-validation-title")).toHaveTextContent(
                "To begin, let's validate your registered business"
            );
            expect(screen.getByLabelText("Business Registration Number (RC number)")).toBeInTheDocument();
            expect(screen.getByLabelText("Tax Identification Number (TIN)")).toBeInTheDocument();
            expect(screen.getByRole("button", { name: /Continue/i })).toBeDisabled();
        });

        it("does not render when isOpen is false", () => {
            render(<DigitalBusinessValidationForm isOpen={false} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            expect(screen.queryByTestId("drawer")).not.toBeInTheDocument();
        });

        it("renders with all required props", () => {
            const onboarding = { businessRegistrationNumber: "*********", taxIdentificationNumber: "********-0001" };

            render(
                <DigitalBusinessValidationForm
                    isOpen={true}
                    onClose={mockOnClose}
                    handlePrev={mockHandlePrev}
                    onboarding={onboarding}
                />
            );

            expect(screen.getByTestId("drawer")).toBeInTheDocument();
        });
    });

    describe("Button Interactions", () => {
        it("calls handlePrev when Previous clicked", () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            fireEvent.click(screen.getByRole("button", { name: /Previous/i }));

            expect(mockHandlePrev).toHaveBeenCalledTimes(1);
        });

        it("enables Continue button when form is valid and touched", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: /Continue/i })).toBeEnabled();
            });
        });

        it("keeps Continue button disabled when form has errors", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "INVALID");
            await userEvent.type(tinInput, "INVALID");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: /Continue/i })).toBeDisabled();
            });
        });
    });

    describe("Form Validation", () => {
        it("shows validation errors when fields are blurred empty", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            await waitFor(() => {
                expect(screen.getByTestId("businessRegistrationNumber-error")).toHaveTextContent(
                    "Business Registration Number must be in the format *********"
                );
                expect(screen.getByTestId("taxIdentificationNumber-error")).toHaveTextContent("TIN is required");
            });

            expect(screen.getByRole("button", { name: /Continue/i })).toBeDisabled();
        });

        it("shows TIN format validation error", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(tinInput, "invalid-tin");
            fireEvent.blur(tinInput);

            await waitFor(() => {
                expect(screen.getByTestId("taxIdentificationNumber-error")).toHaveTextContent("TIN is required");
            });
        });

        it("shows RC format validation error", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");

            await userEvent.clear(rcInput);
            await userEvent.type(rcInput, "INVALID123");
            fireEvent.blur(rcInput);

            await waitFor(() => {
                expect(screen.getByTestId("businessRegistrationNumber-error")).toHaveTextContent(
                    "Business Registration Number must be in the format *********"
                );
            });
        });
    });

    describe("Input Formatting", () => {
        it("formats TIN as user types", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(tinInput, "********9012");

            await waitFor(() => {
                expect(tinInput).toHaveValue("********-9012");
            });
        });

        it("formats TIN with partial input", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(tinInput, "1234");

            await waitFor(() => {
                expect(tinInput).toHaveValue("1234");
            });
        });

        it("formats RC number to always start with 'RC' and keeps only 7 digits", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");

            await userEvent.type(rcInput, "********9");

            await waitFor(() => {
                expect(rcInput).toHaveValue("*********");
            });
        });

        it("handles RC input without initial RC prefix", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");

            await userEvent.clear(rcInput);
            await userEvent.type(rcInput, "1234567");

            await waitFor(() => {
                expect(rcInput).toHaveValue("*********");
            });
        });

        it("handles empty RC input", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");

            await userEvent.clear(rcInput);

            await waitFor(() => {
                expect(rcInput).toHaveValue("RC");
            });
        });

        it("handles TIN formatting with empty input", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.clear(tinInput);

            await waitFor(() => {
                expect(tinInput).toHaveValue("");
            });
        });

        it("limits TIN to 12 digits", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(tinInput, "********9012345");

            await waitFor(() => {
                expect(tinInput).toHaveValue("********-9012");
            });
        });

        it("handles non-numeric characters in TIN", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(tinInput, "12ab34cd56ef78gh90");

            await waitFor(() => {
                expect(tinInput).toHaveValue("********-90");
            });
        });

        it("handles non-numeric characters in RC", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");

            await userEvent.type(rcInput, "abc123def456ghi");

            await waitFor(() => {
                expect(rcInput).toHaveValue("RC123456");
            });
        });
    });

    describe("Keyboard Event Handling", () => {
        it("prevents deleting the RC prefix with backspace", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            await userEvent.type(rcInput, "1234567");

            rcInput.setSelectionRange(0, 0);
            fireEvent.keyDown(rcInput, { key: "Backspace", code: "Backspace" });

            expect(rcInput).toHaveValue("*********");
        });

        it("prevents deleting the RC prefix with delete key", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            await userEvent.type(rcInput, "1234567");

            rcInput.setSelectionRange(1, 1);
            fireEvent.keyDown(rcInput, { key: "Delete", code: "Delete" });

            expect(rcInput).toHaveValue("*********");
        });

        it("prevents moving cursor left past RC prefix", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            await userEvent.type(rcInput, "1234567");

            rcInput.setSelectionRange(1, 1);
            fireEvent.keyDown(rcInput, { key: "ArrowLeft", code: "ArrowLeft" });

            expect(rcInput).toHaveValue("*********");
        });

        it("allows other keys when cursor is after RC prefix", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            await userEvent.type(rcInput, "1234567");

            rcInput.setSelectionRange(5, 5);
            fireEvent.keyDown(rcInput, { key: "Backspace", code: "Backspace" });

            // Should allow normal editing after RC prefix
            expect(rcInput).toHaveValue("*********");
        });
    });

    describe("Form Submission - Success Cases", () => {
        it("submits successfully when both validations pass", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () =>
                    Promise.resolve({
                        tinSuccess: true,
                        rcsuccess: true,
                        businessName: "Test Business",
                    }),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(expect.objectContaining({ unwrap: expect.any(Function) }));
            });

            expect(sendFeedback).toHaveBeenCalledWith("TIN and RC Validated Successfully", "success");
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "SET_IDENTIFIERS",
                    payload: {
                        businessRegistrationNumber: "*********",
                        taxIdentificationNumber: "********-0001",
                        businessName: "Test Business",
                    },
                })
            );
            expect(mockDispatch).toHaveBeenCalledWith(expect.objectContaining({ type: "OPEN_INFO_DIALOG" }));

            expect(rcInput).toHaveValue("RC");
            expect(tinInput).toHaveValue("");
        });

        it("shows loading state during submission", async () => {
            let resolveSubmission;
            businessValidationForm.mockImplementation(() => ({
                unwrap: () =>
                    new Promise((resolve) => {
                        resolveSubmission = resolve;
                    }),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(continueBtn).toHaveAttribute("data-loading", "true");
            });

            resolveSubmission({ tinSuccess: true, rcsuccess: true, businessName: "Test" });

            await waitFor(() => {
                expect(continueBtn).toHaveAttribute("data-loading", "false");
            });
        });
    });

    describe("Form Submission - Validation Failures", () => {
        it("handles both TIN and RC validation failure", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () => Promise.resolve({ tinSuccess: false, rcsuccess: false }),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(
                    "TIN and RC validation failed. Please check your inputs."
                );
            });
        });

        it("handles TIN validation failure only", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () => Promise.resolve({ tinSuccess: false, rcsuccess: true }),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(
                    "RC validated successfully, but TIN validation failed. Please check your inputs."
                );
            });
        });

        it("handles RC validation failure only", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () => Promise.resolve({ tinSuccess: true, rcsuccess: false }),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(
                    "TIN validated successfully, but RC validation failed. Please check your inputs."
                );
            });
        });
    });

    describe("Form Submission - Error Handling", () => {
        it("handles network/dispatch error with Error object", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () => Promise.reject(new Error("Network error")),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith("Network error");
            });
        });

        it("handles network/dispatch error with string", async () => {
            businessValidationForm.mockImplementation(() => ({
                unwrap: () => Promise.reject("String error"),
            }));

            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            fireEvent.blur(rcInput);
            fireEvent.blur(tinInput);

            const continueBtn = screen.getByRole("button", { name: /Continue/i });
            await waitFor(() => expect(continueBtn).toBeEnabled());

            fireEvent.click(continueBtn);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith("Submission failed. Please try again.");
            });
        });
    });

    describe("Exit Confirmation Dialog", () => {
        it("shows exit confirmation when close button is clicked", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const closeButton = screen.getByTestId("drawer-close");
            fireEvent.click(closeButton);

            await waitFor(() => {
                expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
            });
        });

        it("cancels exit confirmation", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            const closeButton = screen.getByTestId("drawer-close");
            fireEvent.click(closeButton);

            await waitFor(() => {
                expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
            });

            const cancelButton = screen.getByTestId("cancel-exit");
            fireEvent.click(cancelButton);

            await waitFor(() => {
                expect(screen.queryByTestId("exit-confirmation")).not.toBeInTheDocument();
            });
        });

        it("confirms exit and resets form", async () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} handlePrev={mockHandlePrev} />);

            // Fill form first
            const rcInput = screen.getByLabelText("Business Registration Number (RC number)");
            const tinInput = screen.getByLabelText("Tax Identification Number (TIN)");

            await userEvent.type(rcInput, "*********");
            await userEvent.type(tinInput, "********-0001");

            const closeButton = screen.getByTestId("drawer-close");
            fireEvent.click(closeButton);

            await waitFor(() => {
                expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
            });

            const confirmButton = screen.getByTestId("confirm-exit");
            fireEvent.click(confirmButton);

            await waitFor(() => {
                expect(mockOnClose).toHaveBeenCalled();
                expect(rcInput).toHaveValue("RC");
                expect(tinInput).toHaveValue("");
            });
        });
    });

    describe("Props and Edge Cases", () => {
        it("handles missing handlePrev prop", () => {
            render(<DigitalBusinessValidationForm isOpen={true} onClose={mockOnClose} />);

            expect(screen.getByRole("button", { name: /Previous/i })).toBeInTheDocument();
        });

        it("handles onboarding prop with null value", () => {
            render(
                <DigitalBusinessValidationForm
                    isOpen={true}
                    onClose={mockOnClose}
                    handlePrev={mockHandlePrev}
                    onboarding={null}
                />
            );

            expect(screen.getByLabelText("Business Registration Number (RC number)")).toHaveValue("RC");
            expect(screen.getByLabelText("Tax Identification Number (TIN)")).toHaveValue("");
        });

        it("handles onboarding prop with undefined value", () => {
            render(
                <DigitalBusinessValidationForm
                    isOpen={true}
                    onClose={mockOnClose}
                    handlePrev={mockHandlePrev}
                    onboarding={undefined}
                />
            );

            expect(screen.getByLabelText("Business Registration Number (RC number)")).toHaveValue("RC");
            expect(screen.getByLabelText("Tax Identification Number (TIN)")).toHaveValue("");
        });
    });
});
