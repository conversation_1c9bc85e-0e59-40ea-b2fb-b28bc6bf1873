/**
 * Test Suite for Enhanced Search Utilities - Outgoing Payments
 *
 * Purpose: This test file validates the enhanced search functionality for outgoing payment transactions.
 * It ensures that search works across all visible fields including the complete counterparty information
 * (both main name and secondary details like bank and account number).
 *
 * Test Coverage:
 * - Original search fields (counterparty, narration, status, amount, date)
 * - Enhanced counterparty 2nd line fields (bank name, account number)
 * - Bulk transfer search (recipient count text)
 * - Edge cases (null/undefined values, empty strings)
 * - Case-insensitive and substring matching
 *
 * Dependencies:
 * - Jest testing framework
 * - ITransfer interface from outgoing module types
 * - advancedSearchMatch function from searchUtils
 */

import { advancedSearchMatch } from "@/components/page-components/dashboard/outgoing/utils/searchUtils";

// Mock data for testing single transfers
const mockSingleTransfer = {
    id: "1",
    paymentRequestId: 123,
    counterparty: "John Doe Corp",
    narration: "Payment for services",
    status: "Successful",
    amount: 1000,
    date: "2024-01-15",
    accountNumber: "**********",
    transferScheduledId: 123,
    bank: "First Bank Nigeria",
    transferType: "Intra-bank",
    totalTransfers: 1, // Single transfer
    bulkTransactionRef: null, // Single transfer - no bulk reference
};

// Mock data for testing bulk transfers
const mockBulkTransfer = {
    id: "2",
    paymentRequestId: 456,
    counterparty: "ACME Industries",
    narration: "Monthly salary payments",
    status: "Pending",
    amount: 50000,
    date: "2024-01-20",
    accountNumber: "**********",
    transferScheduledId: 456,
    bank: "Access Bank",
    transferType: "Inter-bank",
    totalTransfers: 25, // Bulk transfer with 25 recipients
    bulkTransactionRef: "bulk-ref-456", // Bulk transfer reference
};

// Mock data for testing edge cases
const mockTransferWithNulls = {
    id: "3",
    paymentRequestId: 789,
    counterparty: "Tech Solutions Ltd",
    narration: "Software license renewal",
    status: "Failed",
    amount: 2500,
    date: "2024-01-25",
    accountNumber: "**********",
    transferScheduledId: 789,
    bank: null, // Null bank
    transferType: "International",
    totalTransfers: undefined, // Undefined totalTransfers
    bulkTransactionRef: undefined, // Undefined bulk reference
};

describe("Enhanced Search Functionality Tests", () => {
    describe("advancedSearchMatch - Original Fields", () => {
        it("should return true when query is empty or whitespace", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "   ")).toBe(true);
        });

        it("should return true when transfer is null/undefined", () => {
            expect(advancedSearchMatch(null, "test")).toBe(true);
            expect(advancedSearchMatch(undefined, "test")).toBe(true);
        });

        it("should search counterparty field (case-insensitive)", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "john")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "JOHN")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "doe")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "Corp")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "xyz")).toBe(false);
        });

        it("should search narration field (case-insensitive)", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "payment")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "PAYMENT")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "services")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "invoice")).toBe(false);
        });

        it("should search status field (case-insensitive)", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "successful")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "SUCCESSFUL")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "pending")).toBe(true);
            expect(advancedSearchMatch(mockTransferWithNulls, "failed")).toBe(true);
        });

        it("should search amount field", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "1000")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "50000")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "100")).toBe(true); // Partial match
            expect(advancedSearchMatch(mockSingleTransfer, "9999")).toBe(false);
        });

        it("should search date field in multiple formats", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "2024")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "January")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "Jan")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "15")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "2023")).toBe(false);
        });
    });

    describe("advancedSearchMatch - Enhanced Bank Name Search", () => {
        it("should search bank name field (case-insensitive)", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "first")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "FIRST")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "bank")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "nigeria")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "first bank")).toBe(true);
        });

        it("should search different bank names", () => {
            expect(advancedSearchMatch(mockBulkTransfer, "access")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "ACCESS")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "access bank")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "first")).toBe(false); // Different bank
        });

        it("should handle null bank names gracefully", () => {
            expect(advancedSearchMatch(mockTransferWithNulls, "bank")).toBe(false); // Null bank
            expect(() => advancedSearchMatch(mockTransferWithNulls, "test")).not.toThrow();
        });
    });

    describe("advancedSearchMatch - Enhanced Account Number Search", () => {
        it("should search account number field", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "**********")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "1234")).toBe(true); // Partial match
            expect(advancedSearchMatch(mockSingleTransfer, "7890")).toBe(true); // Partial match
            expect(advancedSearchMatch(mockSingleTransfer, "567")).toBe(true); // Middle digits
        });

        it("should search different account numbers", () => {
            expect(advancedSearchMatch(mockBulkTransfer, "**********")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "0987")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "4321")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "1234")).toBe(false); // Different account
        });
    });

    describe("advancedSearchMatch - Enhanced Bulk Transfer Search", () => {
        it("should search bulk transfer recipient count text", () => {
            expect(advancedSearchMatch(mockBulkTransfer, "recipients")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "RECIPIENTS")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "25 recipients")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "25")).toBe(true); // Number only
        });

        it("should not match recipients text for single transfers", () => {
            expect(advancedSearchMatch(mockSingleTransfer, "recipients")).toBe(false);
        });

        it("should handle undefined totalTransfers gracefully", () => {
            expect(advancedSearchMatch(mockTransferWithNulls, "recipients")).toBe(false);
            expect(() => advancedSearchMatch(mockTransferWithNulls, "recipients")).not.toThrow();
        });
    });

    describe("advancedSearchMatch - Complete 2nd Line Search", () => {
        it("should search complete 2nd line text for single transfers", () => {
            // Single transfer 2nd line: "First Bank Nigeria, **********"
            expect(advancedSearchMatch(mockSingleTransfer, "first bank nigeria, **********")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "first bank nigeria,")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, ", **********")).toBe(true);
        });

        it("should search complete 2nd line text for bulk transfers", () => {
            // Bulk transfer 2nd line: "25 recipients"
            expect(advancedSearchMatch(mockBulkTransfer, "25 recipients")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "25")).toBe(true);
            expect(advancedSearchMatch(mockBulkTransfer, "recipients")).toBe(true);
        });
    });

    describe("advancedSearchMatch - Backward Compatibility", () => {
        it("should maintain backward compatibility with existing search behavior", () => {
            // These tests ensure our enhanced search doesn't break existing functionality
            expect(advancedSearchMatch(mockSingleTransfer, "successful")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "1000")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "payment for services")).toBe(true);
            expect(advancedSearchMatch(mockSingleTransfer, "nonexistent")).toBe(false);
        });
    });

    describe("advancedSearchMatch - Edge Cases for Complete Coverage", () => {
        it("should handle invalid date formats gracefully", () => {
            const transferWithInvalidDate = {
                ...mockSingleTransfer,
                date: "invalid-date-format",
            };

            // Should not throw errors and should still search other fields
            expect(() => advancedSearchMatch(transferWithInvalidDate, "john")).not.toThrow();
            expect(advancedSearchMatch(transferWithInvalidDate, "john")).toBe(true); // Should find in counterparty
            expect(advancedSearchMatch(transferWithInvalidDate, "invalid-date-format")).toBe(true); // Should find in date field
        });

        it("should handle date parsing errors gracefully", () => {
            const transferWithBadDate = {
                ...mockSingleTransfer,
                date: "not-a-date-at-all",
            };

            // Should handle date parsing errors without crashing
            expect(() => advancedSearchMatch(transferWithBadDate, "test")).not.toThrow();
            expect(advancedSearchMatch(transferWithBadDate, "not-a-date-at-all")).toBe(true);
        });

        it("should handle date constructor exceptions gracefully", () => {
            // Create a date string that will cause Date constructor to throw
            const transferWithExceptionDate = {
                ...mockSingleTransfer,
                date: "2024-13-45", // Invalid date that might cause exceptions in some environments
            };

            // Should handle any date parsing exceptions without crashing
            expect(() => advancedSearchMatch(transferWithExceptionDate, "test")).not.toThrow();
            expect(advancedSearchMatch(transferWithExceptionDate, "john")).toBe(true); // Should find in other fields
        });

        it("should handle totalTransfers edge cases for bulk transfer formatting", () => {
            // Test with totalTransfers = 0 (should be treated as single transfer)
            const transferWithZeroTotal = {
                ...mockSingleTransfer,
                totalTransfers: 0,
            };
            expect(advancedSearchMatch(transferWithZeroTotal, "recipients")).toBe(false);
            expect(advancedSearchMatch(transferWithZeroTotal, "first bank nigeria, **********")).toBe(true);

            // Test with totalTransfers = 1 (should be treated as single transfer)
            const transferWithOneTotal = {
                ...mockSingleTransfer,
                totalTransfers: 1,
            };
            expect(advancedSearchMatch(transferWithOneTotal, "recipients")).toBe(false);
            expect(advancedSearchMatch(transferWithOneTotal, "first bank nigeria, **********")).toBe(true);

            // Test with totalTransfers = 2 (should be treated as bulk transfer)
            const transferWithTwoTotal = {
                ...mockSingleTransfer,
                totalTransfers: 2,
                bulkTransactionRef: "bulk-ref-test", // Add bulkTransactionRef to make it a bulk transfer
            };
            expect(advancedSearchMatch(transferWithTwoTotal, "recipients")).toBe(true);
            expect(advancedSearchMatch(transferWithTwoTotal, "2 recipients")).toBe(true);
        });

        it("should handle null date gracefully", () => {
            const transferWithNullDate = {
                ...mockSingleTransfer,
                date: null,
            };

            expect(() => advancedSearchMatch(transferWithNullDate, "test")).not.toThrow();
            expect(advancedSearchMatch(transferWithNullDate, "john")).toBe(true); // Should find in other fields
        });

        it("should handle completely empty transfer gracefully", () => {
            const emptyTransfer = {
                id: "",
                paymentRequestId: 0,
                counterparty: "",
                narration: "",
                status: "",
                amount: 0,
                date: "",
                accountNumber: "",
                transferScheduledId: 0,
                bank: "",
                transferType: "Intra-bank",
                totalTransfers: 0,
            };

            expect(() => advancedSearchMatch(emptyTransfer, "test")).not.toThrow();
            expect(advancedSearchMatch(emptyTransfer, "test")).toBe(false);
            expect(advancedSearchMatch(emptyTransfer, "")).toBe(true); // Empty query should return true
        });
    });
});
