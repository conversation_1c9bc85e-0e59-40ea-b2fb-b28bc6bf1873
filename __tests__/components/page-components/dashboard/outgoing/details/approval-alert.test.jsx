import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ApprovalAlert from "@/components/page-components/dashboard/outgoing/details/approval-alert";

// Mock the Alert component
jest.mock("@/components/common/alert", () => ({
    Alert: ({
        variant,
        heading,
        supportingText,
        children,
        className,
        secondaryActionText,
        onSecondaryAction,
        showSecondaryActionRightIcon,
        showIcon,
        showCloseButton,
    }) => (
        <div
            data-testid="alert"
            data-variant={variant}
            className={className}
            data-show-icon={showIcon}
            data-show-close-button={showCloseButton}
            data-show-secondary-action-right-icon={showSecondaryActionRightIcon}
        >
            <h3 data-testid="alert-heading">{heading}</h3>
            <p data-testid="alert-text">{supportingText}</p>
            {children && <div data-testid="alert-children">{children}</div>}
            {secondaryActionText && (
                <button
                    type="button"
                    data-testid="approval-alert-button"
                    onClick={onSecondaryAction}
                    data-variant="secondary"
                    aria-label={`${secondaryActionText} for this transfer`}
                >
                    {secondaryActionText}
                </button>
            )}
        </div>
    ),
}));

describe("ApprovalAlert Component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("renders with approval count when approvals are granted", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={2} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        // Check that the alert is rendered
        expect(screen.getByTestId("alert")).toBeInTheDocument();
        expect(screen.getByTestId("alert")).toHaveAttribute("data-variant", "neutral");

        // Check the approval count heading
        expect(screen.getByTestId("alert-heading")).toHaveTextContent("2 of 3 approvals granted");

        // Check the supporting text
        expect(screen.getByTestId("alert-text")).toHaveTextContent(
            "Your payment will be sent once it meets the above requirement"
        );

        // Check that the "See approvers" button is rendered
        expect(screen.getByTestId("approval-alert-button")).toBeInTheDocument();
        expect(screen.getByTestId("approval-alert-button")).toHaveTextContent("See approvers");
    });

    test("renders with different approval counts", () => {
        const mockOnSeeApprovers = jest.fn();

        // Test 1 of 5 approvals
        const { rerender } = render(
            <ApprovalAlert approvalCount={1} totalApprovals={5} onSeeApprovers={mockOnSeeApprovers} />
        );

        expect(screen.getByTestId("alert-heading")).toHaveTextContent("1 of 5 approvals granted");

        // Test 0 of 2 approvals
        rerender(<ApprovalAlert approvalCount={0} totalApprovals={2} onSeeApprovers={mockOnSeeApprovers} />);

        expect(screen.getByTestId("alert-heading")).toHaveTextContent("0 of 2 approvals granted");

        // Test 3 of 3 approvals (all approved)
        rerender(<ApprovalAlert approvalCount={3} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        expect(screen.getByTestId("alert-heading")).toHaveTextContent("3 of 3 approvals granted");
    });

    test("calls onSeeApprovers when button is clicked", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={2} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        const button = screen.getByTestId("approval-alert-button");
        fireEvent.click(button);

        expect(mockOnSeeApprovers).toHaveBeenCalledTimes(1);
    });

    test("button has correct styling and variant", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={1} totalApprovals={2} onSeeApprovers={mockOnSeeApprovers} />);

        const button = screen.getByTestId("approval-alert-button");
        expect(button).toHaveAttribute("data-variant", "secondary");
        expect(button).toHaveTextContent("See approvers");

        // Check that the alert has the correct styling class for the button
        const alert = screen.getByTestId("alert");
        expect(alert).toHaveClass(
            "[&_button]:!text-[#5C068C]",
            "[&_button]:font-medium",
            "[&_svg_path]:!stroke-[#5C068C]"
        );
    });

    test("renders with proper alert styling classes", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={2} totalApprovals={4} onSeeApprovers={mockOnSeeApprovers} />);

        const alert = screen.getByTestId("alert");
        expect(alert).toHaveAttribute("data-variant", "neutral");
        expect(alert).toHaveAttribute("data-show-icon", "false");
        expect(alert).toHaveAttribute("data-show-close-button", "false");
        expect(alert).toHaveAttribute("data-show-secondary-action-right-icon", "true");
        expect(alert).toHaveClass(
            "[&_button]:!text-[#5C068C]",
            "[&_button]:font-medium",
            "[&_svg_path]:!stroke-[#5C068C]"
        );
    });

    test("handles edge case with zero total approvals", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={0} totalApprovals={0} onSeeApprovers={mockOnSeeApprovers} />);

        expect(screen.getByTestId("alert-heading")).toHaveTextContent("0 of 0 approvals granted");
        expect(screen.getByTestId("approval-alert-button")).toBeInTheDocument();
    });

    test("handles edge case with approval count greater than total", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={5} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        expect(screen.getByTestId("alert-heading")).toHaveTextContent("5 of 3 approvals granted");
    });

    test("renders without onSeeApprovers callback (button still works)", () => {
        render(<ApprovalAlert approvalCount={1} totalApprovals={2} />);

        const button = screen.getByTestId("approval-alert-button");
        expect(button).toBeInTheDocument();

        // Should not throw error when clicked without callback
        fireEvent.click(button);
        expect(button).toBeInTheDocument(); // Still rendered after click
    });

    test("conditional rendering based on transfer status", () => {
        const mockOnSeeApprovers = jest.fn();

        // Test with pending status (should render)
        const { rerender } = render(
            <ApprovalAlert approvalCount={2} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} status="pending" />
        );

        expect(screen.getByTestId("alert")).toBeInTheDocument();

        // Test with completed status (should not render if we implement conditional logic)
        rerender(
            <ApprovalAlert
                approvalCount={3}
                totalApprovals={3}
                onSeeApprovers={mockOnSeeApprovers}
                status="completed"
            />
        );

        // For now, it should still render regardless of status
        expect(screen.getByTestId("alert")).toBeInTheDocument();
    });

    test("supports custom className prop", () => {
        const mockOnSeeApprovers = jest.fn();

        render(
            <ApprovalAlert
                approvalCount={1}
                totalApprovals={2}
                onSeeApprovers={mockOnSeeApprovers}
                className="custom-class"
            />
        );

        const alert = screen.getByTestId("alert");
        expect(alert).toHaveClass("custom-class");
    });

    test("displays proper accessibility attributes", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={2} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        const button = screen.getByTestId("approval-alert-button");
        expect(button).toHaveAttribute("type", "button");

        // Check that button text is descriptive for screen readers
        expect(button).toHaveTextContent("See approvers");
    });

    test("handles missing props gracefully", () => {
        // Test with minimal props
        render(<ApprovalAlert />);

        // Should render with default values (0 approvals)
        expect(screen.getByTestId("alert")).toBeInTheDocument();
        expect(screen.getByTestId("alert-heading")).toHaveTextContent("0 of 0 approvals granted");
    });

    test("button click handler is properly bound", () => {
        const mockOnSeeApprovers = jest.fn();

        render(<ApprovalAlert approvalCount={1} totalApprovals={3} onSeeApprovers={mockOnSeeApprovers} />);

        const button = screen.getByTestId("approval-alert-button");

        // Click multiple times to ensure handler is properly bound
        fireEvent.click(button);
        fireEvent.click(button);
        fireEvent.click(button);

        expect(mockOnSeeApprovers).toHaveBeenCalledTimes(3);
    });

    test("renders with all approval count combinations for comprehensive coverage", () => {
        const mockOnSeeApprovers = jest.fn();

        const testCases = [
            { approvalCount: 0, totalApprovals: 1, expected: "0 of 1 approvals granted" },
            { approvalCount: 1, totalApprovals: 1, expected: "1 of 1 approvals granted" },
            { approvalCount: 2, totalApprovals: 5, expected: "2 of 5 approvals granted" },
            { approvalCount: 10, totalApprovals: 15, expected: "10 of 15 approvals granted" },
        ];

        testCases.forEach(({ approvalCount, totalApprovals, expected }) => {
            const { unmount } = render(
                <ApprovalAlert
                    approvalCount={approvalCount}
                    totalApprovals={totalApprovals}
                    onSeeApprovers={mockOnSeeApprovers}
                />
            );

            expect(screen.getByTestId("alert-heading")).toHaveTextContent(expected);
            unmount();
        });
    });
});
