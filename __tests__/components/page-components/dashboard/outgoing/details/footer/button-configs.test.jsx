import {
    getButton<PERSON><PERSON><PERSON><PERSON><PERSON>,
    createButtonGroup,
} from "@/components/page-components/dashboard/outgoing/details/footer/button-configs";
import * as handlers from "@/components/page-components/dashboard/outgoing/details/footer/handlers";

// Mock the handlers module
jest.mock("@/components/page-components/dashboard/outgoing/details/footer/handlers", () => ({
    createStatusUpdateHandler: jest.fn(() => jest.fn()),
    createDownloadReceiptHandler: jest.fn(() => jest.fn()),
}));

// Mock the statusUtils module
jest.mock("@/components/page-components/dashboard/outgoing/utils/statusUtils", () => ({
    isBulkTransfer: jest.fn((transfer) => transfer.type === "bulkTransfer"),
}));

describe("Button Configs", () => {
    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    // Common test props
    const mockDispatch = jest.fn();
    const mockHandleStatusUpdate = jest.fn();
    const mockHandleOpenReport = jest.fn();

    // Transfer objects for different cases
    const singleTransfer = {
        id: "single-1",
        status: "Pending",
        type: "singleTransfer",
        transferScheduledId: "1234",
    };

    const bulkTransfer = {
        id: "bulk-1",
        status: "Pending",
        type: "bulkTransfer",
        transferScheduledId: "5678",
    };

    const completedTransfer = {
        id: "completed-1",
        status: "Completed",
        type: "singleTransfer",
        transferScheduledId: "9012",
    };

    const failedTransfer = {
        id: "failed-1",
        status: "Failed",
        type: "singleTransfer",
        transferScheduledId: "3456",
    };

    const processingTransfer = {
        id: "processing-1",
        status: "Processing",
        type: "singleTransfer",
        transferScheduledId: "7890",
    };

    const successfulTransfer = {
        id: "successful-1",
        status: "Successful",
        type: "singleTransfer",
        transferScheduledId: "1122",
    };

    const awaitingApprovalTransfer = {
        id: "awaiting-1",
        status: "Awaiting Approval",
        type: "singleTransfer",
        transferScheduledId: "3344",
    };

    const rejectedApprovalTransfer = {
        id: "rejected-1",
        status: "Rejected Approval",
        type: "singleTransfer",
        transferScheduledId: "5566",
    };

    describe("getButtonRendererKey", () => {
        test("should return 'failed' for failed transfers", () => {
            const props = {
                transfer: failedTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("failed");
        });

        test("should return 'processing' for processing transfers", () => {
            const props = {
                transfer: processingTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("processing");
        });

        test("should return 'rejectedApproval' for rejected approval transfers", () => {
            const props = {
                transfer: rejectedApprovalTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("rejectedApproval");
        });

        test("should return 'awaitingApproval' for awaiting approval transfers", () => {
            const props = {
                transfer: awaitingApprovalTransfer,
                activeTab: "scheduled",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("awaitingApproval");
        });

        test("should return 'successful' for successful transfers", () => {
            const props = {
                transfer: successfulTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("successful");
        });

        test("should return 'pending' for pending transfers", () => {
            const props = {
                transfer: singleTransfer, // Pending status
                activeTab: "scheduled",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("pending");
        });

        test("should return 'default' for unsupported statuses", () => {
            const props = {
                transfer: { ...singleTransfer, status: "Unknown" },
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            expect(getButtonRendererKey(props)).toBe("default");
        });
    });

    describe("createButtonGroup", () => {
        test("should create buttons for successful transfers with standard layout", () => {
            const props = {
                transfer: successfulTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            const buttonGroup = createButtonGroup("successful", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(1);
            expect(buttonGroup.buttons[0].text).toBe("Download Receipt");
            expect(buttonGroup.buttons[0].variant).toBe("primary");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);
            expect(buttonGroup.buttons[0].ariaLabel).toBe("Download transaction receipt");

            // Test onClick handlers
            buttonGroup.buttons[0].onClick();
            expect(handlers.createDownloadReceiptHandler).toHaveBeenCalledWith(successfulTransfer, mockDispatch);
        });

        test("should create buttons for successful transfers with loading state", () => {
            const props = {
                transfer: successfulTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            const buttonGroup = createButtonGroup("successful", props, mockDispatch, true);

            expect(buttonGroup.buttons[0].loading).toBe(true);
            expect(buttonGroup.buttons[0].disabled).toBe(true);
        });

        test("should create buttons for awaiting approval transfers in scheduled tab", () => {
            const mockStatusUpdateHandlerFn = jest.fn();
            handlers.createStatusUpdateHandler.mockReturnValue(mockStatusUpdateHandlerFn);

            const props = {
                transfer: awaitingApprovalTransfer,
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("awaitingApproval", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(1);
            expect(buttonGroup.buttons[0].text).toBe("Cancel Transaction");
            expect(buttonGroup.buttons[0].variant).toBe("outline");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);

            // Test onClick handlers
            buttonGroup.buttons[0].onClick();
            expect(handlers.createStatusUpdateHandler).toHaveBeenCalledWith("Cancel", mockHandleStatusUpdate);
        });

        test("should create buttons for awaiting approval transfers in recurring tab", () => {
            const props = {
                transfer: awaitingApprovalTransfer,
                activeTab: "recurring",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("awaitingApproval", props, mockDispatch, false);

            expect(buttonGroup.buttons[0].text).toBe("Cancel mandate");
        });

        test("should not create buttons for awaiting approval transfers in instant tab", () => {
            const props = {
                transfer: awaitingApprovalTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("awaitingApproval", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should handle bulk transfers for awaiting approval", () => {
            const mockStatusUpdateHandlerFn = jest.fn();
            handlers.createStatusUpdateHandler.mockReturnValue(mockStatusUpdateHandlerFn);

            const props = {
                transfer: { ...awaitingApprovalTransfer, type: "bulkTransfer" },
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                handleOpenReport: mockHandleOpenReport,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("awaitingApproval", props, mockDispatch, false);

            expect(buttonGroup.buttons[0].ariaLabel).toBe("Cancel Transaction");
            expect(buttonGroup.buttons[0].variant).toBe("outline");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);

            // Test onClick handlers
            buttonGroup.buttons[0].onClick();
            expect(handlers.createStatusUpdateHandler).toHaveBeenCalledWith("Cancel", mockHandleStatusUpdate);
        });

        test("should create buttons for pending transfers in scheduled tab", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("pending", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(1);
            expect(buttonGroup.buttons[0].text).toBe("Cancel Transaction");
            expect(buttonGroup.buttons[0].variant).toBe("outline");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);
        });

        test("should create buttons for pending transfers in recurring tab", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "recurring",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("pending", props, mockDispatch, false);

            expect(buttonGroup.buttons[0].text).toBe("Cancel mandate");
        });

        test("should not create buttons for pending transfers in instant tab", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("pending", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should create empty buttons for rejected approval transfers", () => {
            const props = {
                transfer: rejectedApprovalTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            const buttonGroup = createButtonGroup("rejectedApproval", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should create empty buttons for processing transfers", () => {
            const props = {
                transfer: processingTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            const buttonGroup = createButtonGroup("processing", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should create empty buttons for failed transfers", () => {
            const props = {
                transfer: failedTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
            };

            const buttonGroup = createButtonGroup("failed", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should create empty buttons for default case in instant tab", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "instant",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("default", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(0);
        });

        test("should create pending buttons for default case in non-instant tab", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("default", props, mockDispatch, false);

            expect(buttonGroup.layout).toBe("standard");
            expect(buttonGroup.buttons.length).toBe(1);
            expect(buttonGroup.buttons[0].text).toBe("Cancel Transaction");
            expect(buttonGroup.buttons[0].variant).toBe("outline");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);
        });

        test("should handle disabled state for buttons", () => {
            const props = {
                transfer: singleTransfer,
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                isUpdatingStatus: true, // This should disable the button
            };

            const buttonGroup = createButtonGroup("pending", props, mockDispatch, false);

            expect(buttonGroup.buttons[0].disabled).toBe(true);
        });

        test("should handle bulk transfers for pending state", () => {
            const mockStatusUpdateHandlerFn = jest.fn();
            handlers.createStatusUpdateHandler.mockReturnValue(mockStatusUpdateHandlerFn);

            const props = {
                transfer: { ...singleTransfer, type: "bulkTransfer" },
                activeTab: "scheduled",
                activeDetailTab: "Details",
                handleStatusUpdate: mockHandleStatusUpdate,
                handleOpenReport: mockHandleOpenReport,
                isUpdatingStatus: false,
            };

            const buttonGroup = createButtonGroup("pending", props, mockDispatch, false);

            expect(buttonGroup.buttons[0].ariaLabel).toBe("Cancel Transaction");
            expect(buttonGroup.buttons[0].variant).toBe("outline");
            expect(buttonGroup.buttons[0].fullWidth).toBe(false);

            // Test onClick handlers
            buttonGroup.buttons[0].onClick();
            expect(handlers.createStatusUpdateHandler).toHaveBeenCalledWith("Cancel", mockHandleStatusUpdate);
        });
    });
});
