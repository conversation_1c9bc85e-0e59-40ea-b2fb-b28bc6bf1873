import { sendCatchFeedback } from "@/functions/feedback";
import { downloadReceipt } from "@/redux/actions/transferActions";

// Mock dependencies BEFORE importing the actual handlers
jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn().mockReturnValue({ type: "DOWNLOAD_RECEIPT" }),
}));

// Import handlers AFTER setting up mocks
import {
    createStatusUpdateHandler,
    createDownloadReceiptHandler,
} from "@/components/page-components/dashboard/outgoing/details/footer/handlers";

describe("Footer Action Handlers", () => {
    // Sample transfer data for testing
    const mockTransfer = {
        id: "transfer-123",
        paymentRequestId: "request-123",
        status: "PENDING",
        transferScheduledId: "transaction-123",
    };

    // Mock dispatch function
    const mockDispatch = jest.fn();

    // Mock status update handler
    const mockStatusUpdateHandler = jest.fn();

    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
    });

    describe("createStatusUpdateHandler", () => {
        it("shows error when action is not Cancel", () => {
            // Setup
            const actionName = "Pause";

            // Create and execute the handler
            const handler = createStatusUpdateHandler(actionName, mockStatusUpdateHandler);
            handler();

            // Verify behavior
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Pause operation is not supported",
                })
            );
            expect(mockStatusUpdateHandler).not.toHaveBeenCalled();
        });

        it("calls handleStatusUpdate with CANCELLED status when action is Cancel", () => {
            // Setup
            const actionName = "Cancel";

            // Create and execute the handler
            const handler = createStatusUpdateHandler(actionName, mockStatusUpdateHandler);
            handler();

            // Verify the status update handler was called with the correct status
            expect(mockStatusUpdateHandler).toHaveBeenCalledWith("CANCELLED");
            expect(sendCatchFeedback).not.toHaveBeenCalled();
        });

        it("does not call sendCatchFeedback when Cancel action has no callback", () => {
            // Setup
            const actionName = "Cancel";

            // Create and execute the handler without callback
            const handler = createStatusUpdateHandler(actionName);
            handler();

            expect(sendCatchFeedback).not.toHaveBeenCalled();
        });
    });

    describe("createDownloadReceiptHandler", () => {
        it("dispatches download receipt action when transaction ID exists", () => {
            // Create and execute the handler
            const handler = createDownloadReceiptHandler(mockTransfer, mockDispatch);
            handler();

            // Verify behavior
            expect(mockDispatch).toHaveBeenCalledWith(
                downloadReceipt({
                    transactionId: mockTransfer.transferScheduledId,
                    isUserInitiated: true,
                })
            );
        });

        it("shows error when no transaction ID exists", () => {
            // Create transfer without transaction ID
            const transferWithoutId = { ...mockTransfer, transferScheduledId: null };

            // Create and execute the handler
            const handler = createDownloadReceiptHandler(transferWithoutId, mockDispatch);
            handler();

            // Verify behavior
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });
    });
});
