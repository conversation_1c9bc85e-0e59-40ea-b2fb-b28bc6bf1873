import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FooterSection } from "@/components/page-components/dashboard/outgoing/details/footer/footer-section";

// Mock Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
    useAppSelector: jest.fn(() => ({ downloadReceiptLoading: false })),
}));

// Mock button-configs to avoid testing implementation details
jest.mock("@/components/page-components/dashboard/outgoing/details/footer/button-configs", () => ({
    getButtonRendererKey: jest.fn(),
    createButtonGroup: jest.fn(() => ({
        layout: "standard",
        buttons: [],
    })),
}));

// Mock the Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, variant, "data-testid": dataTestId, className, style, fullWidth }) => (
        <button
            onClick={onClick}
            disabled={disabled}
            data-variant={variant}
            data-testid={dataTestId}
            className={className}
            style={style}
            data-fullwidth={fullWidth ? "true" : "false"}
        >
            {children}
        </button>
    ),
}));

// Import the mocked functions after module mocking
import {
    getButtonRendererKey,
    createButtonGroup,
} from "@/components/page-components/dashboard/outgoing/details/footer/button-configs";

describe("FooterSection", () => {
    const mockStatusUpdateHandler = jest.fn();
    const mockOpenReportHandler = jest.fn();

    // Common props for most tests
    const baseProps = {
        transfer: {
            id: "123",
            status: "Pending",
            transferScheduledId: "tx123",
        },
        activeTab: "scheduled",
        activeDetailTab: "Details",
        handleOpenReport: mockOpenReportHandler,
        handleStatusUpdate: mockStatusUpdateHandler,
        isUpdatingStatus: false,
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Set default mock return values
        getButtonRendererKey.mockReturnValue("default");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [],
        });
    });

    it("should not render when transfer status is Cancelled", () => {
        // Arrange - The mocks are already set up in beforeEach
        // For cancelled transfers, the component should return null when no buttons are available
        const props = {
            ...baseProps,
            transfer: { ...baseProps.transfer, status: "Cancelled" },
        };

        // Act
        const { container } = render(<FooterSection {...props} />);

        // Assert
        expect(container.firstChild).toBeNull();
    });

    it("should not render when there are no buttons to display", () => {
        // Arrange - Use the default mocks which already return empty buttons array

        // Act
        const { container } = render(<FooterSection {...baseProps} />);

        // Assert
        expect(container.firstChild).toBeNull();
    });

    it("should not render when buttons array is null", () => {
        // Arrange
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: null,
        });

        // Act
        const { container } = render(<FooterSection {...baseProps} />);

        // Assert
        expect(container.firstChild).toBeNull();
    });

    it("should not render when buttons array is undefined", () => {
        // Arrange
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: undefined,
        });

        // Act
        const { container } = render(<FooterSection {...baseProps} />);

        // Assert
        expect(container.firstChild).toBeNull();
    });

    it("should render buttons with standard layout", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("pendingInstant");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Cancel",
                    variant: "outline",
                    ariaLabel: "Cancel transfer",
                    onClick: jest.fn(),
                    dataTestid: "cancel-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);

        // Assert
        expect(screen.getByText("Cancel")).toBeInTheDocument();
        expect(screen.queryByText("Report")).not.toBeInTheDocument();
        // Verify the container has the correct class for standard layout
        expect(screen.getByText("Cancel").closest("div")).toHaveClass(
            "flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-[16px] px-[24px]"
        );
    });

    it("should render buttons with fullWidth layout", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("successful");
        createButtonGroup.mockReturnValue({
            layout: "fullWidth",
            buttons: [
                {
                    text: "Download Receipt",
                    variant: "primary",
                    ariaLabel: "Download transaction receipt",
                    onClick: jest.fn(),
                    fullWidth: true,
                    dataTestid: "download-button",
                },
            ],
        });

        // Act
        const { container } = render(<FooterSection {...baseProps} />);

        // Assert
        expect(screen.queryByText("Report")).not.toBeInTheDocument();
        expect(screen.getByText("Download Receipt")).toBeInTheDocument();

        // Verify the container has the correct class for fullWidth layout
        const mainContainer = container.firstChild;
        expect(mainContainer).toHaveClass("border-t border-[#E3E5E8] p-[24px]");
    });

    it("should trigger button action when clicked", () => {
        // Arrange
        const mockButtonClickHandler = jest.fn();
        getButtonRendererKey.mockReturnValue("pendingInstant");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Cancel",
                    variant: "outline",
                    ariaLabel: "Cancel transfer",
                    onClick: mockButtonClickHandler,
                    dataTestid: "cancel-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        fireEvent.click(screen.getByText("Cancel"));

        // Assert
        expect(mockButtonClickHandler).toHaveBeenCalledTimes(1);
    });

    it("should render disabled buttons correctly", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("pendingInstant");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Cancel",
                    variant: "outline",
                    ariaLabel: "Cancel transfer",
                    onClick: jest.fn(),
                    disabled: true,
                    dataTestid: "cancel-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);

        // Assert
        expect(screen.getByText("Cancel")).toBeDisabled();
    });

    it("should apply full-width-button class and inline style to buttons when fullWidth is true", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("successful");
        createButtonGroup.mockReturnValue({
            layout: "fullWidth",
            buttons: [
                {
                    text: "Download Receipt",
                    variant: "outline",
                    ariaLabel: "Download transaction receipt",
                    onClick: jest.fn(),
                    fullWidth: true,
                    dataTestid: "download-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        const button = screen.getByText("Download Receipt");

        // Assert
        // Check if the button has the full-width-button class
        expect(button).toHaveClass("full-width-button");
        // Check if the button has the inline style for width and display
        expect(button).toHaveStyle("width: 100%; display: block");
    });

    it("should not apply full-width-button class or inline styles when fullWidth is false", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("pendingInstant");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Cancel",
                    variant: "outline",
                    ariaLabel: "Cancel transfer",
                    onClick: jest.fn(),
                    fullWidth: false, // Explicitly set to false
                    dataTestid: "cancel-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        const button = screen.getByText("Cancel");

        // Assert
        // Check that the button doesn't have the full-width-button class
        expect(button).not.toHaveClass("full-width-button");
        // Check that the button doesn't have the inline styles
        expect(button).not.toHaveStyle("width: 100%; display: block");
    });

    it("should conditionally apply styles based on fullWidth value in buttons array", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("custom");
        createButtonGroup.mockReturnValue({
            layout: "fullWidth",
            buttons: [
                {
                    text: "Full Width Button",
                    variant: "primary",
                    ariaLabel: "Full width button",
                    onClick: jest.fn(),
                    fullWidth: true,
                    dataTestid: "full-button",
                },
                {
                    text: "Normal Button",
                    variant: "outline",
                    ariaLabel: "Normal button",
                    onClick: jest.fn(),
                    fullWidth: false,
                    dataTestid: "normal-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        const fullWidthButton = screen.getByText("Full Width Button");
        const normalButton = screen.getByText("Normal Button");

        // Assert
        // Full width button should have the class and styles
        expect(fullWidthButton).toHaveClass("full-width-button");
        expect(fullWidthButton).toHaveStyle("width: 100%; display: block");

        // Normal button should not have the class or styles
        expect(normalButton).not.toHaveClass("full-width-button");
        expect(normalButton).not.toHaveStyle("width: 100%; display: block");
    });

    it("should render buttons with outlined variant correctly", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("awaitingApproval");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Cancel Transaction",
                    variant: "outlined",
                    ariaLabel: "Cancel transaction",
                    onClick: jest.fn(),
                    dataTestid: "cancel-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        const button = screen.getByText("Cancel Transaction");

        // Assert
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute("data-variant", "outlined");
    });

    it("should render loading state correctly for buttons", () => {
        // Arrange
        getButtonRendererKey.mockReturnValue("successful");
        createButtonGroup.mockReturnValue({
            layout: "standard",
            buttons: [
                {
                    text: "Download Receipt",
                    variant: "primary",
                    ariaLabel: "Download transaction receipt",
                    onClick: jest.fn(),
                    loading: true,
                    disabled: true,
                    dataTestid: "download-button",
                },
            ],
        });

        // Act
        render(<FooterSection {...baseProps} />);
        const button = screen.getByText("Download Receipt");

        // Assert
        expect(button).toBeDisabled();
    });

    it("should handle activeTab prop correctly when passing to button configs", () => {
        // Arrange
        const propsWithRecurringTab = {
            ...baseProps,
            activeTab: "recurring",
        };

        // Act
        render(<FooterSection {...propsWithRecurringTab} />);

        // Assert
        expect(getButtonRendererKey).toHaveBeenCalledWith(
            expect.objectContaining({
                activeTab: "recurring",
            })
        );
        expect(createButtonGroup).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({
                activeTab: "recurring",
            }),
            expect.anything(),
            expect.anything()
        );
    });
});
