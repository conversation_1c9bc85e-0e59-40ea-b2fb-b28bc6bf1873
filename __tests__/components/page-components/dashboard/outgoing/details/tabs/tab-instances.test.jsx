/**
 * @jest-environment jsdom
 */
import TransferInstancesTab from "@/components/page-components/dashboard/outgoing/details/tabs/tab-instances";
import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import React from "react";

// Test data
const baseBulk = {
    id: "bulk1",
    totalTransfers: 3,
    amount: "1000",
    transferScheduledId: "tx-bulk",
    date: "2025-04-22T12:00:00Z",
};

const baseSingle = {
    id: "single1",
    totalTransfers: undefined,
    amount: "500",
    transferScheduledId: "tx-single",
    date: "2025-04-21T10:00:00Z",
};

describe("TransferInstancesTab", () => {
    describe("Bulk transfers", () => {
        it("should display 'No instances to display' for all statuses", () => {
            const statuses = ["Active", "Paused", "Completed", "Cancelled", "Foo"];

            statuses.forEach((status) => {
                const { rerender } = render(<TransferInstancesTab transfer={{ ...baseBulk, status }} />);

                expect(screen.getByText("No instances to display.")).toBeInTheDocument();
                expect(screen.queryByText("Next instance")).not.toBeInTheDocument();
                expect(screen.queryByText("Past instances")).not.toBeInTheDocument();

                rerender(<></>); // Clean up between iterations
            });
        });
    });

    describe("Single transfers", () => {
        it("should display 'No instances to display' for all statuses", () => {
            const statuses = [
                "Pending",
                "Awaiting Approval",
                "Waiting",
                "Active",
                "Paused",
                "Completed",
                "Successful",
                "Cancelled",
                "Foo",
            ];

            statuses.forEach((status) => {
                const { rerender } = render(<TransferInstancesTab transfer={{ ...baseSingle, status }} />);

                expect(screen.getByText("No instances to display.")).toBeInTheDocument();
                expect(screen.queryByText("Next instance")).not.toBeInTheDocument();
                expect(screen.queryByText("Past instances")).not.toBeInTheDocument();

                rerender(<></>); // Clean up between iterations
            });
        });
    });

    describe("Edge cases", () => {
        it("should handle null date and amount", () => {
            render(<TransferInstancesTab transfer={{ ...baseBulk, status: "Active", date: null, amount: null }} />);
            expect(screen.getByText("No instances to display.")).toBeInTheDocument();
        });

        it("should handle undefined props", () => {
            render(<TransferInstancesTab transfer={{ ...baseSingle, status: "Active" }} />);
            expect(screen.getByText("No instances to display.")).toBeInTheDocument();
        });
    });
});

// Helper function from component for testing
function isBulkTransfer(transfer) {
    return transfer.totalTransfers !== undefined && transfer.totalTransfers > 1;
}

describe("Helper functions", () => {
    it("isBulkTransfer handles edge cases correctly", () => {
        expect(isBulkTransfer({ totalTransfers: 2 })).toBe(true);
        expect(isBulkTransfer({ totalTransfers: 1 })).toBe(false);
        expect(isBulkTransfer({ totalTransfers: 0 })).toBe(false);
        expect(isBulkTransfer({ totalTransfers: undefined })).toBe(false);
        expect(isBulkTransfer({})).toBe(false);
    });
});
