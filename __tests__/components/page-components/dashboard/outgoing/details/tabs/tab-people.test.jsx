import React from "react";
import { render, screen, cleanup, waitFor } from "@testing-library/react";
import TransferPeopleTab from "@/components/page-components/dashboard/outgoing/details/tabs/tab-people";

// Mock the custom hook
jest.mock("@/hooks/useApprovalRulePeopleInvolved", () => ({
    useApprovalRulePeopleInvolved: jest.fn(),
}));

// Mock the ApprovalFlow component
jest.mock("@/components/common/approval-flow", () => ({
    __esModule: true,
    default: (props) => {
        // Call renderConnectorLine to ensure it's covered in tests
        const color = props.renderConnectorLine ? props.renderConnectorLine(0, 1) : "";

        // Handle shouldFetchAPI default value (true when not explicitly passed)
        const shouldFetchAPI = props.shouldFetchAPI !== undefined ? props.shouldFetchAPI : true;

        // Test renderCustomItem function if provided
        const mockApprover = { id: "1", name: "Test Approver", level: 1 };
        let customItemContent = null;
        if (props.renderCustomItem && props.customData && props.customData.length > 0) {
            customItemContent = props.renderCustomItem(mockApprover, 0);
        }

        // Return a simple component that exposes what we need to test
        return (
            <div data-testid="approval-flow" data-connector-color={color}>
                <span data-testid="approval-flow-amount">{props.amount}</span>
                <span data-testid="approval-flow-type">{props.type}</span>
                <span data-testid="approval-flow-should-fetch-api">{shouldFetchAPI ? "true" : "false"}</span>
                <span data-testid="approval-flow-reference">{props.reference || "no-reference"}</span>
                <span data-testid="approval-flow-custom-data-length">
                    {props.customData ? props.customData.length : 0}
                </span>
                {props.renderConnectorLine && <span data-testid="connector-function-exists">Function exists</span>}
                {customItemContent && <div data-testid="custom-item-rendered">{customItemContent}</div>}
            </div>
        );
    },
}));

// Mock the ApproverListItem component
jest.mock("@/components/common/approver-list-item", () => ({
    __esModule: true,
    default: ({ approver, transferStatus, isLast, spacing }) => (
        <div
            data-testid="approver-list-item"
            data-approver-id={approver.id}
            data-transfer-status={transferStatus}
            data-is-last={isLast}
            data-spacing={spacing}
        >
            {approver.name}
        </div>
    ),
}));

// Mock the Alert component
jest.mock("@/components/common/alert", () => ({
    Alert: ({ variant, heading, supportingText }) => (
        <div data-testid="alert" data-variant={variant}>
            <h3 data-testid="alert-heading">{heading}</h3>
            <p data-testid="alert-text">{supportingText}</p>
        </div>
    ),
}));

// Mock approval data utils
jest.mock("@/utils/approval-data-utils", () => ({
    sortApproversByLevel: jest.fn((data) => data || []),
}));

describe("TransferPeopleTab Component", () => {
    const mockUseApprovalRulePeopleInvolved =
        require("@/hooks/useApprovalRulePeopleInvolved").useApprovalRulePeopleInvolved;

    // Helper function to render component with default hook mock
    const renderWithHookMock = (component, hookReturn = {}) => {
        const defaultHookReturn = {
            data: null,
            loading: false,
            error: null,
            refetch: jest.fn(),
        };

        mockUseApprovalRulePeopleInvolved.mockReturnValue({
            ...defaultHookReturn,
            ...hookReturn,
        });

        return render(component);
    };

    afterEach(() => {
        cleanup();
        jest.clearAllMocks();
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should render ApprovalFlow with correct props", () => {
        const mockTransfer = {
            amount: 5000,
            status: "Successful",
            transactionRef: "TXN123",
        };

        renderWithHookMock(<TransferPeopleTab transfer={mockTransfer} activeTab="instant" />);

        expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
        expect(screen.getByTestId("approval-flow-amount")).toHaveTextContent("5000");
        expect(screen.getByTestId("approval-flow-type")).toHaveTextContent("TRANSFER");
        expect(screen.getByTestId("approval-flow-should-fetch-api")).toHaveTextContent("false");
        expect(screen.getByTestId("approval-flow-reference")).toHaveTextContent("TXN123");
    });

    it("should call custom hook with correct parameters", () => {
        const mockTransfer = {
            amount: 5000,
            status: "Successful",
            transactionRef: "TXN123",
        };

        renderWithHookMock(<TransferPeopleTab transfer={mockTransfer} activeTab="instant" />);

        expect(mockUseApprovalRulePeopleInvolved).toHaveBeenCalledWith({
            amount: 5000,
            type: "TRANSFER",
            reference: "TXN123",
            enabled: true,
        });
    });

    it("should prefer bulkTransactionRef over transactionRef", () => {
        const mockTransfer = {
            amount: 5000,
            status: "Successful",
            transactionRef: "TXN123",
            bulkTransactionRef: "BULK456",
        };

        renderWithHookMock(<TransferPeopleTab transfer={mockTransfer} activeTab="instant" />);

        expect(mockUseApprovalRulePeopleInvolved).toHaveBeenCalledWith({
            amount: 5000,
            type: "TRANSFER",
            reference: "BULK456",
            enabled: true,
        });
    });

    it("should disable hook when transfer lacks required data", () => {
        const mockTransfer = {
            status: "Successful",
            // Missing amount and reference
        };

        renderWithHookMock(<TransferPeopleTab transfer={mockTransfer} activeTab="instant" />);

        expect(mockUseApprovalRulePeopleInvolved).toHaveBeenCalledWith({
            amount: undefined,
            type: "TRANSFER",
            reference: undefined,
            enabled: false,
        });
    });

    it("should pass custom renderConnectorLine function to ApprovalFlow", () => {
        renderWithHookMock(<TransferPeopleTab transfer={{ status: "Successful" }} />);

        expect(screen.getByTestId("connector-function-exists")).toBeInTheDocument();
        expect(screen.getByTestId("approval-flow")).toHaveAttribute("data-connector-color", "#F9F0FE");
    });

    it("should handle different transfer statuses for connector line colors", () => {
        // Test Unknown recurring status
        const { unmount } = renderWithHookMock(
            <TransferPeopleTab transfer={{ status: "Unknown" }} activeTab="recurring" />
        );

        expect(screen.getByTestId("approval-flow")).toHaveAttribute("data-connector-color", "#E9D5FF");

        // Cleanup before next render
        unmount();

        // Test Unknown non-recurring status
        renderWithHookMock(<TransferPeopleTab transfer={{ status: "Unknown" }} activeTab="instant" />);

        expect(screen.getByTestId("approval-flow")).toHaveAttribute("data-connector-color", "#F9F9FA");
    });

    it("should use proper connector colors for various statuses", () => {
        // Test different statuses one by one with cleanup in between
        const statuses = [
            { status: "Successful", expectedColor: "#F9F0FE" },
            { status: "Processing", expectedColor: "#F9F0FE" },
            { status: "Failed", expectedColor: "#F9F0FE" },
            { status: "Awaiting Approval", expectedColor: "#F9F0FE" },
            { status: "Rejected Approval", expectedColor: "#F9F0FE" },
            { status: "Pending", expectedColor: "#F9F0FE" },
        ];

        statuses.forEach(({ status, expectedColor }) => {
            const { unmount } = renderWithHookMock(<TransferPeopleTab transfer={{ status }} activeTab="instant" />);

            expect(screen.getByTestId("approval-flow")).toHaveAttribute("data-connector-color", expectedColor);

            // Cleanup before next render
            unmount();
        });
    });

    it("should use default status and amount if transfer is not provided", () => {
        renderWithHookMock(<TransferPeopleTab />);

        expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
        expect(screen.getByTestId("approval-flow-amount")).toHaveTextContent("0");
    });

    it("should render approval flow for valid transactions", () => {
        renderWithHookMock(<TransferPeopleTab transfer={{ status: "Successful" }} />);

        // Should render the approval flow
        expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
    });

    describe("Hook Data Processing", () => {
        it("should process API data and sort approvers when data is received", async () => {
            const mockSortedData = [
                { id: "1", name: "Approver 1", level: 1 },
                { id: "2", name: "Approver 2", level: 2 },
            ];

            const { sortApproversByLevel } = require("@/utils/approval-data-utils");
            sortApproversByLevel.mockReturnValue(mockSortedData);

            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: mockSortedData,
                loading: false,
                error: null,
            });

            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockSortedData);
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("2");
            });
        });

        it("should handle empty API data array", async () => {
            const { sortApproversByLevel } = require("@/utils/approval-data-utils");
            sortApproversByLevel.mockReturnValue([]);

            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: [],
                loading: false,
                error: null,
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("0");
            });
        });

        it("should handle null API data", async () => {
            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: null,
                loading: false,
                error: null,
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("0");
            });
        });

        it("should handle undefined API data", async () => {
            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: undefined,
                loading: false,
                error: null,
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("0");
            });
        });

        it("should handle non-array API data", async () => {
            const { sortApproversByLevel } = require("@/utils/approval-data-utils");

            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: "invalid data",
                loading: false,
                error: null,
            });

            await waitFor(() => {
                expect(sortApproversByLevel).not.toHaveBeenCalled();
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("0");
            });
        });
    });

    describe("Loading State", () => {
        it("should show loading state when loading is true and transfer has required data", () => {
            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: null,
                loading: true,
                error: null,
            });

            expect(screen.getByText("Loading approval information...")).toBeInTheDocument();
            expect(screen.queryByTestId("approval-flow")).not.toBeInTheDocument();
        });

        it("should not show loading state when transfer lacks required data", () => {
            renderWithHookMock(<TransferPeopleTab transfer={{ status: "Successful" }} />, {
                data: null,
                loading: true,
                error: null,
            });

            expect(screen.queryByText("Loading approval information...")).not.toBeInTheDocument();
            expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
        });
    });

    describe("Error State", () => {
        it("should show error state when error exists", () => {
            renderWithHookMock(<TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123" }} />, {
                data: null,
                loading: false,
                error: "Failed to load approval data",
            });

            expect(screen.getByText("Error loading approval information")).toBeInTheDocument();
            expect(screen.getByText("Please try again or contact support if the problem persists")).toBeInTheDocument();
            expect(screen.queryByTestId("approval-flow")).not.toBeInTheDocument();
        });

        it("should show error state for transactions with errors", () => {
            renderWithHookMock(
                <TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123", status: "Successful" }} />,
                {
                    data: null,
                    loading: false,
                    error: "Failed to load approval data",
                }
            );

            expect(screen.getByText("Error loading approval information")).toBeInTheDocument();
            expect(screen.getByText("Please try again or contact support if the problem persists")).toBeInTheDocument();
        });
    });

    describe("renderApproverItem function", () => {
        it("should test renderCustomApproverItem callback with different transfer statuses", async () => {
            const mockApprovalData = [{ id: "1", name: "Approver 1", level: 1 }];
            const { sortApproversByLevel } = require("@/utils/approval-data-utils");

            // Ensure the mock returns the data
            sortApproversByLevel.mockReturnValue(mockApprovalData);

            // Test with Failed status
            const { unmount } = renderWithHookMock(
                <TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123", status: "Failed" }} />,
                {
                    data: mockApprovalData,
                    loading: false,
                    error: null,
                }
            );

            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockApprovalData);
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("1");
            });

            let customItem = screen.getByTestId("custom-item-rendered");
            let approverItem = customItem.querySelector('[data-testid="approver-list-item"]');
            expect(approverItem).toHaveAttribute("data-transfer-status", "pending");
            expect(approverItem).toHaveAttribute("data-spacing", "mb-1");
            expect(approverItem).toHaveAttribute("data-is-last", "true");
            unmount();

            // Test with Successful status
            sortApproversByLevel.mockReturnValue(mockApprovalData);
            const { unmount: unmount2 } = renderWithHookMock(
                <TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123", status: "Successful" }} />,
                {
                    data: mockApprovalData,
                    loading: false,
                    error: null,
                }
            );

            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockApprovalData);
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("1");
            });

            customItem = screen.getByTestId("custom-item-rendered");
            approverItem = customItem.querySelector('[data-testid="approver-list-item"]');
            expect(approverItem).toHaveAttribute("data-transfer-status", "completed");
            unmount2();

            // Test with other status (should default to pending)
            sortApproversByLevel.mockReturnValue(mockApprovalData);
            renderWithHookMock(
                <TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123", status: "Processing" }} />,
                {
                    data: mockApprovalData,
                    loading: false,
                    error: null,
                }
            );

            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockApprovalData);
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("1");
            });

            customItem = screen.getByTestId("custom-item-rendered");
            approverItem = customItem.querySelector('[data-testid="approver-list-item"]');
            expect(approverItem).toHaveAttribute("data-transfer-status", "pending");
        });

        it("should handle isLast prop correctly based on array length", async () => {
            const mockApprovalData = [
                { id: "1", name: "Approver 1", level: 1 },
                { id: "2", name: "Approver 2", level: 2 },
            ];

            const { sortApproversByLevel } = require("@/utils/approval-data-utils");
            sortApproversByLevel.mockReturnValue(mockApprovalData);

            renderWithHookMock(
                <TransferPeopleTab transfer={{ amount: 5000, transactionRef: "TXN123", status: "Successful" }} />,
                {
                    data: mockApprovalData,
                    loading: false,
                    error: null,
                }
            );

            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockApprovalData);
            });

            await waitFor(() => {
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("2");
            });

            // The mock ApprovalFlow calls renderCustomItem with index 0, so isLast should be false for 2 items
            const customItem = screen.getByTestId("custom-item-rendered");
            const approverItem = customItem.querySelector('[data-testid="approver-list-item"]');
            expect(approverItem).toHaveAttribute("data-is-last", "false");
        });
    });

    describe("renderConnectorLine function edge cases", () => {
        it("should handle connector line colors correctly", () => {
            // Test the renderConnectorLine function directly by checking the data attribute
            // which is set using the function call with index 0 in our mock
            renderWithHookMock(<TransferPeopleTab transfer={{ status: "Pending" }} activeTab="instant" />);

            // The mock calls renderConnectorLine with index 0, which should return #F9F0FE for Pending status
            expect(screen.getByTestId("approval-flow")).toHaveAttribute("data-connector-color", "#F9F0FE");
        });
    });

    describe("Component props passed to ApprovalFlow", () => {
        it("should pass correct props to ApprovalFlow component", async () => {
            const mockTransfer = {
                amount: 7500,
                transactionRef: "TXN789",
                status: "Successful",
            };

            const mockApprovalData = [
                { id: "1", name: "Approver 1", level: 1 },
                { id: "2", name: "Approver 2", level: 2 },
            ];

            const { sortApproversByLevel } = require("@/utils/approval-data-utils");
            sortApproversByLevel.mockReturnValue(mockApprovalData);

            renderWithHookMock(<TransferPeopleTab transfer={mockTransfer} activeTab="scheduled" />, {
                data: mockApprovalData,
                loading: false,
                error: null,
            });

            expect(screen.getByTestId("approval-flow-amount")).toHaveTextContent("7500");
            expect(screen.getByTestId("approval-flow-type")).toHaveTextContent("TRANSFER");
            expect(screen.getByTestId("approval-flow-reference")).toHaveTextContent("TXN789");
            expect(screen.getByTestId("approval-flow-should-fetch-api")).toHaveTextContent("false");

            // Wait for the useEffect to process the data and update the state
            await waitFor(() => {
                expect(sortApproversByLevel).toHaveBeenCalledWith(mockApprovalData);
                expect(screen.getByTestId("approval-flow-custom-data-length")).toHaveTextContent("2");
            });
        });
    });
});
