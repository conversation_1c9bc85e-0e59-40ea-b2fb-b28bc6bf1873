import OutgoingRecipientsTab from "@/components/page-components/dashboard/outgoing/details/tabs/tab-recipients";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import thunk from "redux-thunk";
import { downloadReceipt } from "@/redux/actions/transferActions";

// Configure mock store
const middlewares = [thunk];
const mockStore = configureStore(middlewares);

// Mock dependencies
jest.mock("@/components/common/search-input", () => ({
    __esModule: true,
    default: ({ value, onValueChange, placeholder, size }) => (
        <input
            data-testid="search-input"
            value={value}
            onChange={(e) => onValueChange(e.target.value)}
            placeholder={placeholder}
            className={`search-input ${size}`}
        />
    ),
}));

jest.mock("@/components/common/approval-flow", () => ({
    __esModule: true,
    default: ({ customData, renderCustomItem, shouldFetchAPI, showConnectorLines, approverSpacing }) => (
        <div data-testid="approval-flow" data-show-lines={showConnectorLines} data-spacing={approverSpacing}>
            {customData &&
                customData.map((item, index) => (
                    <div key={item.id || index} data-testid={`recipient-item-${index}`}>
                        {renderCustomItem && renderCustomItem(item, index)}
                    </div>
                ))}
        </div>
    ),
}));

jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children, className, style }) => (
        <div className={className} style={style} data-testid="avatar">
            {children}
        </div>
    ),
    AvatarFallback: ({ children, className, style }) => (
        <div className={className} style={style} data-testid="avatar-fallback">
            {children}
        </div>
    ),
}));

jest.mock("@/components/common/badge", () => ({
    __esModule: true,
    default: ({ text, color, size }) => (
        <span data-testid="badge" data-color={color} data-size={size} className={`badge ${color} ${size}`}>
            {text}
        </span>
    ),
}));

jest.mock("@/components/common/loading-indicator", () => ({
    __esModule: true,
    default: ({ size }) => <div data-testid="loading-indicator">Loading... (size: {size})</div>,
}));

// Mock Redux actions
jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn((params) => async (dispatch) => {
        return Promise.resolve({ type: "DOWNLOAD_RECEIPT_SUCCESS", payload: params });
    }),
}));

// Mock recipient-utils
jest.mock("@/components/page-components/dashboard/outgoing/utils/recipient-utils", () => ({
    prepareRecipientData: jest.fn((transfer, activeTab) => {
        if (transfer.recipients && transfer.recipients.length > 0) {
            return transfer.recipients.filter((r) => r !== null);
        }
        return [transfer];
    }),
    getVisibilityFlags: jest.fn((transfer, activeTab, showStatusBadges, showActionButtons) => ({
        hideBadges: showStatusBadges === false,
        hideButtons: showActionButtons === false,
    })),
    getRecipientName: jest.fn((recipient) => {
        if (!recipient) return null;
        return recipient.counterparty || recipient.destinationAccountName || "Unknown Recipient";
    }),
    getRecipientStatusBadge: jest.fn((status) => {
        // Mock using centralized status mapping behavior
        const statusConfig = {
            Successful: { color: "success", text: "Successful" },
            Failed: { color: "error", text: "Failed" },
            Pending: { color: "warning", text: "Pending" },
            "Awaiting Approval": { color: "warning", text: "Awaiting Approval" },
            Processing: { color: "brand", text: "Processing" },
            "Rejected Approval": { color: "error", text: "Rejected Approval" },
        };
        const config = statusConfig[status] || { color: "neutral", text: status };
        return (
            <span data-testid="badge" data-color={config.color}>
                {config.text}
            </span>
        );
    }),
    formatRecipientAmount: jest.fn((amount) => `₦${amount.toLocaleString()}`),
}));

// Sample test data
const mockSingleTransfer = {
    id: "1",
    counterparty: "John Doe",
    amount: 10000,
    status: "Successful",
    narration: "Payment for services",
    recipients: [],
    transferScheduledId: "123-456",
};

const mockBulkTransfer = {
    id: "2",
    counterparty: "Multiple Recipients",
    amount: 30000,
    status: "Successful",
    narration: "Bulk payment",
    recipients: [
        {
            id: "r1",
            counterparty: "Alice Smith",
            narration: "Monthly salary",
            amount: 10000,
            status: "Successful",
            transferScheduledId: "123",
        },
        {
            id: "r2",
            counterparty: "Bob Johnson",
            narration: "Contract payment",
            amount: 10000,
            status: "Failed",
            transferScheduledId: "124",
        },
        {
            id: "r3",
            counterparty: "Charlie Brown",
            narration: "Service fee",
            amount: 10000,
            status: "Pending",
            transferScheduledId: "125",
        },
    ],
};

describe("OutgoingRecipientsTab", () => {
    let store;

    beforeEach(() => {
        store = mockStore({
            transfer: { loading: false, error: null },
        });
        jest.clearAllMocks();
    });

    const renderWithRedux = (component) => render(<Provider store={store}>{component}</Provider>);

    describe("Component Rendering", () => {
        it("renders search input with correct props", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);
            const searchInput = screen.getByTestId("search-input");
            expect(searchInput).toBeInTheDocument();
            expect(searchInput).toHaveAttribute("placeholder", "Search recipient");
            expect(searchInput).toHaveClass("search-input sm");
        });

        it("renders approval flow with correct configuration", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);
            const approvalFlow = screen.getByTestId("approval-flow");
            expect(approvalFlow).toBeInTheDocument();
            expect(approvalFlow).toHaveAttribute("data-show-lines", "false");
            expect(approvalFlow).toHaveAttribute("data-spacing", "mb-6 sm:mb-8");
        });

        it("renders loading state correctly", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} isLoading={true} />);
            expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
            expect(screen.getByText("Loading recipients...")).toBeInTheDocument();
        });

        it("renders error state correctly", () => {
            const errorMessage = "Failed to load recipients";
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} error={errorMessage} />);
            expect(screen.getByText("Error loading recipients")).toBeInTheDocument();
            expect(screen.getByText(errorMessage)).toBeInTheDocument();
        });

        it("renders empty state when no transfer data", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={null} />);
            expect(screen.getByText("No transfer data available")).toBeInTheDocument();
        });

        it("renders single transfer when no recipients array", () => {
            const emptyTransfer = { ...mockBulkTransfer, recipients: [] };
            renderWithRedux(<OutgoingRecipientsTab transfer={emptyTransfer} />);
            // Should display the transfer itself as a single recipient
            expect(screen.getByText("Multiple Recipients")).toBeInTheDocument();
        });
    });

    describe("Recipient Display", () => {
        it("displays recipient information correctly", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            // Check if recipients are rendered
            expect(screen.getByText("Alice Smith")).toBeInTheDocument();
            expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
            expect(screen.getByText("Charlie Brown")).toBeInTheDocument();

            // Check narrations
            expect(screen.getByText("Monthly salary")).toBeInTheDocument();
            expect(screen.getByText("Contract payment")).toBeInTheDocument();
            expect(screen.getByText("Service fee")).toBeInTheDocument();

            // Check amounts - there should be 3 instances of ₦10,000
            const amounts = screen.getAllByText("₦10,000");
            expect(amounts).toHaveLength(3);
        });

        it("displays status badges for recipients", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const badges = screen.getAllByTestId("badge");
            expect(badges).toHaveLength(3);
            expect(badges[0]).toHaveTextContent("Successful");
            expect(badges[1]).toHaveTextContent("Failed");
            expect(badges[2]).toHaveTextContent("Pending");
        });

        it("displays avatar with initials", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const avatarFallbacks = screen.getAllByTestId("avatar-fallback");
            expect(avatarFallbacks[0]).toHaveTextContent("AS"); // Alice Smith
            expect(avatarFallbacks[1]).toHaveTextContent("BJ"); // Bob Johnson
            expect(avatarFallbacks[2]).toHaveTextContent("CB"); // Charlie Brown
        });

        it("shows download receipt link only for successful transfers", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const downloadButtons = screen.getAllByText("Download receipt");
            expect(downloadButtons).toHaveLength(1); // Only Alice Smith has successful status
        });

        it("hides badges when showStatusBadges is false", () => {
            renderWithRedux(
                <OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" showStatusBadges={false} />
            );

            expect(screen.queryAllByTestId("badge")).toHaveLength(0);
        });

        it("hides action buttons when showActionButtons is false", () => {
            renderWithRedux(
                <OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" showActionButtons={false} />
            );

            expect(screen.queryByText("Download receipt")).not.toBeInTheDocument();
        });
    });

    describe("Search Functionality", () => {
        it("filters recipients based on search query", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const searchInput = screen.getByTestId("search-input");
            fireEvent.change(searchInput, { target: { value: "Alice" } });

            expect(screen.getByText("Alice Smith")).toBeInTheDocument();
            expect(screen.queryByText("Bob Johnson")).not.toBeInTheDocument();
            expect(screen.queryByText("Charlie Brown")).not.toBeInTheDocument();
        });

        it("shows no results message when search yields no matches", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const searchInput = screen.getByTestId("search-input");
            fireEvent.change(searchInput, { target: { value: "Nonexistent" } });

            expect(screen.getByText('No recipients found matching "Nonexistent"')).toBeInTheDocument();
        });
    });

    describe("Download Receipt Functionality", () => {
        it("calls downloadReceipt action when download link is clicked", async () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const downloadLink = screen.getByText("Download receipt");
            fireEvent.click(downloadLink);

            await waitFor(() => {
                expect(downloadReceipt).toHaveBeenCalledWith({
                    transactionId: "123",
                    isUserInitiated: true,
                });
            });
        });

        it("uses transferScheduledId when available for download", async () => {
            const customTransfer = {
                ...mockBulkTransfer,
                recipients: [
                    {
                        ...mockBulkTransfer.recipients[0],
                        transferScheduledId: "custom-123",
                    },
                ],
            };

            renderWithRedux(<OutgoingRecipientsTab transfer={customTransfer} activeTab="instant" />);

            const downloadLink = screen.getByText("Download receipt");
            fireEvent.click(downloadLink);

            await waitFor(() => {
                expect(downloadReceipt).toHaveBeenCalledWith({
                    transactionId: "custom-123",
                    isUserInitiated: true,
                });
            });
        });
    });

    describe("Edge Cases", () => {
        it("handles recipients with missing narration", () => {
            const transferWithMissingNarration = {
                ...mockBulkTransfer,
                recipients: [
                    {
                        ...mockBulkTransfer.recipients[0],
                        narration: null,
                    },
                ],
            };

            renderWithRedux(<OutgoingRecipientsTab transfer={transferWithMissingNarration} activeTab="instant" />);
            expect(screen.getByText("-")).toBeInTheDocument();
        });

        it("handles single transfer converted to recipient array", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockSingleTransfer} activeTab="instant" />);
            expect(screen.getByText("John Doe")).toBeInTheDocument();
            expect(screen.getByText("Payment for services")).toBeInTheDocument();
        });

        it("handles error during recipient data preparation", () => {
            const prepareRecipientData =
                require("@/components/page-components/dashboard/outgoing/utils/recipient-utils").prepareRecipientData;
            prepareRecipientData.mockImplementationOnce(() => {
                throw new Error("Data preparation failed");
            });

            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);
            expect(screen.getByText("Error processing recipient data")).toBeInTheDocument();
        });

        it("handles missing recipient name gracefully", () => {
            const transferWithNoName = {
                ...mockBulkTransfer,
                recipients: [
                    {
                        ...mockBulkTransfer.recipients[0],
                        counterparty: null,
                    },
                ],
            };

            // Mock getRecipientName to return null
            const getRecipientName =
                require("@/components/page-components/dashboard/outgoing/utils/recipient-utils").getRecipientName;
            getRecipientName.mockReturnValueOnce(null);

            renderWithRedux(<OutgoingRecipientsTab transfer={transferWithNoName} activeTab="instant" />);

            // Should handle the null name case without crashing
            expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
        });

        it("handles empty recipients array", () => {
            const prepareRecipientData =
                require("@/components/page-components/dashboard/outgoing/utils/recipient-utils").prepareRecipientData;
            prepareRecipientData.mockReturnValueOnce([]);

            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);
            expect(screen.getByText("No recipients found for this transfer")).toBeInTheDocument();
        });

        it("handles null recipients array", () => {
            const prepareRecipientData =
                require("@/components/page-components/dashboard/outgoing/utils/recipient-utils").prepareRecipientData;
            prepareRecipientData.mockReturnValueOnce(null);

            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);
            expect(screen.getByText("No recipients found for this transfer")).toBeInTheDocument();
        });

        it("handles recipient with null data in renderRecipientItem", () => {
            const transferWithNullRecipient = {
                ...mockBulkTransfer,
                recipients: [null, mockBulkTransfer.recipients[1]],
            };

            renderWithRedux(<OutgoingRecipientsTab transfer={transferWithNullRecipient} activeTab="instant" />);
            // Should skip the null recipient and render the valid one
            expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
            expect(screen.queryByText("null")).not.toBeInTheDocument();
        });

        it("handles error during download receipt", async () => {
            const downloadReceipt = require("@/redux/actions/transferActions").downloadReceipt;
            downloadReceipt.mockImplementationOnce(() => {
                throw new Error("Download failed");
            });

            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab="instant" />);

            const downloadLink = screen.getByText("Download receipt");
            fireEvent.click(downloadLink);

            // Should not crash despite the error
            await waitFor(() => {
                expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
            });
        });

        it("handles recipients with very long names that need to be split", () => {
            const getRecipientName =
                require("@/components/page-components/dashboard/outgoing/utils/recipient-utils").getRecipientName;
            // Reset the mock to its original implementation
            getRecipientName.mockImplementation((recipient) => {
                if (!recipient) return null;
                return recipient.counterparty || recipient.destinationAccountName || "Unknown Recipient";
            });

            const transferWithLongName = {
                ...mockBulkTransfer,
                recipients: [
                    {
                        ...mockBulkTransfer.recipients[0],
                        counterparty: "", // Empty string to test edge case
                    },
                ],
            };

            renderWithRedux(<OutgoingRecipientsTab transfer={transferWithLongName} activeTab="instant" />);

            // Should still render without crashing
            expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
        });
    });

    describe("Props and Configuration", () => {
        it("uses default props when not provided", () => {
            renderWithRedux(<OutgoingRecipientsTab transfer={mockBulkTransfer} />);

            // Should use default activeTab="instant"
            expect(screen.getByTestId("approval-flow")).toBeInTheDocument();

            // Should show badges and buttons by default
            expect(screen.getAllByTestId("badge")).toHaveLength(3);
            expect(screen.getByText("Download receipt")).toBeInTheDocument();
        });

        it("handles all different tab types", () => {
            const tabs = ["instant", "scheduled", "recurring"];

            tabs.forEach((tab) => {
                const { unmount } = renderWithRedux(
                    <OutgoingRecipientsTab transfer={mockBulkTransfer} activeTab={tab} />
                );
                expect(screen.getByTestId("approval-flow")).toBeInTheDocument();
                unmount();
            });
        });
    });
});
