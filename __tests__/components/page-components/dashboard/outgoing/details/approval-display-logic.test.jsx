import {
    shouldShowApprovalAlert,
    transferNeedsApproval,
    getApprovalAlertConfig,
} from "@/components/page-components/dashboard/outgoing/details/approval-display-logic";

describe("Approval Display Logic", () => {
    // Mock transfer data for different scenarios
    const basePendingTransfer = {
        id: "12345",
        status: "Pending",
        type: "single",
        amount: 1000,
        currency: "NGN",
    };

    const awaitingApprovalTransfer = {
        ...basePendingTransfer,
        status: "Awaiting Approval",
    };

    const completedTransfer = {
        ...basePendingTransfer,
        status: "Completed",
    };

    const cancelledTransfer = {
        ...basePendingTransfer,
        status: "Cancelled",
    };

    const failedTransfer = {
        ...basePendingTransfer,
        status: "Failed",
    };

    const bulkTransfer = {
        ...basePendingTransfer,
        type: "bulk",
        hasMultipleNarrations: true,
    };

    const bulkTransferSingleNarration = {
        ...basePendingTransfer,
        type: "bulk",
        hasMultipleNarrations: false,
    };

    describe("Basic Status Conditions", () => {
        it("returns true for pending single transfers with approvers", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("returns true for awaiting approval single transfers with approvers", () => {
            const result = shouldShowApprovalAlert({
                transfer: awaitingApprovalTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("returns false for completed transfers", () => {
            const result = shouldShowApprovalAlert({
                transfer: completedTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("returns false for cancelled transfers", () => {
            const result = shouldShowApprovalAlert({
                transfer: cancelledTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("returns false for failed transfers", () => {
            const result = shouldShowApprovalAlert({
                transfer: failedTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("returns false when no approvers are needed", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: false,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });
    });

    describe("Details Tab Scenarios", () => {
        it("shows alert for single transfers in Details tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("shows alert for bulk transfers with single narration in Details tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransferSingleNarration,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: true,
                hasSingleNarr: true,
            });

            expect(result).toBe(true);
        });

        it("shows alert for bulk transfers with multiple narrations in instant tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: true,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("shows alert for bulk transfers in scheduled tab regardless of narration", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "scheduled",
                isBulk: true,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("shows alert for bulk transfers in recurring tab regardless of narration", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "recurring",
                isBulk: true,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });
    });

    describe("Instances Tab Scenarios", () => {
        it("shows alert for single transfers in Instances tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Instances",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("hides alert for bulk transfers in Instances tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransfer,
                hasApprovers: true,
                activeDetailTab: "Instances",
                activeTab: "instant",
                isBulk: true,
                hasSingleNarr: true,
            });

            expect(result).toBe(false);
        });

        it("hides alert for bulk transfers in Instances tab regardless of narration", () => {
            const result = shouldShowApprovalAlert({
                transfer: bulkTransferSingleNarration,
                hasApprovers: true,
                activeDetailTab: "Instances",
                activeTab: "scheduled",
                isBulk: true,
                hasSingleNarr: true,
            });

            expect(result).toBe(false);
        });
    });

    describe("Other Detail Tabs", () => {
        it("hides alert in People Involved tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "People involved",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("hides alert in Recipients tab", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Recipients",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("hides alert in undefined/unknown tabs", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Unknown",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });
    });

    describe("Edge Cases", () => {
        it("handles missing transfer object", () => {
            const result = shouldShowApprovalAlert({
                transfer: null,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("handles missing transfer status", () => {
            const result = shouldShowApprovalAlert({
                transfer: { ...basePendingTransfer, status: undefined },
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("handles boolean hasApprovers correctly when false", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: false,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false);
        });

        it("handles boolean hasApprovers correctly when true", () => {
            const result = shouldShowApprovalAlert({
                transfer: basePendingTransfer,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(true);
        });

        it("handles case-sensitive status comparison", () => {
            const pendingLowercase = { ...basePendingTransfer, status: "pending" };
            const result = shouldShowApprovalAlert({
                transfer: pendingLowercase,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "instant",
                isBulk: false,
                hasSingleNarr: false,
            });

            expect(result).toBe(false); // Should be case-sensitive
        });
    });

    describe("Complex Scenarios", () => {
        it("correctly handles complex bulk transfer scenarios", () => {
            // Bulk transfer with single narration in scheduled tab should show alert
            const result1 = shouldShowApprovalAlert({
                transfer: bulkTransferSingleNarration,
                hasApprovers: true,
                activeDetailTab: "Details",
                activeTab: "scheduled",
                isBulk: true,
                hasSingleNarr: true,
            });
            expect(result1).toBe(true);

            // Same transfer in Instances tab should hide alert
            const result2 = shouldShowApprovalAlert({
                transfer: bulkTransferSingleNarration,
                hasApprovers: true,
                activeDetailTab: "Instances",
                activeTab: "scheduled",
                isBulk: true,
                hasSingleNarr: true,
            });
            expect(result2).toBe(false);
        });

        it("shows alert for all combinations in Details tab (simplified logic)", () => {
            const testCases = [
                { isBulk: true, activeTab: "instant", hasSingleNarr: false, expected: true },
                { isBulk: true, activeTab: "instant", hasSingleNarr: true, expected: true },
                { isBulk: true, activeTab: "scheduled", hasSingleNarr: false, expected: true },
                { isBulk: true, activeTab: "scheduled", hasSingleNarr: true, expected: true },
                { isBulk: false, activeTab: "instant", hasSingleNarr: false, expected: true },
                { isBulk: false, activeTab: "instant", hasSingleNarr: true, expected: true },
            ];

            testCases.forEach(({ isBulk, activeTab, hasSingleNarr, expected }, index) => {
                const result = shouldShowApprovalAlert({
                    transfer: basePendingTransfer,
                    hasApprovers: true,
                    activeDetailTab: "Details",
                    activeTab,
                    isBulk,
                    hasSingleNarr,
                });
                expect(result).toBe(expected, `Test case ${index + 1} failed`);
            });
        });
    });

    describe("Helper Functions", () => {
        describe("transferNeedsApproval", () => {
            it("returns true for pending transfers with approvers", () => {
                const result = transferNeedsApproval(basePendingTransfer, true);
                expect(result).toBe(true);
            });

            it("returns true for awaiting approval transfers with approvers", () => {
                const result = transferNeedsApproval(awaitingApprovalTransfer, true);
                expect(result).toBe(true);
            });

            it("returns false for completed transfers", () => {
                const result = transferNeedsApproval(completedTransfer, true);
                expect(result).toBe(false);
            });

            it("returns false when no approvers", () => {
                const result = transferNeedsApproval(basePendingTransfer, false);
                expect(result).toBe(false);
            });

            it("returns false for null transfer", () => {
                const result = transferNeedsApproval(null, true);
                expect(result).toBe(false);
            });
        });

        describe("getApprovalAlertConfig", () => {
            it("returns correct configuration for approval alert", () => {
                const config = getApprovalAlertConfig(2, 3);

                expect(config).toEqual({
                    heading: "2 of 3 approvals granted",
                    supportingText: "This transfer requires approval before it can be processed.",
                    buttonText: "See approvers",
                    variant: "info",
                    showIcon: true,
                    showSecondaryActionRightIcon: true,
                });
            });

            it("handles zero approvals", () => {
                const config = getApprovalAlertConfig(0, 5);
                expect(config.heading).toBe("0 of 5 approvals granted");
            });

            it("handles all approvals granted", () => {
                const config = getApprovalAlertConfig(3, 3);
                expect(config.heading).toBe("3 of 3 approvals granted");
            });
        });
    });
});
