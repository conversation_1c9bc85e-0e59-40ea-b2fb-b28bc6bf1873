import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { HeaderSection } from "@/components/page-components/dashboard/outgoing/details/header-section";

// Mock the imported components and functions
jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ className, children }) => (
        <div data-testid="avatar" className={className}>
            {children}
        </div>
    ),
    AvatarImage: ({ src, alt }) => <img data-testid="avatar-image" src={src} alt={alt} />,
    AvatarFallback: ({ className, children }) => (
        <div data-testid="avatar-fallback" className={className}>
            {children}
        </div>
    ),
}));

jest.mock("@/components/common/badge", () => ({
    __esModule: true,
    default: ({ text, size, color, className }) => (
        <div data-testid="badge" data-size={size} data-color={color} className={className}>
            {text}
        </div>
    ),
}));

jest.mock("lucide-react", () => ({
    X: () => <div data-testid="x-icon">X Icon</div>,
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount) => `₦${amount.toLocaleString()}`),
    getNameInitials: jest.fn((name) =>
        name
            .split(" ")
            .map((n) => n[0])
            .join("")
    ),
}));

jest.mock("@/functions/date", () => ({
    formatDateWithTime: jest.fn((date) => `${date} 12:00 PM`),
}));

jest.mock("@/components/page-components/dashboard/outgoing/utils/statusUtils", () => ({
    isBulkTransfer: jest.fn((transfer) => transfer.totalTransfers > 1),
    calculateBulkTransferAmount: jest.fn((transfer) =>
        transfer.recipients
            ? transfer.recipients.reduce((sum, recipient) => sum + recipient.amount, 0)
            : transfer.amount
    ),
}));

jest.mock("@/utils/status-mapping", () => ({
    getStatusMapping: jest.fn((status) => {
        const statusMap = {
            Successful: { text: "Successful", color: "success" },
            Failed: { text: "Failed", color: "error" },
            Pending: { text: "Pending", color: "warning" },
            Processing: { text: "Processing", color: "brand" },
            "In progress": { text: "In progress", color: "brand" },
        };
        return statusMap[status] || { text: status, color: "neutral" };
    }),
}));

describe("HeaderSection Component", () => {
    const mockHandleCloseDetails = jest.fn();

    const mockTransfer = {
        id: "123",
        counterparty: "John Doe",
        narration: "Test transaction",
        date: "2023-05-15",
        status: "Successful",
        amount: 50000,
        accountNumber: "**********",
        transferType: "Intra-bank",
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the component with transfer details", () => {
        render(<HeaderSection transfer={mockTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        // Check if counterparty name is displayed
        expect(screen.getByText("John Doe")).toBeInTheDocument();

        // Check if narration is displayed
        expect(screen.getByText("Test transaction")).toBeInTheDocument();

        // Check if formatted date is displayed
        expect(screen.getByText("2023-05-15 12:00 PM")).toBeInTheDocument();

        // Check if formatted amount is displayed
        expect(screen.getByText("₦50,000")).toBeInTheDocument();

        // Check if status badge is displayed with correct color
        const badge = screen.getByTestId("badge");
        expect(badge).toHaveTextContent("Successful");
        expect(badge).toHaveAttribute("data-color", "success");
    });

    it("calls handleCloseDetails when close button is clicked", () => {
        render(<HeaderSection transfer={mockTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        const closeButton = screen.getByTestId("close-btn");
        fireEvent.click(closeButton);

        expect(mockHandleCloseDetails).toHaveBeenCalledTimes(1);
    });

    it("displays initials in avatar fallback when no image is provided", () => {
        render(<HeaderSection transfer={mockTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        const avatarFallback = screen.getByTestId("avatar-fallback");
        expect(avatarFallback).toHaveTextContent("JD");
    });

    it("renders different badge colors based on status", () => {
        // Test for Failed status
        const failedTransfer = { ...mockTransfer, status: "Failed" };
        const { rerender } = render(
            <HeaderSection transfer={failedTransfer} handleCloseDetails={mockHandleCloseDetails} />
        );

        expect(screen.getByTestId("badge")).toHaveAttribute("data-color", "error");

        // Test for Pending status
        const pendingTransfer = { ...mockTransfer, status: "Pending" };
        rerender(<HeaderSection transfer={pendingTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        expect(screen.getByTestId("badge")).toHaveAttribute("data-color", "warning");

        // Test for Processing status (In progress)
        const inProgressTransfer = { ...mockTransfer, status: "Processing" };
        rerender(<HeaderSection transfer={inProgressTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        expect(screen.getByTestId("badge")).toHaveAttribute("data-color", "brand");

        // Test for unknown status that falls back to neutral
        const unknownTransfer = { ...mockTransfer, status: "SomeUnknownStatus" };
        rerender(<HeaderSection transfer={unknownTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        expect(screen.getByTestId("badge")).toHaveAttribute("data-color", "neutral");
    });

    it("displays dash when date is not provided", () => {
        const transferWithoutDate = { ...mockTransfer, date: null };
        render(<HeaderSection transfer={transferWithoutDate} handleCloseDetails={mockHandleCloseDetails} />);

        // Should display "-" instead of formatted date
        expect(screen.getByText("-")).toBeInTheDocument();
    });

    it("uses neutral color for unknown status (default case)", () => {
        // @ts-ignore - Intentionally using an invalid status to test default case
        const unknownStatusTransfer = { ...mockTransfer, status: "Unknown" };
        render(<HeaderSection transfer={unknownStatusTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        expect(screen.getByTestId("badge")).toHaveAttribute("data-color", "neutral");
    });

    it("renders bulk transfer icon and recipients count for bulk transfers", () => {
        const bulkTransfer = {
            ...mockTransfer,
            totalTransfers: 5,
            recipients: [
                { id: "1", name: "Recipient 1", amount: 10000 },
                { id: "2", name: "Recipient 2", amount: 15000 },
            ],
        };

        render(<HeaderSection transfer={bulkTransfer} handleCloseDetails={mockHandleCloseDetails} />);

        // Check if bulk transfer icon is rendered (svg with specific class)
        const bulkIcon = document.querySelector("svg.bg-\\[\\#F9F0FE\\]");
        expect(bulkIcon).toBeInTheDocument();
        expect(bulkIcon).toHaveClass("bg-[#F9F0FE]");

        // Check if recipients count is displayed instead of counterparty name
        expect(screen.getByText("5 recipients")).toBeInTheDocument();
        expect(screen.queryByText("John Doe")).not.toBeInTheDocument();
    });
});
