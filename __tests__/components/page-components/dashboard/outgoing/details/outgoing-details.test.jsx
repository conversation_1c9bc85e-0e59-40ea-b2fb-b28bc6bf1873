import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import OutgoingDetails from "@/components/page-components/dashboard/outgoing/details/outgoing-details";
import {
    updateTransferStatus,
    fetchTransfers,
    fetchScheduledTransfers,
    fetchRecurringTransfers,
} from "@/redux/actions/transferActions";
import { getTransferApprovers } from "@/redux/actions/sendMoneyActions";
import { sendCatchFeedback } from "@/functions/feedback";
import { useApprovalRulePeopleInvolved } from "@/hooks/useApprovalRulePeopleInvolved";

// Mock dependencies
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, children, className }) => (
        <div data-testid="drawer" className={className}>
            {isOpen ? children : null}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/header-section", () => ({
    HeaderSection: ({ transfer, handleCloseDetails }) => (
        <div data-testid="header-section">
            <button data-testid="close-button" onClick={handleCloseDetails}>
                Close
            </button>
            <div data-testid="transfer-status">{transfer.status}</div>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/outgoing/utils/statusUtils", () => ({
    isBulkTransfer: jest.fn((transfer) => {
        return transfer && (transfer.totalTransfers ?? 1) > 1;
    }),
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/tab-section", () => ({
    TabSection: ({ transfer, activeTab, activeDetailTab, handleTabChange }) => {
        // Import the mocked function
        const { isBulkTransfer } = require("@/components/page-components/dashboard/outgoing/utils/statusUtils");
        const isTransferBulk = isBulkTransfer(transfer);

        return (
            <div data-testid="tab-section">
                <button data-testid="details-tab" onClick={() => handleTabChange("Details")}>
                    Details
                </button>
                <button data-testid="people-tab" onClick={() => handleTabChange("People involved")}>
                    People
                </button>
                {isTransferBulk && (
                    <button data-testid="recipients-tab" onClick={() => handleTabChange("Recipients")}>
                        Recipients
                    </button>
                )}
                {activeTab === "recurring" && (
                    <button data-testid="instances-tab" onClick={() => handleTabChange("Instances")}>
                        Instances
                    </button>
                )}
            </div>
        );
    },
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/footer", () => ({
    FooterSection: ({
        transfer,
        activeTab,
        activeDetailTab,
        handleOpenReport,
        handleStatusUpdate,
        isUpdatingStatus,
    }) => (
        <div data-testid="footer-section">
            {transfer.status !== "Cancelled" && (
                <>
                    <button
                        data-testid="cancel-button"
                        onClick={() => handleStatusUpdate("CANCELLED")}
                        disabled={isUpdatingStatus}
                    >
                        Cancel
                    </button>
                    {(activeTab === "scheduled" || activeTab === "recurring") && (
                        <button
                            data-testid="pause-button"
                            onClick={() => handleStatusUpdate("PAUSED")}
                            disabled={isUpdatingStatus}
                        >
                            Pause
                        </button>
                    )}
                    <button data-testid="report-button" onClick={handleOpenReport}>
                        Report
                    </button>
                </>
            )}
        </div>
    ),
}));

jest.mock("@/components/common/alert", () => ({
    Alert: ({ onSecondaryAction }) => (
        <div data-testid="alert">
            <button data-testid="secondary-action-button" onClick={onSecondaryAction}>
                Secondary Action
            </button>
        </div>
    ),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    updateTransferStatus: jest.fn(() => ({ type: "UPDATE_TRANSFER_STATUS" })),
    fetchTransfers: jest.fn(() => ({ type: "FETCH_TRANSFERS" })),
    fetchScheduledTransfers: jest.fn(() => ({ type: "FETCH_SCHEDULED_TRANSFERS" })),
    fetchRecurringTransfers: jest.fn(() => ({ type: "FETCH_RECURRING_TRANSFERS" })),
}));

jest.mock("@/redux/actions/sendMoneyActions", () => ({
    getTransferApprovers: jest.fn(() => ({ type: "GET_TRANSFER_APPROVERS" })),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

// Mock the useApprovalRulePeopleInvolved hook
jest.mock("@/hooks/useApprovalRulePeopleInvolved", () => ({
    useApprovalRulePeopleInvolved: jest.fn(),
}));

// Mock the ApprovalAlert component
jest.mock("@/components/page-components/dashboard/outgoing/details/approval-alert", () => ({
    __esModule: true,
    default: ({ approvalCount, totalApprovals, onSeeApprovers }) => (
        <div data-testid="approval-alert">
            <div data-testid="approval-count">
                {approvalCount} of {totalApprovals}
            </div>
            <button data-testid="see-approvers-button" onClick={onSeeApprovers}>
                See approvers
            </button>
        </div>
    ),
}));

// Mock timer
jest.useFakeTimers();

// Create mock store
const mockStore = configureStore([]);

describe("OutgoingDetails", () => {
    // Create default props
    const defaultProps = {
        isOpen: true,
        handleCloseDetails: jest.fn(),
        handleOpenReport: jest.fn(),
        activeTab: "instant",
    };

    // Create mock transfer objects for different scenarios
    const singleTransfer = {
        id: "123",
        paymentRequestId: 456,
        transferScheduledId: 789,
        counterparty: "John Doe",
        accountNumber: "**********",
        narration: "Salary Payment",
        status: "Pending",
        amount: 5000,
        transferType: "Intra-bank",
        totalTransfers: 1, // Single transfer: totalTransfers = 1
    };

    const bulkTransfer = {
        id: "bulk-123",
        paymentRequestId: 456,
        transferScheduledId: 789,
        counterparty: "Multiple Recipients",
        accountNumber: "",
        narration: "Bulk Salary Payment",
        status: "Pending",
        amount: 25000,
        transferType: "Intra-bank",
        totalTransfers: 5, // Bulk transfer: totalTransfers > 1
        recipients: [
            { id: "rec-1", status: "Pending", amount: 5000 },
            { id: "rec-2", status: "Failed", amount: 5000 },
            { id: "rec-3", status: "Successful", amount: 5000 },
            { id: "rec-4", status: "In progress", amount: 5000 },
            { id: "rec-5", status: "Waiting", amount: 5000 },
        ],
    };

    const cancelledTransfer = {
        ...singleTransfer,
        status: "Cancelled",
    };

    // Mock approval data for tests
    const mockApprovalData = [
        {
            id: 1,
            userId: 101,
            name: "John Doe",
            role: "Manager",
            approvalLevel: 1,
            status: "APPROVE",
            date: "2025-01-01",
        },
        {
            id: 2,
            userId: 102,
            name: "Jane Smith",
            role: "Director",
            approvalLevel: 2,
            status: null,
            date: null,
        },
        {
            id: 3,
            userId: 103,
            name: "Bob Wilson",
            role: "CFO",
            approvalLevel: 3,
            status: null,
            date: null,
        },
    ];

    // Setup function for the tests
    const setupTest = (transfer, extraProps = {}, approvalData = null) => {
        // Mock the hook's return value
        const mockHookReturn = {
            data: approvalData,
            loading: false,
            error: null,
        };

        // Setup the hook mock
        useApprovalRulePeopleInvolved.mockReturnValue(mockHookReturn);

        const store = mockStore({
            transfer: {
                updateTransferStatusLoading: false,
                downloadReceiptLoading: false,
            },
            sendMoney: {
                getTransferApprovers: {
                    data: approvalData,
                    loading: false,
                    error: null,
                },
            },
        });

        const props = {
            ...defaultProps,
            ...extraProps,
            transfer,
        };

        return render(
            <Provider store={store}>
                <OutgoingDetails {...props} />
            </Provider>
        );
    };

    // Reset mocks between tests
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("renders drawer when isOpen is true", () => {
        setupTest(singleTransfer);
        expect(screen.getByTestId("drawer")).toBeInTheDocument();
    });

    test("renders header section with transfer details", () => {
        setupTest(singleTransfer);
        expect(screen.getByTestId("header-section")).toBeInTheDocument();
        expect(screen.getByTestId("transfer-status")).toHaveTextContent("Pending");
    });

    test("calls handleCloseDetails when close button is clicked", () => {
        setupTest(singleTransfer);
        fireEvent.click(screen.getByTestId("close-button"));
        expect(defaultProps.handleCloseDetails).toHaveBeenCalledTimes(1);
    });

    test("renders tab section with correct tabs", () => {
        setupTest(singleTransfer);
        expect(screen.getByTestId("tab-section")).toBeInTheDocument();
        expect(screen.getByTestId("details-tab")).toBeInTheDocument();
        expect(screen.getByTestId("people-tab")).toBeInTheDocument();
    });

    test("shows recipients tab for bulk transfers", () => {
        setupTest(bulkTransfer);
        expect(screen.getByTestId("recipients-tab")).toBeInTheDocument();
    });

    test("shows instances tab for recurring transfers", () => {
        setupTest(singleTransfer, { activeTab: "recurring" });
        expect(screen.getByTestId("instances-tab")).toBeInTheDocument();
    });

    test("does not show approval alert for transfers with no approvers", () => {
        // Use Awaiting Approval status but no approval data
        const transferAwaitingApproval = {
            ...singleTransfer,
            status: "Awaiting Approval",
        };
        setupTest(transferAwaitingApproval); // No approval data passed
        expect(screen.queryByTestId("approval-alert")).not.toBeInTheDocument();
    });

    test("shows approval alert for pending transfers with approvers", () => {
        // Use a transfer with Pending status
        const transferWithApprovers = {
            ...singleTransfer,
            status: "Pending",
        };
        // Pass approval data to show the alert
        setupTest(transferWithApprovers, {}, mockApprovalData);
        expect(screen.getByTestId("approval-alert")).toBeInTheDocument();
    });

    test("shows approval alert for awaiting approval transfers with approvers", () => {
        // Use a transfer with Awaiting Approval status
        const transferWithApprovers = {
            ...singleTransfer,
            status: "Awaiting Approval",
        };
        // Pass approval data to show the alert
        setupTest(transferWithApprovers, {}, mockApprovalData);
        expect(screen.getByTestId("approval-alert")).toBeInTheDocument();
    });

    test("changes to People involved tab when See approvers is clicked", () => {
        // Use a transfer with approvers so the alert shows up
        const transferWithApprovers = {
            ...singleTransfer,
            status: "Awaiting Approval",
        };
        setupTest(transferWithApprovers, {}, mockApprovalData);
        fireEvent.click(screen.getByTestId("see-approvers-button"));
        // The TabSection mock would update activeDetailTab if this were a real change
        expect(screen.getByTestId("people-tab")).toBeInTheDocument();
    });

    test("dispatches updateTransferStatus for scheduled transfers", () => {
        setupTest(singleTransfer, { activeTab: "scheduled" });
        fireEvent.click(screen.getByTestId("cancel-button"));
        expect(updateTransferStatus).toHaveBeenCalledWith({
            transferScheduledId: singleTransfer.transferScheduledId,
            transferScheduleStatus: "CANCELLED",
            paymentType: "SCHEDULED",
            isUserInitiated: true,
        });
    });

    test("dispatches updateTransferStatus for recurring transfers", () => {
        setupTest(singleTransfer, { activeTab: "recurring" });
        fireEvent.click(screen.getByTestId("cancel-button"));
        expect(updateTransferStatus).toHaveBeenCalledWith({
            transferScheduledId: singleTransfer.transferScheduledId,
            transferScheduleStatus: "CANCELLED",
            paymentType: "RECURRING",
            isUserInitiated: true,
        });
    });

    test("calls fetchScheduledTransfers after status update for scheduled tab", () => {
        setupTest(singleTransfer, { activeTab: "scheduled" });
        fireEvent.click(screen.getByTestId("cancel-button"));

        // Fast-forward timers
        jest.advanceTimersByTime(1500);

        expect(fetchScheduledTransfers).toHaveBeenCalledWith({
            pageNo: 0,
            pageSize: 10,
            paymentType: "SCHEDULED",
        });
    });

    test("calls fetchRecurringTransfers after status update for recurring tab", () => {
        setupTest(singleTransfer, { activeTab: "recurring" });
        fireEvent.click(screen.getByTestId("cancel-button"));

        // Fast-forward timers
        jest.advanceTimersByTime(1500);

        expect(fetchRecurringTransfers).toHaveBeenCalledWith({
            pageNo: 0,
            pageSize: 10,
            paymentType: "RECURRING",
        });
    });

    test("handles transfers with missing transferScheduledId", () => {
        const transferWithoutScheduledId = { ...singleTransfer, transferScheduledId: undefined };
        setupTest(transferWithoutScheduledId, { activeTab: "scheduled" });
        fireEvent.click(screen.getByTestId("cancel-button"));
        expect(updateTransferStatus).toHaveBeenCalledWith({
            transferScheduledId: transferWithoutScheduledId.paymentRequestId,
            transferScheduleStatus: "CANCELLED",
            paymentType: "SCHEDULED",
            isUserInitiated: true,
        });
    });

    test("shows error if transfer has no ID", () => {
        const invalidTransfer = { ...singleTransfer, transferScheduledId: null, paymentRequestId: null };
        setupTest(invalidTransfer, { activeTab: "scheduled" });
        fireEvent.click(screen.getByTestId("cancel-button"));
        expect(sendCatchFeedback).toHaveBeenCalledWith(expect.any(Error));
    });

    test("closes drawer after status update", () => {
        setupTest(singleTransfer, { activeTab: "scheduled" });
        fireEvent.click(screen.getByTestId("cancel-button"));

        // Fast-forward timers
        jest.advanceTimersByTime(1000);

        expect(defaultProps.handleCloseDetails).toHaveBeenCalled();
    });

    describe("Tab Navigation Integration", () => {
        test("should render with Details tab active by default", () => {
            setupTest(singleTransfer);

            // The TabSection mock should render the Details tab
            expect(screen.getByTestId("details-tab")).toBeInTheDocument();
        });

        test("should allow switching to People involved tab", () => {
            setupTest(singleTransfer);

            const peopleTab = screen.getByTestId("people-tab");
            expect(peopleTab).toBeInTheDocument();

            // Click should be handled by the TabSection mock
            fireEvent.click(peopleTab);
            expect(peopleTab).toBeInTheDocument();
        });

        test("should navigate to People involved tab when 'See approvers' button is clicked", () => {
            // Use a transfer with approvers so the alert shows up
            const transferWithApprovers = {
                ...singleTransfer,
                status: "Awaiting Approval",
            };
            setupTest(transferWithApprovers, {}, mockApprovalData);

            // Should show approval alert
            expect(screen.getByTestId("approval-alert")).toBeInTheDocument();

            // Click the "See approvers" button
            const seeApproversButton = screen.getByTestId("see-approvers-button");
            fireEvent.click(seeApproversButton);

            // The TabSection should still be present (navigation handled internally)
            expect(screen.getByTestId("people-tab")).toBeInTheDocument();
        });

        test("should show Recipients tab for bulk transfers", () => {
            setupTest(bulkTransfer);

            // Should show recipients tab for bulk transfers
            expect(screen.getByTestId("recipients-tab")).toBeInTheDocument();
        });

        test("should show Instances tab for recurring transfers", () => {
            setupTest(singleTransfer, { activeTab: "recurring" });

            // Should show instances tab for recurring transfers
            expect(screen.getByTestId("instances-tab")).toBeInTheDocument();
        });

        test("should handle tab navigation when no approvers are present", () => {
            setupTest(singleTransfer); // This uses default transfer ID with no approvers

            // Should not show approval alert when there are no approvers
            expect(screen.queryByTestId("approval-alert")).not.toBeInTheDocument();

            // But tab navigation should still work
            expect(screen.getByTestId("people-tab")).toBeInTheDocument();
            fireEvent.click(screen.getByTestId("people-tab"));
            expect(screen.getByTestId("people-tab")).toBeInTheDocument();
        });

        test("should not display approval alert for non-pending transfers", () => {
            const successfulTransfer = {
                ...singleTransfer,
                status: "Successful",
            };
            // Even with approval data, shouldn't show alert for non-Awaiting Approval status
            setupTest(successfulTransfer, {}, mockApprovalData);

            // Should not show approval alert for successful transfers
            expect(screen.queryByTestId("approval-alert")).not.toBeInTheDocument();
        });

        test("should handle drawer state correctly when closed", () => {
            setupTest(singleTransfer, { isOpen: false });

            // Drawer should not render content when closed
            expect(screen.queryByTestId("tab-section")).not.toBeInTheDocument();
        });

        test("should preserve tab functionality across different transfer types", () => {
            // Test single transfer
            const { rerender } = setupTest(singleTransfer);
            expect(screen.getByTestId("details-tab")).toBeInTheDocument();
            expect(screen.getByTestId("people-tab")).toBeInTheDocument();
            expect(screen.queryByTestId("recipients-tab")).not.toBeInTheDocument();

            // Test bulk transfer - rerender with same store setup
            const store = mockStore({
                transfer: {
                    updateTransferStatusLoading: false,
                    downloadReceiptLoading: false,
                },
                sendMoney: {
                    getTransferApprovers: {
                        data: null,
                        loading: false,
                        error: null,
                    },
                },
            });

            rerender(
                <Provider store={store}>
                    <OutgoingDetails
                        transfer={bulkTransfer}
                        isOpen={true}
                        handleCloseDetails={jest.fn()}
                        activeTab="instant"
                    />
                </Provider>
            );

            expect(screen.getByTestId("details-tab")).toBeInTheDocument();
            expect(screen.getByTestId("people-tab")).toBeInTheDocument();
            expect(screen.getByTestId("recipients-tab")).toBeInTheDocument();
        });

        test("should show approval alert only in Details and Instances tabs", () => {
            const transferWithApprovers = {
                ...singleTransfer,
                status: "Pending",
            };
            setupTest(transferWithApprovers, {}, mockApprovalData);

            // Should show approval alert in Details tab by default
            expect(screen.getByTestId("approval-alert")).toBeInTheDocument();
        });

        test("should handle transfers with zero transferScheduledId", () => {
            const transferWithZeroId = {
                ...singleTransfer,
                transferScheduledId: 0,
                paymentRequestId: 456,
            };
            setupTest(transferWithZeroId, { activeTab: "scheduled" });
            fireEvent.click(screen.getByTestId("cancel-button"));
            expect(updateTransferStatus).toHaveBeenCalledWith({
                transferScheduledId: 0,
                transferScheduleStatus: "CANCELLED",
                paymentType: "SCHEDULED",
                isUserInitiated: true,
            });
        });

        test("should not show footer for Unknown status transfers", () => {
            const unknownTransfer = {
                ...singleTransfer,
                status: "Unknown",
            };
            setupTest(unknownTransfer);
            expect(screen.queryByTestId("footer-section")).not.toBeInTheDocument();
        });

        test("should use bulkTransactionRef over transactionRef for people involved hook", () => {
            const transferWithBulkRef = {
                ...singleTransfer,
                bulkTransactionRef: "BULK123",
                transactionRef: "TRANS456",
            };
            setupTest(transferWithBulkRef, {}, mockApprovalData);

            expect(useApprovalRulePeopleInvolved).toHaveBeenCalledWith({
                amount: transferWithBulkRef.amount,
                type: "TRANSFER",
                reference: "BULK123",
                enabled: true,
            });
        });

        test("should calculate approval counts correctly", () => {
            const transferWithApprovers = {
                ...singleTransfer,
                status: "Awaiting Approval",
            };
            setupTest(transferWithApprovers, {}, mockApprovalData);

            // Should show 1 of 3 approvals (only one person has APPROVE status)
            expect(screen.getByTestId("approval-count")).toHaveTextContent("1 of 3");
        });
    });

    describe("Footer Integration", () => {
        test("should pass activeTab prop correctly to footer section", () => {
            // Test with scheduled tab
            setupTest(singleTransfer, { activeTab: "scheduled" });
            const footer = screen.getByTestId("footer-section");
            expect(footer).toBeInTheDocument();

            // The footer mock should have pause button for scheduled tab
            expect(screen.getByTestId("pause-button")).toBeInTheDocument();
        });

        test("should pass activeTab prop correctly for recurring tab", () => {
            // Test with recurring tab
            setupTest(singleTransfer, { activeTab: "recurring" });
            const footer = screen.getByTestId("footer-section");
            expect(footer).toBeInTheDocument();

            // The footer mock should have pause button for recurring tab
            expect(screen.getByTestId("pause-button")).toBeInTheDocument();
        });

        test("should conditionally render footer based on transfer status", () => {
            // Test with Unknown status - should not render footer
            const unknownTransfer = {
                ...singleTransfer,
                status: "Unknown",
            };
            setupTest(unknownTransfer);
            expect(screen.queryByTestId("footer-section")).not.toBeInTheDocument();

            // Test with cancelled status - footer should render but with no buttons
            setupTest(cancelledTransfer);
            const footer = screen.getByTestId("footer-section");
            expect(footer).toBeInTheDocument();
            expect(screen.queryByTestId("cancel-button")).not.toBeInTheDocument();
        });

        test("should update footer when transfer status changes", () => {
            const { rerender } = setupTest(singleTransfer);

            // Initially should have cancel button
            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();

            // Update status to cancelled
            const store = mockStore({
                transfer: {
                    updateTransferStatusLoading: false,
                    downloadReceiptLoading: false,
                },
                sendMoney: {
                    getTransferApprovers: {
                        data: null,
                        loading: false,
                        error: null,
                    },
                },
            });

            rerender(
                <Provider store={store}>
                    <OutgoingDetails {...defaultProps} transfer={cancelledTransfer} />
                </Provider>
            );

            // Should not have cancel button after status change
            expect(screen.queryByTestId("cancel-button")).not.toBeInTheDocument();
        });

        test("should pass correct activeDetailTab to footer", () => {
            setupTest(singleTransfer);

            // Click on People tab
            fireEvent.click(screen.getByTestId("people-tab"));

            // Footer should still be rendered with correct props
            expect(screen.getByTestId("footer-section")).toBeInTheDocument();
        });

        test("should handle footer actions correctly for different tabs", () => {
            // Test with scheduled tab
            setupTest(singleTransfer, { activeTab: "scheduled" });

            // Click pause button
            fireEvent.click(screen.getByTestId("pause-button"));

            // Should dispatch update with PAUSED status
            expect(updateTransferStatus).toHaveBeenCalledWith({
                transferScheduledId: singleTransfer.transferScheduledId,
                transferScheduleStatus: "PAUSED",
                paymentType: "SCHEDULED",
                isUserInitiated: true,
            });
        });

        test("should integrate footer with tab switching", () => {
            const { rerender } = setupTest(singleTransfer, { activeTab: "instant" });

            // Initially no pause button for instant
            expect(screen.queryByTestId("pause-button")).not.toBeInTheDocument();

            // Switch to scheduled tab
            const store = mockStore({
                transfer: {
                    updateTransferStatusLoading: false,
                    downloadReceiptLoading: false,
                },
                sendMoney: {
                    getTransferApprovers: {
                        data: null,
                        loading: false,
                        error: null,
                    },
                },
            });

            rerender(
                <Provider store={store}>
                    <OutgoingDetails {...defaultProps} transfer={singleTransfer} activeTab="scheduled" />
                </Provider>
            );

            // Should now have pause button
            expect(screen.getByTestId("pause-button")).toBeInTheDocument();
        });

        test("should maintain footer functionality across different transfer types", () => {
            // Test single transfer
            const { rerender } = setupTest(singleTransfer, { activeTab: "scheduled" });
            expect(screen.getByTestId("footer-section")).toBeInTheDocument();
            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();

            // Switch to bulk transfer
            const store = mockStore({
                transfer: {
                    updateTransferStatusLoading: false,
                    downloadReceiptLoading: false,
                },
                sendMoney: {
                    getTransferApprovers: {
                        data: null,
                        loading: false,
                        error: null,
                    },
                },
            });

            rerender(
                <Provider store={store}>
                    <OutgoingDetails {...defaultProps} transfer={bulkTransfer} activeTab="scheduled" />
                </Provider>
            );

            // Footer should still work for bulk transfers
            expect(screen.getByTestId("footer-section")).toBeInTheDocument();
            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();
        });
    });
});
