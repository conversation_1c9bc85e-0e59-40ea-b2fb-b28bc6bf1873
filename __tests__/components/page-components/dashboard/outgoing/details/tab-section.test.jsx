import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { TabSection } from "@/components/page-components/dashboard/outgoing/details/tab-section";

// Mock the child components
jest.mock("@/components/common/tab-switch", () => ({
    __esModule: true,
    default: ({ tabs, activeTab, onChange, panels = [], tabSpacing }) => {
        // Find the index of the active tab, defaulting to 0 if not found
        const activeIndex = tabs.indexOf(activeTab) !== -1 ? tabs.indexOf(activeTab) : 0;

        return (
            <div data-testid="tab-switch" data-tab-spacing={tabSpacing}>
                <div data-testid="tabs">
                    {tabs.map((tab, index) => (
                        <button
                            key={index}
                            data-testid={`tab-${tab.toLowerCase().replace(/\s+/g, "-")}`}
                            onClick={() => onChange(tab)}
                        >
                            {tab}
                        </button>
                    ))}
                </div>
                <div data-testid="panels">{panels[activeIndex] || <div>No panel content</div>}</div>
            </div>
        );
    },
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/tabs/tab-details", () => ({
    __esModule: true,
    default: ({ transfer, activeTab }) => <div data-testid="details-tab">Details Tab Content</div>,
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/tabs/tab-people", () => ({
    __esModule: true,
    default: ({ transfer }) => <div data-testid="people-tab">People Tab Content</div>,
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/tabs/tab-instances", () => ({
    __esModule: true,
    default: ({ transfer, onDownloadReceipt }) => (
        <div data-testid="instances-tab">
            Instances Tab Content
            <button data-testid="instances-download-btn" onClick={() => onDownloadReceipt && onDownloadReceipt("123")}>
                Download
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/outgoing/details/tabs/tab-recipients", () => ({
    __esModule: true,
    default: ({ transfer, activeTab }) => <div data-testid="recipients-tab">Recipients Tab Content</div>,
}));

describe("TabSection Component", () => {
    // Mock handler functions
    const mockHandleTabChange = jest.fn();

    // Save original console.log and process.env
    const originalConsoleLog = console.log;
    const originalNodeEnv = process.env.NODE_ENV;

    // Mock console.log for testing
    let consoleOutput = [];
    const mockConsoleLog = (...args) => {
        consoleOutput.push(args);
    };

    beforeEach(() => {
        // Set up console.log mock
        console.log = mockConsoleLog;
        consoleOutput = [];

        // Set NODE_ENV to development for testing logs
        process.env.NODE_ENV = "development";
    });

    afterEach(() => {
        // Reset mocks after each test
        jest.clearAllMocks();

        // Restore original console.log and NODE_ENV
        console.log = originalConsoleLog;
        process.env.NODE_ENV = originalNodeEnv;
    });

    test("renders with default tabs for non-recurring transfers", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
            // Not a bulk transfer
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="sent"
                activeDetailTab="Details"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should have Details and People involved tabs
        expect(screen.getByTestId("tab-details")).toBeInTheDocument();
        expect(screen.getByTestId("tab-people-involved")).toBeInTheDocument();

        // Should not have Instances tab
        expect(screen.queryByTestId("tab-instances")).not.toBeInTheDocument();

        // Should not have Recipients tab (not a bulk transfer)
        expect(screen.queryByTestId("tab-recipients")).not.toBeInTheDocument();
    });

    test("renders with Instances tab for recurring transfers", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
            // Not a bulk transfer
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="recurring"
                activeDetailTab="Details"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should have all three tabs
        expect(screen.getByTestId("tab-details")).toBeInTheDocument();
        expect(screen.getByTestId("tab-instances")).toBeInTheDocument();
        expect(screen.getByTestId("tab-people-involved")).toBeInTheDocument();

        // Should not have Recipients tab (not a bulk transfer)
        expect(screen.queryByTestId("tab-recipients")).not.toBeInTheDocument();
    });

    test("renders Recipients tab for bulk transfers", () => {
        const mockBulkTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
            totalTransfers: 5, // Bulk transfer with multiple recipients
        };

        render(
            <TabSection
                transfer={mockBulkTransfer}
                activeTab="sent"
                activeDetailTab="Details"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should have Details, People involved, and Recipients tabs
        expect(screen.getByTestId("tab-details")).toBeInTheDocument();
        expect(screen.getByTestId("tab-people-involved")).toBeInTheDocument();
        expect(screen.getByTestId("tab-recipients")).toBeInTheDocument();
    });

    test("uses correct tabSpacing when Recipients tab is active", () => {
        const mockBulkTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
            totalTransfers: 5, // Bulk transfer with multiple recipients
        };

        render(
            <TabSection
                transfer={mockBulkTransfer}
                activeTab="sent"
                activeDetailTab="Recipients" // Recipients tab is active
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should use smaller tab spacing for Recipients tab
        expect(screen.getByTestId("tab-switch")).toHaveAttribute("data-tab-spacing", "h-[20px]");
    });

    test("uses default tabSpacing when Recipients tab is not active", () => {
        const mockBulkTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
            totalTransfers: 5, // Bulk transfer with multiple recipients
        };

        render(
            <TabSection
                transfer={mockBulkTransfer}
                activeTab="sent"
                activeDetailTab="Details" // Details tab is active
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should use default tab spacing
        expect(screen.getByTestId("tab-switch")).toHaveAttribute("data-tab-spacing", "h-[32px]");
    });

    test("calls handleTabChange when a tab is clicked", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="sent"
                activeDetailTab="Details"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Click on People involved tab
        fireEvent.click(screen.getByTestId("tab-people-involved"));

        // Should call handleTabChange with "People involved"
        expect(mockHandleTabChange).toHaveBeenCalledWith("People involved");
    });

    test("renders Details tab when activeDetailTab is 'Details'", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="sent"
                activeDetailTab="Details"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should render the Details tab content
        expect(screen.getByTestId("details-tab")).toBeInTheDocument();
        expect(screen.getByText("Details Tab Content")).toBeInTheDocument();
    });

    // Tests for OutgoingInstancesTab callbacks
    test("has the Instances tab available for recurring transfers", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="recurring"
                activeDetailTab="Instances"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Should render the Instances tab content
        expect(screen.getByTestId("instances-tab")).toBeInTheDocument();
        expect(screen.getByText("Instances Tab Content")).toBeInTheDocument();
    });

    test("handles onDownloadReceipt callback from Instances tab", () => {
        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="recurring"
                activeDetailTab="Instances"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Click on download button in instances tab
        fireEvent.click(screen.getByTestId("instances-download-btn"));

        // The callback is currently empty in the implementation, so no console logs are expected
        expect(consoleOutput.length).toBe(0);
    });

    test("does not log to console in production mode", () => {
        // Set NODE_ENV to production
        process.env.NODE_ENV = "production";

        const mockTransfer = {
            id: "123",
            status: "pending",
            amount: 1000,
            date: "2023-01-01",
        };

        render(
            <TabSection
                transfer={mockTransfer}
                activeTab="recurring"
                activeDetailTab="Instances"
                handleTabChange={mockHandleTabChange}
            />
        );

        // Click on download button in instances tab
        fireEvent.click(screen.getByTestId("instances-download-btn"));

        // Should not log to console in production mode
        expect(consoleOutput.length).toBe(0);
    });
});
