import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ErrorState from "@/components/page-components/dashboard/outgoing/list/error-state";

// Mock the Redux hooks and actions
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
}));

jest.mock("@/redux/features/supportDialog", () => ({
    openSupportDialog: jest.fn(),
}));

// Mock the icons to avoid SVG rendering issues in tests
jest.mock("@/components/icons/outgoing", () => ({
    WarningIcon: () => <div data-testid="warning-icon" />,
    ErrorCircleBackgroundIcon: () => <div data-testid="error-circle-background-icon" />,
}));

describe("ErrorState Component", () => {
    const mockOnRetry = jest.fn();
    const mockDispatch = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        // Set up the mock dispatch function
        require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);
    });

    it("renders correctly with all props", () => {
        render(<ErrorState onRetry={mockOnRetry} />);

        // Check if the component renders with the correct text
        expect(screen.getByText("Error fetching payments")).toBeInTheDocument();
        expect(
            screen.getByText(
                "We're having trouble fetching your payments at this time. Please try again or check your connection."
            )
        ).toBeInTheDocument();

        // Check if buttons are rendered
        expect(screen.getByText("Try again")).toBeInTheDocument();
        expect(screen.getByText("Get support")).toBeInTheDocument();

        // Check if icons are rendered
        expect(screen.getByTestId("warning-icon")).toBeInTheDocument();
        expect(screen.getByTestId("error-circle-background-icon")).toBeInTheDocument();
    });

    it("calls onRetry when the retry button is clicked", () => {
        render(<ErrorState onRetry={mockOnRetry} />);

        // Click the retry button
        fireEvent.click(screen.getByText("Try again"));

        // Check if onRetry was called
        expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it("dispatches openSupportDialog when the support button is clicked", () => {
        const { openSupportDialog } = require("@/redux/features/supportDialog");

        render(<ErrorState onRetry={mockOnRetry} />);

        // Click the support button
        fireEvent.click(screen.getByText("Get support"));

        // Check if dispatch was called with openSupportDialog
        expect(mockDispatch).toHaveBeenCalledTimes(1);
        expect(openSupportDialog).toHaveBeenCalledTimes(1);
    });

    it("always shows user-friendly message regardless of backend error", () => {
        // Test that even if we previously passed error props, the component always shows the correct message
        render(<ErrorState onRetry={mockOnRetry} />);

        // Should always show the user-friendly message, never "Rejected" or other backend errors
        expect(screen.getByText("Error fetching payments")).toBeInTheDocument();
        expect(
            screen.getByText(
                "We're having trouble fetching your payments at this time. Please try again or check your connection."
            )
        ).toBeInTheDocument();

        // Should not show any potentially problematic text like "Rejected"
        expect(screen.queryByText("Rejected")).not.toBeInTheDocument();
        expect(screen.queryByText("Failed")).not.toBeInTheDocument();
    });
});
