import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { getOutgoingColumns } from "@/components/page-components/dashboard/outgoing/list/outgoing-column-data";

// Mock the table-more-action component
jest.mock("@/components/common/table/table-more-action", () => ({
    __esModule: true,
    default: ({ menuItems, data }) => (
        <div data-testid="table-more-action">
            {menuItems.map((item, index) => (
                <button
                    key={index}
                    data-testid={`menu-item-${item.label.toLowerCase().replace(/\s+/g, "-")}`}
                    onClick={() => item.onClick(data)}
                    disabled={item.disabled}
                >
                    {item.label}
                </button>
            ))}
        </div>
    ),
}));

// Mock other dependencies
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ checked, onCheckedChange, indeterminate, ...props }) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
            {...props}
        />
    ),
}));

jest.mock("@/components/icons/outgoing", () => ({
    ArrowDownIcon: () => <span>↓</span>,
}));

jest.mock("@/components/common/badge", () => ({
    __esModule: true,
    default: ({ text }) => <span data-testid="badge">{text}</span>,
}));

jest.mock("@/functions/date", () => ({
    formatDateLong: (date) => date || "formatted date",
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: (amount) => `₦${amount}`,
}));

jest.mock("@/components/page-components/dashboard/outgoing/utils/statusUtils", () => ({
    isBulkTransfer: () => false,
}));

jest.mock("@/utils/status-mapping", () => ({
    getStatusMapping: (status) => ({
        text: status,
        color: status === "Successful" ? "success" : "neutral",
    }),
    isSuccessfulStatus: (status) => status === "Successful",
}));

describe("getOutgoingColumns", () => {
    const mockTableMeta = {
        setIsDetailsOpen: jest.fn(),
        setSelectedTransfer: jest.fn(),
        handleDownloadReceipt: jest.fn(),
        downloadReceiptLoading: false,
        getIsAllPageRowsSelected: jest.fn(() => false),
        toggleAllPageRowsSelected: jest.fn(),
        getIsSomePageRowsSelected: jest.fn(() => false),
    };

    const createMockTransfer = (status = "Pending", overrides = {}) => ({
        id: "1",
        paymentRequestId: 123,
        transferScheduledId: 456,
        date: "2024-01-01",
        counterparty: "John Doe",
        bank: "Test Bank",
        accountNumber: "**********",
        narration: "Test transfer",
        status,
        amount: 10000,
        frequency: "Monthly",
        ...overrides,
    });

    const createMockTable = (meta = mockTableMeta) => ({
        options: {
            meta,
        },
        getIsAllPageRowsSelected: mockTableMeta.getIsAllPageRowsSelected,
        toggleAllPageRowsSelected: mockTableMeta.toggleAllPageRowsSelected,
        getIsSomePageRowsSelected: mockTableMeta.getIsSomePageRowsSelected,
    });

    const createMockRow = (transfer) => ({
        original: transfer,
        getIsSelected: jest.fn(() => false),
        toggleSelected: jest.fn(),
    });

    const createMockColumn = () => ({
        getIsSorted: jest.fn(() => false),
        toggleSorting: jest.fn(),
    });

    const renderColumn = (columnId, transfer, activeTab = "instant", mockTable = null) => {
        const columns = getOutgoingColumns(activeTab);
        const column = columns.find((col) => col.id === columnId || col.accessorKey === columnId);
        const Cell = column.cell;

        const mockRow = createMockRow(transfer);
        const table = mockTable || createMockTable();

        return render(<Cell row={mockRow} table={table} column={createMockColumn()} />);
    };

    const renderHeader = (columnId, activeTab = "instant") => {
        const columns = getOutgoingColumns(activeTab);
        const column = columns.find((col) => col.id === columnId || col.accessorKey === columnId);

        if (typeof column.header === "function") {
            const Header = column.header;
            const mockColumn = createMockColumn();
            const table = createMockTable();
            return render(<Header column={mockColumn} table={table} />);
        }

        return null;
    };

    const renderActionsCell = (transfer, activeTab = "instant") => {
        return renderColumn("actions", transfer, activeTab);
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Menu Items", () => {
        it("should always display View button", () => {
            const transfer = createMockTransfer();
            renderActionsCell(transfer);

            expect(screen.getByTestId("menu-item-view")).toBeInTheDocument();
        });

        it("should display Download receipt button only for successful transfers", () => {
            // Test with successful transfer
            const successfulTransfer = createMockTransfer("Successful");
            const { rerender } = renderActionsCell(successfulTransfer);

            expect(screen.getByTestId("menu-item-download-receipt")).toBeInTheDocument();

            // Test with non-successful transfers
            const statuses = ["Pending", "Failed", "Processing", "Awaiting Approval", "Rejected Approval"];

            statuses.forEach((status) => {
                const transfer = createMockTransfer(status);
                const columns = getOutgoingColumns("instant");
                const actionsColumn = columns.find((col) => col.id === "actions");
                const Cell = actionsColumn.cell;

                rerender(<Cell row={{ original: transfer }} table={createMockTable()} />);

                expect(screen.queryByTestId("menu-item-download-receipt")).not.toBeInTheDocument();
            });
        });

        it("should display Report button for all transfers", () => {
            const statuses = ["Successful", "Pending", "Failed", "Processing"];

            statuses.forEach((status) => {
                const transfer = createMockTransfer(status);
                const { unmount } = renderActionsCell(transfer);

                expect(screen.getByTestId("menu-item-report")).toBeInTheDocument();
                unmount();
            });
        });

        it("should call download receipt handler when clicked for successful transfer", () => {
            const successfulTransfer = createMockTransfer("Successful");
            renderActionsCell(successfulTransfer);

            const downloadButton = screen.getByTestId("menu-item-download-receipt");
            fireEvent.click(downloadButton);

            expect(mockTableMeta.handleDownloadReceipt).toHaveBeenCalledWith(successfulTransfer);
        });

        it("should disable download receipt button when loading", () => {
            const successfulTransfer = createMockTransfer("Successful");
            mockTableMeta.downloadReceiptLoading = true;

            renderActionsCell(successfulTransfer);

            const downloadButton = screen.getByTestId("menu-item-download-receipt");
            expect(downloadButton).toBeDisabled();

            mockTableMeta.downloadReceiptLoading = false;
        });

        it("should do nothing when Report button is clicked", () => {
            const transfer = createMockTransfer();
            renderActionsCell(transfer);

            const reportButton = screen.getByTestId("menu-item-report");

            // Clicking report button should not throw or call any handlers
            expect(() => fireEvent.click(reportButton)).not.toThrow();

            // Verify no handlers were called
            expect(mockTableMeta.setIsDetailsOpen).not.toHaveBeenCalled();
            expect(mockTableMeta.setSelectedTransfer).not.toHaveBeenCalled();
            expect(mockTableMeta.handleDownloadReceipt).not.toHaveBeenCalled();
        });

        it("should call view handler when View button is clicked", () => {
            const transfer = createMockTransfer();
            renderActionsCell(transfer);

            const viewButton = screen.getByTestId("menu-item-view");
            fireEvent.click(viewButton);

            expect(mockTableMeta.setSelectedTransfer).toHaveBeenCalledWith(transfer);
            expect(mockTableMeta.setIsDetailsOpen).toHaveBeenCalledWith(true);
        });

        it("should maintain correct order of menu items", () => {
            const successfulTransfer = createMockTransfer("Successful");
            renderActionsCell(successfulTransfer);

            const menuItems = screen.getAllByTestId(/^menu-item-/);

            // Expected order: View, Download receipt (for successful), Report
            expect(menuItems[0]).toHaveAttribute("data-testid", "menu-item-view");
            expect(menuItems[1]).toHaveAttribute("data-testid", "menu-item-download-receipt");
            expect(menuItems[2]).toHaveAttribute("data-testid", "menu-item-report");
        });
    });

    describe("Column Structure", () => {
        it("should include all required columns for instant tab", () => {
            const columns = getOutgoingColumns("instant");
            const columnIds = columns.map((col) => col.id || col.accessorKey);

            expect(columnIds).toContain("select");
            expect(columnIds).toContain("date");
            expect(columnIds).toContain("counterparty");
            expect(columnIds).toContain("narration");
            expect(columnIds).toContain("status");
            expect(columnIds).toContain("amount");
            expect(columnIds).toContain("actions");
        });

        it("should include frequency column for recurring tab", () => {
            const columns = getOutgoingColumns("recurring");
            const columnIds = columns.map((col) => col.id || col.accessorKey);

            expect(columnIds).toContain("frequency");
        });
    });

    describe("Selection Column", () => {
        it("should render select header with proper checkbox functionality", () => {
            const mockTable = createMockTable();
            const columns = getOutgoingColumns("instant");
            const selectColumn = columns.find((col) => col.id === "select");
            const Header = selectColumn.header;

            render(<Header table={mockTable} />);

            const checkbox = screen.getByRole("checkbox");
            expect(checkbox).toBeInTheDocument();

            // Test checkbox interaction
            fireEvent.click(checkbox);
            expect(mockTableMeta.toggleAllPageRowsSelected).toHaveBeenCalled();
        });

        it("should render select cell with row selection functionality", () => {
            const transfer = createMockTransfer();
            renderColumn("select", transfer);

            const checkbox = screen.getByRole("checkbox");
            expect(checkbox).toBeInTheDocument();

            // Test row selection
            fireEvent.click(checkbox);
            // The mock should be called through the row.toggleSelected function
        });

        it("should handle indeterminate state in select header", () => {
            const mockMetaWithIndeterminate = {
                ...mockTableMeta,
                getIsSomePageRowsSelected: jest.fn(() => true),
                getIsAllPageRowsSelected: jest.fn(() => false),
            };

            const mockTable = createMockTable(mockMetaWithIndeterminate);
            renderColumn("select", createMockTransfer(), "instant", mockTable);

            const checkbox = screen.getByRole("checkbox");
            expect(checkbox).toBeInTheDocument();
        });
    });

    describe("Date Column", () => {
        it("should render date header with sorting functionality for instant tab", () => {
            const mockColumn = createMockColumn();
            mockColumn.getIsSorted = jest.fn(() => "asc");

            // Create custom render for this test to pass the specific column
            const columns = getOutgoingColumns("instant");
            const dateColumn = columns.find((col) => col.accessorKey === "date");
            const Header = dateColumn.header;
            const table = createMockTable();

            render(<Header column={mockColumn} table={table} />);

            expect(screen.getByText("Date")).toBeInTheDocument();
            expect(screen.getByText("↓")).toBeInTheDocument(); // Arrow icon

            const sortButton = screen.getByRole("button");
            fireEvent.click(sortButton);
            // The logic is: column.toggleSorting(column.getIsSorted() === "asc")
            // If currently "asc", then pass true to toggle to desc
            // If not "asc", then pass false to toggle to asc
            expect(mockColumn.toggleSorting).toHaveBeenCalledWith(true); // Should toggle to desc since current is asc
        });

        it("should render date header with 'Date created' label for recurring tab", () => {
            renderHeader("date", "recurring");

            expect(screen.getByText("Date created")).toBeInTheDocument();
        });

        it("should render formatted date in cell", () => {
            const transfer = createMockTransfer("Pending");
            renderColumn("date", transfer);

            expect(screen.getByText("2024-01-01")).toBeInTheDocument();
        });
    });

    describe("Counterparty Column", () => {
        it("should render regular transfer counterparty info", () => {
            const transfer = createMockTransfer("Pending");
            renderColumn("counterparty", transfer);

            expect(screen.getByText("John Doe")).toBeInTheDocument();
            expect(screen.getByText("Test Bank, **********")).toBeInTheDocument();
        });

        it("should render bulk transfer counterparty info", () => {
            // Mock isBulkTransfer to return true for this test
            const originalIsBulkTransfer =
                require("@/components/page-components/dashboard/outgoing/utils/statusUtils").isBulkTransfer;
            require("@/components/page-components/dashboard/outgoing/utils/statusUtils").isBulkTransfer = jest.fn(
                () => true
            );

            const transfer = createMockTransfer("Pending", { totalTransfers: 5 });
            renderColumn("counterparty", transfer);

            expect(screen.getByText("Bulk Transfer")).toBeInTheDocument();
            expect(screen.getByText("5 recipients")).toBeInTheDocument();

            // Restore original function
            require("@/components/page-components/dashboard/outgoing/utils/statusUtils").isBulkTransfer =
                originalIsBulkTransfer;
        });

        it("should handle missing bank and account number gracefully", () => {
            const transfer = createMockTransfer("Pending", { bank: null, accountNumber: null });
            renderColumn("counterparty", transfer);

            expect(screen.getByText("John Doe")).toBeInTheDocument();
            expect(screen.getByText(", null")).toBeInTheDocument(); // Shows null values as strings
        });
    });

    describe("Narration Column", () => {
        it("should render transfer narration", () => {
            const transfer = createMockTransfer("Pending");
            renderColumn("narration", transfer);

            expect(screen.getByText("Test transfer")).toBeInTheDocument();
        });
    });

    describe("Status Column", () => {
        it("should render status badge for successful transfer", () => {
            const transfer = createMockTransfer("Successful");
            renderColumn("status", transfer);

            expect(screen.getByTestId("badge")).toBeInTheDocument();
            expect(screen.getByText("Successful")).toBeInTheDocument();
        });

        it("should render status badge for pending transfer", () => {
            const transfer = createMockTransfer("Pending");
            renderColumn("status", transfer);

            expect(screen.getByTestId("badge")).toBeInTheDocument();
            expect(screen.getByText("Pending")).toBeInTheDocument();
        });
    });

    describe("Amount Column", () => {
        it("should render amount header with sorting functionality", () => {
            const mockColumn = createMockColumn();
            mockColumn.getIsSorted = jest.fn(() => false);

            // Create custom render for this test to pass the specific column
            const columns = getOutgoingColumns("instant");
            const amountColumn = columns.find((col) => col.accessorKey === "amount");
            const Header = amountColumn.header;
            const table = createMockTable();

            render(<Header column={mockColumn} table={table} />);

            expect(screen.getByText("Amount")).toBeInTheDocument();
            expect(screen.getByText("↓")).toBeInTheDocument(); // Arrow icon

            const sortButton = screen.getByRole("button");
            fireEvent.click(sortButton);
            // The logic is: column.toggleSorting(column.getIsSorted() === "asc")
            // If currently false (not "asc"), then pass false to toggle to asc
            expect(mockColumn.toggleSorting).toHaveBeenCalledWith(false); // Should toggle to asc since current is false (not "asc")
        });

        it("should render formatted amount in cell", () => {
            const transfer = createMockTransfer("Pending", { amount: 15000 });
            renderColumn("amount", transfer);

            expect(screen.getByText("₦15000")).toBeInTheDocument();
        });
    });

    describe("Frequency Column", () => {
        it("should render frequency for recurring transfers", () => {
            const transfer = createMockTransfer("Pending", { frequency: "Weekly" });
            renderColumn("frequency", transfer, "recurring");

            expect(screen.getByText("Weekly")).toBeInTheDocument();
        });

        it("should render N/A when frequency is not available", () => {
            const transfer = createMockTransfer("Pending", { frequency: null });
            renderColumn("frequency", transfer, "recurring");

            expect(screen.getByText("N/A")).toBeInTheDocument();
        });
    });
});
