import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import OutgoingList from "@/components/page-components/dashboard/outgoing/list/outgoing-list";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { sendCatchFeedback } from "@/functions/feedback";

// Store the original NODE_ENV
const originalNodeEnv = process.env.NODE_ENV;

// Mock the dependencies
jest.mock("next/navigation", () => {
    const pushMock = jest.fn();
    return {
        useSearchParams: jest.fn(() => ({
            get: jest.fn((param) => {
                if (param === "size") return "10";
                if (param === "page") return "1";
                return null;
            }),
        })),
        useRouter: jest.fn(() => ({
            push: pushMock,
        })),
        usePathname: jest.fn(() => "/dashboard/outgoing"),
    };
});

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

// Mock dynamic imports
jest.mock("next/dynamic", () => () => {
    const DynamicComponent = ({ isOpen, handleCloseDetails, transfer, activeTab, onRetry, onSendNow }) =>
        isOpen ? (
            <div data-testid="outgoing-details-modal">
                <button onClick={handleCloseDetails} data-testid="close-details-button">
                    Close
                </button>
                <button onClick={() => onRetry && onRetry("123")} data-testid="retry-button">
                    Retry
                </button>
                <button onClick={() => onSendNow && onSendNow("123")} data-testid="send-now-button">
                    Send Now
                </button>
            </div>
        ) : null;
    return DynamicComponent;
});

// Mock the DataTable component
jest.mock("@/components/common/table/DataTable", () => ({
    DataTable: ({ table, loading, emptyTabletitle, emptyTabledescription, onRowClick }) => {
        // Store the table meta functions so we can trigger them in tests
        if (table.options.meta) {
            window.tableMeta = table.options.meta;
        }

        return (
            <div data-testid="data-table">
                <div>Loading: {loading ? "true" : "false"}</div>
                {table?.getRowModel()?.rows?.length === 0 && !loading && (
                    <div data-testid="empty-state">
                        <div data-testid="empty-title">{emptyTabletitle}</div>
                        <div data-testid="empty-description">{emptyTabledescription}</div>
                    </div>
                )}
                {!table?.getRowModel()?.rows?.length === 0 && (
                    <>
                        <div data-testid="empty-title">{emptyTabletitle}</div>
                        <div data-testid="empty-description">{emptyTabledescription}</div>
                    </>
                )}
                {/* Mock table row to test row click functionality */}
                <div
                    data-testid="table-row"
                    onClick={() => onRowClick && onRowClick(mockTransfers[0])}
                    style={{ cursor: onRowClick ? "pointer" : "default" }}
                >
                    Test Row
                </div>
                <button
                    data-testid="view-details-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const transfer = mockTransfers[0];
                            window.tableMeta.setSelectedTransfer(transfer);
                            window.tableMeta.setIsDetailsOpen(true);
                        }
                    }}
                >
                    View Details
                </button>
                <button
                    data-testid="download-receipt-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const transfer = mockTransfers[0];
                            window.tableMeta.handleDownloadReceipt(transfer);
                        }
                    }}
                >
                    Download Receipt
                </button>
                <button
                    data-testid="download-receipt-null-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: null };
                            window.tableMeta.handleDownloadReceipt(invalidTransfer);
                        }
                    }}
                >
                    Download Receipt With Null ID
                </button>
                <button
                    data-testid="download-receipt-undefined-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: undefined };
                            window.tableMeta.handleDownloadReceipt(invalidTransfer);
                        }
                    }}
                >
                    Download Receipt With Undefined ID
                </button>
                <button
                    data-testid="download-receipt-zero-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const zeroIdTransfer = { ...mockTransfers[0], transferScheduledId: 0 };
                            window.tableMeta.handleDownloadReceipt(zeroIdTransfer);
                        }
                    }}
                >
                    Download Receipt With Zero ID
                </button>
            </div>
        );
    },
}));

// Mock the EmptyStateComponent
jest.mock("@/components/page-components/dashboard/outgoing/list/empty-state", () => ({
    __esModule: true,
    default: ({ activeTab }) => (
        <div data-testid="empty-state">
            <div>Empty state for {activeTab}</div>
        </div>
    ),
}));

// Mock the Pagination component
jest.mock("@/components/common/pagination", () => ({
    Pagination: ({ initialPage, initialItemsPerPage, onPageChange, onItemsPerPageChange, totalItems }) => (
        <div data-testid="pagination">
            <div>Page: {initialPage}</div>
            <div>Items per page: {initialItemsPerPage}</div>
            <div data-testid="total-items">Total items: {totalItems}</div>
            <button onClick={() => onPageChange(2)} data-testid="next-page-button">
                Next Page
            </button>
            <button onClick={() => onItemsPerPageChange(20)} data-testid="change-items-button">
                Change Items
            </button>
        </div>
    ),
}));

// Mock transfers data
const mockTransfers = [
    {
        id: "1",
        transferScheduledId: 12345,
        date: "2023-01-01T12:00:00Z",
        counterparty: "John Doe",
        accountNumber: "**********",
        bank: "FCMB",
        narration: "Test payment",
        status: "Successful",
        amount: 10000,
        transferType: "Intra-bank",
    },
    {
        id: "2",
        transferScheduledId: 12346,
        date: "2023-01-02T12:00:00Z",
        counterparty: "Jane Smith",
        accountNumber: "**********",
        bank: "GTBank",
        narration: "Test payment 2",
        status: "Failed",
        amount: 20000,
        transferType: "Inter-bank",
    },
];

// Mock Redux state
const mockReduxState = {
    transfer: {
        // Instant transfers
        transfersTotalElements: 10,
        transfersTotalPages: 2,
        transfersCurrentPage: 0,
        transfersHasNext: true,
        transfersHasPrevious: false,

        // Scheduled transfers
        scheduledTransfersTotalElements: 5,
        scheduledTransfersTotalPages: 1,
        scheduledTransfersCurrentPage: 0,
        scheduledTransfersHasNext: false,
        scheduledTransfersHasPrevious: false,

        // Recurring transfers
        recurringTransfersTotalElements: 3,
        recurringTransfersTotalPages: 1,
        recurringTransfersCurrentPage: 0,
        recurringTransfersHasNext: false,
        recurringTransfersHasPrevious: false,

        downloadReceiptLoading: false,
    },
};

describe("OutgoingList Component", () => {
    // Setup before each test
    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Setup dispatch mock
        const mockDispatch = jest.fn();
        useAppDispatch.mockReturnValue(mockDispatch);

        // Setup Redux state mock
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));

        // Setup window for table meta access
        window.tableMeta = null;

        // Mock console.log for development environment logs
        jest.spyOn(console, "log").mockImplementation(() => {});

        // Set NODE_ENV to test by default
        process.env.NODE_ENV = "test";
    });

    // Restore NODE_ENV after all tests
    afterAll(() => {
        process.env.NODE_ENV = originalNodeEnv;
    });

    test("renders data table with transfers", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        expect(screen.getByTestId("data-table")).toBeInTheDocument();
        expect(screen.getByTestId("pagination")).toBeInTheDocument();
    });

    test("renders empty state when transfers array is empty", () => {
        render(<OutgoingList transfers={[]} activeTab="instant" />);

        expect(screen.getByTestId("data-table")).toBeInTheDocument();
        expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        expect(screen.getByTestId("empty-title")).toHaveTextContent("No payments yet");
        expect(screen.getByTestId("empty-description")).toHaveTextContent(
            "Ready for your first payment? Click 'send money' to make your first payment."
        );
    });

    test("handles pagination page change", () => {
        const onPageChangeMock = jest.fn();

        render(<OutgoingList transfers={mockTransfers} activeTab="instant" onPageChange={onPageChangeMock} />);

        fireEvent.click(screen.getByTestId("next-page-button"));
        expect(onPageChangeMock).toHaveBeenCalledWith(2);
    });

    test("handles items per page change", () => {
        const onItemsPerPageChangeMock = jest.fn();

        render(
            <OutgoingList
                transfers={mockTransfers}
                activeTab="instant"
                onItemsPerPageChange={onItemsPerPageChangeMock}
            />
        );

        fireEvent.click(screen.getByTestId("change-items-button"));
        expect(onItemsPerPageChangeMock).toHaveBeenCalledWith(20);
    });

    test("skips filter update when filters haven't changed", () => {
        // Access router push mock directly from the mock
        const routerPushMock = require("next/navigation").useRouter().push;
        const onPageChangeMock = jest.fn();
        const onItemsPerPageChangeMock = jest.fn();

        // Mock the search params to return fixed values
        const searchParamsMock = require("next/navigation").useSearchParams;
        searchParamsMock.mockImplementation(() => ({
            get: (param) => {
                if (param === "size") return "10";
                if (param === "page") return "1";
                return null;
            },
        }));

        const { rerender } = render(
            <OutgoingList
                transfers={mockTransfers}
                activeTab="instant"
                onPageChange={onPageChangeMock}
                onItemsPerPageChange={onItemsPerPageChangeMock}
            />
        );

        // Clear initial calls
        routerPushMock.mockClear();
        onPageChangeMock.mockClear();

        // Now call onChangePage with the same page that's already set (1)
        // This should trigger the early return in updateFilters
        fireEvent.click(screen.getByTestId("next-page-button")); // Would call with page 2

        // Verify callbacks were called because page changed from 1 to 2
        expect(routerPushMock).toHaveBeenCalled();
        expect(onPageChangeMock).toHaveBeenCalledWith(2);

        // Reset mocks
        routerPushMock.mockClear();
        onPageChangeMock.mockClear();

        // Now simulate a scenario where the filters don't change
        // Since our pagination mock always returns page 2, we need to update the search params mock
        searchParamsMock.mockImplementation(() => ({
            get: (param) => {
                if (param === "size") return "10";
                if (param === "page") return "2"; // Now we're on page 2
                return null;
            },
        }));

        // Re-render with updated URL params
        rerender(
            <OutgoingList
                transfers={mockTransfers}
                activeTab="instant"
                onPageChange={onPageChangeMock}
                onItemsPerPageChange={onItemsPerPageChangeMock}
            />
        );

        // Clicking next page button again (which is still page 2 in our mock)
        // This should trigger early return because page didn't change
        fireEvent.click(screen.getByTestId("next-page-button"));

        // Since our pagination component is mocked to always return page 2,
        // and we're now starting on page 2, no state should update
        expect(routerPushMock).not.toHaveBeenCalled();
        expect(onPageChangeMock).not.toHaveBeenCalled();
    });

    test("opens details modal when clicking view details", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Click the view details button which triggers the table meta function
        fireEvent.click(screen.getByTestId("view-details-button"));

        // Check if the details modal is now open
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    test("closes details modal", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Open the modal first
        fireEvent.click(screen.getByTestId("view-details-button"));
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();

        // Close the modal
        fireEvent.click(screen.getByTestId("close-details-button"));

        // Modal should not be visible anymore
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();
    });

    test("opens details modal when clicking on a table row", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Initially, the modal should not be visible
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();

        // Click on the table row
        fireEvent.click(screen.getByTestId("table-row"));

        // Check if the details modal is now open
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    describe("handleDownloadReceipt function", () => {
        let handleDownloadReceipt;
        let mockDispatch;

        beforeEach(() => {
            // Create our own instance of the component to access its methods
            mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            // Directly extract the handleDownloadReceipt function from the component instance
            const { container } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Get the instance's handleDownloadReceipt function
            handleDownloadReceipt = window.tableMeta.handleDownloadReceipt;
        });

        test("directly invokes function with valid transferScheduledId", () => {
            const validTransfer = { ...mockTransfers[0], transferScheduledId: 12345 };

            // Directly call the function
            handleDownloadReceipt(validTransfer);

            // Verify it called the right actions
            expect(downloadReceipt).toHaveBeenCalledWith({
                transactionId: 12345,
                isUserInitiated: true,
            });
            expect(mockDispatch).toHaveBeenCalled();
        });

        test("doesn't log debug info in production environment", () => {
            // Set to production environment
            process.env.NODE_ENV = "production";

            const validTransfer = { ...mockTransfers[0] };

            // Directly call the function
            handleDownloadReceipt(validTransfer);

            // Verify no logging occurred
            expect(console.log).not.toHaveBeenCalled();
        });

        test("handles null transferScheduledId with error", () => {
            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: null };

            // Directly call the function
            handleDownloadReceipt(invalidTransfer);

            // Verify error handling
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });

        test("handles undefined transferScheduledId with error", () => {
            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: undefined };

            // Directly call the function
            handleDownloadReceipt(invalidTransfer);

            // Verify error handling
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });

        test("processes zero as a invalid transferScheduledId", () => {
            const zeroIdTransfer = { ...mockTransfers[0], transferScheduledId: 0 };

            // Set to development for logging check
            process.env.NODE_ENV = "development";

            // Directly call the function
            handleDownloadReceipt(zeroIdTransfer);

            // In JavaScript, 0 is falsy, so it should be treated as invalid
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });
    });

    test("handles retry recipient action", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Open the details modal
        fireEvent.click(screen.getByTestId("view-details-button"));

        // Click retry button - should not throw an error
        fireEvent.click(screen.getByTestId("retry-button"));

        // Verify the modal is still open (the action didn't break anything)
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    test("handles send now recipient action", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);

        // Open the details modal
        fireEvent.click(screen.getByTestId("view-details-button"));

        // Click send now button - should not throw an error
        fireEvent.click(screen.getByTestId("send-now-button"));

        // Verify the modal is still open (the action didn't break anything)
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    describe("getTotalItems function", () => {
        // Create a direct implementation of getTotalItems for testing all branches
        const getTotalItems = (activeTab, state) => {
            switch (activeTab) {
                case "instant":
                    return state.transfer.transfersTotalElements;
                case "scheduled":
                    return state.transfer.scheduledTransfersTotalElements;
                case "recurring":
                    return state.transfer.recurringTransfersTotalElements;
                default:
                    return 0;
            }
        };

        test("directly calls getTotalItems function with all tab cases", () => {
            // Test the "instant" tab case
            expect(getTotalItems("instant", mockReduxState)).toBe(10);

            // Test the "scheduled" tab case
            expect(getTotalItems("scheduled", mockReduxState)).toBe(5);

            // Test the "recurring" tab case
            expect(getTotalItems("recurring", mockReduxState)).toBe(3);

            // Test the default case with an invalid tab
            expect(getTotalItems("invalid_tab", mockReduxState)).toBe(0);
        });

        test("returns correct values with custom state", () => {
            // Create custom state with different values
            const customState = {
                transfer: {
                    ...mockReduxState.transfer,
                    transfersTotalElements: 42,
                    scheduledTransfersTotalElements: 23,
                    recurringTransfersTotalElements: 15,
                },
            };

            // Test all branches with the custom state
            expect(getTotalItems("instant", customState)).toBe(42);
            expect(getTotalItems("scheduled", customState)).toBe(23);
            expect(getTotalItems("recurring", customState)).toBe(15);
            expect(getTotalItems("invalid", customState)).toBe(0);
        });

        // UI tests for verifying getTotalItems integration
        test("returns correct total elements for instant tab in UI", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            const totalItemsText = screen.getByTestId("total-items").textContent;
            expect(totalItemsText).toBe("Total items: 10");
        });

        test("returns correct total elements for scheduled tab in UI", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);

            const totalItemsText = screen.getByTestId("total-items").textContent;
            expect(totalItemsText).toBe("Total items: 5");
        });

        test("returns correct total elements for recurring tab in UI", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);

            const totalItemsText = screen.getByTestId("total-items").textContent;
            expect(totalItemsText).toBe("Total items: 3");
        });

        test("returns 0 for unknown tab (default case) in UI", () => {
            // Create a custom component to test the switch default case
            // by passing an invalid tab value
            render(<OutgoingList transfers={mockTransfers} activeTab="invalid_tab_value" />);

            const totalItemsText = screen.getByTestId("total-items").textContent;
            expect(totalItemsText).toBe("Total items: 0");
        });

        test("handles different redux state values for different tabs in UI", () => {
            // Setup a custom Redux state with different values
            const customReduxState = {
                transfer: {
                    ...mockReduxState.transfer,
                    transfersTotalElements: 25,
                    scheduledTransfersTotalElements: 15,
                    recurringTransfersTotalElements: 8,
                },
            };

            // Mock the selector to return our custom state
            useAppSelector.mockImplementation((selector) => selector(customReduxState));

            // Render with instant tab
            const { rerender } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Check instant tab total
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 25");

            // Re-render with scheduled tab
            rerender(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);

            // Check scheduled tab total
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 15");

            // Re-render with recurring tab
            rerender(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);

            // Check recurring tab total
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 8");
        });

        test("handles undefined, null, or zero Redux state values gracefully", () => {
            // Create custom state with edge case values
            const edgeCaseState = {
                transfer: {
                    ...mockReduxState.transfer,
                    transfersTotalElements: undefined,
                    scheduledTransfersTotalElements: null,
                    recurringTransfersTotalElements: 0,
                },
            };

            // Mock the selector to return our edge case state
            useAppSelector.mockImplementation((selector) => selector(edgeCaseState));

            // Test with instant tab (undefined value)
            const { rerender } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should show 0 items when state is undefined
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 0");

            // Test with scheduled tab (null value)
            rerender(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);

            // Should show 0 items when state is null
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 0");

            // Test with recurring tab (zero value)
            rerender(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);

            // Should show 0 items when state is explicitly 0
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 0");

            // Verify direct function calls as well
            const getTotalItems = (activeTab, state) => {
                switch (activeTab) {
                    case "instant":
                        return state.transfer.transfersTotalElements;
                    case "scheduled":
                        return state.transfer.scheduledTransfersTotalElements;
                    case "recurring":
                        return state.transfer.recurringTransfersTotalElements;
                    default:
                        return 0;
                }
            };

            // Test direct function calls with edge case values
            expect(getTotalItems("instant", edgeCaseState)).toBe(undefined);
            expect(getTotalItems("scheduled", edgeCaseState)).toBe(null);
            expect(getTotalItems("recurring", edgeCaseState)).toBe(0);
        });

        test("handles missing properties in Redux state", () => {
            // Create a state with missing properties
            const incompleteState = {
                transfer: {
                    // Missing transfersTotalElements
                    scheduledTransfersTotalElements: 5,
                    // Missing recurringTransfersTotalElements
                },
            };

            // Mock the selector to return our incomplete state
            useAppSelector.mockImplementation((selector) => selector(incompleteState));

            // Test with instant tab (missing property)
            const { rerender } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should show 0 items when property is missing
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 0");

            // Test with recurring tab (missing property)
            rerender(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);

            // Should show 0 items when property is missing
            expect(screen.getByTestId("total-items").textContent).toBe("Total items: 0");
        });
    });

    describe("updateFilters function", () => {
        // Direct implementation of updateFilters for testing
        const testUpdateFilters = (newFilters, currentFilters, callbacks) => {
            // This replicates the early return logic in updateFilters
            if (newFilters.page === currentFilters.page && newFilters.size === currentFilters.size) {
                return false; // Return false to indicate early return
            }

            // If we made it here, filters are different
            if (newFilters.page !== currentFilters.page && callbacks.onPageChange) {
                callbacks.onPageChange(newFilters.page || 1);
            }

            if (newFilters.size !== currentFilters.size && callbacks.onItemsPerPageChange) {
                callbacks.onItemsPerPageChange(newFilters.size || 10);
            }

            return true; // Return true to indicate function completed normally
        };

        test("directly tests early return condition in updateFilters", () => {
            // Mock required dependencies
            const onPageChangeMock = jest.fn();
            const onItemsPerPageChangeMock = jest.fn();

            // Test case: When filters are the same
            const currentFilters = { page: 1, size: 10 };
            const sameFilters = { page: 1, size: 10 };
            const callbacks = {
                onPageChange: onPageChangeMock,
                onItemsPerPageChange: onItemsPerPageChangeMock,
            };

            // This should trigger early return
            const earlyReturn = testUpdateFilters(sameFilters, currentFilters, callbacks);
            expect(earlyReturn).toBe(false);
            expect(onPageChangeMock).not.toHaveBeenCalled();
            expect(onItemsPerPageChangeMock).not.toHaveBeenCalled();
        });

        test("directly tests page change in updateFilters", () => {
            // Mock required dependencies
            const onPageChangeMock = jest.fn();
            const onItemsPerPageChangeMock = jest.fn();

            const currentFilters = { page: 1, size: 10 };
            const newPageFilters = { page: 2, size: 10 };
            const callbacks = {
                onPageChange: onPageChangeMock,
                onItemsPerPageChange: onItemsPerPageChangeMock,
            };

            // This should call onPageChange
            const pageChanged = testUpdateFilters(newPageFilters, currentFilters, callbacks);
            expect(pageChanged).toBe(true);
            expect(onPageChangeMock).toHaveBeenCalledWith(2);
            expect(onItemsPerPageChangeMock).not.toHaveBeenCalled();
        });

        test("directly tests size change in updateFilters", () => {
            // Mock required dependencies
            const onPageChangeMock = jest.fn();
            const onItemsPerPageChangeMock = jest.fn();

            const currentFilters = { page: 1, size: 10 };
            const newSizeFilters = { page: 1, size: 20 };
            const callbacks = {
                onPageChange: onPageChangeMock,
                onItemsPerPageChange: onItemsPerPageChangeMock,
            };

            // This should call onItemsPerPageChange
            const sizeChanged = testUpdateFilters(newSizeFilters, currentFilters, callbacks);
            expect(sizeChanged).toBe(true);
            expect(onPageChangeMock).not.toHaveBeenCalled();
            expect(onItemsPerPageChangeMock).toHaveBeenCalledWith(20);
        });

        test("directly tests both changes in updateFilters", () => {
            // Mock required dependencies
            const onPageChangeMock = jest.fn();
            const onItemsPerPageChangeMock = jest.fn();

            const currentFilters = { page: 1, size: 10 };
            const bothChangeFilters = { page: 2, size: 20 };
            const callbacks = {
                onPageChange: onPageChangeMock,
                onItemsPerPageChange: onItemsPerPageChangeMock,
            };

            // This should call both callbacks
            const bothChanged = testUpdateFilters(bothChangeFilters, currentFilters, callbacks);
            expect(bothChanged).toBe(true);
            expect(onPageChangeMock).toHaveBeenCalledWith(2);
            expect(onItemsPerPageChangeMock).toHaveBeenCalledWith(20);
        });
    });

    // Additional tests for URL-based pagination state (after the existing tests)
    describe("URL-based pagination state initialization", () => {
        test("initializes currentFilters from URL search parameters", () => {
            // Mock the search params with specific values for size and page
            jest.spyOn(require("next/navigation"), "useSearchParams").mockReturnValue({
                get: (param) => {
                    if (param === "size") return "25";
                    if (param === "page") return "3";
                    return null;
                },
            });

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Verify pagination component gets the correct initial values
            expect(screen.getByTestId("pagination")).toHaveTextContent("Page: 3");
            expect(screen.getByTestId("pagination")).toHaveTextContent("Items per page: 25");
        });

        test("uses default values when URL parameters are not provided", () => {
            // Mock the search params to return null values
            jest.spyOn(require("next/navigation"), "useSearchParams").mockReturnValue({
                get: () => null,
            });

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Verify pagination component gets the default values
            expect(screen.getByTestId("pagination")).toHaveTextContent("Page: 1");
            expect(screen.getByTestId("pagination")).toHaveTextContent("Items per page: 10");
        });

        test("handles invalid URL parameter values gracefully", () => {
            // Mock the search params with invalid values
            jest.spyOn(require("next/navigation"), "useSearchParams").mockReturnValue({
                get: (param) => {
                    if (param === "size") return "not-a-number";
                    if (param === "page") return "also-not-a-number";
                    return null;
                },
            });

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should fall back to default values when Number() returns NaN
            expect(screen.getByTestId("pagination")).toHaveTextContent("Page: 1");
            expect(screen.getByTestId("pagination")).toHaveTextContent("Items per page: 10");
        });
    });

    describe("Router URL update functionality", () => {
        test("updates URL with query parameters and prevents scrolling", async () => {
            // Access the router push mock directly
            const mockRouter = require("next/navigation").useRouter();
            const routerPushMock = mockRouter.push;

            // Create mock callbacks
            const onPageChangeMock = jest.fn();
            const onItemsPerPageChangeMock = jest.fn();

            render(
                <OutgoingList
                    transfers={mockTransfers}
                    activeTab="instant"
                    onPageChange={onPageChangeMock}
                    onItemsPerPageChange={onItemsPerPageChangeMock}
                />
            );

            // Reset the mock to clear any initial calls
            routerPushMock.mockClear();

            // Trigger a page change which should update the URL
            fireEvent.click(screen.getByTestId("next-page-button"));

            // Verify router.push was called with correct parameters
            expect(routerPushMock).toHaveBeenCalledWith(expect.stringMatching(/\/dashboard\/outgoing\?.*page=2/), {
                scroll: false,
            });

            // Reset the mock for the next test
            routerPushMock.mockClear();

            // Trigger an items per page change
            fireEvent.click(screen.getByTestId("change-items-button"));

            // This should also update the URL with new size parameter
            expect(routerPushMock).toHaveBeenCalledWith(expect.stringMatching(/\/dashboard\/outgoing\?.*size=20/), {
                scroll: false,
            });
            expect(routerPushMock).toHaveBeenCalledWith(expect.stringMatching(/\/dashboard\/outgoing\?.*page=1/), {
                scroll: false,
            });
        });

        test("correctly builds query string with multiple parameters", async () => {
            // Access the router push mock directly
            const mockRouter = require("next/navigation").useRouter();
            const routerPushMock = mockRouter.push;

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Reset the mock to clear any initial calls
            routerPushMock.mockClear();

            // First change page to set up multiple params
            fireEvent.click(screen.getByTestId("next-page-button"));

            // Then change items per page
            fireEvent.click(screen.getByTestId("change-items-button"));

            // Last router push should have both parameters
            const lastCall = routerPushMock.mock.calls[routerPushMock.mock.calls.length - 1];

            // Check that both parameters exist in the URL
            expect(lastCall[0]).toMatch(/page=1/);
            expect(lastCall[0]).toMatch(/size=20/);

            // Check that the scroll option is set to false
            expect(lastCall[1]).toEqual({ scroll: false });
        });

        test("handles empty query parameters correctly", async () => {
            // Mock search params to return null
            jest.spyOn(require("next/navigation"), "useSearchParams").mockReturnValue({
                get: () => null,
            });

            // Access the router push mock directly
            const mockRouter = require("next/navigation").useRouter();
            const routerPushMock = mockRouter.push;

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Reset the mock to clear any initial calls
            routerPushMock.mockClear();

            // Change page which should create the first URL parameter
            fireEvent.click(screen.getByTestId("next-page-button"));

            // Check that the URL was formed correctly - should include both size and page
            expect(routerPushMock).toHaveBeenCalledWith(
                expect.stringMatching(/\/dashboard\/outgoing\?.*size=10.*page=2/),
                { scroll: false }
            );
        });
    });
});
