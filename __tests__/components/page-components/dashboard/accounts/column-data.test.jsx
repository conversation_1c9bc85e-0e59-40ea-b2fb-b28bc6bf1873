/* eslint-disable quotes */
/* eslint-disable @typescript-eslint/no-require-imports */
import { fireEvent, render, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";

// Import the components/exports under test
import {
    columns,
    DownloadStatementDialog,
    recentTransactionColumns,
    TransferFundsDialog,
} from "@/components/page-components/dashboard/accounts/column-data";

// Mocks for external components used by the dialogs
jest.mock("reactjs-popup", () => ({ children }) => <div>s{children}</div>);

jest.mock("@/components/common/dropdown", () => (props) => (
    <select
        data-testid="dropdown"
        aria-label={props.label}
        onChange={(e) => props.onChange({ label: e.target.value, value: e.target.value })}
        value={props.value?.value || ""}
    >
        {props.options.map((option) => (
            <option key={option.value} value={option.value}>
                {option.label}
            </option>
        ))}
    </select>
));

jest.mock("@/components/common/label-input", () => {
    const React = require("react");
    return (props) => {
        const [val, setVal] = React.useState(props.value || "");
        return (
            <input
                data-testid={props["data-testid"]}
                placeholder={props.placeholder}
                value={val}
                onChange={(e) => {
                    setVal(e.target.value);
                    props.onChange && props.onChange(e);
                }}
            />
        );
    };
});

// Mock the Button component to render a simple button with its children.
jest.mock("@/components/common/buttonv3", () => ({
    Button: (props) => (
        <button type={props.type || "button"} onClick={props.onClick} data-testid={props["data-testid"]}>
            {props.children}
        </button>
    ),
}));

// Mock the country flag components
jest.mock("@/components/common/country-code/flags", () => ({
    NGFlag: () => <div data-testid="ng-flag">NG Flag</div>,
    USFlag: () => <div data-testid="us-flag">US Flag</div>,
    UKFlag: () => <div data-testid="uk-flag">UK Flag</div>,
}));

// Update the Radix UI Dialog mocks to render children only when open is true.
jest.mock("@radix-ui/react-dialog", () => {
    const ActualDialog = jest.requireActual("@radix-ui/react-dialog");
    return {
        ...ActualDialog,
        Root: (props) => (props.open ? <div data-testid="dialog-root">{props.children}</div> : null),
        Overlay: (props) => <div data-testid="dialog-overlay" {...props} />,
        Content: (props) => (
            <div data-testid="dialog-content" {...props}>
                {props.children}
            </div>
        ),
        Title: (props) => <h2 {...props}>{props.children}</h2>,
        Description: (props) => <div {...props}>{props.children}</div>,
    };
});

// Mock DownloadStatementModal to return an element with data-testid="account-statement-modal" when open is true.
jest.mock("@/components/page-components/dashboard/accounts/download-statement-modal", () => ({
    __esModule: true,
    default: ({ open, onClose }) =>
        open ? (
            <div data-testid="account-statement-modal">
                Modal Content
                <button data-testid="modal-close" onClick={onClose}>
                    Close Modal
                </button>
            </div>
        ) : null,
}));

// Capture TableMoreAction props in the main columns and recentTransactionColumns actions cells.
let tableMoreActionProps = null;
jest.mock("@/components/common/table/table-more-action", () => {
    return jest.fn((props) => {
        tableMoreActionProps = props;
        return (
            <div data-testid="table-more-action">
                {props.menuItems.map((m, i) => (
                    <button key={i} onClick={() => m.onClick(props.data)}>
                        {m.label}
                    </button>
                ))}
            </div>
        );
    });
});

// Create a dummy redux store for components that require Redux context.
// Add initial state expected by components (e.g., dashboard.getAccountStatement)
const initialState = {
    dashboard: {
        getAccountStatement: { loading: false, success: false },
    },
};
const mockStore = configureStore([]);
const dummyStore = mockStore(initialState);

// --- Tests for Main Table Columns ---
describe("Main Table Columns", () => {
    describe("Account column with flags", () => {
        it("renders account column with Nigerian flag for NGN currency", () => {
            const row = {
                original: {
                    accountName: "Main Account",
                    accountNumber: "*********",
                    currencyCode: "ngn",
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);

            expect(screen.getByText("Main Account")).toBeInTheDocument();
            expect(screen.getByText("NGN - *********")).toBeInTheDocument();
            expect(screen.getByTestId("ng-flag")).toBeInTheDocument();
        });

        it("renders account column with US flag for USD currency", () => {
            const row = {
                original: {
                    accountName: "USD Account",
                    accountNumber: "*********",
                    currencyCode: "USD",
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);

            expect(screen.getByText("USD Account")).toBeInTheDocument();
            expect(screen.getByText("USD - *********")).toBeInTheDocument();
            expect(screen.getByTestId("us-flag")).toBeInTheDocument();
        });

        it("renders account column with UK flag for GBP currency", () => {
            const row = {
                original: {
                    accountName: "GBP Account",
                    accountNumber: "*********",
                    currencyCode: "gbp",
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);

            expect(screen.getByText("GBP Account")).toBeInTheDocument();
            expect(screen.getByText("GBP - *********")).toBeInTheDocument();
            expect(screen.getByTestId("uk-flag")).toBeInTheDocument();
        });

        it("renders account column with Nigerian flag for unknown currency (default)", () => {
            const row = {
                original: {
                    accountName: "Unknown Currency Account",
                    accountNumber: "*********",
                    currencyCode: "XYZ",
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);

            expect(screen.getByText("Unknown Currency Account")).toBeInTheDocument();
            expect(screen.getByText("XYZ - *********")).toBeInTheDocument();
            expect(screen.getByTestId("ng-flag")).toBeInTheDocument();
        });

        it("renders account column with Nigerian flag when currencyCode is null or undefined", () => {
            const row = {
                original: {
                    accountName: "No Currency Account",
                    accountNumber: "*********",
                    currencyCode: null,
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);

            expect(screen.getByText("No Currency Account")).toBeInTheDocument();
            expect(screen.getByTestId("ng-flag")).toBeInTheDocument();
        });

        it("renders '-' when accountName and accountNumber are null or undefined", () => {
            // Test with undefined values
            const rowUndefined = {
                original: {
                    accountName: undefined,
                    accountNumber: undefined,
                    currencyCode: "NGN",
                },
            };
            const AccountCell = columns[0].cell;
            const { rerender } = render(<AccountCell row={rowUndefined} />);
            // Expect the fallback '-' to appear twice (once for each missing field)
            expect(screen.getAllByText("-").length).toEqual(1);
            // Should still render the flag
            expect(screen.getByTestId("ng-flag")).toBeInTheDocument();

            // Test with null values
            const rowNull = {
                original: {
                    accountName: null,
                    accountNumber: null,
                    currencyCode: "USD",
                },
            };
            rerender(<AccountCell row={rowNull} />);
            expect(screen.getAllByText("-").length).toEqual(1);
            // Should render US flag for USD currency
            expect(screen.getByTestId("us-flag")).toBeInTheDocument();
        });
    });

    it("renders balance column correctly", () => {
        // We assume renderFormatAmount simply returns a formatted string.
        const row = { original: { balance: 5000 } };
        const BalanceCell = columns[1].cell;
        render(<BalanceCell row={row} />);
        expect(screen.getByText(/5,000/)).toBeInTheDocument();
    });

    it("renders moneyIn column correctly", () => {
        const row = { original: { totalInflow: 3000 } };
        const MoneyInCell = columns[2].cell;
        render(<MoneyInCell row={row} />);
        expect(screen.getByText(/3,000/)).toBeInTheDocument();
    });

    it("renders moneyOut column correctly", () => {
        const row = { original: { totalOutflow: 2000 } };
        const MoneyOutCell = columns[3].cell;
        render(<MoneyOutCell row={row} />);
        // It prepends a "-" sign.
        expect(screen.getByText(/-.*2,000/)).toBeInTheDocument();
    });

    describe("Actions cell in main columns", () => {
        const dummyTable = {
            options: {
                meta: {
                    openTransferModal: jest.fn(),
                    openStatementModal: jest.fn(),
                },
            },
        };
        const dummyRow = {
            original: { accountNumber: "*********" },
        };

        beforeEach(() => {
            jest.clearAllMocks();
            tableMoreActionProps = null;
        });

        it("renders TableMoreAction with 2 menu items", () => {
            const ActionsCell = columns[4].cell;
            render(<ActionsCell row={dummyRow} table={dummyTable} />);
            expect(tableMoreActionProps).not.toBeNull();
            expect(tableMoreActionProps.menuItems.length).toBe(2);
            const labels = tableMoreActionProps.menuItems.map((item) => item.label);
            expect(labels).toEqual(["Download statement", "Transfer funds"]);
        });

        it("calls openStatementModal when 'Download statement' is clicked", () => {
            const ActionsCell = columns[4].cell;
            render(<ActionsCell row={dummyRow} table={dummyTable} />);
            const downloadButton = screen.getByText("Download statement");
            fireEvent.click(downloadButton);
            expect(dummyTable.options.meta.openStatementModal).toHaveBeenCalled();
        });

        it("calls openTransferModal when 'Transfer funds' is clicked", () => {
            const ActionsCell = columns[4].cell;
            render(<ActionsCell row={dummyRow} table={dummyTable} />);
            const transferButton = screen.getByText("Transfer funds");
            fireEvent.click(transferButton);
            expect(dummyTable.options.meta.openTransferModal).toHaveBeenCalled();
        });
    });
});

// --- Tests for DownloadStatementDialog ---
describe("DownloadStatementDialog", () => {
    it("renders the 'Get statement' button and opens the modal on click", async () => {
        render(
            <Provider store={dummyStore}>
                <DownloadStatementDialog />
            </Provider>
        );
        const getStatementButton = screen.getByText(/Get statement/i);
        expect(getStatementButton).toBeInTheDocument();
        // Initially modal should not be present.
        expect(screen.queryByTestId("account-statement-modal")).not.toBeInTheDocument();
        fireEvent.click(getStatementButton);
        // After clicking, the modal should appear.
        await waitFor(() => {
            expect(screen.getByTestId("account-statement-modal")).toBeInTheDocument();
        });
    });

    it("closes the modal when the close button is clicked", async () => {
        render(
            <Provider store={dummyStore}>
                <DownloadStatementDialog />
            </Provider>
        );
        fireEvent.click(screen.getByText(/Get statement/i));
        await waitFor(() => {
            expect(screen.getByTestId("account-statement-modal")).toBeInTheDocument();
        });
        const closeButton = screen.getByTestId("modal-close");
        fireEvent.click(closeButton);
        await waitFor(() => {
            expect(screen.queryByTestId("account-statement-modal")).not.toBeInTheDocument();
        });
    });
});

// --- Tests for TransferFundsDialog ---
describe("TransferFundsDialog", () => {
    it("opens and closes the transfer funds dialog correctly", async () => {
        render(
            <Provider store={dummyStore}>
                <TransferFundsDialog />
            </Provider>
        );
        const openButton = screen.getByText("Transfer Funds");
        fireEvent.click(openButton);
        // The dialog content should appear.
        expect(screen.getByTestId("dialog-content")).toBeInTheDocument();
        // Click Cancel button.
        const cancelButton = within(screen.getByTestId("dialog-content")).getByText("Cancel");
        fireEvent.click(cancelButton);
        await waitFor(() => {
            expect(screen.queryByTestId("dialog-content")).not.toBeInTheDocument();
        });
    });

    it("updates dropdown values for 'Transfer from' and 'Destination account'", () => {
        render(
            <Provider store={dummyStore}>
                <TransferFundsDialog />
            </Provider>
        );
        fireEvent.click(screen.getByText("Transfer Funds"));
        const dropdowns = screen.getAllByTestId("dropdown");
        // Change first dropdown (Transfer from)
        userEvent.selectOptions(dropdowns[0], "Expense account - *********");
        expect(dropdowns[0].value).toBe("Main account - **********"); // to be changed
        // Change second dropdown (Destination account)
        userEvent.selectOptions(dropdowns[1], "Account Placeholder");
        expect(dropdowns[1].value).toBe("Expense account - *********"); // to be changed
    });

    it("updates the amount and narration inputs", () => {
        render(
            <Provider store={dummyStore}>
                <TransferFundsDialog />
            </Provider>
        );
        fireEvent.click(screen.getByText("Transfer Funds"));
        const amountInput = screen.getByTestId("amount");
        const narrationInput = screen.getByTestId("narration");
        fireEvent.change(amountInput, { target: { value: "₦500,000.00" } });
        expect(amountInput.value).toBe("₦500,000.00");
        fireEvent.change(narrationInput, { target: { value: "Payment for rent" } });
        expect(narrationInput.value).toBe("Payment for rent");
    });

    it("closes the dialog when 'Transfer funds' button is clicked", async () => {
        render(
            <Provider store={dummyStore}>
                <TransferFundsDialog />
            </Provider>
        );
        fireEvent.click(screen.getByText("Transfer Funds"));
        const dialogContent = screen.getByTestId("dialog-content");
        // Find the Transfer funds button (the one that is not the Cancel button)
        const transferButton = within(dialogContent).getByText("Transfer funds");
        fireEvent.click(transferButton);
        await waitFor(() => {
            expect(screen.queryByTestId("dialog-content")).not.toBeInTheDocument();
        });
    });
});

// --- Tests for recentTransactionColumns ---
describe("Recent Transaction Columns", () => {
    // Dummy payment object for transactions.
    const dummyPayment = {
        transactionId: "tx-123",
        transactionType: "Credit",
        createdDate: "2023-01-01T12:34:56Z",
        counterpartyType: "Vendor",
        amount: "1000",
    };

    // Dummy row object
    const dummyRow = {
        original: dummyPayment,
        getValue: (key) => dummyPayment[key],
        getIsSelected: () => false,
        toggleSelected: jest.fn(),
    };

    // Dummy meta functions for recent transactions.
    const setIsOpenMock = jest.fn();
    const setIsReportOpenMock = jest.fn();
    const setTransactionIdMock = jest.fn();
    const handleReportDownloadMock = jest.fn();

    const dummyTable = {
        options: {
            meta: {
                setIsOpen: setIsOpenMock,
                setIsReportOpen: setIsReportOpenMock,
                setTransactionId: setTransactionIdMock,
                handleReportDownload: handleReportDownloadMock,
            },
        },
    };

    // Wrap the actions cell renderer in a component.
    function ActionsCellWrapper({ row, table }) {
        const actionsColumn = recentTransactionColumns.find((col) => col.id === "actions");
        return actionsColumn ? actionsColumn.cell({ row, table }) : null;
    }

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders TableMoreAction with proper menu items (3 items)", () => {
        render(<ActionsCellWrapper row={dummyRow} table={dummyTable} />);
        expect(tableMoreActionProps).not.toBeNull();
        expect(tableMoreActionProps.menuItems.length).toBe(2);
        const labels = tableMoreActionProps.menuItems.map((item) => item.label);
        expect(labels).toEqual(["View", "Download receipt"]);
    });

    it('calls setTransactionId and setIsOpen when "View" is clicked', () => {
        render(<ActionsCellWrapper row={dummyRow} table={dummyTable} />);
        const { menuItems } = tableMoreActionProps;
        const viewItem = menuItems.find((item) => item.label === "View");
        viewItem.onClick(dummyPayment);
        expect(setTransactionIdMock).toHaveBeenCalledWith(dummyPayment.transactionId);
        expect(setIsOpenMock).toHaveBeenCalledWith(true);
    });

    it('calls handleReportDownloadMock when "Download receipt" is clicked', () => {
        render(<ActionsCellWrapper row={dummyRow} table={dummyTable} />);
        const { menuItems } = tableMoreActionProps;
        const downloadItem = menuItems.find((item) => item.label === "Download receipt");
        downloadItem.onClick(dummyPayment);
        expect(setTransactionIdMock).not.toHaveBeenCalled();
        expect(setIsOpenMock).not.toHaveBeenCalled();
        expect(setIsReportOpenMock).not.toHaveBeenCalled();
        expect(handleReportDownloadMock).toHaveBeenCalled();
    });

    describe("CreatedDate Column", () => {
        const createdDateColumn = recentTransactionColumns.find((col) => col.accessorKey === "createdDate");
        it("renders header correctly and toggles sorting", () => {
            const toggleSortingMock = jest.fn();
            const dummyColumn = {
                toggleSorting: toggleSortingMock,
                getIsSorted: () => "asc",
            };
            const Header = createdDateColumn.header;
            render(Header({ column: dummyColumn }));
            const headerButton = screen.getByText("Date");
            expect(headerButton).toBeInTheDocument();
            fireEvent.click(headerButton);
            expect(toggleSortingMock).toHaveBeenCalledWith(true);
        });

        it("renders the cell with formatted date and time", () => {
            const Cell = createdDateColumn.cell;
            const { container } = render(Cell({ row: { original: { createdDate: "2023-01-01T12:34:56Z" } } }));
            const formattedDate = within(container).getByText((content) => content.includes("2023"));
            expect(formattedDate).toBeInTheDocument();
        });
    });

    describe("Amount Column", () => {
        const amountColumn = recentTransactionColumns.find((col) => col.accessorKey === "amount");
        it("renders header correctly and toggles sorting", () => {
            const toggleSortingMock = jest.fn();
            const dummyColumn = {
                toggleSorting: toggleSortingMock,
                getIsSorted: () => "",
            };
            const Header = amountColumn.header;
            render(Header({ column: dummyColumn }));
            const headerButton = screen.getByText("Amount");
            expect(headerButton).toBeInTheDocument();
            fireEvent.click(headerButton);
            expect(toggleSortingMock).toHaveBeenCalledWith(false);
        });

        it("renders cell correctly for Credit transactions", () => {
            const dummyRowCredit = {
                original: { amount: "1000", transactionType: "Credit" },
                getValue: (key) => (key === "amount" ? "1000" : null),
            };
            const Cell = amountColumn.cell;
            const { container } = render(Cell({ row: dummyRowCredit }));
            const formatted = new Intl.NumberFormat("en-NG", {
                style: "currency",
                currency: "NGN",
            }).format(1000);
            expect(container.textContent).toContain(`+ ${formatted}`);
            expect(container.firstChild.className).toMatch(/text-success/);
        });

        it("renders cell correctly for Debit transactions", () => {
            const dummyRowDebit = {
                original: { amount: "2000", transactionType: "Debit" },
                getValue: (key) => (key === "amount" ? "2000" : null),
            };
            const Cell = amountColumn.cell;
            const { container } = render(Cell({ row: dummyRowDebit }));
            const formatted = new Intl.NumberFormat("en-NG", {
                style: "currency",
                currency: "NGN",
            }).format(2000);
            expect(container.textContent).toContain(`- ${formatted}`);
            expect(container.firstChild.className).not.toMatch(/text-success/);
        });
    });
});

// --- Additional Tests for Nullish Fallbacks ---

describe("Nullish Fallbacks in Main Table Columns", () => {
    it("renders formatted 0 for balance when balance is undefined or null", () => {
        // Assuming renderFormatAmount formats 0 as "0" or "₦0.00" (adjust the expected text accordingly)
        const rowUndefined = { original: { balance: undefined } };
        const BalanceCell = columns[1].cell;
        const { rerender } = render(<BalanceCell row={rowUndefined} />);
        expect(screen.getByText(/0/)).toBeInTheDocument();

        const rowNull = { original: { balance: null } };
        rerender(<BalanceCell row={rowNull} />);
        expect(screen.getByText(/0/)).toBeInTheDocument();
    });

    it("renders formatted 0 for money in when totalInflow is undefined or null", () => {
        const rowUndefined = { original: { totalInflow: undefined } };
        const MoneyInCell = columns[2].cell;
        const { rerender } = render(<MoneyInCell row={rowUndefined} />);
        expect(screen.getByText(/0/)).toBeInTheDocument();

        const rowNull = { original: { totalInflow: null } };
        rerender(<MoneyInCell row={rowNull} />);
        expect(screen.getByText(/0/)).toBeInTheDocument();
    });

    it("renders formatted 0 for money out when totalOutflow is undefined or null", () => {
        const rowUndefined = { original: { totalOutflow: undefined } };
        const MoneyOutCell = columns[3].cell;
        const { rerender } = render(<MoneyOutCell row={rowUndefined} />);
        // Since money out always prepends a "-" sign
        expect(screen.getByText(/-.*0/)).toBeInTheDocument();

        const rowNull = { original: { totalOutflow: null } };
        rerender(<MoneyOutCell row={rowNull} />);
        expect(screen.getByText(/-.*0/)).toBeInTheDocument();
    });

    describe("Account column with flags", () => {
        it("renders account column with account name and number when both are provided", () => {
            const row = {
                original: {
                    accountName: "Main Account",
                    accountNumber: "*********",
                    currencyCode: "ngn",
                },
            };
            const AccountCell = columns[0].cell;
            render(<AccountCell row={row} />);
            expect(screen.getByText("Main Account")).toBeInTheDocument();
            expect(screen.getByText("NGN - *********")).toBeInTheDocument();
            expect(screen.getByTestId("ng-flag")).toBeInTheDocument();
        });

        it("renders '-' when accountName is null or undefined and accountNumber is provided", () => {
            const rowUndefined = {
                original: {
                    accountName: undefined,
                    accountNumber: "*********",
                    currencyCode: "NGN",
                },
            };
            const AccountCell = columns[0].cell;
            const { rerender } = render(<AccountCell row={rowUndefined} />);
            expect(screen.getByText("-")).toBeInTheDocument();
            expect(screen.getByText("NGN - *********")).toBeInTheDocument();

            const rowNull = {
                original: {
                    accountName: null,
                    accountNumber: "*********",
                    currencyCode: "USD",
                },
            };
            rerender(<AccountCell row={rowNull} />);
            expect(screen.getByText("-")).toBeInTheDocument();
            expect(screen.getByText("USD - *********")).toBeInTheDocument();
        });

        it("renders '-' when accountNumber is null or undefined and accountName is provided", () => {
            const rowUndefined = {
                original: {
                    accountName: "Main Account",
                    accountNumber: undefined,
                    currencyCode: "NGN",
                },
            };
            const AccountCell = columns[0].cell;
            const { rerender } = render(<AccountCell row={rowUndefined} />);
            expect(screen.getByText("Main Account")).toBeInTheDocument();
            expect(screen.getByText("NGN - -")).toBeInTheDocument();

            const rowNull = {
                original: {
                    accountName: "Main Account",
                    accountNumber: null,
                    currencyCode: "USD",
                },
            };
            rerender(<AccountCell row={rowNull} />);
            expect(screen.getByText("Main Account")).toBeInTheDocument();
            expect(screen.getByText("USD - -")).toBeInTheDocument();
        });
    });

    describe("TransferFundsDialog - Dropdown onChange Tests", () => {
        let mockStore;
        const initialState = {
            dashboard: {
                getAccountStatement: { loading: false, success: false },
            },
        };
        mockStore = configureStore([])(initialState);

        beforeEach(() => {
            jest.clearAllMocks();
        });

        const renderWithProvider = (component) => {
            return render(<Provider store={mockStore}>{component}</Provider>);
        };

        it("renders 'Transfer from' and 'Destination account' dropdowns with correct initial values", () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            expect(dropdowns).toHaveLength(2);

            expect(dropdowns[0]).toHaveValue("Main account - **********");
            expect(dropdowns[1]).toHaveValue("Expense account - *********");
        });

        it("updates 'Transfer from' dropdown value and state correctly", async () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            const transferFromDropdown = dropdowns[0];

            await userEvent.selectOptions(transferFromDropdown, "Expense account - *********");
            expect(transferFromDropdown).toHaveValue("Expense account - *********");

            const options = within(transferFromDropdown).getAllByRole("option");
            expect(options.map((opt) => opt.textContent)).toEqual([
                "Main account - **********",
                "Expense account - *********",
                "Account Placeholder",
            ]);
        });

        it("updates 'Destination account' dropdown value and state correctly", async () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            const destinationDropdown = dropdowns[1];

            await userEvent.selectOptions(destinationDropdown, "Account Placeholder");
            expect(destinationDropdown).toHaveValue("Account Placeholder");

            const options = within(destinationDropdown).getAllByRole("option");
            expect(options.map((opt) => opt.textContent)).toEqual([
                "Main account - **********",
                "Expense account - *********",
                "Account Placeholder",
            ]);
        });

        it("allows selecting the same account for both dropdowns", async () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            const transferFromDropdown = dropdowns[0];
            const destinationDropdown = dropdowns[1];

            await userEvent.selectOptions(transferFromDropdown, "Main account - **********");
            await userEvent.selectOptions(destinationDropdown, "Main account - **********");

            expect(transferFromDropdown).toHaveValue("Main account - **********");
            expect(destinationDropdown).toHaveValue("Main account - **********");
        });

        it("handles multiple rapid changes to dropdowns", async () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            const transferFromDropdown = dropdowns[0];
            const destinationDropdown = dropdowns[1];

            await userEvent.selectOptions(transferFromDropdown, "Expense account - *********");
            await userEvent.selectOptions(transferFromDropdown, "Account Placeholder");
            await userEvent.selectOptions(transferFromDropdown, "Main account - **********");

            await userEvent.selectOptions(destinationDropdown, "Account Placeholder");
            await userEvent.selectOptions(destinationDropdown, "Main account - **********");
            await userEvent.selectOptions(destinationDropdown, "Expense account - *********");

            expect(transferFromDropdown).toHaveValue("Main account - **********");
            expect(destinationDropdown).toHaveValue("Expense account - *********");
        });

        it("persists dropdown state when dialog is closed and reopened", async () => {
            renderWithProvider(<TransferFundsDialog />);
            fireEvent.click(screen.getByText("Transfer Funds"));

            const dropdowns = screen.getAllByTestId("dropdown");
            const transferFromDropdown = dropdowns[0];
            const destinationDropdown = dropdowns[1];

            await userEvent.selectOptions(transferFromDropdown, "Expense account - *********");
            await userEvent.selectOptions(destinationDropdown, "Account Placeholder");

            const cancelButton = screen.getByText("Cancel");
            fireEvent.click(cancelButton);
            await waitFor(() => {
                expect(screen.queryByTestId("dialog-content")).not.toBeInTheDocument();
            });

            fireEvent.click(screen.getByText("Transfer Funds"));

            const newDropdowns = screen.getAllByTestId("dropdown");
            expect(newDropdowns[0]).toHaveValue("Expense account - *********");
            expect(newDropdowns[1]).toHaveValue("Account Placeholder");
        });
    });
});
