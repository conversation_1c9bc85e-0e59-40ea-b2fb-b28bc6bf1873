/* eslint-disable @typescript-eslint/no-require-imports */
import AccountSwitcher from "@/components/page-components/dashboard/accounts/account-switcher";
import { getAccounts } from "@/redux/actions/accountActions";
import { getAccountDetails } from "@/redux/actions/dashboardActions";
import { onSwitchAccount } from "@/redux/slices/accountSlice";
import { fireEvent, render, screen, waitFor, within } from "@testing-library/react";
import React from "react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// --- Mocks ---

jest.mock("@/redux/actions/accountActions", () => ({
    getAccounts: jest.fn(() => ({ type: "GET_ACCOUNTS" })),
}));

jest.mock("@/redux/actions/dashboardActions", () => ({
    getAccountDetails: jest.fn(() => ({ type: "GET_ACCOUNT_DETAILS" })),
}));

// Mock the dropdown menu components
jest.mock("@/components/common/dropdown-menu", () => ({
    DropdownMenu: ({ children }) => <div>{children}</div>,
    DropdownMenuTrigger: ({ children, asChild }) => {
        if (asChild) {
            return <div data-testid="dropdown-trigger">{children}</div>;
        }
        return <div data-testid="dropdown-trigger">{children}</div>;
    },
    DropdownMenuContent: ({ children, className, align }) => (
        <div data-testid="dropdown-content" className={className} data-align={align}>
            {children}
        </div>
    ),
    DropdownMenuItem: ({ children, className, onClick }) => (
        <div data-testid="dropdown-item" className={className} onClick={onClick}>
            {children}
        </div>
    ),
}));

// Mock the flag components
jest.mock("@/components/common/country-code/flags", () => ({
    NGFlag: () => <span data-testid="NGFlag">NGFlag</span>,
    USFlag: () => <span data-testid="USFlag">USFlag</span>,
    UKFlag: () => <span data-testid="UKFlag">UKFlag</span>,
    EuroFlag: () => <span data-testid="EuroFlag">EuroFlag</span>,
}));

// Mock the Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, variant, className, ...props }) => (
        <button data-testid="button" variant={variant} className={className} {...props}>
            {children}
        </button>
    ),
}));

// Mock the renderFormatAmount function
jest.mock("@/functions/facilities", () => ({
    renderFormatAmount: jest.fn((amount, currency) => {
        const currencyMap = {
            NGN: "₦",
            USD: "$",
            GBP: "£",
            EUR: "€",
        };
        const symbol = currencyMap[currency?.toUpperCase()] || "₦";
        return `${symbol}${amount.toLocaleString()}.00`;
    }),
}));

// Create a mock store
const mockStore = configureStore([]);

describe("AccountSwitcher Component", () => {
    let store;

    // Helper for creating state with default dashboard values
    const defaultDashboardState = {
        getAccountDetails: {
            data: { balance: 1000 },
            loading: false,
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Component Rendering", () => {
        beforeEach(() => {
            store = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "NGN",
                            accountName: "Savings Account",
                            accountNumber: "********",
                        },
                        {
                            balance: 2000,
                            currencyCode: "GBP",
                            accountName: "Business Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            store.dispatch = jest.fn();
        });

        test("renders account selector with selected account information", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the account name and number are displayed
            expect(screen.getByText("Main Account | ********")).toBeInTheDocument();

            // Check that the dropdown trigger is rendered
            expect(screen.getByTestId("dropdown-trigger")).toBeInTheDocument();
        });

        test("renders balance display with formatted amount", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the formatted balance is displayed
            expect(screen.getByText("$1,000.00")).toBeInTheDocument();
        });

        test("renders loading skeleton when dashboard data is loading", () => {
            const loadingStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: {
                    getAccountDetails: { data: null, loading: true },
                },
            });
            loadingStore.dispatch = jest.fn();

            render(
                <Provider store={loadingStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the loading skeleton is rendered (it's a div with animate-pulse class)
            const loadingElement = screen.getByTestId("loading-skeleton");
            expect(loadingElement).toBeInTheDocument();
            expect(loadingElement).toHaveClass("animate-pulse", "bg-gray-200", "h-10", "w-48", "rounded");
        });

        test("renders fallback amount when dashboard data is null", () => {
            const nullDataStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: {
                    getAccountDetails: { data: null, loading: false },
                },
            });
            nullDataStore.dispatch = jest.fn();

            render(
                <Provider store={nullDataStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the fallback amount is displayed
            expect(screen.getByText("$0.00")).toBeInTheDocument();
        });

        test("renders 'Select account' when no account is selected", () => {
            const noAccountStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: null,
                    accounts: [],
                },
                dashboard: {
                    getAccountDetails: { data: null, loading: false },
                },
            });
            noAccountStore.dispatch = jest.fn();

            render(
                <Provider store={noAccountStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the fallback text is displayed
            expect(screen.getByText(/Select account/)).toBeInTheDocument();
        });
    });

    describe("Dropdown Functionality", () => {
        beforeEach(() => {
            store = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "NGN",
                            accountName: "Savings Account",
                            accountNumber: "********",
                        },
                        {
                            balance: 2000,
                            currencyCode: "GBP",
                            accountName: "Business Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            store.dispatch = jest.fn();
        });

        test("renders dropdown content with all accounts", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            const dropdownContent = screen.getByTestId("dropdown-content");
            expect(dropdownContent).toBeInTheDocument();

            // Check that both accounts are rendered
            expect(screen.getByText("Savings Account")).toBeInTheDocument();
            expect(screen.getByText("Business Account")).toBeInTheDocument();
        });

        test("dispatches onSwitchAccount when an account is clicked", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Find and click the first account item
            const accountItems = screen.getAllByTestId("dropdown-item");
            fireEvent.click(accountItems[0]);

            expect(store.dispatch).toHaveBeenCalledWith(
                onSwitchAccount({
                    balance: 5000,
                    currencyCode: "NGN",
                    accountName: "Savings Account",
                    accountNumber: "********",
                })
            );
        });

        test("dispatches onSwitchAccount with correct account data when clicking different accounts", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Find and click the second account item
            const accountItems = screen.getAllByTestId("dropdown-item");
            fireEvent.click(accountItems[1]);

            expect(store.dispatch).toHaveBeenCalledWith(
                onSwitchAccount({
                    balance: 2000,
                    currencyCode: "GBP",
                    accountName: "Business Account",
                    accountNumber: "********",
                })
            );
        });

        test("applies correct styling to selected account", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            const accountItems = screen.getAllByTestId("dropdown-item");

            // The selected account (Main Account) should have the selected styling (bg-gray-100)
            // Since the selected account is "Main Account" with accountNumber "********"
            // and the first account in the list is "Savings Account" with "********",
            // the first account should NOT be selected, so it should have hover styling
            expect(accountItems[0]).toHaveClass("hover:bg-primary-ghost");

            // The second account should also have hover styling since it's not selected
            expect(accountItems[1]).toHaveClass("hover:bg-primary-ghost");
        });

        test("renders correct flag icons for each account", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that the correct flags are rendered
            expect(screen.getByTestId("NGFlag")).toBeInTheDocument();
            expect(screen.getByTestId("UKFlag")).toBeInTheDocument();
        });

        test("renders account details with currency and account number", () => {
            render(
                <Provider store={store}>
                    <AccountSwitcher />
                </Provider>
            );

            // Check that currency codes and account numbers are displayed
            expect(screen.getByText("NGN - ********")).toBeInTheDocument();
            expect(screen.getByText("GBP - ********")).toBeInTheDocument();
        });

        test("renders 'No accounts found' when accounts array is empty", () => {
            const emptyAccountsStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: defaultDashboardState,
            });
            emptyAccountsStore.dispatch = jest.fn();

            render(
                <Provider store={emptyAccountsStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByText("No accounts found")).toBeInTheDocument();
        });
    });

    describe("Currency Flag Rendering", () => {
        test("renders NGFlag for NGN currency", () => {
            const ngnStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "NGN",
                            accountName: "NGN Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            ngnStore.dispatch = jest.fn();

            render(
                <Provider store={ngnStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("NGFlag")).toBeInTheDocument();
        });

        test("renders USFlag for USD currency", () => {
            const usdStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "USD",
                            accountName: "USD Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            usdStore.dispatch = jest.fn();

            render(
                <Provider store={usdStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("USFlag")).toBeInTheDocument();
        });

        test("renders UKFlag for GBP currency", () => {
            const gbpStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "GBP",
                            accountName: "GBP Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            gbpStore.dispatch = jest.fn();

            render(
                <Provider store={gbpStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("UKFlag")).toBeInTheDocument();
        });

        test("renders EuroFlag for EUR currency", () => {
            const eurStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "EUR",
                            accountName: "EUR Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            eurStore.dispatch = jest.fn();

            render(
                <Provider store={eurStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("EuroFlag")).toBeInTheDocument();
        });

        test("renders NGFlag as default for unrecognized currency", () => {
            const unknownStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "XYZ",
                            accountName: "Unknown Account",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            unknownStore.dispatch = jest.fn();

            render(
                <Provider store={unknownStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("NGFlag")).toBeInTheDocument();
        });

        test("handles case-insensitive currency codes", () => {
            const mixedCaseStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            balance: 5000,
                            currencyCode: "ngn",
                            accountName: "Lowercase NGN",
                            accountNumber: "********",
                        },
                        {
                            balance: 2000,
                            currencyCode: "UsD",
                            accountName: "Mixed Case USD",
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            mixedCaseStore.dispatch = jest.fn();

            render(
                <Provider store={mixedCaseStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByTestId("NGFlag")).toBeInTheDocument();
            expect(screen.getByTestId("USFlag")).toBeInTheDocument();
        });
    });

    describe("Side Effects", () => {
        test("calls getAccounts on mount when component is first rendered", async () => {
            const idleStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: defaultDashboardState,
            });
            idleStore.dispatch = jest.fn();

            render(
                <Provider store={idleStore}>
                    <AccountSwitcher />
                </Provider>
            );

            await waitFor(() => {
                expect(getAccounts).toHaveBeenCalled();
            });
        });

        test("calls getAccountDetails when selectedAccount is defined", async () => {
            const customStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: defaultDashboardState,
            });
            customStore.dispatch = jest.fn();

            render(
                <Provider store={customStore}>
                    <AccountSwitcher />
                </Provider>
            );

            await waitFor(() => {
                expect(getAccountDetails).toHaveBeenCalledWith({ accountNumber: "********" });
            });
        });

        test("does not call getAccountDetails when selectedAccount is null", async () => {
            const noAccountStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: null,
                    accounts: [],
                },
                dashboard: defaultDashboardState,
            });
            noAccountStore.dispatch = jest.fn();

            render(
                <Provider store={noAccountStore}>
                    <AccountSwitcher />
                </Provider>
            );

            await waitFor(() => {
                expect(getAccounts).toHaveBeenCalled();
            });

            // Should not call getAccountDetails when selectedAccount is null
            expect(getAccountDetails).not.toHaveBeenCalled();
        });

        test("calls getAccountDetails when selectedAccount changes", async () => {
            const { rerender } = render(
                <Provider
                    store={mockStore({
                        account: {
                            loadingStatus: "loaded",
                            selectedAccount: {
                                balance: 1000,
                                currencyCode: "USD",
                                accountName: "Account 1",
                                accountNumber: "********",
                            },
                            accounts: [],
                        },
                        dashboard: defaultDashboardState,
                    })}
                >
                    <AccountSwitcher />
                </Provider>
            );

            await waitFor(() => {
                expect(getAccountDetails).toHaveBeenCalledWith({ accountNumber: "********" });
            });

            // Clear the mock to verify it's called again
            getAccountDetails.mockClear();

            // Rerender with a different selectedAccount
            rerender(
                <Provider
                    store={mockStore({
                        account: {
                            loadingStatus: "loaded",
                            selectedAccount: {
                                balance: 2000,
                                currencyCode: "GBP",
                                accountName: "Account 2",
                                accountNumber: "********",
                            },
                            accounts: [],
                        },
                        dashboard: defaultDashboardState,
                    })}
                >
                    <AccountSwitcher />
                </Provider>
            );

            await waitFor(() => {
                expect(getAccountDetails).toHaveBeenCalledWith({ accountNumber: "********" });
            });
        });
    });

    describe("Edge Cases", () => {
        test("handles undefined currencyCode gracefully", () => {
            const undefinedCurrencyStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 500,
                        accountName: "No Currency Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: {
                    getAccountDetails: { data: { balance: 500 }, loading: false },
                },
            });
            undefinedCurrencyStore.dispatch = jest.fn();

            render(
                <Provider store={undefinedCurrencyStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Should default to NGN formatting
            expect(screen.getByText("₦500.00")).toBeInTheDocument();
        });

        test("handles null selectedAccount gracefully", () => {
            const nullAccountStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: null,
                    accounts: [],
                },
                dashboard: {
                    getAccountDetails: { data: null, loading: false },
                },
            });
            nullAccountStore.dispatch = jest.fn();

            render(
                <Provider store={nullAccountStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Should show fallback text and default amount
            expect(screen.getByText(/Select account/)).toBeInTheDocument();
            expect(screen.getByText("₦0.00")).toBeInTheDocument();
        });

        test("handles empty accounts array", () => {
            const emptyAccountsStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [],
                },
                dashboard: defaultDashboardState,
            });
            emptyAccountsStore.dispatch = jest.fn();

            render(
                <Provider store={emptyAccountsStore}>
                    <AccountSwitcher />
                </Provider>
            );

            expect(screen.getByText("No accounts found")).toBeInTheDocument();
        });

        test("handles accounts with missing properties", () => {
            const incompleteAccountsStore = mockStore({
                account: {
                    loadingStatus: "loaded",
                    selectedAccount: {
                        balance: 1000,
                        currencyCode: "USD",
                        accountName: "Main Account",
                        accountNumber: "********",
                    },
                    accounts: [
                        {
                            // Missing properties
                            accountNumber: "********",
                        },
                    ],
                },
                dashboard: defaultDashboardState,
            });
            incompleteAccountsStore.dispatch = jest.fn();

            render(
                <Provider store={incompleteAccountsStore}>
                    <AccountSwitcher />
                </Provider>
            );

            // Should still render the account item
            expect(screen.getByTestId("dropdown-item")).toBeInTheDocument();
        });
    });
});
