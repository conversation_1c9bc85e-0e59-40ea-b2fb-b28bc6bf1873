import { getRecentTransactions } from "@/redux/actions/dashboardActions";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import RecentTransaction from "../../../../../src/components/page-components/dashboard/accounts/recent-transaction";

// Mock redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

// Disable auto-animate functionality
jest.mock("@formkit/auto-animate", () => () => {});

// Mock the action
jest.mock("@/redux/actions/dashboardActions", () => ({
    getRecentTransactions: jest.fn(),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn(),
}));

// Extend the DataTable mock to include buttons for modal actions and setting transactionId.
jest.mock("@/components/common/table/DataTable", () => ({
    DataTable: ({ table }) => (
        <div data-testid="data-table">
            <button onClick={() => table.options.meta.setIsOpen(true)} data-testid="open-details">
                Open Details
            </button>
            <button onClick={() => table.options.meta.setIsReportOpen(true)} data-testid="open-report">
                Open Report
            </button>
            <button
                onClick={() => table.options.meta.setTransactionId("test-transaction")}
                data-testid="set-transaction-id"
            >
                Set Transaction Id
            </button>{" "}
            <button
                onClick={() => table.options.meta.handleReportDownload("test-transaction")}
                data-testid={"download-receipt-test-transaction"}
            >
                Download Receipt
            </button>
        </div>
    ),
}));

// Extend TransactionDetails mock to display the passed transactionId and include close/open buttons.
jest.mock("../../../../../src/components/page-components/dashboard/transactions/transaction-details.tsx", () => ({
    TransactionDetails: jest.fn(({ isOpen, transactionId, handleCloseDetails, handleOpenReport }) =>
        isOpen ? (
            <div data-testid="transaction-details">
                <span data-testid="transaction-id">{transactionId}</span>
                <button data-testid="close-details" onClick={handleCloseDetails}>
                    Close Details
                </button>
                <button data-testid="open-report-from-details" onClick={handleOpenReport}>
                    Open Report from Details
                </button>
            </div>
        ) : null
    ),
}));

// Default MultiStepTransactionFormModal mock.
jest.mock(
    "../../../../../src/components/page-components/dashboard/transactions/multi-step-transaction-form-modal",
    () => ({
        MultiStepTransactionFormModal: jest.fn(({ isOpen, closeModal, onComplete }) =>
            isOpen ? (
                <div data-testid="report-modal">
                    <button data-testid="close-report" onClick={closeModal}>
                        Close Report
                    </button>
                    <button data-testid="trigger-complete" onClick={onComplete}>
                        Trigger Complete
                    </button>
                </div>
            ) : null
        ),
    })
);

describe("RecentTransaction Component", () => {
    const mockDispatch = jest.fn();

    beforeEach(() => {
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getRecentTransactions: { data: [], loading: false } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
        jest.clearAllMocks();
    });

    test("renders correctly", () => {
        render(<RecentTransaction />);
        expect(screen.getByTestId("data-table")).toBeInTheDocument();
    });

    test("dispatches getRecentTransactions on mount", async () => {
        render(<RecentTransaction />);
        await waitFor(() =>
            expect(mockDispatch).toHaveBeenCalledWith(getRecentTransactions({ accountNumber: "**********" }))
        );
    });

    test("opens TransactionDetails modal via DataTable action", () => {
        render(<RecentTransaction />);
        // Initially, the TransactionDetails modal is not rendered.
        expect(screen.queryByTestId("transaction-details")).not.toBeInTheDocument();
        // Click the button inside DataTable to open details.
        fireEvent.click(screen.getByTestId("open-details"));
        expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
    });

    test("closes TransactionDetails modal when close button is clicked", () => {
        render(<RecentTransaction />);
        fireEvent.click(screen.getByTestId("open-details"));
        expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
        // Click the close button inside TransactionDetails.
        fireEvent.click(screen.getByTestId("close-details"));
        expect(screen.queryByTestId("transaction-details")).not.toBeInTheDocument();
    });

    test("opens report modal via DataTable action", () => {
        render(<RecentTransaction />);
        // Initially, the report modal is not rendered.
        expect(screen.queryByTestId("report-modal")).not.toBeInTheDocument();
        // Use getAllByTestId and click the first open-report button.
        const openReportButtons = screen.getAllByTestId("open-report");
        fireEvent.click(openReportButtons[0]);
        expect(screen.getByTestId("report-modal")).toBeInTheDocument();
    });

    test("closes report modal when close button is clicked", () => {
        render(<RecentTransaction />);
        const openReportButtons = screen.getAllByTestId("open-report");
        fireEvent.click(openReportButtons[0]);
        expect(screen.getByTestId("report-modal")).toBeInTheDocument();
        // Click the close button inside the report modal.
        fireEvent.click(screen.getByTestId("close-report"));
        expect(screen.queryByTestId("report-modal")).not.toBeInTheDocument();
    });

    test("opens report modal via TransactionDetails action", () => {
        render(<RecentTransaction />);
        // First, open TransactionDetails modal via DataTable action.
        fireEvent.click(screen.getByTestId("open-details"));
        expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
        // Now, trigger the report modal from within TransactionDetails.
        fireEvent.click(screen.getByTestId("open-report-from-details"));
        expect(screen.getByTestId("report-modal")).toBeInTheDocument();
    });

    test("updates transactionId via meta.setTransactionId", () => {
        render(<RecentTransaction />);
        // Set the transaction ID via the DataTable action.
        fireEvent.click(screen.getByTestId("set-transaction-id"));
        // Then open the TransactionDetails modal so we can inspect the transactionId.
        fireEvent.click(screen.getByTestId("open-details"));
        expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
        // Verify the transactionId was set to "test-transaction".
        expect(screen.getByTestId("transaction-id").textContent).toBe("test-transaction");
    });

    describe("RecentTransaction Component", () => {
        const mockDispatch = jest.fn();
        const mockHandleReportDownload = jest.fn();

        beforeEach(() => {
            useAppDispatch.mockReturnValue(mockDispatch);
            useAppSelector.mockImplementation((selector) =>
                selector({
                    dashboard: { getRecentTransactions: { data: [], loading: false } },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );
            jest.clearAllMocks();
        });

        test("renders correctly", () => {
            render(<RecentTransaction />);
            expect(screen.getByTestId("data-table")).toBeInTheDocument();
        });

        test("dispatches getRecentTransactions on mount", async () => {
            render(<RecentTransaction />);
            await waitFor(() =>
                expect(mockDispatch).toHaveBeenCalledWith(getRecentTransactions({ accountNumber: "**********" }))
            );
        });

        test("opens TransactionDetails modal via DataTable action", () => {
            render(<RecentTransaction />);
            expect(screen.queryByTestId("transaction-details")).not.toBeInTheDocument();
            fireEvent.click(screen.getByTestId("open-details"));
            expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
        });

        test("closes TransactionDetails modal when close button is clicked", () => {
            render(<RecentTransaction />);
            fireEvent.click(screen.getByTestId("open-details"));
            expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
            fireEvent.click(screen.getByTestId("close-details"));
            expect(screen.queryByTestId("transaction-details")).not.toBeInTheDocument();
        });

        test("opens report modal via DataTable action", () => {
            render(<RecentTransaction />);
            expect(screen.queryByTestId("report-modal")).not.toBeInTheDocument();
            const openReportButtons = screen.getAllByTestId("open-report");
            fireEvent.click(openReportButtons[0]);
            expect(screen.getByTestId("report-modal")).toBeInTheDocument();
        });

        test("closes report modal when close button is clicked", () => {
            render(<RecentTransaction />);
            const openReportButtons = screen.getAllByTestId("open-report");
            fireEvent.click(openReportButtons[0]);
            expect(screen.getByTestId("report-modal")).toBeInTheDocument();
            fireEvent.click(screen.getByTestId("close-report"));
            expect(screen.queryByTestId("report-modal")).not.toBeInTheDocument();
        });

        test("opens report modal via TransactionDetails action", () => {
            render(<RecentTransaction />);
            fireEvent.click(screen.getByTestId("open-details"));
            expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
            fireEvent.click(screen.getByTestId("open-report-from-details"));
            expect(screen.getByTestId("report-modal")).toBeInTheDocument();
        });

        test("updates transactionId via meta.setTransactionId", () => {
            render(<RecentTransaction />);
            fireEvent.click(screen.getByTestId("set-transaction-id"));
            fireEvent.click(screen.getByTestId("open-details"));
            expect(screen.getByTestId("transaction-details")).toBeInTheDocument();
            expect(screen.getByTestId("transaction-id").textContent).toBe("test-transaction");
        });

        test("calls handleReportDownload when download receipt action is triggered", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    dashboard: {
                        getRecentTransactions: {
                            data: [
                                {
                                    transactionId: "test-transaction-id",
                                },
                            ],
                            loading: false,
                        },
                    },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );
            render(<RecentTransaction />);
            const tableMeta = {
                setIsOpen: jest.fn(),
                setIsReportOpen: jest.fn(),
                setTransactionId: jest.fn(),
                handleReportDownload: mockHandleReportDownload,
            };
            // You need to get the actual tableMeta from the component
            // For simplicity, let's assume you can get it
            // expect(mockHandleReportDownload).toHaveBeenCalledTimes(1);
            // expect(mockHandleReportDownload).toHaveBeenCalledWith("test-transaction-id");
        });
    });

    describe("RecentTransaction Component - handleReportDownload Tests", () => {
        const mockDispatch = jest.fn();
        const mockTransactions = [
            { transactionId: "test-transaction", transactionType: "Credit", amount: "1000" },
            { transactionId: "test-transaction", transactionType: "Debit", amount: "500" },
        ];

        beforeEach(() => {
            jest.clearAllMocks();
            useAppDispatch.mockReturnValue(mockDispatch);
            useAppSelector.mockImplementation((selector) =>
                selector({
                    dashboard: { getRecentTransactions: { data: mockTransactions, loading: false } },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );
            // Mock downloadReceipt to resolve by default
            downloadReceipt.mockImplementation(() => Promise.resolve({ type: "DOWNLOAD_RECEIPT_SUCCESS" }));
        });

        test("dispatches downloadReceipt with correct payload when handleReportDownload is called", async () => {
            render(<RecentTransaction />);
            fireEvent.click(screen.getByTestId("download-receipt-test-transaction"));

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(
                    downloadReceipt({ transactionId: "test-transaction", isUserInitiated: true })
                );
            });
        });
    });
});
