import AccountRequestDialog from "@/components/page-components/dashboard/accounts/account-request-dialog";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

import { closeRequestAccountDialog } from "@/redux/features/uiDialogSlice";
import userEvent from "@testing-library/user-event";

const mockStore = configureStore([]);

describe("AccountRequestDialog", () => {
    let store;

    beforeEach(() => {
        store = mockStore({
            uiDialog: { isOpen: true },
        });
        store.dispatch = jest.fn();
    });

    test("renders correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        expect(screen.getByText("Request an account")).toBeInTheDocument();
    });

    test("dispatches closeRequestAccountDialog on close button click", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        fireEvent.click(screen.getByTestId("close-button"));
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    test("handles dropdown selection correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        fireEvent.change(screen.getByLabelText("What would you like to request for?"), {
            target: { value: "I want to add an existing account" },
        });
        expect(screen.getByText("Account Type")).toBeInTheDocument();
    });

    test("calls dispatch on cancel button click", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        fireEvent.click(screen.getByText("Cancel"));
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    test("renders the dialog correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        expect(screen.getByText("Request an account")).toBeInTheDocument();
    });

    test("closes the dialog when close button is clicked", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const closeButton = screen.getByTestId("close-button");
        fireEvent.click(closeButton);
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    test("allows selecting request type", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const dropdown = screen.getByLabelText("What would you like to request for?");
        fireEvent.change(dropdown, { target: { value: "I want to add an existing account" } });
        expect(dropdown.value).toBe("I want to add an existing account");
    });

    test("submits the form correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);
        // Since submitValues logs the form values, we can't directly test dispatch,
        // but we can confirm no errors occur during submission.
    });
    // new 1
    test("renders the component correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        expect(screen.getByText("Request an account")).toBeInTheDocument();
    });

    test("dispatches close dialog action on close button click", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const closeButton = screen.getByTestId("close-button");
        fireEvent.click(closeButton);
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    test("changes request type to existing account and displays additional fields", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const dropdown = screen.getByLabelText("What would you like to request for?");
        fireEvent.change(dropdown, { target: { value: "I want to add an existing account" } });
        expect(screen.getByText("Account name")).toBeInTheDocument();
    });

    test("submits form with valid data", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);
        expect(store.dispatch).not.toHaveBeenCalledWith(closeRequestAccountDialog()); // Ensures form submit does not close modal unintentionally
    });

    //new 2
    it("renders the component correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        expect(screen.getByText("Request an account")).toBeInTheDocument();
    });

    it("closes the modal when the close button is clicked", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        fireEvent.click(screen.getByTestId("close-button"));
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    it("updates state when selecting a request type", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        const requestTypeDropdown = screen.getByLabelText("What would you like to request for?");
        fireEvent.change(requestTypeDropdown, { target: { value: "I want to add an existing account" } });

        expect(requestTypeDropdown).toHaveValue("I want to add an existing account");
    });

    it("updates isExistingAccount state correctly based on request type selection", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        const requestTypeDropdown = screen.getByLabelText("What would you like to request for?");
        fireEvent.change(requestTypeDropdown, { target: { value: "I want to add an existing account" } });
        expect(screen.getByText("Account name")).toBeInTheDocument(); // Ensuring Account Name dropdown appears
    });

    it("submits the form correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);
        // Ensure form submission logic is executed
    });

    //new 3
    it("renders the component correctly", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        expect(screen.getByText(/Request an account/i)).toBeInTheDocument();
    });

    it("dispatches closeRequestAccountDialog action when close button is clicked", () => {
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );
        const closeButton = screen.getByTestId("close-button");
        fireEvent.click(closeButton);
        expect(store.dispatch).toHaveBeenCalledWith(closeRequestAccountDialog());
    });

    // it("updates state when selecting request type", async () => {
    //   render(
    //     <Provider store={store}>
    //       <AccountRequestDialog />
    //     </Provider>
    //   );

    //   const dropdown = screen.getByLabelText("What would you like to request for?");
    //   await userEvent.selectOptions(dropdown, { target: { value: "I want to add an existing account" } });

    //   expect(screen.getByText("I want to add an existing account")).toBeInTheDocument();
    // });

    it("submits the form when submit button is clicked", async () => {
        console.log = jest.fn();
        render(
            <Provider store={store}>
                <AccountRequestDialog />
            </Provider>
        );

        const submitButton = screen.getByText("Submit");
        await userEvent.click(submitButton);
    });
});
