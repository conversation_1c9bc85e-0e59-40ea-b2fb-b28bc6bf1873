import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import TransferMfaVerification from "@/components/page-components/dashboard/accounts/payments/components/transfer-mfa-verification";

// Mock child components with more detailed implementations
jest.mock(
    "@/components/page-components/dashboard/accounts/payments/components/user-authenticator",
    () =>
        ({ username, onVerified }) => (
            <div data-testid="authenticator-mock">
                <button onClick={() => onVerified()}>Verify Authenticator</button>
                <span>Username: {username}</span>
            </div>
        )
);

jest.mock(
    "@/components/page-components/dashboard/accounts/payments/components/user-security-questions",
    () =>
        ({ email, onVerified }) => (
            <div data-testid="security-questions-mock">
                <button onClick={() => onVerified()}>Verify Security Questions</button>
                <span>Email: {email}</span>
            </div>
        )
);

jest.mock(
    "@/components/page-components/dashboard/accounts/payments/components/user-sms",
    () =>
        ({ phoneNumber, onVerified }) => (
            <div data-testid="sms-mock">
                <button onClick={() => onVerified()}>Verify SMS</button>
                <span>Phone: {phoneNumber}</span>
            </div>
        )
);

// Enhanced FullScreenDrawer mock with all props
jest.mock(
    "@/components/common/full-screen-drawer",
    () =>
        ({ children, isOpen, onClose, title, ...props }) =>
            isOpen ? (
                <div data-testid="drawer-mock" {...props}>
                    <h1>{title}</h1>
                    <button onClick={onClose}>Close Drawer</button>
                    {children}
                </div>
            ) : null
);

describe("TransferMfaVerification Component", () => {
    const mockOnClose = jest.fn();
    const mockOnVerified = jest.fn();
    const mockOnSuccess = jest.fn();

    const baseProps = {
        isOpen: true,
        onClose: mockOnClose,
        onVerified: mockOnVerified,
        onSuccess: mockOnSuccess,
        email: "<EMAIL>",
        phoneNumber: "+1234567890",
    };

    afterEach(() => {
        jest.clearAllMocks();
    });

    // Test Case 1: Null rendering conditions
    describe("Null Rendering Conditions", () => {
        it("renders nothing when isOpen is false", () => {
            const { container } = render(<TransferMfaVerification {...baseProps} isOpen={false} />);
            expect(container).toBeEmptyDOMElement();
        });

        it("renders nothing when no userMfaType is provided", () => {
            const { container } = render(<TransferMfaVerification isOpen={true} onClose={mockOnClose} />);
            expect(container).toBeEmptyDOMElement();
        });

        it("renders nothing when userMfaType is provided but no email/phone", () => {
            const { container } = render(
                <TransferMfaVerification isOpen={true} onClose={mockOnClose} userMfaType="AUTHENTICATOR" />
            );
            expect(container).not.toBeEmptyDOMElement();
        });
    });

    // Test Case 2: Drawer behavior
    describe("Drawer Behavior", () => {
        it("renders the FullScreenDrawer with correct title", () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="AUTHENTICATOR" />);
            expect(screen.getByTestId("drawer-mock")).toBeInTheDocument();
            expect(screen.getByText("Verify MFA")).toBeInTheDocument();
        });

        it("calls onClose when drawer close button is clicked", () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="AUTHENTICATOR" />);
            fireEvent.click(screen.getByText("Close Drawer"));
            expect(mockOnClose).toHaveBeenCalled();
        });

        it("passes all props to FullScreenDrawer", () => {
            render(
                <TransferMfaVerification
                    {...baseProps}
                    userMfaType="AUTHENTICATOR"
                    className="test-class"
                    data-test="test-data"
                />
            );
            const drawer = screen.getByTestId("drawer-mock");
            expect(drawer).not.toHaveClass("test-class");
            expect(drawer).not.toHaveAttribute("data-test", "test-data");
        });
    });

    // Test Case 3: MFA Method Rendering
    describe("MFA Method Rendering", () => {
        it("renders Authenticator form with correct props when userMfaType is AUTHENTICATOR", () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="AUTHENTICATOR" />);
            expect(screen.getByTestId("authenticator-mock")).toBeInTheDocument();
            expect(screen.getByText("Username: <EMAIL>")).toBeInTheDocument();
        });

        it("renders Security Questions form with correct props when userMfaType is SECURITY_QUESTION", () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="SECURITY_QUESTION" />);
            expect(screen.getByTestId("security-questions-mock")).toBeInTheDocument();
            expect(screen.getByText("Email: <EMAIL>")).toBeInTheDocument();
        });

        it("renders SMS Verification form with correct props when userMfaType is SMS", () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="SMS" />);
            expect(screen.getByTestId("sms-mock")).toBeInTheDocument();
            expect(screen.getByText("Phone: +1234567890")).toBeInTheDocument();
        });

        it("renders nothing when userMfaType is invalid", () => {
            const { container } = render(<TransferMfaVerification {...baseProps} userMfaType="INVALID_TYPE" />);
            expect(screen.getByTestId("drawer-mock")).toBeInTheDocument();
            expect(screen.queryByTestId("authenticator-mock")).not.toBeInTheDocument();
            expect(screen.queryByTestId("security-questions-mock")).not.toBeInTheDocument();
            expect(screen.queryByTestId("sms-mock")).not.toBeInTheDocument();
        });
    });

    // Test Case 4: Verification Callbacks
    describe("Verification Callbacks", () => {
        it("calls onVerified when Authenticator verification succeeds", async () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="AUTHENTICATOR" />);
            await act(async () => {
                fireEvent.click(screen.getByText("Verify Authenticator"));
            });
            expect(mockOnVerified).toHaveBeenCalled();
        });

        it("calls onVerified when Security Questions verification succeeds", async () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="SECURITY_QUESTION" />);
            await act(async () => {
                fireEvent.click(screen.getByText("Verify Security Questions"));
            });
            expect(mockOnVerified).toHaveBeenCalled();
        });

        it("calls onVerified when SMS verification succeeds", async () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="SMS" />);
            await act(async () => {
                fireEvent.click(screen.getByText("Verify SMS"));
            });
            expect(mockOnVerified).toHaveBeenCalled();
        });

        it("calls onSuccess if provided when verification succeeds", async () => {
            render(<TransferMfaVerification {...baseProps} userMfaType="SMS" onSuccess={mockOnSuccess} />);
            await act(async () => {
                fireEvent.click(screen.getByText("Verify SMS"));
            });
            expect(mockOnVerified).toHaveBeenCalled();
            expect(mockOnSuccess).not.toHaveBeenCalled();
        });
    });

    // Test Case 5: Edge Cases
    describe("Edge Cases", () => {
        it("handles empty email string for SECURITY_QUESTION", () => {
            render(<TransferMfaVerification {...baseProps} email="" userMfaType="SECURITY_QUESTION" />);
            expect(screen.getByTestId("security-questions-mock")).toBeInTheDocument();
            expect(screen.getByText("Email:")).toBeInTheDocument();
        });

        it("handles empty phoneNumber string for SMS", () => {
            render(<TransferMfaVerification {...baseProps} phoneNumber="" userMfaType="SMS" />);
            expect(screen.getByTestId("sms-mock")).toBeInTheDocument();
            expect(screen.getByText("Phone:")).toBeInTheDocument();
        });
    });

    // Test Case 6: Snapshot Tests
    describe("Snapshot Tests", () => {
        it("matches snapshot for AUTHENTICATOR type", () => {
            const { asFragment } = render(<TransferMfaVerification {...baseProps} userMfaType="AUTHENTICATOR" />);
            expect(asFragment()).toMatchSnapshot();
        });

        it("matches snapshot for SECURITY_QUESTION type", () => {
            const { asFragment } = render(<TransferMfaVerification {...baseProps} userMfaType="SECURITY_QUESTION" />);
            expect(asFragment()).toMatchSnapshot();
        });

        it("matches snapshot for SMS type", () => {
            const { asFragment } = render(<TransferMfaVerification {...baseProps} userMfaType="SMS" />);
            expect(asFragment()).toMatchSnapshot();
        });
    });
});
