// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TransferMfaVerification Component Snapshot Tests matches snapshot for AUTHENTICATOR type 1`] = `
<DocumentFragment>
  <div
    data-testid="drawer-mock"
  >
    <h1>
      Verify MFA
    </h1>
    <button>
      Close Drawer
    </button>
    <div
      data-testid="authenticator-mock"
    >
      <button>
        Verify Authenticator
      </button>
      <span>
        Username: <EMAIL>
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`TransferMfaVerification Component Snapshot Tests matches snapshot for SECURITY_QUESTION type 1`] = `
<DocumentFragment>
  <div
    data-testid="drawer-mock"
  >
    <h1>
      Verify MFA
    </h1>
    <button>
      Close Drawer
    </button>
    <div
      data-testid="security-questions-mock"
    >
      <button>
        Verify Security Questions
      </button>
      <span>
        Email: <EMAIL>
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`TransferMfaVerification Component Snapshot Tests matches snapshot for SMS type 1`] = `
<DocumentFragment>
  <div
    data-testid="drawer-mock"
  >
    <h1>
      Verify MFA
    </h1>
    <button>
      Close Drawer
    </button>
    <div
      data-testid="sms-mock"
    >
      <button>
        Verify SMS
      </button>
      <span>
        Phone: +1234567890
      </span>
    </div>
  </div>
</DocumentFragment>
`;
