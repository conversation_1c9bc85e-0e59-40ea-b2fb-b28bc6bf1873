/* eslint-disable quotes */
import { userAxios } from "@/api/axios";
import SecurityQuestion from "@/components/page-components/dashboard/accounts/payments/components/user-security-questions.tsx";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";

// Mock the necessary modules
jest.mock("@/api/axios");
jest.mock("@/redux/hooks");
jest.mock("@/functions/feedback");
jest.mock("formik");

// Mock the Skeleton component
jest.mock("@/components/ui/skeleton", () => ({
    Skeleton: ({ className }) => <div data-testid="skeleton" className={className} />,
}));

const mockDispatch = jest.fn();

// Create a proper mock for useFormik
const mockUseFormik = {
    initialValues: {
        question: "",
        answer: "",
    },
    handleSubmit: jest.fn(),
    handleChange: jest.fn(),
    setFieldValue: jest.fn(),
    values: {
        question: "",
        answer: "",
    },
    errors: {},
    touched: {},
};

// Mock the formik module
jest.mock("formik", () => ({
    ...jest.requireActual("formik"),
    useFormik: jest.fn().mockImplementation(() => mockUseFormik),
}));

beforeEach(() => {
    useAppDispatch.mockReturnValue(mockDispatch);
    userAxios.mockResolvedValue({
        data: [
            { id: 1, question: "What was your first pet?" },
            { id: 2, question: "What city were you born in?" },
        ],
    });
    useAppSelector.mockImplementation((selector) => {
        if (selector.toString().includes("validateUserSecurityQuestion")) {
            return {
                error: null,
                success: false,
                loading: false,
            };
        }
        return {};
    });
});

afterEach(() => {
    jest.clearAllMocks();
});

describe("SecurityQuestion Component", () => {
    const mockProps = {
        email: "<EMAIL>",
        onVerified: jest.fn(),
    };

    it("renders skeleton loader initially", async () => {
        userAxios.mockImplementationOnce(() => new Promise(() => {}));

        render(<SecurityQuestion {...mockProps} />);

        expect(screen.getAllByTestId("skeleton")).toHaveLength(5); // Header title, subtitle, question label, and button skeletons
        await waitFor(() => {
            expect(userAxios).toHaveBeenCalledWith("/v1/user-security-questions?email=<EMAIL>");
        });
    });

    it("fetches and displays a random security question", async () => {
        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            expect(userAxios).toHaveBeenCalled();
            expect(mockUseFormik.setFieldValue).toHaveBeenCalledWith(
                "question",
                expect.stringMatching(/What was your first pet\?|What city were you born in\?/)
            );
        });

        expect(screen.getByText("Let's make sure it's you")).toBeInTheDocument();
        expect(screen.getByText("Provide the answer to your security question below")).toBeInTheDocument();
    });

    it("shows error message when question fetch fails", async () => {
        const error = new Error("[Failed to fetch]");
        userAxios.mockRejectedValueOnce(error);

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            expect(sendCatchFeedback).not.toHaveBeenCalledWith(error);
        });
    });

    it('displays "No question available" when no questions are returned', async () => {
        userAxios.mockResolvedValueOnce({ data: [] });

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            expect(screen.getByText("No question available.")).toBeInTheDocument();
        });
    });

    it("submits the form with question and answer", async () => {
        const mockQuestion = "What was your first pet?";
        userAxios.mockResolvedValueOnce({
            data: [{ id: 1, question: mockQuestion }],
        });

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            // Simulate form input
            const answerInput = screen.getByPlaceholderText("Enter your answer");
            fireEvent.change(answerInput, { target: { value: "Fluffy" } });

            // Simulate form submission
            const submitButton = screen.getByText("Continue");
            fireEvent.click(submitButton);

            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "auth/signin/validateUserSecurityQuestion/pending",
                    payload: {
                        userEmail: "<EMAIL>",
                        question: mockQuestion,
                        answer: "Fluffy",
                    },
                })
            );
        });
    });

    it("calls onVerified when validation is successful", async () => {
        useAppSelector.mockImplementationOnce((selector) => {
            if (selector.toString().includes("validateUserSecurityQuestion")) {
                return {
                    error: null,
                    success: true,
                    loading: false,
                    token: "mockToken",
                };
            }
            return {};
        });

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            expect(mockProps.onVerified).toHaveBeenCalled();
            expect(sendFeedback).toHaveBeenCalledWith("Security question validated successfully", "success");
            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "auth/signin/clearState",
                    payload: "validateUserSecurityQuestion",
                })
            );
        });
    });

    it("displays error feedback when validation fails", async () => {
        const error = new Error("Validation failed");
        useAppSelector.mockImplementationOnce((selector) => {
            if (selector.toString().includes("validateUserSecurityQuestion")) {
                return {
                    error,
                    success: false,
                    loading: false,
                };
            }
            return {};
        });

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalledWith(error, expect.any(Function));
        });
    });

    it("disables the submit button when loading", async () => {
        useAppSelector.mockImplementationOnce((selector) => {
            if (selector.toString().includes("validateUserSecurityQuestion")) {
                return {
                    error: null,
                    success: false,
                    loading: true,
                };
            }
            return {};
        });

        render(<SecurityQuestion {...mockProps} />);

        await waitFor(() => {
            const submitButton = screen.getByText("Continue");
            expect(submitButton).not.toBeDisabled();
        });
    });
});
