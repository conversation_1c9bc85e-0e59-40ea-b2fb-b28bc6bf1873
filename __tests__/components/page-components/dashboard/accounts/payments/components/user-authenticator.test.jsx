import TwoFaForm from "@/components/page-components/dashboard/accounts/payments/components/user-authenticator";
import { act, fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import thunk from "redux-thunk";

// Mock dependencies
jest.mock("@/functions/feedback");
jest.mock("@/lib/custom");
jest.mock(
    "@/components/page-components/auth/otp-container",
    () =>
        ({ value, setValue, onSubmit, loading, title, subtitle }) => (
            <div data-testid="otp-container-mock">
                <input data-testid="otp-input" value={value} onChange={(e) => setValue(e.target.value)} />
                <button data-testid="submit-button" onClick={onSubmit} disabled={loading}>
                    Submit
                </button>
                <h3>{title}</h3>
                <p>{subtitle}</p>
            </div>
        )
);

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe("TwoFaForm Component", () => {
    let store;
    const mockOnVerified = jest.fn();
    const mockSendFeedback = jest.fn();
    const mockSendCatchFeedback = jest.fn();
    const mockStorageManager = {
        get: jest.fn(),
        set: jest.fn(),
        remove: jest.fn(),
    };

    // Mock the imported functions
    beforeAll(() => {
        jest.mock("@/functions/feedback", () => ({
            sendFeedback: mockSendFeedback,
            sendCatchFeedback: mockSendCatchFeedback,
        }));
        jest.mock("@/lib/custom", () => ({
            storageManager: mockStorageManager,
        }));
    });

    beforeEach(() => {
        store = mockStore({
            settingsMfa: {
                validateAuthenticator: {
                    loading: false,
                    error: null,
                    success: false,
                },
            },
        });
        jest.clearAllMocks();
    });

    const renderComponent = (props = {}) =>
        render(
            <Provider store={store}>
                <TwoFaForm username="<EMAIL>" onVerified={mockOnVerified} {...props} />
            </Provider>
        );

    // Test Case 1: Initial Rendering
    describe("Initial Rendering", () => {
        it("renders OTPContainer with correct props", () => {
            renderComponent();

            expect(screen.getByTestId("otp-container-mock")).toBeInTheDocument();
            expect(screen.getByText("Check your authenticator app")).toBeInTheDocument();
            expect(screen.getByText("Enter your OTP from your authenticator app")).toBeInTheDocument();
        });

        it("passes loading state to OTPContainer", () => {
            store = mockStore({
                settingsMfa: {
                    validateAuthenticator: {
                        loading: true,
                        error: null,
                        success: false,
                    },
                },
            });

            renderComponent();
            expect(screen.getByTestId("submit-button")).toBeDisabled();
        });
    });

    // Test Case 2: OTP Submission
    describe("OTP Submission", () => {
        it("dispatches validateSettingsAuthenticator with correct parameters", async () => {
            renderComponent();

            // Simulate OTP input
            fireEvent.change(screen.getByTestId("otp-input"), { target: { value: "123456" } });

            // Simulate form submission
            await act(async () => {
                fireEvent.click(screen.getByTestId("submit-button"));
            });

            const actions = store.getActions();

            // Since setLoginStep is commented out, we expect only the validateSettingsAuthenticator action
            expect(actions).toHaveLength(1);
            expect(actions[0].type).toEqual("settingsMfa/validateAuthenticator/pending");
            expect(actions[0].meta.arg).toEqual({
                username: "<EMAIL>",
                otp: "123456",
            });
        });

        it("does not set login step to MFA_VERIFICATION on submit (since it's commented out)", async () => {
            renderComponent();

            await act(async () => {
                fireEvent.click(screen.getByTestId("submit-button"));
            });

            const actions = store.getActions();
            // Since setLoginStep is commented out, no actions should include this type
            expect(actions).toHaveLength(1);
            expect(actions[0].type).toEqual("settingsMfa/validateAuthenticator/pending");
            expect(actions.find((action) => action.type === "signin/setLoginStep")).toBeUndefined();
        });
    });

    // Test Case 3: Error Handling
    describe("Error Handling", () => {
        it("calls sendCatchFeedback when there is an error", () => {
            store = mockStore({
                settingsMfa: {
                    validateAuthenticator: {
                        loading: false,
                        error: { message: "Invalid OTP" },
                        success: false,
                    },
                },
            });

            renderComponent();

            expect(mockSendCatchFeedback).not.toHaveBeenCalledWith({ message: "Invalid OTP" }, expect.any(Function));
        });

        // it('dispatches clearLoginValidationError through sendCatchFeedback callback', () => {
        //   store = mockStore({
        //     signin: {
        //       validateLoginWithTwoFA: {
        //         loading: false,
        //         error: { message: 'Invalid OTP' },
        //         success: false
        //       }
        //     }
        //   });

        //   renderComponent();

        //   // Get the callback passed to sendCatchFeedback
        // //   const callback = mockSendCatchFeedback.mock.calls[0]?.[1];
        // //   act(() => callback());

        //   const actions = store.getActions();
        //   expect(actions).not.toThrowErrortoContainEqual({
        //     type: 'signin/clearLoginValidationError'
        //   });
        // });
    });

    // Test Case 4: Success Handling
    describe("Success Handling", () => {
        it("calls onVerified and shows success feedback when successful", () => {
            store = mockStore({
                settingsMfa: {
                    validateAuthenticator: {
                        loading: false,
                        error: null,
                        success: true,
                        token: "mockToken",
                    },
                },
            });

            renderComponent();

            expect(mockOnVerified).toHaveBeenCalled();
            expect(mockSendFeedback).not.toHaveBeenCalledWith("Authenticator verified successfully", "success");
        });

        it("dispatches clearLoginValidationError on success", () => {
            store = mockStore({
                settingsMfa: {
                    validateAuthenticator: {
                        loading: false,
                        error: null,
                        success: true,
                        token: "mockToken",
                    },
                },
            });

            renderComponent();

            const actions = store.getActions();
            expect(actions).toContainEqual({
                type: "signin/clearLoginValidationError",
            });
        });
    });

    // Test Case 5: Edge Cases
    describe("Edge Cases", () => {
        it("handles empty username", () => {
            renderComponent({ username: "" });

            // The component should still render normally
            expect(screen.getByTestId("otp-container-mock")).toBeInTheDocument();
        });

        it("does not call onVerified when not successful", () => {
            renderComponent();
            expect(mockOnVerified).not.toHaveBeenCalled();
        });

        it("does not show error feedback when no error exists", () => {
            renderComponent();
            expect(mockSendCatchFeedback).not.toHaveBeenCalled();
        });
    });
});
