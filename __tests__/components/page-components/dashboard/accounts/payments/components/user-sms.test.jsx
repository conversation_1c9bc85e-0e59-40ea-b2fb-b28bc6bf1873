import SmsVerification from "@/components/page-components/dashboard/accounts/payments/components/user-sms";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useOtp } from "@/hooks/useOtp";
import { act, fireEvent, render, screen } from "@testing-library/react";

// Mock dependencies
jest.mock("@/hooks/useOtp");
jest.mock("@/functions/feedback");
jest.mock(
    "@/components/page-components/auth/otp-container",
    () =>
        ({ value, setValue, onSubmit, onResend, loading, resendLoading, title, subtitle, resendText }) => (
            <div data-testid="otp-container-mock">
                <input data-testid="otp-input" value={value} onChange={(e) => setValue(e.target.value)} />
                <button data-testid="submit-button" onClick={onSubmit} disabled={loading}>
                    Verify
                </button>
                <button data-testid="resend-button" onClick={onResend} disabled={resendLoading}>
                    {resendText}
                </button>
                <h3>{title}</h3>
                <p>{subtitle}</p>
            </div>
        )
);

const mockUseOtp = useOtp;
const mockSendFeedback = sendFeedback;
const mockSendCatchFeedback = sendCatchFeedback;

describe("SmsVerification Component", () => {
    const mockOnVerified = jest.fn();
    const phoneNumber = "+1234567890";

    const mockOtpFunctions = {
        validateOtp: jest.fn(),
        resendOtp: jest.fn(),
        sendOtp: jest.fn(),
        validateOtpLoading: false,
        validateOtpSuccess: false,
        validateOtpError: null,
        resendOtpLoading: false,
        resendOtpSuccess: false,
        resendOtpError: null,
        clearOtpState: jest.fn(),
    };

    beforeEach(() => {
        mockUseOtp.mockReturnValue(mockOtpFunctions);
        jest.clearAllMocks();
    });

    const renderComponent = () => {
        return render(<SmsVerification phoneNumber={phoneNumber} onVerified={mockOnVerified} />);
    };

    // Test Case 1: Initial Rendering and Mount Behavior
    describe("Initial Rendering", () => {
        it("renders OTPContainer with correct props", () => {
            renderComponent();

            expect(screen.getByTestId("otp-container-mock")).toBeInTheDocument();
            expect(screen.getByText("Check your mobile for OTP")).toBeInTheDocument();
            expect(screen.getByText("Enter the SMS OTP sent to your mobile")).toBeInTheDocument();
            expect(screen.getByText("Send New Code")).toBeInTheDocument();
        });

        it("sends OTP on mount with correct parameters", () => {
            renderComponent();

            expect(mockOtpFunctions.sendOtp).toHaveBeenCalledWith({
                receiver: phoneNumber,
                receiverType: "SMS",
            });
        });

        it("clears OTP state on mount", () => {
            renderComponent();

            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("validate");
            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("resend");
        });
    });

    // Test Case 2: OTP Submission
    describe("OTP Submission", () => {
        it("calls validateOtp with correct parameters when form is submitted", async () => {
            renderComponent();

            // Simulate OTP input
            fireEvent.change(screen.getByTestId("otp-input"), { target: { value: "123456" } });

            // Simulate form submission
            await act(async () => {
                fireEvent.click(screen.getByTestId("submit-button"));
            });

            expect(mockOtpFunctions.validateOtp).toHaveBeenCalledWith({
                receiver: phoneNumber,
                otp: "123456",
            });
        });

        it("disables submit button when loading", () => {
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                validateOtpLoading: true,
            });

            renderComponent();
            expect(screen.getByTestId("submit-button")).toBeDisabled();
        });
    });

    // Test Case 3: OTP Resend
    describe("OTP Resend", () => {
        it("calls resendOtp with correct parameters when resend is clicked", async () => {
            renderComponent();

            await act(async () => {
                fireEvent.click(screen.getByTestId("resend-button"));
            });

            expect(mockOtpFunctions.resendOtp).toHaveBeenCalledWith(phoneNumber);
        });

        it("disables resend button when resend is loading", () => {
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                resendOtpLoading: true,
            });

            renderComponent();
            expect(screen.getByTestId("resend-button")).toBeDisabled();
        });
    });

    // Test Case 4: Success Handling
    describe("Success Handling", () => {
        it("calls onVerified and shows success feedback when validation succeeds", () => {
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                validateOtpSuccess: true,
                validateOtpToken: "mockToken",
            });

            renderComponent();

            expect(mockOnVerified).toHaveBeenCalled();
            expect(mockSendFeedback).toHaveBeenCalledWith("OTP verified successfully", "success");
            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("validate");
        });

        it("shows success feedback when resend succeeds", () => {
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                resendOtpSuccess: true,
            });

            renderComponent();

            expect(mockSendFeedback).toHaveBeenCalledWith("OTP has been sent successfully", "success");
            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("resend");
        });
    });

    // Test Case 5: Error Handling
    describe("Error Handling", () => {
        it("handles validation error and clears OTP value", () => {
            const error = { message: "Invalid OTP" };
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                validateOtpError: error,
            });

            renderComponent();

            expect(mockSendCatchFeedback).toHaveBeenCalledWith(error, expect.any(Function));

            // Test the error clearing callback
            const clearCallback = mockSendCatchFeedback.mock.calls[0][1];
            act(() => clearCallback());
            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("validate");
        });

        it("handles resend error", () => {
            const error = { message: "Resend failed" };
            mockUseOtp.mockReturnValue({
                ...mockOtpFunctions,
                resendOtpError: error,
            });

            renderComponent();

            expect(mockSendCatchFeedback).toHaveBeenCalledWith(error, expect.any(Function));

            // Test the error clearing callback
            const clearCallback = mockSendCatchFeedback.mock.calls[0][1];
            act(() => clearCallback());
            expect(mockOtpFunctions.clearOtpState).toHaveBeenCalledWith("resend");
        });
    });

    // Test Case 6: Edge Cases
    describe("Edge Cases", () => {
        it("handles empty phone number", () => {
            render(<SmsVerification phoneNumber="" onVerified={mockOnVerified} />);

            // Should still attempt to send OTP
            expect(mockOtpFunctions.sendOtp).toHaveBeenCalledWith({
                receiver: "",
                receiverType: "SMS",
            });
        });

        it("does not call onVerified when not successful", () => {
            renderComponent();
            expect(mockOnVerified).not.toHaveBeenCalled();
        });
    });
});
