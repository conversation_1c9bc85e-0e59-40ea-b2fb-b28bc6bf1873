/* eslint-disable @typescript-eslint/no-require-imports */
import AddTransferFunds from "@/components/page-components/dashboard/accounts/payments/add-transfer-funds";
import { sendFeedback } from "@/functions/feedback";
import { openReviewTransferFundsDialog } from "@/redux/features/uiDialogSlice";
import { handleTransferFunds } from "@/redux/slices/accountSlice";
import "@testing-library/jest-dom";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// Mocks
jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: (value, decimal = 0) =>
        `₦${Number(value).toLocaleString("en-US", { minimumFractionDigits: decimal, maximumFractionDigits: decimal })}`,
}));

jest.mock("@/redux/features/uiDialogSlice", () => ({
    openReviewTransferFundsDialog: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

jest.mock("@/redux/slices/accountSlice", () => ({
    handleTransferFunds: jest.fn(),
    getAccountDetails: jest.fn(),
}));

// Mock AccountSelector
jest.mock("@/components/page-components/dashboard/accounts/payments/components/account-selector", () => ({
    __esModule: true,
    default: ({ labelName, selectedAccount, onChange, updateExternalBalance }) => (
        <div data-testid={`${labelName}-selector`}>
            <span>{selectedAccount?.accountName || "None selected"}</span>
            <button
                data-testid={`change-${labelName}`}
                onClick={() => {
                    onChange({ labelName, accountNumber: "99999", accountName: "Updated Account" });
                    if (updateExternalBalance) {
                        updateExternalBalance(5000);
                    }
                }}
            >
                Change {labelName}
            </button>
        </div>
    ),
}));

// Mock Redux hooks
const mockDispatch = jest.fn();
jest.mock("@/redux/hooks", () => {
    const original = jest.requireActual("@/redux/hooks");
    return {
        ...original,
        useAppDispatch: () => mockDispatch,
        useAppSelector: jest.fn(),
    };
});

const mockStore = configureStore([]);
const mockOnClose = jest.fn();

describe("AddTransferFunds Component", () => {
    let store;
    const mockUseSelector = jest.spyOn(require("@/redux/hooks"), "useAppSelector");

    const baseState = {
        account: {
            transferFunds: {
                fromAccount: { accountNumber: "12345", accountName: "Account 1", currencyCode: "NGN" },
                destinationAccount: { accountNumber: "67890", accountName: "Account 2", currencyCode: "NGN" },
                amount: "1000",
                narration: "Test narration",
            },
            selectedAccount: { accountNumber: "12345", accountName: "Account 1" },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
        store = mockStore(baseState);
        mockUseSelector.mockImplementation((selector) => selector(baseState));
    });

    afterAll(() => {
        mockUseSelector.mockRestore();
    });

    it("renders all UI elements and initial values", () => {
        render(
            <Provider store={store}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        expect(screen.getByTestId("Transfer from-selector")).toHaveTextContent("Account 1");
        expect(screen.getByTestId("Destination account-selector")).toHaveTextContent("Account 2");
        expect(screen.getByTestId("amount")).toHaveValue("₦1,000.00");
        expect(screen.getByTestId("narration")).toHaveValue("Test narration");
        expect(screen.getByTestId("payment-info-title")).toHaveTextContent("Payment information");
    });

    it("renders with empty state when no transferFunds data", () => {
        const emptyState = {
            account: {
                transferFunds: {
                    fromAccount: null,
                    destinationAccount: null,
                    amount: "",
                    narration: "",
                },
                selectedAccount: null,
            },
        };
        mockUseSelector.mockImplementation((selector) => selector(emptyState));

        render(
            <Provider store={mockStore(emptyState)}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        expect(screen.getByTestId("Transfer from-selector")).toHaveTextContent("None selected");
        expect(screen.getByTestId("Destination account-selector")).toHaveTextContent("None selected");
        expect(screen.getByTestId("amount")).toHaveValue("");
        expect(screen.getByTestId("narration")).toHaveValue("");
    });

    it("dispatches update when Transfer from is changed via AccountSelector", async () => {
        render(
            <Provider store={store}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        fireEvent.click(screen.getByTestId("change-Transfer from"));

        await waitFor(() => {
            expect(handleTransferFunds).toHaveBeenCalledWith({
                labelName: "Transfer from",
                accountNumber: "99999",
                accountName: "Updated Account",
            });
        });
    });

    it("dispatches update when Destination account is changed via AccountSelector", async () => {
        render(
            <Provider store={store}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        fireEvent.click(screen.getByTestId("change-Destination account"));

        await waitFor(() => {
            expect(handleTransferFunds).toHaveBeenCalledWith({
                labelName: "Destination account",
                accountNumber: "99999",
                accountName: "Updated Account",
            });
        });
    });

    it("updates account balance when Transfer from account is changed", async () => {
        render(
            <Provider store={store}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        fireEvent.click(screen.getByTestId("change-Transfer from"));

        // The mock AccountSelector will call updateExternalBalance with 5000
        // This tests the setAccountBalance functionality
        await waitFor(() => {
            expect(handleTransferFunds).toHaveBeenCalled();
        });
    });

    describe("Amount Input Handling", () => {
        it("dispatches handleTransferFunds on amount input change", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.change(screen.getByTestId("amount"), { target: { value: "2000" } });

            await waitFor(() => {
                expect(handleTransferFunds).toHaveBeenCalledWith({
                    labelName: "amount",
                    amount: "2000",
                });
            });
        });

        it("removes non-numeric characters from amount input", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.change(screen.getByTestId("amount"), { target: { value: "2000abc!@#" } });

            await waitFor(() => {
                expect(handleTransferFunds).toHaveBeenCalledWith({
                    labelName: "amount",
                    amount: "2000",
                });
            });
        });

        it("prevents multiple decimal points in amount", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Try to input multiple decimal points
            fireEvent.change(screen.getByTestId("amount"), { target: { value: "100.50.25" } });

            // Should not dispatch because of multiple decimal points
            expect(handleTransferFunds).not.toHaveBeenCalledWith({
                labelName: "amount",
                amount: "100.50.25",
            });
        });

        it("limits decimal places to 2", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.change(screen.getByTestId("amount"), { target: { value: "100.123456" } });

            await waitFor(() => {
                expect(handleTransferFunds).toHaveBeenCalledWith({
                    labelName: "amount",
                    amount: "100.12",
                });
            });
        });

        it("formats amount correctly when not focused", () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            expect(screen.getByTestId("amount")).toHaveValue("₦1,000.00");
        });

        it("does not format amount when focused", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.focus(screen.getByTestId("amount"));
            await waitFor(() => {
                expect(screen.getByTestId("amount")).toHaveValue("1000");
            });
        });

        it("returns empty string when amount is NaN and not focused", () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "invalid" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            expect(screen.getByTestId("amount")).toHaveValue("");
        });

        it("clears amount if value is zero on blur", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "0" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.blur(screen.getByTestId("amount"));

            await waitFor(() => {
                expect(handleTransferFunds).toHaveBeenCalledWith({
                    labelName: "amount",
                    amount: "",
                });
            });
        });

        it("converts string amount to number on blur", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "1500.50" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.blur(screen.getByTestId("amount"));

            await waitFor(() => {
                expect(handleTransferFunds).toHaveBeenCalledWith({
                    labelName: "amount",
                    amount: 1500.5,
                });
            });
        });

        it("shows error when amount exceeds account balance", () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "6000" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Simulate account balance being set
            fireEvent.click(screen.getByTestId("change-Transfer from"));

            // The error should be shown when amount exceeds balance
            expect(screen.getByTestId("amount")).toBeInTheDocument();
        });
    });

    it("dispatches handleTransferFunds on narration change", async () => {
        render(
            <Provider store={store}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        fireEvent.change(screen.getByTestId("narration"), { target: { value: "Updated note" } });

        await waitFor(() => {
            expect(handleTransferFunds).toHaveBeenCalledWith({
                labelName: "narration",
                narration: "Updated note",
            });
        });
    });

    describe("Form Validation", () => {
        it("calls sendFeedback if Transfer from is missing", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, fromAccount: null },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Select source account", "error");
            });
        });

        it("calls sendFeedback if Transfer from accountNumber is missing", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        fromAccount: { accountName: "Account 1" }, // Missing accountNumber
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Select source account", "error");
            });
        });

        it("calls sendFeedback if Destination account is missing", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, destinationAccount: null },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Select destination account", "error");
            });
        });

        it("calls sendFeedback if Destination account accountNumber is missing", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        destinationAccount: { accountName: "Account 2" }, // Missing accountNumber
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Select destination account", "error");
            });
        });

        it("calls sendFeedback if accounts are the same", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        destinationAccount: baseState.account.transferFunds.fromAccount,
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Account cannot be the same.", "error");
            });
        });

        it("checks if currency is the same", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        fromAccount: { accountNumber: "12345", accountName: "Account 1", currencyCode: "NGN" },
                        destinationAccount: { accountNumber: "67890", accountName: "Account 2", currencyCode: "USD" },
                        amount: "1000",
                        narration: "Test narration",
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Please select accounts that have the same currency",
                    "error"
                );
            });
        });

        it("calls sendFeedback if amount is empty", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Amount is required", "error");
            });
        });

        it("calls sendFeedback if amount is null", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: null },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Amount is required", "error");
            });
        });

        it("opens review dialog if all fields are valid", async () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Continue"));

            await waitFor(() => {
                expect(openReviewTransferFundsDialog).toHaveBeenCalled();
            });
        });
    });

    describe("Button States and Navigation", () => {
        it("closes the modal when Previous is clicked", () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.click(screen.getByText("Previous"));
            expect(mockOnClose).toHaveBeenCalled();
        });

        it("disables Continue button when amount exceeds account balance", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        amount: "6000",
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Simulate setting account balance by changing Transfer from
            fireEvent.click(screen.getByTestId("change-Transfer from"));

            // Since the mock sets balance to 5000 and amount is 6000, button should be disabled
            await waitFor(() => {
                expect(screen.getByText("Continue")).toBeDisabled();
            });
        });

        it("enables Continue button when amount is within account balance", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        amount: "500",
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            expect(screen.getByText("Continue")).not.toBeDisabled();
        });

        it("enables Continue button when account balance is unknown", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: {
                        ...baseState.account.transferFunds,
                        amount: "500",
                    },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            expect(screen.getByText("Continue")).not.toBeDisabled();
        });
    });

    describe("useEffect Hook", () => {
        it("auto-dispatches source account to store on mount if selectedAccount exists", () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            expect(handleTransferFunds).toHaveBeenCalledWith({
                labelName: "Transfer from",
                accountNumber: "12345",
                accountName: "Account 1",
            });
        });

        it("does not dispatch if selectedAccount is null", () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    selectedAccount: null,
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Should not auto-dispatch when selectedAccount is null
            expect(handleTransferFunds).not.toHaveBeenCalledWith({
                labelName: "Transfer from",
                accountNumber: "12345",
                accountName: "Account 1",
            });
        });

        it("does not dispatch if selectedAccount is undefined", () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    selectedAccount: undefined,
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Should not auto-dispatch when selectedAccount is undefined
            expect(handleTransferFunds).not.toHaveBeenCalledWith({
                labelName: "Transfer from",
                accountNumber: "12345",
                accountName: "Account 1",
            });
        });
    });

    describe("Component Rendering States", () => {
        it("renders when isOpen is false", () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={false} onClose={mockOnClose} />
                </Provider>
            );

            // Component should render but drawer should be closed
            expect(screen.queryByTestId("payment-info-title")).not.toBeInTheDocument();
        });

        it("renders stepper with correct current step", () => {
            render(
                <Provider store={store}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            // Check if stepper elements are rendered (this depends on your Stepper component implementation)
            expect(screen.getByTestId("payment-info-title")).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty string amount on blur", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: "" },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.blur(screen.getByTestId("amount"));

            // Should not dispatch when amount is empty string
            expect(handleTransferFunds).not.toHaveBeenCalledWith({
                labelName: "amount",
                amount: expect.any(Number),
            });
        });

        it("handles null amount on blur", async () => {
            const state = {
                ...baseState,
                account: {
                    ...baseState.account,
                    transferFunds: { ...baseState.account.transferFunds, amount: null },
                },
            };
            mockUseSelector.mockImplementation((selector) => selector(state));

            render(
                <Provider store={mockStore(state)}>
                    <AddTransferFunds isOpen={true} onClose={mockOnClose} />
                </Provider>
            );

            fireEvent.blur(screen.getByTestId("amount"));

            // Should not dispatch when amount is null
            expect(handleTransferFunds).not.toHaveBeenCalledWith({
                labelName: "amount",
                amount: expect.any(Number),
            });
        });
    });
    it("enables Continue button when amount is within account balance and narration is provided", async () => {
        const state = {
            ...baseState,
            account: {
                ...baseState.account,
                transferFunds: {
                    ...baseState.account.transferFunds,
                    amount: "500",
                    narration: "Test narration",
                },
            },
        };
        mockUseSelector.mockImplementation((selector) => selector(state));

        render(
            <Provider store={mockStore(state)}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        expect(screen.getByText("Continue")).not.toBeDisabled();
    });

    it("disables Continue button when narration is empty", async () => {
        const state = {
            ...baseState,
            account: {
                ...baseState.account,
                transferFunds: {
                    ...baseState.account.transferFunds,
                    amount: "500",
                    narration: "",
                },
            },
        };
        mockUseSelector.mockImplementation((selector) => selector(state));

        render(
            <Provider store={mockStore(state)}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        expect(screen.getByText("Continue")).toBeDisabled();
    });

    it("disables Continue button when narration is null", async () => {
        const state = {
            ...baseState,
            account: {
                ...baseState.account,
                transferFunds: {
                    ...baseState.account.transferFunds,
                    amount: "500",
                    narration: null,
                },
            },
        };
        mockUseSelector.mockImplementation((selector) => selector(state));

        render(
            <Provider store={mockStore(state)}>
                <AddTransferFunds isOpen={true} onClose={mockOnClose} />
            </Provider>
        );

        expect(screen.getByText("Continue")).toBeDisabled();
    });
});
