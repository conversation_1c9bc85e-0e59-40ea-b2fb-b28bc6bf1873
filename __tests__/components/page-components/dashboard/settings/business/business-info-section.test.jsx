import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BusinessInfoSection } from "@/components/page-components/dashboard/settings/business/business-info-section";
import { BusinessInfo } from "@/redux/types/settings";

// Mock the EditableField component
jest.mock("@/components/page-components/dashboard/settings/ui/editable-field", () => ({
    EditableField: ({ name, label, value, emptyValueText, onSave, onSaveWithToken, requireMfa, required }) => (
        <div data-testid={`editable-field-${name}`}>
            <h3>{label}</h3>
            <p>{value || emptyValueText}</p>
            <button onClick={() => onSave && onSave("new value")} data-testid={`save-${name}`}>
                Save without MFA
            </button>
            <button
                onClick={() => onSaveWithToken && onSaveWithToken("new value", "123456")}
                data-testid={`save-with-token-${name}`}
            >
                Save with MFA
            </button>
            <span data-testid={`require-mfa-${name}`}>{requireMfa ? "MFA Required" : "MFA Optional"}</span>
            <span data-testid={`required-${name}`}>{required ? "Required" : "Optional"}</span>
        </div>
    ),
}));

describe("BusinessInfoSection", () => {
    const mockHandleSave = jest.fn();
    const mockHandleSaveWithToken = jest.fn();

    const mockBusinessInfo = {
        businessName: "Test Business Ltd",
        registrationNumber: "123456789",
        tradingName: "Test Trading",
        email: "<EMAIL>",
        phoneNumber: "1234567890",
    };

    const defaultProps = {
        handleSave: mockHandleSave,
        handleSaveWithToken: mockHandleSaveWithToken,
        businessInfo: mockBusinessInfo,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Rendering", () => {
        it("renders the component with all business information", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            // Check section title
            expect(screen.getByText("Business information")).toBeInTheDocument();

            // Check all business info fields are displayed
            expect(screen.getByText("Legal business name")).toBeInTheDocument();
            expect(screen.getByText("Test Business Ltd")).toBeInTheDocument();

            expect(screen.getByText("Business registration number")).toBeInTheDocument();
            expect(screen.getByText("123456789")).toBeInTheDocument();

            expect(screen.getByText("Business email address")).toBeInTheDocument();
            expect(screen.getByText("<EMAIL>")).toBeInTheDocument();

            expect(screen.getByText("Business phone number")).toBeInTheDocument();
            expect(screen.getByText("1234567890")).toBeInTheDocument();
        });

        it("renders editable field for trading name", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            const editableField = screen.getByTestId("editable-field-tradingName");
            expect(editableField).toBeInTheDocument();
            expect(screen.getByText("Trading name")).toBeInTheDocument();
            expect(screen.getByText("Test Trading")).toBeInTheDocument();
        });

        it("configures editable field with correct props", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            // Check MFA requirement
            expect(screen.getByTestId("require-mfa-tradingName")).toHaveTextContent("MFA Required");

            // Check required field
            expect(screen.getByTestId("required-tradingName")).toHaveTextContent("Required");
        });
    });

    describe("Null/Empty business info handling", () => {
        it("handles null business info gracefully", () => {
            render(<BusinessInfoSection {...defaultProps} businessInfo={null} />);

            // Check fallback messages
            expect(screen.getByText("No business name available")).toBeInTheDocument();
            expect(screen.getByText("No registration number available")).toBeInTheDocument();
            expect(screen.getByText("No business phone number available")).toBeInTheDocument();
            expect(screen.getByText("--")).toBeInTheDocument(); // Email fallback
            expect(screen.getByText("No trading name available")).toBeInTheDocument();
        });

        it("handles undefined business info fields", () => {
            const incompleteBusinessInfo = {
                businessName: undefined,
                registrationNumber: null,
                tradingName: "",
                email: undefined,
                phoneNumber: null,
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={incompleteBusinessInfo} />);

            // Check fallback messages
            expect(screen.getByText("No business name available")).toBeInTheDocument();
            expect(screen.getByText("No registration number available")).toBeInTheDocument();
            expect(screen.getByText("No business phone number available")).toBeInTheDocument();
            expect(screen.getByText("--")).toBeInTheDocument(); // Email fallback
            expect(screen.getByText("No trading name available")).toBeInTheDocument();
        });

        it("handles empty string values", () => {
            const emptyBusinessInfo = {
                businessName: "",
                registrationNumber: "",
                tradingName: "",
                email: "",
                phoneNumber: "",
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={emptyBusinessInfo} />);

            // Empty strings show empty paragraphs (no fallback text)
            const emptyParagraphs = screen.getAllByText("");
            expect(emptyParagraphs.length).toBeGreaterThan(0);

            // Trading name should show fallback text
            expect(screen.getByText("No trading name available")).toBeInTheDocument();
        });
    });

    describe("Editable field functionality", () => {
        it("calls handleSave when save without MFA is clicked", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            const saveButton = screen.getByTestId("save-tradingName");
            fireEvent.click(saveButton);

            expect(mockHandleSave).toHaveBeenCalledWith("tradingName");
        });

        it("calls handleSaveWithToken when save with MFA is clicked", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            const saveWithTokenButton = screen.getByTestId("save-with-token-tradingName");
            fireEvent.click(saveWithTokenButton);

            expect(mockHandleSaveWithToken).toHaveBeenCalledWith("tradingName");
        });

        it("handles missing handleSaveWithToken prop", () => {
            const propsWithoutToken = {
                ...defaultProps,
                handleSaveWithToken: undefined,
            };

            render(<BusinessInfoSection {...propsWithoutToken} />);

            // Should still render the editable field
            expect(screen.getByTestId("editable-field-tradingName")).toBeInTheDocument();

            // Save without MFA should still work
            const saveButton = screen.getByTestId("save-tradingName");
            fireEvent.click(saveButton);
            expect(mockHandleSave).toHaveBeenCalledWith("tradingName");
        });

        it("handles missing handleSave prop", () => {
            const propsWithoutSave = {
                ...defaultProps,
                handleSave: undefined,
            };

            // Component requires handleSave to be provided, so this should throw an error
            expect(() => {
                render(<BusinessInfoSection {...propsWithoutSave} />);
            }).toThrow("handleSave is not a function");
        });
    });

    describe("Component structure and styling", () => {
        it("has correct CSS classes for layout", () => {
            const { container } = render(<BusinessInfoSection {...defaultProps} />);

            // Check main container classes
            const mainDiv = container.firstChild;
            expect(mainDiv).toHaveClass("border-b", "border-b-[#E3E5E8]", "pb-6");

            // Check title styling
            const title = screen.getByText("Business information");
            expect(title).toHaveClass("text-xl", "font-semibold", "text-black", "mb-6");

            // Check that the space-y-6 container exists
            const spaceYContainer = container.querySelector(".space-y-6");
            expect(spaceYContainer).toBeInTheDocument();
        });

        it("renders all business info sections in correct order", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            const sections = [
                "Legal business name",
                "Business registration number",
                "Trading name",
                "Business email address",
                "Business phone number",
            ];

            sections.forEach((sectionTitle, index) => {
                const section = screen.getByText(sectionTitle);
                expect(section).toBeInTheDocument();
                // Check that it's an h3 element (subsection heading)
                expect(section.tagName).toBe("H3");
            });
        });

        it("renders read-only fields with correct styling", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            // Check read-only field styling
            const readOnlyFields = ["Test Business Ltd", "123456789", "<EMAIL>", "1234567890"];

            readOnlyFields.forEach((fieldValue) => {
                const field = screen.getByText(fieldValue);
                expect(field).toHaveClass("text-base", "text-subText");
            });
        });
    });

    describe("Edge cases", () => {
        it("handles very long business names", () => {
            const longBusinessInfo = {
                ...mockBusinessInfo,
                businessName:
                    "This is a very long business name that might exceed normal display limits and should be handled gracefully by the component",
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={longBusinessInfo} />);

            expect(screen.getByText(longBusinessInfo.businessName)).toBeInTheDocument();
        });

        it("handles special characters in business info", () => {
            const specialCharBusinessInfo = {
                ...mockBusinessInfo,
                businessName: "Business & Co. (Ltd.)",
                tradingName: "Trading-Name_123",
                email: "<EMAIL>",
                phoneNumber: "******-567-8900",
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={specialCharBusinessInfo} />);

            expect(screen.getByText("Business & Co. (Ltd.)")).toBeInTheDocument();
            expect(screen.getByText("Trading-Name_123")).toBeInTheDocument();
            expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
            expect(screen.getByText("******-567-8900")).toBeInTheDocument();
        });

        it("handles zero values and edge cases", () => {
            const edgeCaseBusinessInfo = {
                ...mockBusinessInfo,
                registrationNumber: "0",
                tradingName: "0",
                phoneNumber: "0",
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={edgeCaseBusinessInfo} />);

            // Zero values should be displayed as-is, not as fallback messages
            const zeroElements = screen.getAllByText("0");
            expect(zeroElements.length).toBeGreaterThan(0);
            expect(screen.queryByText("No registration number available")).not.toBeInTheDocument();
            expect(screen.queryByText("No business phone number available")).not.toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper heading hierarchy", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            // Main section heading should be h2
            const mainHeading = screen.getByText("Business information");
            expect(mainHeading.tagName).toBe("H2");

            // Subsection headings should be h3
            const subsectionHeadings = [
                "Legal business name",
                "Business registration number",
                "Trading name",
                "Business email address",
                "Business phone number",
            ];

            subsectionHeadings.forEach((headingText) => {
                const heading = screen.getByText(headingText);
                expect(heading.tagName).toBe("H3");
            });
        });

        it("provides meaningful text content", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            // Check that all business information is accessible
            expect(screen.getByText("Test Business Ltd")).toBeInTheDocument();
            expect(screen.getByText("123456789")).toBeInTheDocument();
            expect(screen.getByText("Test Trading")).toBeInTheDocument();
            expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
            expect(screen.getByText("1234567890")).toBeInTheDocument();
        });
    });

    describe("Integration with EditableField", () => {
        it("passes correct props to EditableField component", () => {
            render(<BusinessInfoSection {...defaultProps} />);

            const editableField = screen.getByTestId("editable-field-tradingName");
            expect(editableField).toBeInTheDocument();

            // Check that the field shows the correct value
            expect(screen.getByText("Test Trading")).toBeInTheDocument();

            // Check that MFA is required
            expect(screen.getByTestId("require-mfa-tradingName")).toHaveTextContent("MFA Required");

            // Check that field is required
            expect(screen.getByTestId("required-tradingName")).toHaveTextContent("Required");
        });

        it("handles empty trading name correctly", () => {
            const businessInfoWithoutTradingName = {
                ...mockBusinessInfo,
                tradingName: "",
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={businessInfoWithoutTradingName} />);

            // Should show fallback text
            expect(screen.getByText("No trading name available")).toBeInTheDocument();
        });

        it("handles null trading name correctly", () => {
            const businessInfoWithNullTradingName = {
                ...mockBusinessInfo,
                tradingName: null,
            };

            render(<BusinessInfoSection {...defaultProps} businessInfo={businessInfoWithNullTradingName} />);

            // Should show fallback text
            expect(screen.getByText("No trading name available")).toBeInTheDocument();
        });
    });
});
