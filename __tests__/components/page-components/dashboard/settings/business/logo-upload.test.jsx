import React from "react";
import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import { LogoUpload } from "@/components/page-components/dashboard/settings/business/logo-upload";
import { act } from "react-dom/test-utils";

// Mock the components used in LogoUpload
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, "data-testid": dataTestId }) => (
        <button onClick={onClick} disabled={disabled} data-testid={dataTestId}>
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/settings", () => ({
    ImageIcon2: () => <span data-testid="image-icon">ImageIcon</span>,
}));

jest.mock("next/image", () => ({
    __esModule: true,
    default: ({ src, alt, onError, width, height }) => (
        <img src={src} alt={alt} width={width} height={height} onError={onError} data-testid="logo-image" />
    ),
}));

// Mock window alert
const originalAlert = window.alert;
let alertMock;

describe("LogoUpload Component", () => {
    const mockProps = {
        acceptedFileTypes: [".jpg", ".png"],
        handleFileChange: jest.fn(),
        handleDelete: jest.fn(),
    };

    beforeEach(() => {
        alertMock = jest.fn();
        window.alert = alertMock;
        jest.useFakeTimers();
        jest.clearAllMocks();
        console.error = jest.fn();
        console.log = jest.fn();
    });

    afterEach(() => {
        window.alert = originalAlert;
        jest.useRealTimers();
    });

    // Test 1: Should render with defaults when no logo
    test("renders with initials when no logo is provided", () => {
        render(<LogoUpload {...mockProps} />);

        // Should render with default initials "BR"
        expect(screen.getByText("BR")).toBeInTheDocument();

        // Upload button should be enabled
        expect(screen.getByText("Upload")).toBeInTheDocument();
        expect(screen.getByText("Upload").closest("button")).not.toBeDisabled();

        // Remove button should be disabled
        expect(screen.getByText("Remove").closest("button")).toBeDisabled();
    });

    // Test 2: Should render logo when provided
    test("renders image when logoUrl is provided", () => {
        render(<LogoUpload {...mockProps} logoUrl="https://example.com/logo.png" />);

        // Should render the image
        const imgElement = screen.getByTestId("logo-image");
        expect(imgElement).toBeInTheDocument();

        // Both buttons should be enabled
        expect(screen.getByText("Upload").closest("button")).not.toBeDisabled();
        expect(screen.getByText("Remove").closest("button")).not.toBeDisabled();
    });

    // Test 3: Should show custom initials for business name
    test("renders custom initials from business name", () => {
        render(<LogoUpload {...mockProps} businessName="Acme Corporation" />);

        // Should display "AC" as initials (first letters of "Acme Corporation")
        expect(screen.getByText("AC")).toBeInTheDocument();
    });

    // Test 4: Testing initials with single word business name
    test("renders single initial for one-word business name", () => {
        render(<LogoUpload {...mockProps} businessName="Acme" />);

        // Should display "A" (only first letter since it's a single word)
        expect(screen.getByText("A")).toBeInTheDocument();
    });

    // Test 5: Handle delete button click
    test("calls handleDelete when remove button is clicked", () => {
        render(<LogoUpload {...mockProps} logoUrl="https://example.com/logo.png" />);

        fireEvent.click(screen.getByTestId("remove-logo-button"));

        // Should call the handleDelete function
        expect(mockProps.handleDelete).toHaveBeenCalledTimes(1);
    });

    // Test 6: Handle file change with valid file
    test("calls handleFileChange when a valid file is selected", () => {
        render(<LogoUpload {...mockProps} />);

        // Create a mock file of acceptable size (2MB - now within the 10MB limit)
        const file = new File(["file content"], "logo.png", { type: "image/png" });
        Object.defineProperty(file, "size", { value: 2 * 1024 * 1024 });

        const input = screen.getByTestId("file-input");
        fireEvent.change(input, { target: { files: [file] } });

        // Should call handleFileChange
        expect(mockProps.handleFileChange).toHaveBeenCalledWith(file);
    });

    // Test 6a: Handle file change with large but valid file (between 1MB and 10MB)
    test("calls handleFileChange when a large valid file is selected (up to 10MB)", () => {
        render(<LogoUpload {...mockProps} />);

        // Create a mock file that's large but within the 10MB limit (9MB)
        const file = new File(["file content"], "large-logo.png", { type: "image/png" });
        Object.defineProperty(file, "size", { value: 9 * 1024 * 1024 });

        const input = screen.getByTestId("file-input");
        fireEvent.change(input, { target: { files: [file] } });

        // Should call handleFileChange without showing an alert
        expect(mockProps.handleFileChange).toHaveBeenCalledWith(file);
        expect(alertMock).not.toHaveBeenCalled();
    });

    // Test 7: Handle file change with oversized file
    test("shows alert when file exceeds size limit", () => {
        render(<LogoUpload {...mockProps} />);

        // Create a mock file that's too large (12MB - exceeds 10MB limit)
        const file = new File(["file content"], "large-logo.png", { type: "image/png" });
        Object.defineProperty(file, "size", { value: 12 * 1024 * 1024 });

        const input = screen.getByTestId("file-input");
        fireEvent.change(input, { target: { files: [file] } });

        // Should show alert and not call handleFileChange
        expect(alertMock).toHaveBeenCalledWith(
            "File size exceeds the maximum limit of 10MB. Please select a smaller file."
        );
        expect(mockProps.handleFileChange).not.toHaveBeenCalled();
    });

    // Test 8: Handle loading states for upload
    test("shows loading state while uploading", () => {
        render(<LogoUpload {...mockProps} uploadLoading={true} />);

        // Should show uploading text and disable buttons
        expect(screen.getByText("Uploading...")).toBeInTheDocument();
        expect(screen.getByText("Uploading...").closest("button")).toBeDisabled();
        expect(screen.getByText("Remove").closest("button")).toBeDisabled();
    });

    // Test 9: Handle loading states for delete
    test("shows loading state while deleting", () => {
        render(<LogoUpload {...mockProps} logoUrl="https://example.com/logo.png" deleteLoading={true} />);

        // Should show removing text and disable buttons
        expect(screen.getByText("Removing...")).toBeInTheDocument();
        expect(screen.getByText("Upload").closest("button")).toBeDisabled();
        expect(screen.getByText("Removing...").closest("button")).toBeDisabled();
    });

    // Test 10: Handle image load error and retry logic
    test("handles image error and retries loading", async () => {
        render(<LogoUpload {...mockProps} logoUrl="https://example.com/bad-image.png" />);

        // Simulate image load error
        const imgElement = screen.getByTestId("logo-image");
        fireEvent.error(imgElement);

        // Fast-forward timer to trigger retry
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        // Should still show the image (not initials) after first retry
        expect(screen.getByTestId("logo-image")).toBeInTheDocument();

        // Simulate error again for second retry
        fireEvent.error(imgElement);

        // Fast-forward timer again
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        // Should still show the image (not initials) after second retry
        expect(screen.getByTestId("logo-image")).toBeInTheDocument();

        // Simulate error again for third retry
        fireEvent.error(imgElement);

        // Fast-forward timer again
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        // Should still show the image (not initials) after third retry
        expect(screen.getByTestId("logo-image")).toBeInTheDocument();

        // Simulate error one more time to exhaust retries
        fireEvent.error(imgElement);

        // After max retries, should show initials
        await waitFor(() => {
            expect(screen.getByText("BR")).toBeInTheDocument();
        });
    });

    // Test 11: Test URL formatting for data URLs
    test("correctly handles data URLs without adding cache busting", () => {
        const dataUrl =
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";
        render(<LogoUpload {...mockProps} logoUrl={dataUrl} />);

        const imgElement = screen.getByTestId("logo-image");
        expect(imgElement.src).toBe(dataUrl);
    });

    // Test 12: Test URL formatting for regular URLs
    test("adds cache busting parameter to regular URLs", () => {
        // Mock Date.now for consistent testing
        const originalDateNow = Date.now;
        Date.now = jest.fn(() => 1234567890);

        render(<LogoUpload {...mockProps} logoUrl="https://example.com/logo.png" />);

        const imgElement = screen.getByTestId("logo-image");
        expect(imgElement.src).toContain("https://example.com/logo.png?t=1234567890-0");

        // Restore original Date.now
        Date.now = originalDateNow;
    });

    // Test 13: Test URL formatting for URLs with existing query parameters
    test("correctly formats URLs with existing query parameters", () => {
        // Mock Date.now for consistent testing
        const originalDateNow = Date.now;
        Date.now = jest.fn(() => 1234567890);

        render(<LogoUpload {...mockProps} logoUrl="https://example.com/logo.png?version=1" />);

        const imgElement = screen.getByTestId("logo-image");
        expect(imgElement.src).toContain("https://example.com/logo.png?version=1&t=1234567890-0");

        // Restore original Date.now
        Date.now = originalDateNow;
    });

    // Test 14: Test effect that resets error state when logoUrl changes
    test("resets error state when logoUrl changes", async () => {
        const { rerender } = render(<LogoUpload {...mockProps} logoUrl="https://example.com/bad-image.png" />);

        // Simulate image error to set error state
        const imgElement = screen.getByTestId("logo-image");
        fireEvent.error(imgElement);

        // Max out retries
        for (let i = 0; i < 3; i++) {
            act(() => {
                jest.advanceTimersByTime(1000);
            });
            fireEvent.error(imgElement);
        }

        // Should show initials after max retries
        await waitFor(() => {
            expect(screen.getByText("BR")).toBeInTheDocument();
        });

        // Now update the logoUrl
        rerender(<LogoUpload {...mockProps} logoUrl="https://example.com/new-logo.png" />);

        // Should render the image again
        expect(screen.getByTestId("logo-image")).toBeInTheDocument();
    });
});
