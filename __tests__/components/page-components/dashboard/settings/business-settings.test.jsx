import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BusinessSettings } from "@/components/page-components/dashboard/settings/business-settings";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
    fetchBusinessInfo,
    updateBusinessInfo,
    updateBusinessInfoWithToken,
    uploadBusinessLogo,
    deleteBusinessLogo,
    fetchBusinessLogo,
} from "@/redux/actions/settingsActions";
import { sendFeedback } from "@/functions/feedback";

// Mock hooks and actions
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/settingsActions", () => ({
    fetchBusinessInfo: jest.fn(),
    updateBusinessInfo: jest.fn(),
    updateBusinessInfoWithToken: jest.fn(),
    uploadBusinessLogo: jest.fn(),
    deleteBusinessLogo: jest.fn(),
    fetchBusinessLogo: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

// Mock child components
jest.mock("@/components/page-components/dashboard/settings/business", () => ({
    LogoUpload: ({ handleFileChange, handleDelete }) => (
        <div data-testid="logo-upload">
            <button data-testid="upload-logo-btn" onClick={() => handleFileChange(new File(["test"], "test.png"))}>
                Upload Logo
            </button>
            <button data-testid="delete-logo-btn" onClick={handleDelete}>
                Delete Logo
            </button>
        </div>
    ),
    BusinessInfoSection: ({ handleSave, businessInfo }) => (
        <div data-testid="business-info-section">
            <button data-testid="save-trading-name-btn" onClick={() => handleSave("tradingName")("New Trading Name")}>
                Save Trading Name
            </button>
            <button
                data-testid="save-business-email-btn"
                onClick={() => handleSave("businessEmail")("<EMAIL>")}
            >
                Save Email
            </button>
            <button data-testid="save-business-phone-btn" onClick={() => handleSave("businessPhone")("1234567890")}>
                Save Phone
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/settings/address", () => ({
    AddressForm: ({ handleSaveAddress, toggleAddressEdit }) => (
        <div data-testid="address-form">
            <button
                data-testid="save-address-btn"
                onClick={() =>
                    handleSaveAddress({
                        number: "123",
                        street: "Test Street",
                        city: "Test City",
                        state: "Test State",
                        country: "Nigeria",
                    })
                }
            >
                Save Address
            </button>
            <button
                data-testid="save-empty-address-btn"
                onClick={() =>
                    handleSaveAddress({
                        number: "",
                        street: "",
                        city: "",
                        state: "",
                        country: "",
                    })
                }
            >
                Save Empty Address
            </button>
            <button data-testid="cancel-address-btn" onClick={toggleAddressEdit}>
                Cancel
            </button>
        </div>
    ),
    AddressDisplay: ({ toggleAddressEdit }) => (
        <div data-testid="address-display">
            <button data-testid="edit-address-btn" onClick={toggleAddressEdit}>
                Edit Address
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/settings/ui", () => ({
    BusinessSettingsSkeleton: () => <div data-testid="business-settings-skeleton" />,
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ onClick, children }) => (
        <button data-testid="retry-button" onClick={onClick}>
            {children}
        </button>
    ),
}));

describe("BusinessSettings Component", () => {
    // Mock dispatch function
    const mockDispatch = jest.fn();
    const mockUnwrap = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        // Set up mock dispatch to handle thunk actions with unwrap
        mockDispatch.mockImplementation(() => ({
            unwrap: mockUnwrap,
        }));

        mockUnwrap.mockResolvedValue({});

        useAppDispatch.mockReturnValue(mockDispatch);

        // Default successful state
        useAppSelector.mockImplementation((selector) => {
            // Mock the structure needed by the component
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            tradingName: "Test Trading",
                            businessEmail: "<EMAIL>",
                            phoneNumber: "1234567890",
                            logoUrl: "https://example.com/logo.png",
                            address: {
                                number: "123",
                                street: "Test Street",
                                city: "Test City",
                                state: "Test State",
                                country: "Nigeria",
                            },
                        },
                        loading: false,
                        error: null,
                        updateLoading: false,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: {
                    countries: [{ name: "Nigeria" }, { name: "Ghana" }],
                },
            };

            return selector(state);
        });

        // Mock the thunk actions to return promises with unwrap
        fetchBusinessInfo.mockReturnValue({ type: "fetchBusinessInfo" });
        updateBusinessInfo.mockReturnValue({ type: "updateBusinessInfo" });
        updateBusinessInfoWithToken.mockReturnValue({ type: "updateBusinessInfoWithToken" });
        uploadBusinessLogo.mockReturnValue({ type: "uploadBusinessLogo" });
        deleteBusinessLogo.mockReturnValue({ type: "deleteBusinessLogo" });
        fetchBusinessLogo.mockReturnValue({ type: "fetchBusinessLogo" });
    });

    test("renders loading skeleton when loading", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: null,
                        loading: true,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });

        render(<BusinessSettings />);

        expect(screen.getByTestId("business-settings-skeleton")).toBeInTheDocument();
    });

    test("renders error state with retry button when there is an error", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: null,
                        loading: false,
                        error: "Failed to fetch business information",
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });

        render(<BusinessSettings />);

        expect(screen.getByText("Failed to fetch business information")).toBeInTheDocument();
        expect(screen.getByTestId("retry-button")).toBeInTheDocument();

        // Test retry functionality
        fireEvent.click(screen.getByTestId("retry-button"));
        expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessInfo());
    });

    test("renders business information correctly when data is loaded", () => {
        render(<BusinessSettings />);

        // Check if main components are rendered
        expect(screen.getByTestId("logo-upload")).toBeInTheDocument();
        expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
        expect(screen.getByTestId("address-display")).toBeInTheDocument();

        // Verify data fetching on mount
        expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessInfo());
        expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessLogo());
    });

    test("handles saving business information fields", async () => {
        render(<BusinessSettings />);

        // Save trading name
        fireEvent.click(screen.getByTestId("save-trading-name-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "New Trading Name" }));

        await waitFor(() => {
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your trading name has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });

        // Save business email
        fireEvent.click(screen.getByTestId("save-business-email-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ businessEmail: "<EMAIL>" }));

        await waitFor(() => {
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your business email has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });

        // Save business phone
        fireEvent.click(screen.getByTestId("save-business-phone-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ phoneNumber: "1234567890" }));

        await waitFor(() => {
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your business phone has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });
    });

    test("handles logo upload and delete", async () => {
        render(<BusinessSettings />);

        // Test logo upload
        fireEvent.click(screen.getByTestId("upload-logo-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(uploadBusinessLogo(expect.any(File)));

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessInfo());
        });

        // Test logo delete
        fireEvent.click(screen.getByTestId("delete-logo-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(deleteBusinessLogo());

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessInfo());
        });
    });

    test("toggles address editing mode", () => {
        render(<BusinessSettings />);

        // Initially in display mode
        expect(screen.getByTestId("address-display")).toBeInTheDocument();

        // Switch to edit mode
        fireEvent.click(screen.getByTestId("edit-address-btn"));

        // Now in edit mode
        expect(screen.getByTestId("address-form")).toBeInTheDocument();

        // Cancel editing
        fireEvent.click(screen.getByTestId("cancel-address-btn"));

        // Back to display mode
        expect(screen.getByTestId("address-display")).toBeInTheDocument();
    });

    test("handles saving address information", async () => {
        render(<BusinessSettings />);

        // Switch to edit mode
        fireEvent.click(screen.getByTestId("edit-address-btn"));

        // Save address
        fireEvent.click(screen.getByTestId("save-address-btn"));

        expect(mockDispatch).toHaveBeenCalledWith(
            updateBusinessInfo({
                street: "Test Street",
                number: "123",
                city: "Test City",
                state: "Test State",
                country: "Nigeria",
            })
        );

        await waitFor(() => {
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your address has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });
    });

    test("shows error when saving empty address", () => {
        render(<BusinessSettings />);

        // Switch to edit mode
        fireEvent.click(screen.getByTestId("edit-address-btn"));

        // Try to save empty address
        fireEvent.click(screen.getByTestId("save-empty-address-btn"));

        expect(sendFeedback).toHaveBeenCalledWith(
            "Please fill at least one address field",
            "error",
            undefined,
            "Error"
        );

        // Should not dispatch update
        expect(mockDispatch).not.toHaveBeenCalledWith(
            updateBusinessInfo(
                expect.objectContaining({
                    street: "",
                    number: "",
                    city: "",
                    state: "",
                    country: "",
                })
            )
        );
    });

    test("handles API error during business info fetch", async () => {
        // Mock API error
        mockUnwrap.mockRejectedValueOnce({ message: "API fetch error" });

        render(<BusinessSettings />);

        await waitFor(() => {
            expect(screen.getByText("API fetch error")).toBeInTheDocument();
        });

        expect(screen.getByTestId("retry-button")).toBeInTheDocument();
    });

    test("parses string address format correctly", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            address: "123 Test Street, Test City, Test State",
                        },
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });

        render(<BusinessSettings />);

        // Component should render without errors
        expect(screen.getByTestId("address-display")).toBeInTheDocument();
    });

    test("handles null address gracefully", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            address: null,
                        },
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });

        render(<BusinessSettings />);

        // Component should render without errors
        expect(screen.getByTestId("address-display")).toBeInTheDocument();
    });

    test("handles saving address with undefined formValues by using state values", async () => {
        // Mock an AddressForm component that explicitly calls handleSaveAddress without arguments
        // to test the code path where it falls back to state values
        const AddressFormWithUndefinedValues = ({ handleSaveAddress }) => (
            <div data-testid="address-form-undefined">
                <button
                    data-testid="save-address-undefined-values-btn"
                    onClick={() => handleSaveAddress()} // Explicitly call with no arguments
                >
                    Save Address with Undefined
                </button>
            </div>
        );

        // Temporarily replace the AddressForm mock
        const originalAddressForm = jest.requireMock(
            "@/components/page-components/dashboard/settings/address"
        ).AddressForm;
        jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
            AddressFormWithUndefinedValues;

        try {
            render(<BusinessSettings />);

            // Switch to edit mode
            fireEvent.click(screen.getByTestId("edit-address-btn"));

            // Click the button that calls handleSaveAddress without arguments
            fireEvent.click(screen.getByTestId("save-address-undefined-values-btn"));

            // Verify updateBusinessInfo was called with the state values
            expect(mockDispatch).toHaveBeenCalledWith(
                updateBusinessInfo({
                    street: "Test Street", // These values come from the component's state
                    number: "123",
                    city: "Test City",
                    state: "Test State",
                    country: "Nigeria",
                })
            );

            // Verify success feedback and state changes
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your address has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );
            });
        } finally {
            // Restore the original mock to avoid affecting other tests
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                originalAddressForm;
        }
    });

    test("handles API errors in address save without breaking the component", async () => {
        // Create a rejected promise for updateBusinessInfo
        mockUnwrap.mockRejectedValueOnce({ message: "Failed to update address" });

        render(<BusinessSettings />);

        // Switch to edit mode
        fireEvent.click(screen.getByTestId("edit-address-btn"));

        // Try to save the address which will cause a rejection
        fireEvent.click(screen.getByTestId("save-address-btn"));

        // Verify updateBusinessInfo was called with the right parameters
        expect(mockDispatch).toHaveBeenCalledWith(
            updateBusinessInfo({
                street: "Test Street",
                number: "123",
                city: "Test City",
                state: "Test State",
                country: "Nigeria",
            })
        );

        // Verify component doesn't crash and remains in edit mode
        await waitFor(() => {
            expect(screen.getByTestId("address-form")).toBeInTheDocument();
        });
    });

    test("extracts error message from various error formats", async () => {
        // String error
        mockUnwrap.mockRejectedValueOnce("String error message");

        const { rerender } = render(<BusinessSettings />);

        await waitFor(() => {
            expect(screen.getByText("String error message")).toBeInTheDocument();
        });

        // Error object with message
        mockUnwrap.mockRejectedValueOnce({ message: "Error object message" });

        // Clear mocks and re-render
        jest.clearAllMocks();
        rerender(<BusinessSettings />);

        fireEvent.click(screen.getByTestId("retry-button"));

        await waitFor(() => {
            expect(screen.getByText("Error object message")).toBeInTheDocument();
        });

        // Axios error format
        mockUnwrap.mockRejectedValueOnce({
            response: {
                data: {
                    message: "Axios error message",
                },
            },
        });

        // Clear mocks and re-render
        jest.clearAllMocks();
        rerender(<BusinessSettings />);

        fireEvent.click(screen.getByTestId("retry-button"));

        await waitFor(() => {
            expect(screen.getByText("Axios error message")).toBeInTheDocument();
        });

        // Unknown error format
        mockUnwrap.mockRejectedValueOnce({});

        // Clear mocks and re-render
        jest.clearAllMocks();
        rerender(<BusinessSettings />);

        fireEvent.click(screen.getByTestId("retry-button"));

        await waitFor(() => {
            expect(screen.getByText("Unable to get business information")).toBeInTheDocument();
        });
    });

    test("handles missing businessInfo data gracefully", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: undefined,
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });
        render(<BusinessSettings />);
        // Should render skeleton when data is undefined
        expect(screen.getByTestId("business-settings-skeleton")).toBeInTheDocument();
        expect(screen.queryByTestId("business-info-section")).not.toBeInTheDocument();
    });

    test("handles missing logoUrl and address fields", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            tradingName: "Test Trading",
                            businessEmail: "<EMAIL>",
                            phoneNumber: "1234567890",
                        },
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });
        render(<BusinessSettings />);
        expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
    });

    test("handles upload and delete loading states", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            tradingName: "Test Trading",
                            businessEmail: "<EMAIL>",
                            phoneNumber: "1234567890",
                        },
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: true,
                        deleteLoading: true,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });
        render(<BusinessSettings />);
        // Should still render logo upload and info section
        expect(screen.getByTestId("logo-upload")).toBeInTheDocument();
        expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
    });

    test("handles retry after error and successful fetch", async () => {
        // Simulate error state
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: null,
                        loading: false,
                        error: "Failed to fetch business information",
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });
        render(<BusinessSettings />);
        fireEvent.click(screen.getByTestId("retry-button"));
        expect(mockDispatch).toHaveBeenCalledWith(fetchBusinessInfo());
    });

    test("handles rapid toggling of address edit mode", () => {
        render(<BusinessSettings />);
        // Toggle edit mode on and off rapidly
        fireEvent.click(screen.getByTestId("edit-address-btn"));
        fireEvent.click(screen.getByTestId("cancel-address-btn"));
        fireEvent.click(screen.getByTestId("edit-address-btn"));
        expect(screen.getByTestId("address-form")).toBeInTheDocument();
    });

    test("handles empty countries array gracefully", () => {
        useAppSelector.mockImplementation((selector) => {
            const state = {
                settings: {
                    businessInfo: {
                        data: {
                            businessName: "Test Business",
                            address: {
                                number: "123",
                                street: "Test Street",
                                city: "Test City",
                                state: "Test State",
                                country: "Nigeria",
                            },
                        },
                        loading: false,
                        error: null,
                    },
                    businessLogo: {
                        uploadLoading: false,
                        deleteLoading: false,
                    },
                },
                countries: { countries: [] },
            };
            return selector(state);
        });
        render(<BusinessSettings />);
        expect(screen.getByTestId("address-display")).toBeInTheDocument();
    });

    // BusinessInfoSection specific tests
    describe("BusinessInfoSection Integration", () => {
        test("renders BusinessInfoSection with correct props", () => {
            render(<BusinessSettings />);

            // Verify BusinessInfoSection is rendered
            expect(screen.getByTestId("business-info-section")).toBeInTheDocument();

            // Verify the component receives the correct props structure
            const businessInfoSection = screen.getByTestId("business-info-section");
            expect(businessInfoSection).toBeInTheDocument();
        });

        test("BusinessInfoSection receives handleSave function", () => {
            render(<BusinessSettings />);

            // Test that handleSave function works for trading name
            fireEvent.click(screen.getByTestId("save-trading-name-btn"));

            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "New Trading Name" }));
        });

        test("BusinessInfoSection receives handleSaveWithToken function", () => {
            // Mock the BusinessInfoSection to test handleSaveWithToken
            const BusinessInfoSectionWithToken = ({ handleSaveWithToken, businessInfo }) => (
                <div data-testid="business-info-section-with-token">
                    <button
                        data-testid="save-with-token-btn"
                        onClick={() => handleSaveWithToken("tradingName")("New Trading Name", "123456")}
                    >
                        Save with Token
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionWithToken;

            try {
                render(<BusinessSettings />);

                fireEvent.click(screen.getByTestId("save-with-token-btn"));

                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        tradingName: "New Trading Name",
                        token: "123456",
                    })
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });

        test("BusinessInfoSection receives complete businessInfo data", () => {
            const completeBusinessInfo = {
                businessName: "Complete Business",
                tradingName: "Complete Trading",
                businessEmail: "<EMAIL>",
                phoneNumber: "9876543210",
                logoUrl: "https://example.com/complete-logo.png",
                address: {
                    number: "456",
                    street: "Complete Street",
                    city: "Complete City",
                    state: "Complete State",
                    country: "Complete Country",
                },
            };

            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: completeBusinessInfo,
                            loading: false,
                            error: null,
                            updateLoading: false,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Verify BusinessInfoSection is rendered with complete data
            expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
        });

        test("BusinessInfoSection handles partial businessInfo data", () => {
            const partialBusinessInfo = {
                businessName: "Partial Business",
                // Missing other fields
            };

            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: partialBusinessInfo,
                            loading: false,
                            error: null,
                            updateLoading: false,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Verify BusinessInfoSection handles partial data gracefully
            expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
        });

        test("BusinessInfoSection handles null businessInfo gracefully", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: null,
                            loading: false,
                            error: null,
                            updateLoading: false,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Should render skeleton when businessInfo is null
            expect(screen.getByTestId("business-settings-skeleton")).toBeInTheDocument();
            expect(screen.queryByTestId("business-info-section")).not.toBeInTheDocument();
        });

        test("BusinessInfoSection handles undefined businessInfo gracefully", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: undefined,
                            loading: false,
                            error: null,
                            updateLoading: false,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Should render skeleton when businessInfo is undefined
            expect(screen.getByTestId("business-settings-skeleton")).toBeInTheDocument();
            expect(screen.queryByTestId("business-info-section")).not.toBeInTheDocument();
        });

        test("BusinessInfoSection handles all field types correctly", () => {
            render(<BusinessSettings />);

            // Test trading name save
            fireEvent.click(screen.getByTestId("save-trading-name-btn"));
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "New Trading Name" }));

            // Test business email save
            fireEvent.click(screen.getByTestId("save-business-email-btn"));
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ businessEmail: "<EMAIL>" }));

            // Test business phone save
            fireEvent.click(screen.getByTestId("save-business-phone-btn"));
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ phoneNumber: "1234567890" }));
        });

        test("BusinessInfoSection handles empty string values", () => {
            // Mock BusinessInfoSection to test empty string handling
            const BusinessInfoSectionEmpty = ({ handleSave, businessInfo }) => (
                <div data-testid="business-info-section-empty">
                    <button data-testid="save-empty-trading-name-btn" onClick={() => handleSave("tradingName")("")}>
                        Save Empty Trading Name
                    </button>
                    <button data-testid="save-empty-email-btn" onClick={() => handleSave("businessEmail")("")}>
                        Save Empty Email
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionEmpty;

            try {
                render(<BusinessSettings />);

                // Test empty trading name
                fireEvent.click(screen.getByTestId("save-empty-trading-name-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "" }));

                // Test empty email
                fireEvent.click(screen.getByTestId("save-empty-email-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ businessEmail: "" }));
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });

        test("BusinessInfoSection handles whitespace-only values", () => {
            // Mock BusinessInfoSection to test whitespace handling
            const BusinessInfoSectionWhitespace = ({ handleSave, businessInfo }) => (
                <div data-testid="business-info-section-whitespace">
                    <button data-testid="save-whitespace-btn" onClick={() => handleSave("tradingName")("   ")}>
                        Save Whitespace
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionWhitespace;

            try {
                render(<BusinessSettings />);

                fireEvent.click(screen.getByTestId("save-whitespace-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "   " }));
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });

        test("BusinessInfoSection handles special characters in values", () => {
            // Mock BusinessInfoSection to test special characters
            const BusinessInfoSectionSpecial = ({ handleSave, businessInfo }) => (
                <div data-testid="business-info-section-special">
                    <button
                        data-testid="save-special-btn"
                        onClick={() => handleSave("tradingName")("Test & Co. (Ltd.)")}
                    >
                        Save Special Characters
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionSpecial;

            try {
                render(<BusinessSettings />);

                fireEvent.click(screen.getByTestId("save-special-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "Test & Co. (Ltd.)" }));
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });

        test("BusinessInfoSection handles very long values", () => {
            const longValue = "A".repeat(1000);

            // Mock BusinessInfoSection to test long values
            const BusinessInfoSectionLong = ({ handleSave, businessInfo }) => (
                <div data-testid="business-info-section-long">
                    <button data-testid="save-long-btn" onClick={() => handleSave("tradingName")(longValue)}>
                        Save Long Value
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionLong;

            try {
                render(<BusinessSettings />);

                fireEvent.click(screen.getByTestId("save-long-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: longValue }));
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });

        test("BusinessInfoSection handles rapid successive saves", async () => {
            render(<BusinessSettings />);

            // Rapidly click save buttons
            fireEvent.click(screen.getByTestId("save-trading-name-btn"));
            fireEvent.click(screen.getByTestId("save-business-email-btn"));
            fireEvent.click(screen.getByTestId("save-business-phone-btn"));

            // Verify all calls were made
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ tradingName: "New Trading Name" }));
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ businessEmail: "<EMAIL>" }));
            expect(mockDispatch).toHaveBeenCalledWith(updateBusinessInfo({ phoneNumber: "1234567890" }));

            // Verify success feedback for all operations
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your trading name has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );
            });
        });

        test("BusinessInfoSection handles businessInfo with missing optional fields", () => {
            const minimalBusinessInfo = {
                businessName: "Minimal Business",
                tradingName: "Minimal Trading",
                // Missing businessEmail, phoneNumber, logoUrl, address
            };

            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: minimalBusinessInfo,
                            loading: false,
                            error: null,
                            updateLoading: false,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Verify BusinessInfoSection handles minimal data gracefully
            expect(screen.getByTestId("business-info-section")).toBeInTheDocument();
        });

        test("BusinessInfoSection handles handleSaveWithToken for all field types", () => {
            // Mock the BusinessInfoSection to test handleSaveWithToken for all fields
            const BusinessInfoSectionWithTokenAll = ({ handleSaveWithToken, businessInfo }) => (
                <div data-testid="business-info-section-with-token-all">
                    <button
                        data-testid="save-trading-name-with-token-btn"
                        onClick={() => handleSaveWithToken("tradingName")("New Trading Name", "123456")}
                    >
                        Save Trading Name with Token
                    </button>
                    <button
                        data-testid="save-business-email-with-token-btn"
                        onClick={() => handleSaveWithToken("businessEmail")("<EMAIL>", "123456")}
                    >
                        Save Email with Token
                    </button>
                    <button
                        data-testid="save-business-phone-with-token-btn"
                        onClick={() => handleSaveWithToken("businessPhone")("1234567890", "123456")}
                    >
                        Save Phone with Token
                    </button>
                </div>
            );

            // Temporarily replace the mock
            const originalMock = jest.requireMock(
                "@/components/page-components/dashboard/settings/business"
            ).BusinessInfoSection;
            jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                BusinessInfoSectionWithTokenAll;

            try {
                render(<BusinessSettings />);

                // Test trading name with token
                fireEvent.click(screen.getByTestId("save-trading-name-with-token-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        tradingName: "New Trading Name",
                        token: "123456",
                    })
                );

                // Test business email with token
                fireEvent.click(screen.getByTestId("save-business-email-with-token-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        businessEmail: "<EMAIL>",
                        token: "123456",
                    })
                );

                // Test business phone with token
                fireEvent.click(screen.getByTestId("save-business-phone-with-token-btn"));
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        phoneNumber: "1234567890",
                        token: "123456",
                    })
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/business").BusinessInfoSection =
                    originalMock;
            }
        });
    });

    // handleSaveAddressWithToken specific tests
    describe("handleSaveAddressWithToken Integration", () => {
        test("handles saving address with token and form address", async () => {
            // Mock the AddressForm to test handleSaveAddressWithToken
            const AddressFormWithToken = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-with-token">
                    <button
                        data-testid="save-address-with-token-btn"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: "456",
                                street: "Token Street",
                                city: "Token City",
                                state: "Token State",
                                country: "Token Country",
                            })
                        }
                    >
                        Save Address with Token
                    </button>
                    <button data-testid="cancel-address-with-token-btn" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithToken;

            try {
                render(<BusinessSettings />);

                // Switch to edit mode
                fireEvent.click(screen.getByTestId("edit-address-btn"));

                // Submit with token and form address
                fireEvent.click(screen.getByTestId("save-address-with-token-btn"));

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        address: {
                            number: "456",
                            street: "Token Street",
                            city: "Token City",
                            state: "Token State",
                            country: "Token Country",
                        },
                        token: "123456",
                    })
                );

                // Verify fetchBusinessInfo is called after update
                await waitFor(() => {
                    expect(fetchBusinessInfo).toHaveBeenCalledTimes(2);
                });

                // Verify feedback is shown
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your address has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );

                // Should return to display mode
                await waitFor(() => {
                    expect(screen.getByTestId("address-display")).toBeInTheDocument();
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });

        test("handles saving address with token and fallback to state values", async () => {
            // Mock the AddressForm to test handleSaveAddressWithToken with undefined form address
            const AddressFormWithTokenUndefined = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-with-token-undefined">
                    <button
                        data-testid="save-address-with-token-undefined-btn"
                        onClick={() => handleSaveAddressWithToken("123456")}
                    >
                        Save Address with Token Undefined
                    </button>
                    <button data-testid="cancel-address-with-token-undefined-btn" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithTokenUndefined;

            try {
                render(<BusinessSettings />);

                // Switch to edit mode
                fireEvent.click(screen.getByTestId("edit-address-btn"));

                // Submit with token but undefined form address
                fireEvent.click(screen.getByTestId("save-address-with-token-undefined-btn"));

                // Verify dispatch was called with state values
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        address: {
                            number: "123", // from component state
                            street: "Test Street",
                            city: "Test City",
                            state: "Test State",
                            country: "Nigeria",
                        },
                        token: "123456",
                    })
                );

                // Verify fetchBusinessInfo is called after update
                await waitFor(() => {
                    expect(fetchBusinessInfo).toHaveBeenCalledTimes(2);
                });

                // Verify feedback is shown
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your address has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });

        test("handles saving address with token and empty values", async () => {
            // Mock the AddressForm to test handleSaveAddressWithToken with empty values
            const AddressFormWithTokenEmpty = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-with-token-empty">
                    <button
                        data-testid="save-address-with-token-empty-btn"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: "",
                                street: "",
                                city: "",
                                state: "",
                                country: "",
                            })
                        }
                    >
                        Save Address with Token Empty
                    </button>
                    <button data-testid="cancel-address-with-token-empty-btn" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithTokenEmpty;

            try {
                render(<BusinessSettings />);

                // Switch to edit mode
                fireEvent.click(screen.getByTestId("edit-address-btn"));

                // Submit with token and empty values
                fireEvent.click(screen.getByTestId("save-address-with-token-empty-btn"));

                // Should show error for empty address
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Please fill at least one address field",
                    "error",
                    undefined,
                    "Error"
                );

                // Should not dispatch update
                expect(mockDispatch).not.toHaveBeenCalledWith(
                    updateBusinessInfoWithToken(
                        expect.objectContaining({
                            address: {
                                number: "",
                                street: "",
                                city: "",
                                state: "",
                                country: "",
                            },
                            token: "123456",
                        })
                    )
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });

        test("handles API error during address save with token", async () => {
            // Mock the AddressForm to test handleSaveAddressWithToken
            const AddressFormWithToken = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-with-token-error">
                    <button
                        data-testid="save-address-with-token-error-btn"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: "456",
                                street: "Token Street",
                                city: "Token City",
                                state: "Token State",
                                country: "Token Country",
                            })
                        }
                    >
                        Save Address with Token Error
                    </button>
                    <button data-testid="cancel-address-with-token-error-btn" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithToken;

            try {
                // Setup error response for updateBusinessInfoWithToken
                mockUnwrap.mockRejectedValueOnce({ message: "API Error" });

                render(<BusinessSettings />);

                // Switch to edit mode
                fireEvent.click(screen.getByTestId("edit-address-btn"));

                // Submit with token and form address
                fireEvent.click(screen.getByTestId("save-address-with-token-error-btn"));

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updateBusinessInfoWithToken({
                        address: {
                            number: "456",
                            street: "Token Street",
                            city: "Token City",
                            state: "Token State",
                            country: "Token Country",
                        },
                        token: "123456",
                    })
                );

                // Verify component doesn't crash and remains in edit mode
                await waitFor(() => {
                    expect(screen.getByTestId("address-form-with-token-error")).toBeInTheDocument();
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });
    });

    // Additional tests for parseBusinessAddress edge cases
    describe("parseBusinessAddress Edge Cases", () => {
        test("handles string address with number extraction", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "123 Main Street, Test City, Test State",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });

        test("handles string address without number", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "Main Street, Test City, Test State",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });

        test("handles string address with empty parts", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "123 Main Street,, Test State,",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });

        test("handles string address with only one part", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "123 Main Street",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });

        test("handles string address with empty string", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });

        test("handles string address with whitespace only", () => {
            useAppSelector.mockImplementation((selector) => {
                const state = {
                    settings: {
                        businessInfo: {
                            data: {
                                businessName: "Test Business",
                                address: "   ",
                            },
                            loading: false,
                            error: null,
                        },
                        businessLogo: {
                            uploadLoading: false,
                            deleteLoading: false,
                        },
                    },
                    countries: { countries: [] },
                };
                return selector(state);
            });

            render(<BusinessSettings />);

            // Component should render without errors
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });
    });
});
