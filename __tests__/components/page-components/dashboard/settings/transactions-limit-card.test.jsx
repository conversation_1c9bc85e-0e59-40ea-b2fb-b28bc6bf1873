import React from "react";
import { render, fireEvent, screen, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { createStore } from "redux";
import { TransactionLimitsCard } from "../../../../../src/components/page-components/dashboard/settings/transactions-limit-card";

// Create a configurable mock store
const createMockStore = (overrides = {}) => {
    const defaultState = {
        transactionLimits: {
            updateLimit: {
                loading: false,
                success: false,
                error: null,
            },
        },
        transferMfaSlice: {
            getTeamMemberDetails: { loading: false, success: false },
            teamMember: null,
        },
        ...overrides,
    };

    const mockReducer = (state = defaultState, action) => state;
    return createStore(mockReducer);
};

// Mock the feedback function
jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

// Mock components with proper structure to avoid warnings
jest.mock("@/components/common/custom-modal", () => ({
    __esModule: true,
    default: (props) =>
        props.isOpen ? (
            <div data-testid="custom-modal">
                <div data-testid="modal-title">{props.title}</div>
                <div data-testid="modal-is-open" />
                <div>{props.children}</div>
                <button data-testid="modal-close-button" onClick={props.onRequestClose}>
                    Close Modal
                </button>
            </div>
        ) : null,
}));

jest.mock("@/components/common/buttonv3", () => ({
    __esModule: true,
    Button: ({ variant, onClick, children, loading, disabled }) => (
        <button
            data-testid={`button-${children?.toString().toLowerCase().replace(/\s+/g, "-")}`}
            onClick={onClick}
            className={`mock-button-${variant || "default"}`}
            disabled={loading || disabled}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: (props) => (
        <div>
            <label htmlFor={props.name}>{props.label}</label>
            <input
                data-testid={`input-${props.name}`}
                id={props.name}
                name={props.name}
                type={props.type || "text"}
                onChange={(e) => props.onChange && props.onChange(e)}
                onBlur={() => props.onBlur && props.onBlur()}
                value={props.value || ""}
            />
        </div>
    ),
}));

// Mock Settings MFA verification component
jest.mock("@/components/page-components/dashboard/settings/components/settings-mfa-verification", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onVerified, userMfaType }) =>
        isOpen ? (
            <div data-testid="settings-mfa-verification-modal">
                <div data-testid="mfa-type">{userMfaType}</div>
                <button data-testid="close-settings-mfa-verification" onClick={onClose}>
                    Close Settings MFA
                </button>
                <button data-testid="verify-settings-mfa" onClick={() => onVerified("test-mfa-token")}>
                    Verify Settings MFA
                </button>
            </div>
        ) : null,
}));

// Mock MFA actions
jest.mock("@/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(() => ({ type: "GET_TEAM_MEMBER_DETAILS" })),
}));

// Mock resetAllStates action
jest.mock("@/redux/slices/settingsMfaSlice", () => ({
    resetAllStates: jest.fn(() => ({ type: "RESET_ALL_STATES" })),
}));

describe("TransactionLimitsCard", () => {
    const mockLimits = {
        daily: 1000,
        perTransaction: 500,
        maximum: 10000,
    };

    const mockProps = {
        title: "Transaction Limits",
        subtitle: "Set your transaction limits",
        icon: <div data-testid="icon" />,
        limits: mockLimits,
        onUpdateLimits: jest.fn(),
    };

    const renderComponent = (storeOverrides = {}, propOverrides = {}) => {
        const store = createMockStore(storeOverrides);
        const finalProps = { ...mockProps, ...propOverrides };
        return render(
            <Provider store={store}>
                <TransactionLimitsCard {...finalProps} />
            </Provider>
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Component Rendering", () => {
        it("should render without crashing", () => {
            expect(() => renderComponent()).not.toThrow();
        });

        it("should render the component with initial state", () => {
            renderComponent();

            expect(screen.getByText(mockProps.title)).toBeInTheDocument();
            expect(screen.getByText(mockProps.subtitle)).toBeInTheDocument();
            expect(screen.getByTestId("icon")).toBeInTheDocument();
            // Check for the right arrow SVG instead of test id
            expect(screen.getByRole("button")).toBeInTheDocument();
        });

        it("should not show expanded content initially", () => {
            renderComponent();

            expect(screen.queryByText("Daily limit")).not.toBeInTheDocument();
            expect(screen.queryByText("Per transaction")).not.toBeInTheDocument();
            expect(screen.queryByTestId("adjust-limit")).not.toBeInTheDocument();
        });

        it("should expand the card when clicked", () => {
            renderComponent();

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("Daily limit")).toBeInTheDocument();
            expect(screen.getByText("Per transaction")).toBeInTheDocument();
            expect(screen.getByTestId("adjust-limit")).toBeInTheDocument();
        });

        it("should display formatted currency amounts when expanded", () => {
            renderComponent();

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("₦1,000.00")).toBeInTheDocument();
            expect(screen.getByText("₦500.00")).toBeInTheDocument();
        });
    });

    describe("Foreign/FX Transfer Handling", () => {
        it("should disable expand button for Foreign/FX Transfer", () => {
            renderComponent({}, { title: "Foreign/FX Transfer" });

            const expandButton = screen.getByTestId("expand-button");
            expect(expandButton).toBeDisabled();
        });

        it("should not expand when Foreign/FX Transfer button is clicked", () => {
            renderComponent({}, { title: "Foreign/FX Transfer" });

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.queryByText("Daily limit")).not.toBeInTheDocument();
        });
    });

    describe("MFA Flow", () => {
        const stateWithMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: true,
                    preferredMfaMethod: "SMS",
                },
            },
        };

        const stateWithoutMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: false,
                    preferredMfaMethod: null,
                },
            },
        };

        it("should show MFA verification modal when team member has MFA enabled", () => {
            renderComponent(stateWithMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("settings-mfa-verification-modal")).toBeInTheDocument();
            expect(screen.getByTestId("mfa-type")).toHaveTextContent("SMS");
        });

        it("should open transaction limits modal directly when team member has no MFA", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();
            expect(screen.getByTestId("modal-title")).toHaveTextContent("Adjust transaction limits");
        });

        it("should open transaction limits modal after successful MFA verification", () => {
            renderComponent(stateWithMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            fireEvent.click(screen.getByTestId("verify-settings-mfa"));

            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();
            expect(screen.getByTestId("modal-title")).toHaveTextContent("Adjust transaction limits");
        });

        it("should close MFA verification modal when close button is clicked", () => {
            renderComponent(stateWithMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("settings-mfa-verification-modal")).toBeInTheDocument();

            fireEvent.click(screen.getByTestId("close-settings-mfa-verification"));

            expect(screen.queryByTestId("settings-mfa-verification-modal")).not.toBeInTheDocument();
            expect(screen.queryByTestId("modal-is-open")).not.toBeInTheDocument();
        });

        it("should reset MFA state when transaction limits modal is closed", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            // Modal should be open
            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();

            // Close the modal using the close button
            const modalCloseButton = screen.getByTestId("modal-close-button");
            fireEvent.click(modalCloseButton);

            // Modal should be closed
            expect(screen.queryByTestId("modal-is-open")).not.toBeInTheDocument();

            // Try to open the modal again - should require fresh MFA verification
            fireEvent.click(adjustLimitsButton);

            // For users without MFA, modal should open directly again
            // For users with MFA, this would trigger MFA verification again
            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();
        });

        it("should reset MFA state when transaction limits modal is cancelled", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            // Modal should be open
            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();

            // Cancel the modal
            const cancelButton = screen.getByTestId("button-cancel");
            fireEvent.click(cancelButton);

            // Modal should be closed
            expect(screen.queryByTestId("modal-is-open")).not.toBeInTheDocument();

            // Try to open the modal again - should work normally
            fireEvent.click(adjustLimitsButton);
            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();
        });
    });

    describe("Form Functionality", () => {
        const stateWithoutMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: false,
                    preferredMfaMethod: null,
                },
            },
        };

        it("should save new limits when 'Save changes' is clicked", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");
            const perTransactionInput = screen.getByTestId("input-per-transaction");

            fireEvent.change(dailyLimitInput, { target: { value: "2,000" } });
            fireEvent.change(perTransactionInput, { target: { value: "1,000" } });

            const saveButton = screen.getByTestId("button-save-changes");
            fireEvent.click(saveButton);

            expect(mockProps.onUpdateLimits).toHaveBeenCalledWith(
                {
                    daily: 2000,
                    perTransaction: 1000,
                    maximum: 10000,
                },
                ""
            );
        });

        it("should close the modal when 'Cancel' is clicked", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();

            const cancelButton = screen.getByTestId("button-cancel");
            fireEvent.click(cancelButton);

            expect(screen.queryByTestId("modal-is-open")).not.toBeInTheDocument();
        });

        it("should close the modal when onRequestClose is called", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();

            const modalCloseButton = screen.getByTestId("modal-close-button");
            fireEvent.click(modalCloseButton);

            expect(screen.queryByTestId("modal-is-open")).not.toBeInTheDocument();
            expect(mockProps.onUpdateLimits).not.toHaveBeenCalled();
        });
    });

    describe("Input Formatting and Validation", () => {
        const stateWithoutMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: false,
                    preferredMfaMethod: null,
                },
            },
        };

        it("should format input with commas as user types", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");

            fireEvent.change(dailyLimitInput, { target: { value: "1000000" } });

            expect(dailyLimitInput.value).toBe("₦1,000,000");
        });

        it("should handle empty input on blur", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");

            fireEvent.change(dailyLimitInput, { target: { value: "" } });
            fireEvent.blur(dailyLimitInput);

            expect(dailyLimitInput.value).toBe("₦0");
        });

        it("should handle non-numeric input", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");
            fireEvent.change(dailyLimitInput, { target: { value: "abc" } });

            const saveButton = screen.getByTestId("button-save-changes");
            fireEvent.click(saveButton);

            expect(mockProps.onUpdateLimits).toHaveBeenCalled();
        });

        it("should handle negative values", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");
            fireEvent.change(dailyLimitInput, { target: { value: "-1000" } });

            const saveButton = screen.getByTestId("button-save-changes");
            fireEvent.click(saveButton);

            expect(mockProps.onUpdateLimits).toHaveBeenCalled();
        });
    });

    describe("Success and Error Handling", () => {
        const stateWithoutMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: false,
                    preferredMfaMethod: null,
                },
            },
        };

        it("should show success feedback when update is successful", () => {
            jest.useFakeTimers();

            // Start with initial state (no success)
            const { rerender } = renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            // Click save to trigger the update (this sets the hasSubmittedUpdate flag)
            const saveButton = screen.getByTestId("button-save-changes");
            fireEvent.click(saveButton);

            // Now simulate the success state by re-rendering with success state
            const successState = {
                ...stateWithoutMFA,
                transactionLimits: {
                    updateLimit: {
                        loading: false,
                        success: true,
                        error: null,
                    },
                },
            };

            const successStore = createMockStore(successState);
            rerender(
                <Provider store={successStore}>
                    <TransactionLimitsCard {...mockProps} />
                </Provider>
            );

            // Fast-forward time to trigger timeout
            act(() => {
                jest.advanceTimersByTime(1500);
            });

            const { sendFeedback } = require("@/functions/feedback");
            expect(sendFeedback).toHaveBeenCalledWith("Transaction limits updated successfully", "success");

            jest.useRealTimers();
        });

        it("should handle loading state correctly", () => {
            const loadingState = {
                ...stateWithoutMFA,
                transactionLimits: {
                    updateLimit: {
                        loading: true,
                        success: false,
                        error: null,
                    },
                },
            };

            renderComponent(loadingState);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const saveButton = screen.getByTestId("button-save-changes");
            expect(saveButton).toBeDisabled();
        });

        it("should keep modal open when there's an error", () => {
            const errorState = {
                ...stateWithoutMFA,
                transactionLimits: {
                    updateLimit: {
                        loading: false,
                        success: false,
                        error: "Failed to update transaction limits",
                    },
                },
            };

            renderComponent(errorState);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            expect(screen.getByTestId("modal-is-open")).toBeInTheDocument();
        });
    });

    describe("Edge Cases and Number Formatting", () => {
        it("should handle very large numbers correctly", () => {
            const largeNumberProps = {
                limits: {
                    daily: 1000000000000, // 1 trillion
                    perTransaction: 500000000, // 500 million
                    maximum: 9999999999999, // Almost 10 trillion
                },
            };

            renderComponent({}, largeNumberProps);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("₦1,000,000,000,000.00")).toBeInTheDocument();
            expect(screen.getByText("₦500,000,000.00")).toBeInTheDocument();
        });

        it("should handle null/undefined limit values", () => {
            const propsWithNullLimits = {
                limits: {
                    daily: null,
                    perTransaction: undefined,
                    maximum: 0,
                },
            };

            renderComponent({}, propsWithNullLimits);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            // Check for the actual formatted values that appear
            expect(screen.getByText("₦0.00")).toBeInTheDocument(); // daily: null becomes 0
            expect(screen.getByText("₦NaN")).toBeInTheDocument(); // perTransaction: undefined becomes NaN
        });

        it("should handle medium-length numbers (9-12 digits) for text sizing", () => {
            const mediumNumberProps = {
                limits: {
                    daily: **********, // 10 digits
                    perTransaction: 123456789, // 9 digits
                    maximum: **********1, // 11 digits
                },
            };

            renderComponent({}, mediumNumberProps);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("₦1,234,567,890.00")).toBeInTheDocument();
            expect(screen.getByText("₦123,456,789.00")).toBeInTheDocument();
        });

        it("should handle very long numbers (>12 digits) for text sizing", () => {
            const veryLargeNumberProps = {
                limits: {
                    daily: **********123456, // 16 digits
                    perTransaction: 9876543210987, // 13 digits
                    maximum: 1000000000000, // 13 digits
                },
            };

            renderComponent({}, veryLargeNumberProps);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("₦1,234,567,890,123,456.00")).toBeInTheDocument();
            expect(screen.getByText("₦9,876,543,210,987.00")).toBeInTheDocument();
        });

        it("should handle props change to trigger useEffect", () => {
            const { rerender } = renderComponent();

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            expect(screen.getByText("₦1,000.00")).toBeInTheDocument();
            expect(screen.getByText("₦500.00")).toBeInTheDocument();

            const newProps = {
                limits: {
                    daily: 2000,
                    perTransaction: 1000,
                    maximum: 20000,
                },
            };

            const store = createMockStore();
            rerender(
                <Provider store={store}>
                    <TransactionLimitsCard {...mockProps} {...newProps} />
                </Provider>
            );

            expect(screen.getByText("₦2,000.00")).toBeInTheDocument();
            expect(screen.getByText("₦1,000.00")).toBeInTheDocument();
        });
    });

    describe("MFA Token Handling", () => {
        const stateWithMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: true,
                    preferredMfaMethod: "SMS",
                },
            },
        };

        it("should pass MFA token when saving limits after MFA verification", () => {
            renderComponent(stateWithMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            fireEvent.click(screen.getByTestId("verify-settings-mfa"));

            const dailyLimitInput = screen.getByTestId("input-daily-limit");
            const perTransactionInput = screen.getByTestId("input-per-transaction");

            fireEvent.change(dailyLimitInput, { target: { value: "3,000" } });
            fireEvent.change(perTransactionInput, { target: { value: "1,500" } });

            const saveButton = screen.getByTestId("button-save-changes");
            fireEvent.click(saveButton);

            expect(mockProps.onUpdateLimits).toHaveBeenCalledWith(
                {
                    daily: 3000,
                    perTransaction: 1500,
                    maximum: 10000,
                },
                "test-mfa-token"
            );
        });
    });

    describe("Form Submission", () => {
        const stateWithoutMFA = {
            transferMfaSlice: {
                getTeamMemberDetails: { loading: false, success: true },
                teamMember: {
                    email: "<EMAIL>",
                    phoneNumber: "+**********",
                    mfaStatus: false,
                    preferredMfaMethod: null,
                },
            },
        };

        it("should handle form submission via Enter key", () => {
            renderComponent(stateWithoutMFA);

            const expandButton = screen.getByTestId("expand-button");
            fireEvent.click(expandButton);

            const adjustLimitsButton = screen.getByTestId("adjust-limit");
            fireEvent.click(adjustLimitsButton);

            const dailyLimitInput = screen.getByTestId("input-daily-limit");
            fireEvent.change(dailyLimitInput, { target: { value: "5000" } });

            const form = document.querySelector("form");
            fireEvent.submit(form);

            expect(mockProps.onUpdateLimits).toHaveBeenCalledWith(
                {
                    daily: 5000,
                    perTransaction: 500,
                    maximum: 10000,
                },
                ""
            );
        });
    });
});
