import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import SettingsSmsVerification from "../../../../../../src/components/page-components/dashboard/settings/components/settings-sms-verification";
import settingsMfaReducer from "../../../../../../src/redux/slices/settingsMfaSlice";

// Mock the useOtp hook
const mockUseOtp = {
    resendOtp: jest.fn(),
    sendOtp: jest.fn(),
    resendOtpLoading: false,
    resendOtpSuccess: false,
    resendOtpError: null,
    clearOtpState: jest.fn(),
};

jest.mock("../../../../../../src/hooks/useOtp", () => ({
    useOtp: () => mockUseOtp,
}));

// Mock feedback functions
jest.mock("../../../../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock OTPContainer component
jest.mock(
    "../../../../../../src/components/page-components/auth/otp-container",
    () =>
        ({
            value,
            setValue,
            onSubmit,
            onResend,
            loading,
            resendLoading,
            variant,
            title,
            type,
            subtitle,
            resendText,
        }) => (
            <div data-testid="otp-container">
                <h2>{title}</h2>
                <p>{subtitle}</p>
                <input
                    data-testid="otp-input"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    placeholder="Enter OTP"
                />
                <button data-testid="submit-otp" onClick={onSubmit} disabled={loading}>
                    {loading ? "Verifying..." : "Submit OTP"}
                </button>
                <button data-testid="resend-otp" onClick={onResend} disabled={resendLoading}>
                    {resendLoading ? "Sending..." : resendText}
                </button>
                <div data-testid="otp-props">
                    variant: {variant}, type: {type}
                </div>
            </div>
        )
);

describe("SettingsSmsVerification Component", () => {
    let store;
    const mockOnVerified = jest.fn();
    const baseProps = {
        phoneNumber: "+**********",
        onVerified: mockOnVerified,
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settingsMfa: settingsMfaReducer,
            },
        });
        jest.clearAllMocks();
        Object.keys(mockUseOtp).forEach((key) => {
            if (typeof mockUseOtp[key] === "function") {
                mockUseOtp[key].mockClear();
            }
        });
        mockUseOtp.resendOtpLoading = false;
        mockUseOtp.resendOtpSuccess = false;
        mockUseOtp.resendOtpError = null;
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <SettingsSmsVerification {...baseProps} {...props} />
            </Provider>
        );
    };

    describe("Component Rendering", () => {
        it("should render OTPContainer with correct props", () => {
            renderWithProvider();

            expect(screen.getByTestId("otp-container")).toBeInTheDocument();
            expect(screen.getByText("Check your mobile for OTP")).toBeInTheDocument();
            expect(screen.getByText("Enter the SMS OTP sent to your mobile")).toBeInTheDocument();
            expect(screen.getByText("variant: autoVerify, type: phone")).toBeInTheDocument();
        });

        it("should initialize with empty OTP value", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            expect(otpInput.value).toBe("");
        });
    });

    describe("OTP Management", () => {
        it("should send OTP on component mount", () => {
            renderWithProvider();

            expect(mockUseOtp.sendOtp).toHaveBeenCalledWith({
                receiver: "+**********",
                receiverType: "SMS",
            });
            expect(mockUseOtp.clearOtpState).toHaveBeenCalledWith("validate");
            expect(mockUseOtp.clearOtpState).toHaveBeenCalledWith("resend");
        });

        it("should handle OTP input changes", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            fireEvent.change(otpInput, { target: { value: "123456" } });

            expect(otpInput.value).toBe("123456");
        });

        it("should handle OTP resend", () => {
            renderWithProvider();

            const resendButton = screen.getByTestId("resend-otp");
            fireEvent.click(resendButton);

            expect(mockUseOtp.resendOtp).toHaveBeenCalledWith("+**********");
        });
    });

    describe("OTP Submission", () => {
        it("should submit OTP for validation", async () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            const submitButton = screen.getByTestId("submit-otp");

            fireEvent.change(otpInput, { target: { value: "123456" } });
            fireEvent.click(submitButton);

            // The dispatch should be called with the OTP validation action
            // We can't easily test the dispatch directly, but we can verify the component behavior
            expect(submitButton).toBeInTheDocument();
        });

        it("should show loading state during validation", () => {
            // Mock loading state
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/pending",
            });

            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            expect(submitButton).toBeDisabled();
            expect(submitButton.textContent).toBe("Verifying...");
        });
    });

    describe("Success Handling", () => {
        it("should call onVerified with token on successful validation", async () => {
            renderWithProvider();

            // Mock successful validation
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/fulfilled",
                payload: { token: "sms-token", message: "Success" },
            });

            await waitFor(() => {
                expect(mockOnVerified).toHaveBeenCalledWith("sms-token");
            });
        });

        // it("should show success feedback on successful validation", async () => {
        //     const { sendFeedback } = require("../../../../../../src/functions/feedback");

        //     renderWithProvider();

        //     // Mock successful validation
        //     store.dispatch({
        //         type: "settingsMfa/validateSmsOtp/fulfilled",
        //         payload: { token: "sms-token", message: "Success" },
        //     });

        //     await waitFor(() => {
        //         expect(sendFeedback).toHaveBeenCalledWith("OTP verified successfully", "success");
        //     });
        // });

        it("should show success feedback on successful resend", async () => {
            const { sendFeedback } = require("../../../../../../src/functions/feedback");

            mockUseOtp.resendOtpSuccess = true;
            renderWithProvider();

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("OTP has been sent successfully", "success");
                expect(mockUseOtp.clearOtpState).toHaveBeenCalledWith("resend");
            });
        });
    });

    describe("Error Handling", () => {
        it("should handle validation errors", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            renderWithProvider();

            // Mock validation error
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/rejected",
                payload: "Invalid OTP",
            });

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(new Error("Invalid OTP"));
            });
        });

        it("should clear OTP value on validation error", async () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            fireEvent.change(otpInput, { target: { value: "123456" } });

            // Mock validation error
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/rejected",
                payload: "Invalid OTP",
            });

            await waitFor(() => {
                expect(otpInput.value).toBe("");
            });
        });

        it("should handle resend errors", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            const resendError = new Error("Resend failed");
            mockUseOtp.resendOtpError = resendError;

            renderWithProvider();

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(resendError, expect.any(Function));
            });
        });
    });

    describe("Loading States", () => {
        it("should show resend loading state", () => {
            mockUseOtp.resendOtpLoading = true;
            renderWithProvider();

            const resendButton = screen.getByTestId("resend-otp");
            expect(resendButton).toBeDisabled();
            expect(resendButton.textContent).toBe("Sending...");
        });

        it("should show validation loading state", () => {
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/pending",
            });

            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            expect(submitButton).toBeDisabled();
        });
    });

    describe("Props Validation", () => {
        it("should use provided phone number", () => {
            const customPhone = "+**********";
            renderWithProvider({ phoneNumber: customPhone });

            expect(mockUseOtp.sendOtp).toHaveBeenCalledWith({
                receiver: customPhone,
                receiverType: "SMS",
            });
        });

        it("should call custom onVerified callback", async () => {
            const customOnVerified = jest.fn();
            renderWithProvider({ onVerified: customOnVerified });

            // Mock successful validation
            store.dispatch({
                type: "settingsMfa/validateSmsOtp/fulfilled",
                payload: { token: "test-token" },
            });

            await waitFor(() => {
                expect(customOnVerified).toHaveBeenCalledWith("test-token");
            });
        });
    });

    describe("Component Lifecycle", () => {
        it("should clear OTP states on mount", () => {
            renderWithProvider();

            expect(mockUseOtp.clearOtpState).toHaveBeenCalledWith("validate");
            expect(mockUseOtp.clearOtpState).toHaveBeenCalledWith("resend");
        });

        it("should send OTP automatically on mount", () => {
            renderWithProvider();

            expect(mockUseOtp.sendOtp).toHaveBeenCalledTimes(1);
        });

        it("should handle phone number changes", () => {
            const { rerender } = renderWithProvider({ phoneNumber: "+**********" });

            expect(mockUseOtp.sendOtp).toHaveBeenCalledWith({
                receiver: "+**********",
                receiverType: "SMS",
            });

            rerender(
                <Provider store={store}>
                    <SettingsSmsVerification phoneNumber="+**********" onVerified={mockOnVerified} />
                </Provider>
            );

            expect(mockUseOtp.sendOtp).toHaveBeenCalledWith({
                receiver: "+**********",
                receiverType: "SMS",
            });
        });
    });
});
