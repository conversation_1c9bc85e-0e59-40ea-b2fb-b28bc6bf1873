import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import SettingsSecurityQuestion from "../../../../../../src/components/page-components/dashboard/settings/components/settings-security-questions";
import settingsMfaReducer from "../../../../../../src/redux/slices/settingsMfaSlice";
import { userAxios } from "../../../../../../src/api/axios";

// Mock axios
jest.mock("../../../../../../src/api/axios", () => ({
    userAxios: jest.fn(),
}));

// Mock feedback functions
jest.mock("../../../../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock formik components
jest.mock("../../../../../../src/components/common/label-input", () => ({ formik, name, label, placeholder }) => (
    <div data-testid={`label-input-${name}`}>
        <label>{label}</label>
        <input
            name={name}
            placeholder={placeholder}
            value={formik.values[name]}
            onChange={formik.handleChange}
            data-testid={`input-${name}`}
        />
        {formik.errors[name] && <span data-testid={`error-${name}`}>{formik.errors[name]}</span>}
    </div>
));

// Mock Button component
jest.mock("../../../../../../src/components/common/buttonv3", () => ({
    Button: ({ children, loading, disabled, onClick, type }) => (
        <button data-testid="submit-button" disabled={disabled || loading} onClick={onClick} type={type}>
            {loading ? "Loading..." : children}
        </button>
    ),
}));

// Mock Skeleton component
jest.mock("../../../../../../src/components/ui/skeleton", () => ({
    Skeleton: ({ className }) => <div data-testid="skeleton" className={className} />,
}));

describe("SettingsSecurityQuestion Component", () => {
    let store;
    const mockOnVerified = jest.fn();
    const baseProps = {
        email: "<EMAIL>",
        onVerified: mockOnVerified,
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settingsMfa: settingsMfaReducer,
            },
        });
        jest.clearAllMocks();
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <SettingsSecurityQuestion {...baseProps} {...props} />
            </Provider>
        );
    };

    describe("Loading State", () => {
        it("should show loading skeleton while fetching questions", () => {
            userAxios.mockImplementation(() => new Promise(() => {})); // Never resolves

            renderWithProvider();

            const skeletons = screen.getAllByTestId("skeleton");
            expect(skeletons).toHaveLength(2); // Two skeleton elements
            expect(screen.getByText("Let's make sure it's you")).toBeInTheDocument();
            expect(screen.getByText("Provide the answer to your security question below")).toBeInTheDocument();
        });
    });

    describe("Question Fetching", () => {
        it("should fetch and display a random security question", async () => {
            const mockQuestions = [
                { question: "What is your favorite color?" },
                { question: "What is your pet's name?" },
                { question: "What city were you born in?" },
            ];

            userAxios.mockResolvedValueOnce({ data: mockQuestions });

            renderWithProvider();

            await waitFor(() => {
                expect(userAxios).toHaveBeenCalledWith("/v1/user-security-questions?email=<EMAIL>");
            });

            await waitFor(() => {
                const questionLabels = mockQuestions.map((q) => q.question);
                const displayedQuestion = screen.getByTestId("label-input-answer").querySelector("label").textContent;
                expect(questionLabels).toContain(displayedQuestion);
            });
        });

        it("should handle error when fetching questions", async () => {
            const mockError = new Error("Failed to fetch questions");
            userAxios.mockRejectedValueOnce(mockError);

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByText("Error")).toBeInTheDocument();
                expect(screen.getByText("Failed to fetch questions")).toBeInTheDocument();
            });
        });

        it("should show message when no questions are available", async () => {
            userAxios.mockResolvedValueOnce({ data: [] });

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByText("Error")).toBeInTheDocument();
                expect(
                    screen.getByText("Cannot read properties of undefined (reading 'question')")
                ).toBeInTheDocument();
            });
        });
    });

    describe("Form Interaction", () => {
        beforeEach(async () => {
            const mockQuestions = [{ question: "What is your favorite color?" }];
            userAxios.mockResolvedValueOnce({ data: mockQuestions });

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByTestId("input-answer")).toBeInTheDocument();
            });
        });

        it("should allow user to enter an answer", async () => {
            const answerInput = screen.getByTestId("input-answer");

            fireEvent.change(answerInput, { target: { value: "Blue" } });

            expect(answerInput.value).toBe("Blue");
        });

        it("should show validation error for empty answer", async () => {
            const submitButton = screen.getByTestId("submit-button");

            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.getByTestId("error-answer")).toBeInTheDocument();
                expect(screen.getByText("Answer is required")).toBeInTheDocument();
            });
        });

        it("should submit form with valid data", async () => {
            const answerInput = screen.getByTestId("input-answer");
            const submitButton = screen.getByTestId("submit-button");

            fireEvent.change(answerInput, { target: { value: "Blue" } });
            fireEvent.click(submitButton);

            // Form should be submitted (no validation errors)
            await waitFor(() => {
                expect(screen.queryByTestId("error-answer")).not.toBeInTheDocument();
            });
        });
    });

    describe("MFA Validation", () => {
        beforeEach(async () => {
            const mockQuestions = [{ question: "What is your favorite color?" }];
            userAxios.mockResolvedValueOnce({ data: mockQuestions });

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByTestId("input-answer")).toBeInTheDocument();
            });
        });

        it("should call onVerified with token on successful validation", async () => {
            // Mock successful validation in Redux state
            store.dispatch({
                type: "settingsMfa/validateSecurityQuestion/fulfilled",
                payload: { token: "test-token", message: "Success" },
            });

            await waitFor(() => {
                expect(mockOnVerified).toHaveBeenCalledWith("test-token");
            });
        });

        it("should show loading state during validation", async () => {
            // Mock loading state
            store.dispatch({
                type: "settingsMfa/validateSecurityQuestion/pending",
            });

            await waitFor(() => {
                const submitButton = screen.getByTestId("submit-button");
                expect(submitButton).toBeDisabled();
                expect(submitButton.textContent).toBe("Loading...");
            });
        });

        it("should handle validation error", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            // Mock error state
            store.dispatch({
                type: "settingsMfa/validateSecurityQuestion/rejected",
                payload: "Invalid answer",
            });

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalled();
            });
        });
    });

    describe("Props Validation", () => {
        it("should use provided email in API call", async () => {
            const customEmail = "<EMAIL>";
            const mockQuestions = [{ question: "Test question?" }];
            userAxios.mockResolvedValueOnce({ data: mockQuestions });

            renderWithProvider({ email: customEmail });

            await waitFor(() => {
                expect(userAxios).toHaveBeenCalledWith(`/v1/user-security-questions?email=${customEmail}`);
            });
        });

        it("should call onVerified callback when provided", async () => {
            const customOnVerified = jest.fn();
            renderWithProvider({ onVerified: customOnVerified });

            // Mock successful validation
            store.dispatch({
                type: "settingsMfa/validateSecurityQuestion/fulfilled",
                payload: { token: "test-token" },
            });

            await waitFor(() => {
                expect(customOnVerified).toHaveBeenCalledWith("test-token");
            });
        });
    });

    describe("Edge Cases", () => {
        it("should handle empty questions array", async () => {
            userAxios.mockResolvedValueOnce({ data: [] });

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByText("Error")).toBeInTheDocument();
                expect(
                    screen.getByText("Cannot read properties of undefined (reading 'question')")
                ).toBeInTheDocument();
            });
        });

        it("should handle malformed question data", async () => {
            userAxios.mockResolvedValueOnce({ data: [{ invalidField: "test" }] });

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByText("No question available.")).toBeInTheDocument();
            });
        });

        it("should handle network timeout", async () => {
            const timeoutError = new Error("Network timeout");
            timeoutError.code = "ECONNABORTED";
            userAxios.mockRejectedValueOnce(timeoutError);

            renderWithProvider();

            await waitFor(() => {
                expect(screen.getByText("Error")).toBeInTheDocument();
            });
        });
    });
});
