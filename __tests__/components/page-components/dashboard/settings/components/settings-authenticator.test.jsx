import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import SettingsAuthenticator from "../../../../../../src/components/page-components/dashboard/settings/components/settings-authenticator";
import settingsMfaReducer from "../../../../../../src/redux/slices/settingsMfaSlice";

// Mock feedback functions
jest.mock("../../../../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock OTPContainer component
jest.mock(
    "../../../../../../src/components/page-components/auth/otp-container",
    () =>
        ({ value, setValue, onSubmit, loading, variant, title, type, subtitle, resendText }) => (
            <div data-testid="otp-container">
                <h2>{title}</h2>
                <p>{subtitle}</p>
                <input
                    data-testid="otp-input"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    placeholder="Enter authenticator code"
                />
                <button data-testid="submit-otp" onClick={onSubmit} disabled={loading}>
                    {loading ? "Verifying..." : "Submit Code"}
                </button>
                <div data-testid="otp-props">
                    variant: {variant}, type: {type}, resendText: "{resendText}"
                </div>
            </div>
        )
);

describe("SettingsAuthenticator Component", () => {
    let store;
    const mockOnVerified = jest.fn();
    const baseProps = {
        username: "<EMAIL>",
        onVerified: mockOnVerified,
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settingsMfa: settingsMfaReducer,
            },
        });
        jest.clearAllMocks();
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <SettingsAuthenticator {...baseProps} {...props} />
            </Provider>
        );
    };

    describe("Component Rendering", () => {
        it("should render OTPContainer with correct props", () => {
            renderWithProvider();

            expect(screen.getByTestId("otp-container")).toBeInTheDocument();
            expect(screen.getByText("Enter your authenticator code")).toBeInTheDocument();
            expect(screen.getByText("Open your authenticator app and enter the 6-digit code")).toBeInTheDocument();
            expect(screen.getByText('variant: autoVerify, type: pin, resendText: ""')).toBeInTheDocument();
        });

        it("should initialize with empty OTP value", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            expect(otpInput.value).toBe("");
        });

        it("should render submit button", () => {
            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            expect(submitButton).toBeInTheDocument();
            expect(submitButton.textContent).toBe("Submit Code");
        });
    });

    describe("OTP Input Handling", () => {
        it("should handle OTP input changes", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            fireEvent.change(otpInput, { target: { value: "123456" } });

            expect(otpInput.value).toBe("123456");
        });

        it("should allow entering 6-digit codes", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            fireEvent.change(otpInput, { target: { value: "987654" } });

            expect(otpInput.value).toBe("987654");
        });

        it("should handle partial input", () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            fireEvent.change(otpInput, { target: { value: "123" } });

            expect(otpInput.value).toBe("123");
        });
    });

    describe("Form Submission", () => {
        it("should submit authenticator code for validation", async () => {
            renderWithProvider();

            const otpInput = screen.getByTestId("otp-input");
            const submitButton = screen.getByTestId("submit-otp");

            fireEvent.change(otpInput, { target: { value: "123456" } });
            fireEvent.click(submitButton);

            // The component should trigger the validation
            expect(submitButton).toBeInTheDocument();
        });

        it("should submit with empty OTP if user clicks submit", () => {
            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            fireEvent.click(submitButton);

            // Should still allow submission (validation will handle empty values)
            expect(submitButton).toBeInTheDocument();
        });
    });

    describe("Loading States", () => {
        it("should show loading state during validation", () => {
            // Mock loading state
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/pending",
            });

            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            expect(submitButton).toBeDisabled();
            expect(submitButton.textContent).toBe("Verifying...");
        });

        it("should enable button when not loading", () => {
            renderWithProvider();

            const submitButton = screen.getByTestId("submit-otp");
            expect(submitButton).not.toBeDisabled();
            expect(submitButton.textContent).toBe("Submit Code");
        });
    });

    describe("Success Handling", () => {
        it("should call onVerified with token on successful validation", async () => {
            renderWithProvider();

            // Mock successful validation
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/fulfilled",
                payload: { token: "auth-token", message: "Success" },
            });

            await waitFor(() => {
                expect(mockOnVerified).toHaveBeenCalledWith("auth-token");
            });
        });

        // it("should show success feedback on successful validation", async () => {
        //     const { sendFeedback } = require("../../../../../../src/functions/feedback");

        //     renderWithProvider();

        //     // Mock successful validation
        //     store.dispatch({
        //         type: "settingsMfa/validateAuthenticator/fulfilled",
        //         payload: { token: "auth-token", message: "Success" },
        //     });

        //     await waitFor(() => {
        //         expect(sendFeedback).toHaveBeenCalledWith("Authenticator verified successfully", "success");
        //     });
        // });

        it("should handle successful validation without message", async () => {
            renderWithProvider();

            // Mock successful validation without message
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/fulfilled",
                payload: { token: "auth-token" },
            });

            await waitFor(() => {
                expect(mockOnVerified).toHaveBeenCalledWith("auth-token");
            });
        });
    });

    describe("Error Handling", () => {
        it("should handle validation errors", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            renderWithProvider();

            // Mock validation error
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/rejected",
                payload: "Invalid authenticator code",
            });

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(new Error("Invalid authenticator code"));
            });
        });

        it("should handle network errors", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            renderWithProvider();

            // Mock network error
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/rejected",
                payload: "Network error occurred",
            });

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(new Error("Network error occurred"));
            });
        });

        it("should handle empty error message", async () => {
            const { sendCatchFeedback } = require("../../../../../../src/functions/feedback");

            renderWithProvider();

            // Mock error without message
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/rejected",
                payload: "",
            });

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(new Error("Failed to validate authenticator"));
            });
        });
    });

    describe("Props Validation", () => {
        it("should use provided username in validation", async () => {
            const customUsername = "<EMAIL>";
            renderWithProvider({ username: customUsername });

            const otpInput = screen.getByTestId("otp-input");
            const submitButton = screen.getByTestId("submit-otp");

            fireEvent.change(otpInput, { target: { value: "123456" } });
            fireEvent.click(submitButton);

            // The username should be used in the validation call
            // We can't easily verify the dispatch call, but we can ensure the component renders correctly
            expect(submitButton).toBeInTheDocument();
        });

        it("should call custom onVerified callback", async () => {
            const customOnVerified = jest.fn();
            renderWithProvider({ onVerified: customOnVerified });

            // Mock successful validation
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/fulfilled",
                payload: { token: "test-token" },
            });

            await waitFor(() => {
                expect(customOnVerified).toHaveBeenCalledWith("test-token");
            });
        });
    });

    describe("Redux State Integration", () => {
        it("should reflect pending state correctly", () => {
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/pending",
            });

            renderWithProvider();

            const state = store.getState().settingsMfa.validateAuthenticator;
            expect(state.loading).toBe(true);
            expect(state.success).toBe(false);
            expect(state.error).toBe(null);
        });

        it("should reflect fulfilled state correctly", () => {
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/fulfilled",
                payload: { token: "test-token" },
            });

            renderWithProvider();

            const state = store.getState().settingsMfa.validateAuthenticator;
            expect(state.loading).toBe(false);
            expect(state.success).toBe(true);
            expect(state.token).toBe("test-token");
        });

        it("should reflect rejected state correctly", () => {
            store.dispatch({
                type: "settingsMfa/validateAuthenticator/rejected",
                payload: "Validation failed",
            });

            renderWithProvider();

            const state = store.getState().settingsMfa.validateAuthenticator;
            expect(state.loading).toBe(false);
            expect(state.success).toBe(false);
            expect(state.error).toBe("Validation failed");
        });
    });

    describe("Component Behavior", () => {
        it("should not have resend functionality", () => {
            renderWithProvider();

            // Authenticator doesn't have resend functionality
            const propsDiv = screen.getByTestId("otp-props");
            expect(propsDiv).toHaveTextContent('resendText: ""');
        });

        it("should use autoVerify variant", () => {
            renderWithProvider();

            const propsDiv = screen.getByTestId("otp-props");
            expect(propsDiv).toHaveTextContent("variant: autoVerify");
        });

        it("should use pin type for OTP container", () => {
            renderWithProvider();

            const propsDiv = screen.getByTestId("otp-props");
            expect(propsDiv).toHaveTextContent("type: pin");
        });
    });
});
