import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import SettingsMfaVerification from "../../../../../../src/components/page-components/dashboard/settings/components/settings-mfa-verification";
import settingsMfaReducer from "../../../../../../src/redux/slices/settingsMfaSlice";

// Mock the individual MFA components
jest.mock(
    "../../../../../../src/components/page-components/dashboard/settings/components/settings-security-questions",
    () =>
        ({ email, onVerified }) => (
            <div data-testid="settings-security-questions-mock">
                <button onClick={() => onVerified("mock-token")}>Verify Security Questions</button>
                <span>Email: {email}</span>
            </div>
        )
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/settings/components/settings-sms-verification",
    () =>
        ({ phoneNumber, onVerified }) => (
            <div data-testid="settings-sms-verification-mock">
                <button onClick={() => onVerified("mock-token")}>Verify SMS</button>
                <span>Phone: {phoneNumber}</span>
            </div>
        )
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/settings/components/settings-authenticator",
    () =>
        ({ username, onVerified }) => (
            <div data-testid="settings-authenticator-mock">
                <button onClick={() => onVerified("mock-token")}>Verify Authenticator</button>
                <span>Username: {username}</span>
            </div>
        )
);

// Mock FullScreenDrawer
jest.mock(
    "../../../../../../src/components/common/full-screen-drawer",
    () =>
        ({ isOpen, onClose, title, children }) =>
            isOpen ? (
                <div data-testid="drawer-mock">
                    <h1>{title}</h1>
                    <button onClick={onClose}>Close Drawer</button>
                    {children}
                </div>
            ) : null
);

describe("SettingsMfaVerification Component", () => {
    let store;
    const baseProps = {
        userMfaType: "SECURITY_QUESTION",
        onClose: jest.fn(),
        isOpen: true,
        onVerified: jest.fn(),
        email: "<EMAIL>",
        phoneNumber: "+**********",
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                settingsMfa: settingsMfaReducer,
            },
        });
        jest.clearAllMocks();
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <SettingsMfaVerification {...baseProps} {...props} />
            </Provider>
        );
    };

    // Test Case 1: Basic Rendering
    describe("Basic Rendering", () => {
        it("renders the component when isOpen is true", () => {
            renderWithProvider();
            expect(screen.getByTestId("drawer-mock")).toBeInTheDocument();
            expect(screen.getByText("Verify MFA")).toBeInTheDocument();
        });

        it("does not render when isOpen is false", () => {
            renderWithProvider({ isOpen: false });
            expect(screen.queryByTestId("drawer-mock")).not.toBeInTheDocument();
        });

        it("returns null when required props are missing", () => {
            const { container } = renderWithProvider({
                userMfaType: null,
                email: "",
                phoneNumber: "",
            });
            expect(container.firstChild).toBeNull();
        });
    });

    // Test Case 2: MFA Method Rendering
    describe("MFA Method Rendering", () => {
        it("renders Security Questions component when userMfaType is SECURITY_QUESTION", () => {
            renderWithProvider({ userMfaType: "SECURITY_QUESTION" });
            expect(screen.getByTestId("settings-security-questions-mock")).toBeInTheDocument();
            expect(screen.getByText("Email: <EMAIL>")).toBeInTheDocument();
        });

        it("renders SMS Verification component when userMfaType is SMS", () => {
            renderWithProvider({ userMfaType: "SMS" });
            expect(screen.getByTestId("settings-sms-verification-mock")).toBeInTheDocument();
            expect(screen.getByText("Phone: +**********")).toBeInTheDocument();
        });

        it("renders Authenticator component when userMfaType is AUTHENTICATOR", () => {
            renderWithProvider({ userMfaType: "AUTHENTICATOR" });
            expect(screen.getByTestId("settings-authenticator-mock")).toBeInTheDocument();
            expect(screen.getByText("Username: <EMAIL>")).toBeInTheDocument();
        });
    });

    // Test Case 3: Event Handling
    describe("Event Handling", () => {
        it("calls onVerified with token when MFA verification succeeds", () => {
            renderWithProvider({ userMfaType: "SECURITY_QUESTION" });

            const verifyButton = screen.getByText("Verify Security Questions");
            verifyButton.click();

            expect(baseProps.onVerified).toHaveBeenCalledWith("mock-token");
        });

        it("calls onClose when drawer is closed", () => {
            renderWithProvider();

            const closeButton = screen.getByText("Close Drawer");
            closeButton.click();

            expect(baseProps.onClose).toHaveBeenCalled();
        });
    });
});
