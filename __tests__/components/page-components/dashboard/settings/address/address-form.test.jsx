import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { AddressForm } from "@/components/page-components/dashboard/settings/address/address-form";
import userEvent from "@testing-library/user-event";
import { sendFeedback } from "@/functions/feedback";
import * as formik from "formik";

// Mock Redux hooks
const mockDispatch = jest.fn();
const mockUseAppSelector = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: (selector) => mockUseAppSelector(selector),
}));

// Mock Redux actions
jest.mock("@/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(() => ({ type: "GET_TEAM_MEMBER_DETAILS" })),
}));

jest.mock("@/redux/slices/settingsMfaSlice", () => ({
    resetAllStates: jest.fn(() => ({ type: "RESET_ALL_STATES" })),
}));

jest.mock("@/redux/slices/transferMfaSlice", () => ({
    clearGetTeamMemberState: jest.fn(() => ({ type: "CLEAR_GET_TEAM_MEMBER_STATE" })),
}));

// Mock sendFeedback function
jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

// Mock the MFA Verification component
jest.mock("@/components/page-components/dashboard/settings/components/settings-mfa-verification", () => ({
    __esModule: true,
    default: ({ onClose, onVerified, isOpen, userMfaType, email, phoneNumber }) => (
        <div data-testid="mfa-verification-modal" style={{ display: isOpen ? "block" : "none" }}>
            <h3>MFA Verification</h3>
            <p>User MFA Type: {userMfaType}</p>
            <p>Email: {email}</p>
            <p>Phone: {phoneNumber}</p>
            <button onClick={() => onVerified("123456")} data-testid="mfa-verify-button">
                Verify
            </button>
            <button onClick={onClose} data-testid="mfa-close-button">
                Close
            </button>
        </div>
    ),
}));

// Mocking the components used by AddressForm
jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({ name, label, formik, type, placeholder, className, ...rest }) => (
        <div data-testid={`mock-input-${name}`}>
            <label>{label}</label>
            <input
                type={type}
                name={name}
                value={formik.values[name]}
                onChange={(e) => {
                    formik.handleChange(e);
                    formik.setFieldTouched(name, true, false);
                }}
                onBlur={formik.handleBlur}
                placeholder={placeholder}
                {...rest}
            />
            {formik.touched[name] && formik.errors[name] && (
                <div data-testid={`error-${name}`}>{formik.errors[name]}</div>
            )}
        </div>
    ),
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ name, label, formik, options, placeholder, isDisabled, value, ...rest }) => {
        // Handle the value properly to set the select's value
        let selectedValue = "";
        if (value) {
            selectedValue = value.value || "";
        } else if (formik?.values[name]) {
            selectedValue = formik.values[name];
        }

        return (
            <div data-testid={`mock-dropdown-${name}`}>
                <label>{label}</label>
                <select
                    name={name}
                    value={selectedValue}
                    onChange={(e) => {
                        formik?.handleChange(e);
                        formik?.setFieldTouched(name, true, false);
                    }}
                    onBlur={formik?.handleBlur}
                    disabled={isDisabled}
                    data-value={selectedValue} // Add this for easier testing
                    {...rest}
                >
                    <option value="">{placeholder}</option>
                    {options.map((option, index) => (
                        <option key={index} value={option.value}>
                            {option.value}
                        </option>
                    ))}
                </select>
                {formik?.touched[name] && formik?.errors[name] && (
                    <div data-testid={`error-${name}`}>{formik.errors[name]}</div>
                )}
            </div>
        );
    },
}));

jest.mock("@/components/common/buttonv3", () => ({
    __esModule: true,
    Button: ({ children, onClick, type, disabled, variant, loading, ...rest }) => (
        <button
            onClick={onClick}
            type={type}
            disabled={disabled || loading}
            data-variant={variant}
            data-loading={loading}
            data-testid={`mock-button-${children}`}
            {...rest}
        >
            {children}
        </button>
    ),
}));

jest.mock("next/image", () => ({
    __esModule: true,
    default: ({ src, alt, ...props }) => <img src={src} alt={alt} {...props} />,
}));

// Mock Formik to expose onSubmit for testing
jest.mock("formik", () => {
    const actual = jest.requireActual("formik");
    return {
        ...actual,
        Formik: ({ children, onSubmit, initialValues, validationSchema }) => {
            // Store the onSubmit handler for testing
            global.mockFormikSubmit = onSubmit;

            const formik = {
                values: initialValues,
                handleSubmit: (e) => {
                    if (e) e.preventDefault();
                    onSubmit(initialValues);
                },
                isValid: true,
                dirty: true,
                handleChange: jest.fn(),
                handleBlur: jest.fn(),
                touched: {},
                errors: {},
                setFieldTouched: jest.fn(),
            };

            return (
                <form onSubmit={formik.handleSubmit} data-testid="mock-form">
                    {typeof children === "function" ? children(formik) : children}
                </form>
            );
        },
        Form: ({ children, ...props }) => <div {...props}>{children}</div>,
    };
});

// Test data
const mockProps = {
    number: "123",
    address: "Main Street",
    city: "New York",
    state: "NY",
    country: "United States",
    countries: [
        {
            name: { common: "United States" },
            flags: { svg: "us-flag.svg" },
        },
        {
            name: { common: "Canada" },
            flags: { svg: "canada-flag.svg" },
        },
    ],
    isCountriesLoading: false,
    toggleAddressEdit: jest.fn(),
    handleSaveAddressWithToken: jest.fn(),
    updateLoading: false,
};

// Mock team member data
const mockTeamMember = {
    mfaStatus: true,
    preferredMfaMethod: "email",
    email: "<EMAIL>",
    phoneNumber: "1234567890",
};

describe("AddressForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        global.mockFormikSubmit = null;
        mockDispatch.mockClear();

        // Default Redux state mocks
        mockUseAppSelector.mockImplementation((selector) => {
            const state = {
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: false,
                        loading: false,
                    },
                    teamMember: mockTeamMember,
                },
            };
            return selector(state);
        });
    });

    test("renders form with initial values correctly", () => {
        render(<AddressForm {...mockProps} />);

        // Check if form fields are rendered with correct initial values
        const houseNumberInput = screen.getByTestId("mock-input-houseNumber");
        const addressInput = screen.getByTestId("mock-input-address");
        const cityInput = screen.getByTestId("mock-input-city");
        const stateInput = screen.getByTestId("mock-input-state");
        const countryDropdown = screen.getByTestId("mock-dropdown-country");

        expect(houseNumberInput).toBeInTheDocument();
        expect(addressInput).toBeInTheDocument();
        expect(cityInput).toBeInTheDocument();
        expect(stateInput).toBeInTheDocument();
        expect(countryDropdown).toBeInTheDocument();
    });

    test("displays loading state in country dropdown when countries are loading", () => {
        render(<AddressForm {...mockProps} isCountriesLoading={true} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toHaveTextContent("Loading countries...");
    });

    test("shows MFA modal when form is submitted with handleSaveAddressWithToken", async () => {
        // Mock successful team member fetch
        mockUseAppSelector.mockImplementation((selector) => {
            const state = {
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: true,
                        loading: false,
                    },
                    teamMember: mockTeamMember,
                },
            };
            return selector(state);
        });

        render(<AddressForm {...mockProps} />);

        // Submit the form
        const form = screen.getByTestId("mock-form");
        fireEvent.submit(form);

        // Wait for MFA modal to appear
        await waitFor(() => {
            expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
        });

        expect(mockDispatch).toHaveBeenCalled();
    });

    test("calls handleSaveAddressWithToken when MFA verification is successful", async () => {
        // Mock successful team member fetch
        mockUseAppSelector.mockImplementation((selector) => {
            const state = {
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: true,
                        loading: false,
                    },
                    teamMember: mockTeamMember,
                },
            };
            return selector(state);
        });

        render(<AddressForm {...mockProps} />);

        // Submit the form
        const form = screen.getByTestId("mock-form");
        fireEvent.submit(form);

        // Wait for MFA modal and verify
        await waitFor(() => {
            expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId("mfa-verify-button"));

        await waitFor(() => {
            expect(mockProps.handleSaveAddressWithToken).toHaveBeenCalledWith("123456", {
                number: mockProps.number,
                street: mockProps.address,
                city: mockProps.city,
                state: mockProps.state,
                country: mockProps.country,
            });
        });
    });

    test("shows error feedback when MFA is not enabled", async () => {
        // Mock team member without MFA
        mockUseAppSelector.mockImplementation((selector) => {
            const state = {
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: true,
                        loading: false,
                    },
                    teamMember: { ...mockTeamMember, mfaStatus: false },
                },
            };
            return selector(state);
        });

        render(<AddressForm {...mockProps} />);

        // Submit the form
        const form = screen.getByTestId("mock-form");
        fireEvent.submit(form);

        await waitFor(() => {
            expect(sendFeedback).toHaveBeenCalledWith(
                "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                "error",
                undefined,
                "2FA Required"
            );
        });
    });

    test("calls toggleAddressEdit when cancel button is clicked", async () => {
        const user = userEvent.setup();
        render(<AddressForm {...mockProps} />);

        const cancelButton = screen.getByTestId("mock-button-Cancel");
        await user.click(cancelButton);

        expect(mockProps.toggleAddressEdit).toHaveBeenCalled();
    });

    test("disables buttons when updateLoading is true", () => {
        render(<AddressForm {...mockProps} updateLoading={true} />);

        const saveButton = screen.getByTestId("mock-button-Save");
        const cancelButton = screen.getByTestId("mock-button-Cancel");

        // Check if the disabled attribute exists on the buttons
        expect(cancelButton.hasAttribute("disabled")).toBe(true);
        expect(saveButton.hasAttribute("disabled")).toBe(true);
    });

    // test("validates form fields correctly", async () => {
    //     // Create a form with validation errors
    //     // Store original implementation
    //     const originalFormik = formik.Formik;

    //     // Mock implementation for this test
    //     formikMock.Formik = ({ children }) => {
    //         // Create mock formik with validation errors
    //         const formikInstance = {
    //             values: {
    //                 houseNumber: "abc", // Invalid house number (should be numbers only)
    //                 address: "", // Empty address (required)
    //                 city: "New York",
    //                 state: "NY",
    //                 country: "United States",
    //             },
    //             touched: {
    //                 houseNumber: true,
    //                 address: true,
    //             },
    //             errors: {
    //                 houseNumber: "House number must contain only numbers",
    //                 address: "Address is required",
    //             },
    //             isValid: false,
    //             dirty: true,
    //             handleChange: jest.fn(),
    //             handleBlur: jest.fn(),
    //             setFieldTouched: jest.fn(),
    //         };

    //         return <div>{children(formikInstance)}</div>;
    //     };

    //     try {
    //         render(<AddressForm {...mockProps} />);

    //         // Check for validation error messages
    //         expect(screen.getByTestId("error-houseNumber")).toBeInTheDocument();
    //         expect(screen.getByTestId("error-houseNumber")).toHaveTextContent("House number must contain only numbers");

    //         expect(screen.getByTestId("error-address")).toBeInTheDocument();
    //         expect(screen.getByTestId("error-address")).toHaveTextContent("Address is required");

    //         // Save button should be disabled when form has errors (using our custom check)
    //         const saveButton = screen.getByTestId("mock-button-Save");
    //         expect(saveButton.hasAttribute("disabled")).toBe(true);
    //     } finally {
    //         // Restore original implementation
    //         formikMock.Formik = originalFormik;
    //     }
    // });

    test("validates that address must be at least 3 characters", async () => {
        // Create a form with a specific validation error
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const formikMock = require("formik");

        // Store original implementation
        const originalFormik = formikMock.Formik;

        // Mock implementation for this test
        formikMock.Formik = ({ children }) => {
            // Create mock formik with validation error for short address
            const formikInstance = {
                values: {
                    houseNumber: "123",
                    address: "AB", // Too short (less than 3 chars)
                    city: "New York",
                    state: "NY",
                    country: "United States",
                },
                touched: {
                    address: true,
                },
                errors: {
                    address: "Address must be at least 3 characters",
                },
                isValid: false,
                dirty: true,
                handleChange: jest.fn(),
                handleBlur: jest.fn(),
                setFieldTouched: jest.fn(),
            };

            return <div>{children(formikInstance)}</div>;
        };

        try {
            render(<AddressForm {...mockProps} />);

            // Check for specific validation error message
            expect(screen.getByTestId("error-address")).toBeInTheDocument();
            expect(screen.getByTestId("error-address")).toHaveTextContent("Address must be at least 3 characters");
        } finally {
            // Restore original implementation
            formikMock.Formik = originalFormik;
        }
    });

    test("allows changing all form fields", async () => {
        const user = userEvent.setup();
        render(<AddressForm {...mockProps} />);

        // Change all form fields
        const houseNumberInput = screen.getByTestId("mock-input-houseNumber").querySelector("input");
        const addressInput = screen.getByTestId("mock-input-address").querySelector("input");
        const cityInput = screen.getByTestId("mock-input-city").querySelector("input");
        const stateInput = screen.getByTestId("mock-input-state").querySelector("input");
        const countrySelect = screen.getByTestId("mock-dropdown-country").querySelector("select");

        await user.clear(houseNumberInput);
        await user.type(houseNumberInput, "456");

        await user.clear(addressInput);
        await user.type(addressInput, "New Address Street");

        await user.clear(cityInput);
        await user.type(cityInput, "Los Angeles");

        await user.clear(stateInput);
        await user.type(stateInput, "CA");

        fireEvent.change(countrySelect, { target: { value: "Canada" } });

        // Submit the form
        const form = screen.getByTestId("mock-form");
        fireEvent.submit(form);

        // Check if dispatch was called (MFA flow)
        expect(mockDispatch).toHaveBeenCalled();
    });

    test("handles empty countries array", () => {
        render(<AddressForm {...mockProps} countries={[]} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();
    });

    test("handles null countries", () => {
        render(<AddressForm {...mockProps} countries={null} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();
    });

    test("handles country without flags", () => {
        const propsWithoutFlags = {
            ...mockProps,
            countries: [
                {
                    name: { common: "United States" },
                    // No flags property
                },
            ],
        };

        render(<AddressForm {...propsWithoutFlags} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();
    });

    test("handles country value that doesn't match any country in options", () => {
        const propsWithNonMatchingCountry = {
            ...mockProps,
            country: "Unknown Country", // Country that doesn't exist in the countries array
        };

        render(<AddressForm {...propsWithNonMatchingCountry} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();

        // Use the data-value attribute to check the value
        const countrySelect = countryDropdown.querySelector("select");
        expect(countrySelect.dataset.value).toBe("Unknown Country");
    });

    test("handles empty country value", () => {
        const propsWithEmptyCountry = {
            ...mockProps,
            country: "", // Empty country value
        };

        render(<AddressForm {...propsWithEmptyCountry} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();

        // Empty value should result in empty select value
        const countrySelect = countryDropdown.querySelector("select");
        expect(countrySelect.dataset.value).toBe("");
    });

    test("handles matching country value correctly", () => {
        // The mockProps already includes a country that matches one in the countries array
        render(<AddressForm {...mockProps} />);

        const countryDropdown = screen.getByTestId("mock-dropdown-country");
        expect(countryDropdown).toBeInTheDocument();

        // The select should have the matching country value
        const countrySelect = countryDropdown.querySelector("select");
        expect(countrySelect.dataset.value).toBe("United States");
    });

    test("correctly handles country flag fallback logic", () => {
        // Test the flag fallback logic directly by creating a simple object with the same structure
        const testFlagFallbackLogic = (country) => {
            return country.flags?.svg ?? country.flags?.png ?? country.flags?.alt ?? "https://via.placeholder.com/28";
        };

        // Test cases with different flag configurations
        expect(
            testFlagFallbackLogic({
                flags: { svg: "svg-flag.svg", png: "png-flag.png", alt: "alt-text" },
            })
        ).toBe("svg-flag.svg"); // Should use SVG when available

        expect(
            testFlagFallbackLogic({
                flags: { png: "png-flag.png", alt: "alt-text" },
            })
        ).toBe("png-flag.png"); // Should use PNG when SVG not available

        expect(
            testFlagFallbackLogic({
                flags: { alt: "alt-text" },
            })
        ).toBe("alt-text"); // Should use alt when SVG and PNG not available

        expect(
            testFlagFallbackLogic({
                flags: {},
            })
        ).toBe("https://via.placeholder.com/28"); // Should use placeholder for empty flags

        expect(
            testFlagFallbackLogic({
                flags: null,
            })
        ).toBe("https://via.placeholder.com/28"); // Should use placeholder for null flags

        expect(
            testFlagFallbackLogic({
                // No flags property
            })
        ).toBe("https://via.placeholder.com/28"); // Should use placeholder when no flags property
    });

    describe("House number validation for special characters and alphanumeric values", () => {
        test("accepts house number with special characters", async () => {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const formikMock = require("formik");
            const originalFormik = formikMock.Formik;

            // Mock implementation that allows special characters
            formikMock.Formik = ({ children }) => {
                const formikInstance = {
                    values: {
                        houseNumber: "123-A", // House number with special character
                        address: "Main Street",
                        city: "New York",
                        state: "NY",
                        country: "United States",
                    },
                    touched: {},
                    errors: {}, // No errors for special characters
                    isValid: true,
                    dirty: true,
                    handleChange: jest.fn(),
                    handleBlur: jest.fn(),
                    setFieldTouched: jest.fn(),
                };

                return <div>{children(formikInstance)}</div>;
            };

            try {
                render(<AddressForm {...mockProps} />);

                // Should not show validation error for special characters
                expect(screen.queryByTestId("error-houseNumber")).not.toBeInTheDocument();
            } finally {
                formikMock.Formik = originalFormik;
            }
        });

        test("accepts house number with alphanumeric values", async () => {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const formikMock = require("formik");
            const originalFormik = formikMock.Formik;

            // Mock implementation that allows alphanumeric values
            formikMock.Formik = ({ children }) => {
                const formikInstance = {
                    values: {
                        houseNumber: "123A", // House number with alphanumeric value
                        address: "Main Street",
                        city: "New York",
                        state: "NY",
                        country: "United States",
                    },
                    touched: {},
                    errors: {}, // No errors for alphanumeric values
                    isValid: true,
                    dirty: true,
                    handleChange: jest.fn(),
                    handleBlur: jest.fn(),
                    setFieldTouched: jest.fn(),
                };

                return <div>{children(formikInstance)}</div>;
            };

            try {
                render(<AddressForm {...mockProps} />);

                // Should not show validation error for alphanumeric values
                expect(screen.queryByTestId("error-houseNumber")).not.toBeInTheDocument();
            } finally {
                formikMock.Formik = originalFormik;
            }
        });

        test("accepts house number with complex alphanumeric and special characters", async () => {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const formikMock = require("formik");
            const originalFormik = formikMock.Formik;

            // Mock implementation that allows complex house numbers
            formikMock.Formik = ({ children }) => {
                const formikInstance = {
                    values: {
                        houseNumber: "Apt 123-B", // Complex house number with letters, numbers, and special characters
                        address: "Main Street",
                        city: "New York",
                        state: "NY",
                        country: "United States",
                    },
                    touched: {},
                    errors: {}, // No errors for complex house numbers
                    isValid: true,
                    dirty: true,
                    handleChange: jest.fn(),
                    handleBlur: jest.fn(),
                    setFieldTouched: jest.fn(),
                };

                return <div>{children(formikInstance)}</div>;
            };

            try {
                render(<AddressForm {...mockProps} />);

                // Should not show validation error for complex house numbers
                expect(screen.queryByTestId("error-houseNumber")).not.toBeInTheDocument();
            } finally {
                formikMock.Formik = originalFormik;
            }
        });

        test("accepts house number with letters only", async () => {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const formikMock = require("formik");
            const originalFormik = formikMock.Formik;

            // Mock implementation that allows letters only
            formikMock.Formik = ({ children }) => {
                const formikInstance = {
                    values: {
                        houseNumber: "Building A", // House number with letters only
                        address: "Main Street",
                        city: "New York",
                        state: "NY",
                        country: "United States",
                    },
                    touched: {},
                    errors: {}, // No errors for letters only
                    isValid: true,
                    dirty: true,
                    handleChange: jest.fn(),
                    handleBlur: jest.fn(),
                    setFieldTouched: jest.fn(),
                };

                return <div>{children(formikInstance)}</div>;
            };

            try {
                render(<AddressForm {...mockProps} />);

                // Should not show validation error for letters only
                expect(screen.queryByTestId("error-houseNumber")).not.toBeInTheDocument();
            } finally {
                formikMock.Formik = originalFormik;
            }
        });

        test("still requires house number to be non-empty", async () => {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const formikMock = require("formik");
            const originalFormik = formikMock.Formik;

            // Mock implementation with empty house number
            formikMock.Formik = ({ children }) => {
                const formikInstance = {
                    values: {
                        houseNumber: "", // Empty house number
                        address: "Main Street",
                        city: "New York",
                        state: "NY",
                        country: "United States",
                    },
                    touched: {
                        houseNumber: true,
                    },
                    errors: {
                        houseNumber: "House number is required", // Should still be required
                    },
                    isValid: false,
                    dirty: true,
                    handleChange: jest.fn(),
                    handleBlur: jest.fn(),
                    setFieldTouched: jest.fn(),
                };

                return <div>{children(formikInstance)}</div>;
            };

            try {
                render(<AddressForm {...mockProps} />);

                // Should show validation error for empty house number
                expect(screen.getByTestId("error-houseNumber")).toBeInTheDocument();
                expect(screen.getByTestId("error-houseNumber")).toHaveTextContent("House number is required");
            } finally {
                formikMock.Formik = originalFormik;
            }
        });
    });

    test("getSelectedCountry function correctly handles different scenarios", () => {
        // Create a mock implementation of getSelectedCountry that mimics our implementation
        const mockCountries = [{ name: { common: "United States" } }, { name: { common: "Canada" } }];

        const mockCountryOptions = mockCountries.map((country) => ({
            label: country.name.common, // Simplified for testing
            value: country.name.common,
        }));

        const getSelectedCountry = (countryName) => {
            // Return the matching country from options if it exists
            const matchingCountry = mockCountryOptions.find((option) => option.value === countryName);
            if (matchingCountry) return matchingCountry;

            // If countryName exists but doesn't match any country in options, create a default option
            if (countryName) {
                return {
                    label: countryName,
                    value: countryName,
                };
            }

            return null;
        };

        // Test with matching country
        const matchingResult = getSelectedCountry("United States");
        expect(matchingResult).toEqual({
            label: "United States",
            value: "United States",
        });

        // Test with non-matching country
        const nonMatchingResult = getSelectedCountry("Unknown Country");
        expect(nonMatchingResult).toEqual({
            label: "Unknown Country",
            value: "Unknown Country",
        });

        // Test with empty string
        const emptyResult = getSelectedCountry("");
        expect(emptyResult).toBeNull();

        // Test with null
        const nullResult = getSelectedCountry(null);
        expect(nullResult).toBeNull();

        // Test with undefined
        const undefinedResult = getSelectedCountry(undefined);
        expect(undefinedResult).toBeNull();
    });
});
