import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { PersonalSettings } from "@/components/page-components/dashboard/settings/personal-settings";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchPersonalInfo, updatePersonalInfo, updatePersonalInfoWithToken } from "@/redux/actions/settingsActions";
import { sendFeedback } from "@/functions/feedback";

// Mock the dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/settingsActions", () => ({
    fetchPersonalInfo: jest.fn(),
    updatePersonalInfo: jest.fn(),
    updatePersonalInfoWithToken: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

// Mock child components
jest.mock("@/components/page-components/dashboard/settings/ui/editable-field", () => ({
    EditableField: ({ name, label, value, onSave }) => (
        <div data-testid={`editable-field-${name}`}>
            <span>
                {label}: {value}
            </span>
            <button data-testid={`edit-button-${name}`} onClick={() => onSave("new value")}>
                Edit
            </button>
        </div>
    ),
}));

// Create a version of the AddressForm that allows us to test the address object creation
jest.mock("@/components/page-components/dashboard/settings/address", () => {
    return {
        AddressForm: ({ handleSaveAddress, handleSaveAddressWithToken, toggleAddressEdit }) => (
            <div data-testid="address-form">
                <button
                    data-testid="save-address"
                    onClick={() =>
                        handleSaveAddress({
                            number: "123",
                            street: "Test Street",
                            city: "Test City",
                            state: "Test State",
                            country: "Test Country",
                        })
                    }
                >
                    Save
                </button>
                <button data-testid="save-address-undefined-values" onClick={() => handleSaveAddress()}>
                    Save With Undefined
                </button>
                <button
                    data-testid="save-address-null-values"
                    onClick={() =>
                        handleSaveAddress({
                            number: null,
                            street: null,
                            city: null,
                            state: null,
                            country: null,
                        })
                    }
                >
                    Save With Null
                </button>
                <button
                    data-testid="save-address-with-token"
                    onClick={() =>
                        handleSaveAddressWithToken("123456", {
                            number: "456",
                            street: "Token Street",
                            city: "Token City",
                            state: "Token State",
                            country: "Token Country",
                        })
                    }
                >
                    Save With Token
                </button>
                <button
                    data-testid="save-address-with-token-undefined"
                    onClick={() => handleSaveAddressWithToken("123456")}
                >
                    Save With Token Undefined
                </button>
                <button
                    data-testid="save-address-with-token-null"
                    onClick={() =>
                        handleSaveAddressWithToken("123456", {
                            number: null,
                            street: null,
                            city: null,
                            state: null,
                            country: null,
                        })
                    }
                >
                    Save With Token Null
                </button>
                <button data-testid="cancel-address" onClick={toggleAddressEdit}>
                    Cancel
                </button>
            </div>
        ),
        AddressDisplay: ({ toggleAddressEdit }) => (
            <div data-testid="address-display">
                <button data-testid="edit-address" onClick={toggleAddressEdit}>
                    Edit Address
                </button>
            </div>
        ),
    };
});

jest.mock("@/components/page-components/dashboard/settings/ui", () => ({
    PersonalSettingsSkeleton: () => <div data-testid="skeleton">Loading Skeleton</div>,
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick }) => (
        <button data-testid="retry-button" onClick={onClick}>
            {children}
        </button>
    ),
}));

const mockPersonalInfo = {
    fullName: "John Doe",
    email: "<EMAIL>",
    preferredName: "Johnny",
    phoneNumber: "1234567890",
    dateOfBirth: "1990-01-01",
    role: "customer-service",
    address: {
        number: "123",
        street: "Main St",
        city: "Anytown",
        state: "Anystate",
        country: "USA",
    },
};

// Create a mock dispatch function that returns an object with an unwrap method
const createMockDispatchResult = (shouldResolve = true, resolveValue = {}, rejectValue = "Error") => {
    return {
        unwrap: shouldResolve ? jest.fn().mockResolvedValue(resolveValue) : jest.fn().mockRejectedValue(rejectValue),
    };
};

const mockDispatch = jest.fn();

describe("PersonalSettings Component", () => {
    // Reset all mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();

        // Set up default behavior for dispatch and action creators
        useAppDispatch.mockReturnValue(mockDispatch);
        mockDispatch.mockImplementation(() => createMockDispatchResult(true));

        fetchPersonalInfo.mockReturnValue({ type: "fetchPersonalInfo" });
        updatePersonalInfo.mockReturnValue({ type: "updatePersonalInfo" });
        updatePersonalInfoWithToken.mockReturnValue({ type: "updatePersonalInfoWithToken" });
    });

    it("should fetch personal info on mount", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        render(<PersonalSettings />);

        expect(useAppDispatch).toHaveBeenCalled();
        expect(fetchPersonalInfo).toHaveBeenCalled();
        expect(mockDispatch).toHaveBeenCalledWith({ type: "fetchPersonalInfo" });
    });

    it("should show loading skeleton when data is loading", () => {
        // Mock loading state
        useAppSelector.mockReturnValue({
            data: null,
            loading: true,
            error: null,
            updateLoading: false,
        });

        render(<PersonalSettings />);

        expect(screen.getByTestId("skeleton")).toBeInTheDocument();
    });

    it("should show error state and retry button when fetch fails", async () => {
        // Mock error state
        const errorMessage = "Failed to fetch data";

        // Initial state with error
        useAppSelector.mockReturnValue({
            data: null,
            loading: false,
            error: errorMessage,
            updateLoading: false,
        });

        // Mock dispatch to return a rejected promise for the first call (initial fetch)
        mockDispatch.mockImplementationOnce(() => createMockDispatchResult(false, {}, errorMessage));

        render(<PersonalSettings />);

        await waitFor(() => {
            expect(screen.getByText(errorMessage)).toBeInTheDocument();
        });

        // Reset mock for retry - now it should resolve
        mockDispatch.mockImplementation(() => createMockDispatchResult(true));

        // Test retry functionality
        fireEvent.click(screen.getByTestId("retry-button"));

        expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
    });

    it("should display personal information when data is loaded", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        render(<PersonalSettings />);

        // Check if personal info is displayed
        expect(screen.getByText("Full legal name")).toBeInTheDocument();
        expect(screen.getByText(mockPersonalInfo.fullName)).toBeInTheDocument();
        expect(screen.getByText("Email address")).toBeInTheDocument();
        expect(screen.getByText(mockPersonalInfo.email)).toBeInTheDocument();
        expect(screen.getByText("Phone number")).toBeInTheDocument();
        expect(screen.getByText(mockPersonalInfo.phoneNumber)).toBeInTheDocument();
    });

    it("should show fallback text when data field is missing", async () => {
        // Mock data with missing fields
        useAppSelector.mockReturnValue({
            data: {
                ...mockPersonalInfo,
                fullName: null,
                email: null,
                phoneNumber: null,
            },
            loading: false,
            error: null,
            updateLoading: false,
        });

        render(<PersonalSettings />);

        await waitFor(() => {
            expect(screen.getByText("No full name available")).toBeInTheDocument();
            expect(screen.getByText("No email available")).toBeInTheDocument();
            expect(screen.getByText("No phone number available")).toBeInTheDocument();
        });
    });

    it("should handle saving personal information fields", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        // Setup success response for updatePersonalInfo
        mockDispatch.mockImplementation((action) => {
            if (action.type === "updatePersonalInfo") {
                return createMockDispatchResult(true);
            }
            return createMockDispatchResult(true);
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Test saving preferred name
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-button-preferred-name"));
        });

        // Verify dispatch was called with correct parameters
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                preferredName: "new value",
                address: {},
            })
        );

        // Verify fetchPersonalInfo is called after update
        await waitFor(() => {
            expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
        });

        // Verify feedback is shown
        expect(sendFeedback).toHaveBeenCalledWith(
            "Your preferred name has been updated successfully",
            "success",
            undefined,
            "Success"
        );
    });

    it("should toggle address edit mode", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Initially should show address display
        expect(screen.getByTestId("address-display")).toBeInTheDocument();

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Should now show address form
        expect(screen.getByTestId("address-form")).toBeInTheDocument();

        // Toggle back to display mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("cancel-address"));
        });

        // Should show address display again
        expect(screen.getByTestId("address-display")).toBeInTheDocument();
    });

    it("should handle saving address information", async () => {
        // Mock successful data loading with countries
        useAppSelector.mockImplementation((selector) => {
            // Safely handle the selector function by checking its type
            if (typeof selector === "function") {
                const state = {
                    settings: {
                        personalInfo: { data: mockPersonalInfo, loading: false, error: null, updateLoading: false },
                    },
                    countries: { countries: [{ code: "US", name: "United States" }] },
                };
                return selector(state);
            }

            return {
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
                countries: [{ code: "US", name: "United States" }],
            };
        });

        // Setup success response for updatePersonalInfo
        mockDispatch.mockImplementation((action) => {
            if (action.type === "updatePersonalInfo") {
                return createMockDispatchResult(true);
            }
            return createMockDispatchResult(true);
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Submit the address form
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address"));
        });

        // Verify dispatch was called with correct parameters
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                address: {
                    number: "123",
                    street: "Test Street",
                    city: "Test City",
                    state: "Test State",
                    country: "Test Country",
                },
            })
        );

        // Verify fetchPersonalInfo is called after update
        await waitFor(() => {
            expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
        });

        // Verify feedback is shown
        expect(sendFeedback).toHaveBeenCalledWith(
            "Your address has been updated successfully",
            "success",
            undefined,
            "Success"
        );

        // Should return to display mode
        await waitFor(() => {
            expect(screen.getByTestId("address-display")).toBeInTheDocument();
        });
    });

    it("should handle fallback to state values when no form address is provided", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Submit with undefined values (triggers the || fallback logic)
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address-undefined-values"));
        });

        // Verify dispatch was called with correct parameters
        // Since no form address was provided, it should use the local state values
        // which are initialized from mockPersonalInfo
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                address: {
                    number: "123", // from mockPersonalInfo via local state
                    street: "Main St",
                    city: "Anytown",
                    state: "Anystate",
                    country: "USA",
                },
            })
        );
    });

    it("should use empty strings as fallbacks for null values in address object", async () => {
        // Mock successful data loading
        useAppSelector.mockReturnValue({
            data: mockPersonalInfo,
            loading: false,
            error: null,
            updateLoading: false,
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Submit with null values (triggers the || "" fallback logic)
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address-null-values"));
        });

        // Verify dispatch was called with correct parameters
        // Since address values are null, it should use empty strings as fallbacks
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                address: {
                    number: "", // null || "" => ""
                    street: "",
                    city: "",
                    state: "",
                    country: "",
                },
            })
        );
    });

    it("should handle string address format correctly", async () => {
        // Mock personal info with string address
        const stringAddressMock = {
            ...mockPersonalInfo,
            address: "123 Main St, Anytown, Anystate, USA",
        };

        useAppSelector.mockReturnValue({
            data: stringAddressMock,
            loading: false,
            error: null,
            updateLoading: false,
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Submit the address form
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address"));
        });

        // Verify dispatch was called with correct parameters
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                address: {
                    number: "123",
                    street: "Test Street",
                    city: "Test City",
                    state: "Test State",
                    country: "Test Country",
                },
            })
        );
    });

    it("should initialize address state with empty strings for missing address fields", async () => {
        // Mock personal info with incomplete address object
        const incompleteAddressMock = {
            ...mockPersonalInfo,
            address: {
                // Only include some fields to test the || "" fallback for missing fields
                number: "123",
                // street is missing
                city: "Anytown",
                // state is missing
                country: "USA",
            },
        };

        // Mock useSelector to return the incomplete address
        useAppSelector.mockReturnValue({
            data: incompleteAddressMock,
            loading: false,
            error: null,
            updateLoading: false,
        });

        await act(async () => {
            render(<PersonalSettings />);
        });

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Submit the form without providing any values
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address-undefined-values"));
        });

        // Check that the form submission happened with the expected values
        expect(mockDispatch).toHaveBeenCalledWith(
            updatePersonalInfo({
                address: {
                    number: "123", // This was in the original object
                    street: "", // This was missing and should be empty string
                    city: "Anytown", // This was in the original object
                    state: "", // This was missing and should be empty string
                    country: "USA", // This was in the original object
                },
            })
        );
    });

    it("should handle complex error objects from the API", async () => {
        // Mock complex API error
        const complexError = {
            response: {
                data: {
                    message: "API validation error",
                },
            },
        };

        // Mock dispatch to return a rejected promise to simulate API error
        mockDispatch.mockImplementation(() => createMockDispatchResult(false, {}, complexError));

        // Directly mock the error state
        useAppSelector.mockReturnValue({
            data: null,
            loading: false,
            error: complexError,
            updateLoading: false,
        });

        render(<PersonalSettings />);

        // Check that the error message is displayed
        await waitFor(() => {
            expect(screen.getByText("API validation error")).toBeInTheDocument();
        });

        // Verify retry button is present
        expect(screen.getByTestId("retry-button")).toBeInTheDocument();

        // Test retry functionality
        fireEvent.click(screen.getByTestId("retry-button"));
        expect(fetchPersonalInfo).toHaveBeenCalled();
    });

    it("should handle invalid address properties gracefully", async () => {
        // Create an address object with invalid properties that don't throw errors but are invalid
        const invalidAddressMock = {
            ...mockPersonalInfo,
            address: {
                // Use undefined values instead of throwing properties
                number: undefined,
                street: null,
                city: "Test City",
                state: {}, // Wrong type - should be handled
                country: 12345, // Wrong type - should be handled
            },
        };

        useAppSelector.mockReturnValue({
            data: invalidAddressMock,
            loading: false,
            error: null,
            updateLoading: false,
        });

        // Render the component
        await act(async () => {
            render(<PersonalSettings />);
        });

        // Verify the component rendered
        expect(screen.getByText("Personal information")).toBeInTheDocument();

        // Toggle to edit mode
        await act(async () => {
            fireEvent.click(screen.getByTestId("edit-address"));
        });

        // Verify address form is displayed
        expect(screen.getByTestId("address-form")).toBeInTheDocument();

        // Submit with undefined values
        await act(async () => {
            fireEvent.click(screen.getByTestId("save-address-undefined-values"));
        });

        // Check that the update still works
        expect(updatePersonalInfo).toHaveBeenCalled();
    });

    // handleSaveAddressWithToken specific tests
    describe("handleSaveAddressWithToken Integration", () => {
        it("should handle saving address with token and form address", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Submit with token and form address
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token"));
            });

            // Verify dispatch was called with correct parameters
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "456",
                        street: "Token Street",
                        city: "Token City",
                        state: "Token State",
                        country: "Token Country",
                    },
                    token: "123456",
                })
            );

            // Verify fetchPersonalInfo is called after update
            await waitFor(() => {
                expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
            });

            // Verify feedback is shown
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your address has been updated successfully",
                "success",
                undefined,
                "Success"
            );

            // Should return to display mode
            await waitFor(() => {
                expect(screen.getByTestId("address-display")).toBeInTheDocument();
            });
        });

        it("should handle saving address with token and fallback to state values", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Submit with token but undefined form address (triggers fallback to state)
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token-undefined"));
            });

            // Verify dispatch was called with state values
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "123", // from mockPersonalInfo via local state
                        street: "Main St",
                        city: "Anytown",
                        state: "Anystate",
                        country: "USA",
                    },
                    token: "123456",
                })
            );

            // Verify fetchPersonalInfo is called after update
            await waitFor(() => {
                expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
            });

            // Verify feedback is shown
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your address has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });

        it("should handle saving address with token and null values", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Submit with token and null values
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token-null"));
            });

            // Verify dispatch was called with empty strings for null values
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "", // null || "" => ""
                        street: "",
                        city: "",
                        state: "",
                        country: "",
                    },
                    token: "123456",
                })
            );

            // Verify fetchPersonalInfo is called after update
            await waitFor(() => {
                expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
            });

            // Verify feedback is shown
            expect(sendFeedback).toHaveBeenCalledWith(
                "Your address has been updated successfully",
                "success",
                undefined,
                "Success"
            );
        });

        it("should handle API error during address save with token", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup error response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(false, {}, "API Error");
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Submit with token and form address
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token"));
            });

            // Verify dispatch was called with correct parameters
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "456",
                        street: "Token Street",
                        city: "Token City",
                        state: "Token State",
                        country: "Token Country",
                    },
                    token: "123456",
                })
            );

            // Verify component doesn't crash and remains in edit mode
            await waitFor(() => {
                expect(screen.getByTestId("address-form")).toBeInTheDocument();
            });
        });

        it("should handle rapid successive address saves with token", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Rapidly submit multiple address saves with token
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token"));
                fireEvent.click(screen.getByTestId("save-address-with-token-undefined"));
            });

            // Verify both calls were made
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "456",
                        street: "Token Street",
                        city: "Token City",
                        state: "Token State",
                        country: "Token Country",
                    },
                    token: "123456",
                })
            );

            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "123",
                        street: "Main St",
                        city: "Anytown",
                        state: "Anystate",
                        country: "USA",
                    },
                    token: "123456",
                })
            );

            // Verify fetchPersonalInfo is called after updates
            await waitFor(() => {
                expect(fetchPersonalInfo).toHaveBeenCalledTimes(3); // Initial + 2 updates
            });
        });

        it("should handle address save with token when state values are empty", async () => {
            // Mock personal info with empty address
            const emptyAddressMock = {
                ...mockPersonalInfo,
                address: {
                    number: "",
                    street: "",
                    city: "",
                    state: "",
                    country: "",
                },
            };

            useAppSelector.mockReturnValue({
                data: emptyAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            await act(async () => {
                render(<PersonalSettings />);
            });

            // Toggle to edit mode
            await act(async () => {
                fireEvent.click(screen.getByTestId("edit-address"));
            });

            // Submit with token but undefined form address
            await act(async () => {
                fireEvent.click(screen.getByTestId("save-address-with-token-undefined"));
            });

            // Verify dispatch was called with empty strings
            expect(mockDispatch).toHaveBeenCalledWith(
                updatePersonalInfoWithToken({
                    address: {
                        number: "", // empty state value
                        street: "",
                        city: "",
                        state: "",
                        country: "",
                    },
                    token: "123456",
                })
            );

            // Verify fetchPersonalInfo is called after update
            await waitFor(() => {
                expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
            });
        });

        it("should handle address save with token and partial form address", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Create a custom AddressForm mock for this test
            const AddressFormWithPartial = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-partial">
                    <button
                        data-testid="save-address-with-token-partial"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: "789",
                                street: "Partial Street",
                                // city, state, country are missing
                            })
                        }
                    >
                        Save With Token Partial
                    </button>
                    <button data-testid="cancel-address-partial" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithPartial;

            try {
                // Setup success response for updatePersonalInfoWithToken
                mockDispatch.mockImplementation((action) => {
                    if (action.type === "updatePersonalInfoWithToken") {
                        return createMockDispatchResult(true);
                    }
                    return createMockDispatchResult(true);
                });

                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Toggle to edit mode
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-address"));
                });

                // Submit with token and partial form address
                await act(async () => {
                    fireEvent.click(screen.getByTestId("save-address-with-token-partial"));
                });

                // Verify dispatch was called with partial address (undefined values should be empty strings)
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        address: {
                            number: "789",
                            street: "Partial Street",
                            city: "", // undefined || "" => ""
                            state: "",
                            country: "",
                        },
                        token: "123456",
                    })
                );

                // Verify fetchPersonalInfo is called after update
                await waitFor(() => {
                    expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });

        it("should handle address save with token and very long values", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            const longValue = "A".repeat(1000);

            // Create a custom AddressForm mock for this test
            const AddressFormWithLong = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-long">
                    <button
                        data-testid="save-address-with-token-long"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: longValue,
                                street: longValue,
                                city: longValue,
                                state: longValue,
                                country: longValue,
                            })
                        }
                    >
                        Save With Token Long
                    </button>
                    <button data-testid="cancel-address-long" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithLong;

            try {
                // Setup success response for updatePersonalInfoWithToken
                mockDispatch.mockImplementation((action) => {
                    if (action.type === "updatePersonalInfoWithToken") {
                        return createMockDispatchResult(true);
                    }
                    return createMockDispatchResult(true);
                });

                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Toggle to edit mode
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-address"));
                });

                // Submit with token and long values
                await act(async () => {
                    fireEvent.click(screen.getByTestId("save-address-with-token-long"));
                });

                // Verify dispatch was called with long values
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        address: {
                            number: longValue,
                            street: longValue,
                            city: longValue,
                            state: longValue,
                            country: longValue,
                        },
                        token: "123456",
                    })
                );

                // Verify fetchPersonalInfo is called after update
                await waitFor(() => {
                    expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });

        it("should handle address save with token and special characters", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Create a custom AddressForm mock for this test
            const AddressFormWithSpecial = ({ handleSaveAddressWithToken, toggleAddressEdit }) => (
                <div data-testid="address-form-special">
                    <button
                        data-testid="save-address-with-token-special"
                        onClick={() =>
                            handleSaveAddressWithToken("123456", {
                                number: "123-A",
                                street: "Test & Co. Street",
                                city: "São Paulo",
                                state: "New York (NY)",
                                country: "Côte d'Ivoire",
                            })
                        }
                    >
                        Save With Token Special
                    </button>
                    <button data-testid="cancel-address-special" onClick={toggleAddressEdit}>
                        Cancel
                    </button>
                </div>
            );

            // Temporarily replace the AddressForm mock
            const originalAddressForm = jest.requireMock(
                "@/components/page-components/dashboard/settings/address"
            ).AddressForm;
            jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                AddressFormWithSpecial;

            try {
                // Setup success response for updatePersonalInfoWithToken
                mockDispatch.mockImplementation((action) => {
                    if (action.type === "updatePersonalInfoWithToken") {
                        return createMockDispatchResult(true);
                    }
                    return createMockDispatchResult(true);
                });

                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Toggle to edit mode
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-address"));
                });

                // Submit with token and special characters
                await act(async () => {
                    fireEvent.click(screen.getByTestId("save-address-with-token-special"));
                });

                // Verify dispatch was called with special characters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        address: {
                            number: "123-A",
                            street: "Test & Co. Street",
                            city: "São Paulo",
                            state: "New York (NY)",
                            country: "Côte d'Ivoire",
                        },
                        token: "123456",
                    })
                );

                // Verify fetchPersonalInfo is called after update
                await waitFor(() => {
                    expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/address").AddressForm =
                    originalAddressForm;
            }
        });
    });

    // handleSaveWithToken specific tests
    describe("handleSaveWithToken Integration", () => {
        it("should handle saving personal information fields with token", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            // Create a custom EditableField mock for this test
            const EditableFieldWithToken = ({ name, label, value, onSaveWithToken }) => (
                <div data-testid={`editable-field-with-token-${name}`}>
                    <span>
                        {label}: {value}
                    </span>
                    <button
                        data-testid={`edit-button-with-token-${name}`}
                        onClick={() => onSaveWithToken("new value", "123456")}
                    >
                        Edit with Token
                    </button>
                </div>
            );

            // Temporarily replace the EditableField mock
            const originalEditableField = jest.requireMock(
                "@/components/page-components/dashboard/settings/ui/editable-field"
            ).EditableField;
            jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                EditableFieldWithToken;

            try {
                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Test saving preferred name with token
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-preferred-name"));
                });

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        preferredName: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Verify fetchPersonalInfo is called after update
                await waitFor(() => {
                    expect(fetchPersonalInfo).toHaveBeenCalledTimes(2);
                });

                // Verify feedback is shown
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your preferred name has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                    originalEditableField;
            }
        });

        it("should handle saving all field types with token", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            // Create a custom EditableField mock for this test
            const EditableFieldWithTokenAll = ({ name, label, value, onSaveWithToken }) => (
                <div data-testid={`editable-field-with-token-all-${name}`}>
                    <span>
                        {label}: {value}
                    </span>
                    <button
                        data-testid={`edit-button-with-token-all-${name}`}
                        onClick={() => onSaveWithToken("new value", "123456")}
                    >
                        Edit with Token
                    </button>
                </div>
            );

            // Temporarily replace the EditableField mock
            const originalEditableField = jest.requireMock(
                "@/components/page-components/dashboard/settings/ui/editable-field"
            ).EditableField;
            jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                EditableFieldWithTokenAll;

            try {
                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Test saving preferred name with token
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-all-preferred-name"));
                });

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        preferredName: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Test saving date of birth with token
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-all-date-of-birth"));
                });

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        dateOfBirth: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Test saving role with token
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-all-role"));
                });

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        role: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Verify fetchPersonalInfo is called after updates
                await waitFor(() => {
                    expect(fetchPersonalInfo).toHaveBeenCalledTimes(4); // Initial + 3 updates
                });
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                    originalEditableField;
            }
        });

        it("should handle API error during save with token", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup error response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(false, {}, "API Error");
                }
                return createMockDispatchResult(true);
            });

            // Create a custom EditableField mock for this test
            const EditableFieldWithTokenError = ({ name, label, value, onSaveWithToken }) => (
                <div data-testid={`editable-field-with-token-error-${name}`}>
                    <span>
                        {label}: {value}
                    </span>
                    <button
                        data-testid={`edit-button-with-token-error-${name}`}
                        onClick={() => onSaveWithToken("new value", "123456")}
                    >
                        Edit with Token Error
                    </button>
                </div>
            );

            // Temporarily replace the EditableField mock
            const originalEditableField = jest.requireMock(
                "@/components/page-components/dashboard/settings/ui/editable-field"
            ).EditableField;
            jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                EditableFieldWithTokenError;

            try {
                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Test saving with token that causes an error
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-error-preferred-name"));
                });

                // Verify dispatch was called with correct parameters
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        preferredName: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Verify component doesn't crash
                expect(screen.getByTestId("editable-field-with-token-error-preferred-name")).toBeInTheDocument();
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                    originalEditableField;
            }
        });

        it("should handle unknown field names in handleSaveWithToken", async () => {
            // Mock successful data loading
            useAppSelector.mockReturnValue({
                data: mockPersonalInfo,
                loading: false,
                error: null,
                updateLoading: false,
            });

            // Setup success response for updatePersonalInfoWithToken
            mockDispatch.mockImplementation((action) => {
                if (action.type === "updatePersonalInfoWithToken") {
                    return createMockDispatchResult(true);
                }
                return createMockDispatchResult(true);
            });

            // Create a custom EditableField mock for this test
            const EditableFieldWithTokenUnknown = ({ name, label, value, onSaveWithToken }) => (
                <div data-testid={`editable-field-with-token-unknown-${name}`}>
                    <span>
                        {label}: {value}
                    </span>
                    <button
                        data-testid={`edit-button-with-token-unknown-${name}`}
                        onClick={() => onSaveWithToken("new value", "123456")}
                    >
                        Edit with Token Unknown
                    </button>
                </div>
            );

            // Temporarily replace the EditableField mock
            const originalEditableField = jest.requireMock(
                "@/components/page-components/dashboard/settings/ui/editable-field"
            ).EditableField;
            jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                EditableFieldWithTokenUnknown;

            try {
                await act(async () => {
                    render(<PersonalSettings />);
                });

                // Test saving unknown field with token
                await act(async () => {
                    fireEvent.click(screen.getByTestId("edit-button-with-token-unknown-preferred-name"));
                });

                // Verify dispatch was called with correct parameters (unknown field should use field.replace)
                expect(mockDispatch).toHaveBeenCalledWith(
                    updatePersonalInfoWithToken({
                        preferredName: "new value",
                        address: {},
                        token: "123456",
                    })
                );

                // Verify feedback is shown with transformed field name
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Your preferred name has been updated successfully",
                    "success",
                    undefined,
                    "Success"
                );
            } finally {
                // Restore original mock
                jest.requireMock("@/components/page-components/dashboard/settings/ui/editable-field").EditableField =
                    originalEditableField;
            }
        });
    });

    // Additional tests for address initialization edge cases
    describe("Address Initialization Edge Cases", () => {
        it("should handle address initialization with undefined address", () => {
            // Mock personal info with undefined address
            const undefinedAddressMock = {
                ...mockPersonalInfo,
                address: undefined,
            };

            useAppSelector.mockReturnValue({
                data: undefinedAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with null address", () => {
            // Mock personal info with null address
            const nullAddressMock = {
                ...mockPersonalInfo,
                address: null,
            };

            useAppSelector.mockReturnValue({
                data: nullAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with empty string address", () => {
            // Mock personal info with empty string address
            const emptyStringAddressMock = {
                ...mockPersonalInfo,
                address: "",
            };

            useAppSelector.mockReturnValue({
                data: emptyStringAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with whitespace string address", () => {
            // Mock personal info with whitespace string address
            const whitespaceAddressMock = {
                ...mockPersonalInfo,
                address: "   ",
            };

            useAppSelector.mockReturnValue({
                data: whitespaceAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with incomplete address object", () => {
            // Mock personal info with incomplete address object
            const incompleteAddressMock = {
                ...mockPersonalInfo,
                address: {
                    number: "123",
                    // street is missing
                    city: "Test City",
                    // state is missing
                    country: "Test Country",
                },
            };

            useAppSelector.mockReturnValue({
                data: incompleteAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with address object containing null values", () => {
            // Mock personal info with address object containing null values
            const nullValuesAddressMock = {
                ...mockPersonalInfo,
                address: {
                    number: null,
                    street: null,
                    city: null,
                    state: null,
                    country: null,
                },
            };

            useAppSelector.mockReturnValue({
                data: nullValuesAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });

        it("should handle address initialization with address object containing undefined values", () => {
            // Mock personal info with address object containing undefined values
            const undefinedValuesAddressMock = {
                ...mockPersonalInfo,
                address: {
                    number: undefined,
                    street: undefined,
                    city: undefined,
                    state: undefined,
                    country: undefined,
                },
            };

            useAppSelector.mockReturnValue({
                data: undefinedValuesAddressMock,
                loading: false,
                error: null,
                updateLoading: false,
            });

            render(<PersonalSettings />);

            // Component should render without errors
            expect(screen.getByText("Personal information")).toBeInTheDocument();
        });
    });
});
