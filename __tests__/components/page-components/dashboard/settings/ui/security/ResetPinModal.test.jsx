import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import ResetPinModal from "../../../../../../../src/components/page-components/dashboard/settings/ui/security/ResetPinModal";
import securityReducer from "../../../../../../../src/redux/slices/securitySlice";

// Mock the CustomModal component
jest.mock(
    "@/components/common/custom-modal",
    () =>
        ({ isOpen, onRequestClose, children, title }) =>
            isOpen ? (
                <div data-testid="custom-modal">
                    <h1>{title}</h1>
                    <button onClick={onRequestClose} data-testid="close-modal">
                        Close
                    </button>
                    {children}
                </div>
            ) : null
);

// Mock the Button component
jest.mock("../../../../../../../src/components/common/buttonv3", () => ({
    Button: ({ children, loading, disabled, onClick, type, variant }) => (
        <button
            data-testid={variant === "outline" ? "cancel-button" : "submit-button"}
            disabled={disabled || loading}
            onClick={onClick}
            type={type}
        >
            {loading ? "Loading..." : children}
        </button>
    ),
}));

// Mock LabelInput component
jest.mock(
    "../../../../../../../src/components/common/label-input",
    () =>
        ({ formik, name, label, type, placeholder, value, onChange, onBlur, useFormik, showError, error }) => (
            <div data-testid={`label-input-${name}`}>
                <label>{label}</label>
                <input
                    name={name}
                    type={type}
                    placeholder={placeholder}
                    value={useFormik === false ? value : formik?.values[name]}
                    onChange={useFormik === false ? onChange : formik?.handleChange}
                    onBlur={useFormik === false ? onBlur : formik?.handleBlur}
                    data-testid={`input-${name}`}
                />
                {((useFormik === false && showError && error) || formik?.errors[name]) && (
                    <span data-testid={`error-${name}`}>{useFormik === false ? error : formik.errors[name]}</span>
                )}
            </div>
        )
);

// Mock feedback functions
jest.mock("../../../../../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

describe("ResetPinModal Component", () => {
    let store;
    const mockOnRequestClose = jest.fn();
    const baseProps = {
        isOpen: true,
        onRequestClose: mockOnRequestClose,
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                security: securityReducer,
            },
        });
        jest.clearAllMocks();
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <ResetPinModal {...baseProps} {...props} />
            </Provider>
        );
    };

    describe("Component Rendering", () => {
        it("should render modal when isOpen is true", () => {
            renderWithProvider();

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
            expect(screen.getByText("Reset transaction PIN")).toBeInTheDocument();
        });

        it("should not render when isOpen is false", () => {
            renderWithProvider({ isOpen: false });

            expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
        });

        it("should render all PIN input fields", () => {
            renderWithProvider();

            expect(screen.getByTestId("label-input-newPIN")).toBeInTheDocument();
            expect(screen.getByTestId("label-input-confirmNewPIN")).toBeInTheDocument();
            expect(screen.getByText("New PIN")).toBeInTheDocument();
            expect(screen.getByText("Confirm New PIN")).toBeInTheDocument();
        });

        it("should render both buttons", () => {
            renderWithProvider();

            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();
            expect(screen.getByTestId("submit-button")).toBeInTheDocument();
            expect(screen.getByText("Cancel")).toBeInTheDocument();
            expect(screen.getByText("Reset PIN")).toBeInTheDocument();
        });

        it("should render subtitle text", () => {
            renderWithProvider();

            expect(screen.getByText("This PIN will be required to authorize all transactions")).toBeInTheDocument();
        });
    });

    describe("Form Validation", () => {
        it("should validate PIN length", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Enter invalid PIN (less than 6 digits)
            fireEvent.change(newPinInput, { target: { value: "123" } });
            fireEvent.blur(newPinInput); // Trigger validation

            // Wait for validation to complete
            await waitFor(() => {
                expect(submitButton).toBeDisabled();
            });
        });

        it("should validate PIN confirmation match", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Enter mismatched PINs
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "654321" } });
            fireEvent.blur(confirmPinInput); // Trigger validation

            // Wait for validation to complete
            await waitFor(() => {
                expect(submitButton).toBeDisabled();
            });
        });

        it("should pass validation with valid matching inputs", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.queryByText("New PIN is required")).not.toBeInTheDocument();
                expect(screen.queryByText("Confirm New PIN is required")).not.toBeInTheDocument();
                expect(screen.queryByText("PINs must match")).not.toBeInTheDocument();
            });
        });
    });

    describe("PIN Reset Without Token", () => {
        it("should use resetTransactionPin action when no token provided", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should dispatch resetTransactionPin action
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });
    });

    describe("PIN Reset With Token", () => {
        it("should use resetTransactionPin action with token when token provided", async () => {
            renderWithProvider({ token: "test-mfa-token" });

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should dispatch resetTransactionPin action with token
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });
    });

    describe("Loading States", () => {
        it("should show loading state during PIN reset", () => {
            // Mock loading state
            store.dispatch({
                type: "security/resetTransactionPin/pending",
            });

            renderWithProvider();

            const submitButton = screen.getByTestId("submit-button");
            expect(submitButton).toBeDisabled();
            expect(submitButton.textContent).toBe("Loading...");
        });
    });

    describe("Success Handling", () => {
        it("should close modal on successful PIN reset", async () => {
            renderWithProvider();

            // Mock successful PIN reset
            store.dispatch({
                type: "security/resetTransactionPin/fulfilled",
                payload: { message: "PIN reset successfully" },
            });

            await waitFor(() => {
                expect(mockOnRequestClose).toHaveBeenCalled();
            });
        });
    });

    describe("Modal Interaction", () => {
        it("should call onRequestClose when close button is clicked", () => {
            renderWithProvider();

            const closeButton = screen.getByTestId("close-modal");
            fireEvent.click(closeButton);

            expect(mockOnRequestClose).toHaveBeenCalled();
        });

        it("should call onRequestClose when cancel button is clicked", () => {
            renderWithProvider();

            const cancelButton = screen.getByTestId("cancel-button");
            fireEvent.click(cancelButton);

            expect(mockOnRequestClose).toHaveBeenCalled();
        });

        it("should reset form when modal is closed via cancel button", () => {
            renderWithProvider();

            // Fill in some values
            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Verify form has values
            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("123456");

            // Click cancel button (which calls handleClose)
            const cancelButton = screen.getByTestId("cancel-button");
            fireEvent.click(cancelButton);

            // Verify form was reset
            expect(newPinInput.value).toBe("");
            expect(confirmPinInput.value).toBe("");
        });
    });

    describe("Error Handling", () => {
        it("should call onError when there's an error and callback is provided", async () => {
            const mockOnError = jest.fn();

            renderWithProvider({
                token: "test-token",
                onError: mockOnError,
            });

            // Fill in valid PIN values
            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Submit the form
            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Mock an error response
            await waitFor(() => {
                store.dispatch({
                    type: "security/resetTransactionPin/rejected",
                    error: { message: "Token expired" },
                });
            });

            // Verify that onError was called to close modals and reset state
            expect(mockOnError).toHaveBeenCalled();
        });

        it("should not call onError when there's an error but no callback provided", async () => {
            renderWithProvider({ token: "test-token" });

            // Fill in valid PIN values
            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Submit the form
            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Mock an error response
            await waitFor(() => {
                store.dispatch({
                    type: "security/resetTransactionPin/rejected",
                    error: { message: "Token expired" },
                });
            });

            // Should not throw any errors
            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("should reset form when error occurs and onError callback is provided", async () => {
            const mockOnError = jest.fn();

            renderWithProvider({
                token: "test-token",
                onError: mockOnError,
            });

            // Fill in valid PIN values
            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Verify form has values
            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("123456");

            // Submit the form
            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Mock an error response
            await waitFor(() => {
                store.dispatch({
                    type: "security/resetTransactionPin/rejected",
                    error: { message: "Token expired" },
                });
            });

            // Verify that onError was called
            expect(mockOnError).toHaveBeenCalled();

            // Verify that form was reset (inputs should be empty)
            await waitFor(() => {
                expect(newPinInput.value).toBe("");
                expect(confirmPinInput.value).toBe("");
            });
        });
    });

    describe("Input Restrictions", () => {
        it("should only accept numeric input", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");

            // Try to enter non-numeric characters
            fireEvent.change(newPinInput, { target: { value: "abc123def" } });

            // Should only contain the numeric characters
            expect(newPinInput.value).toBe("123");
        });

        it("should limit input to 6 digits maximum", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");

            // Try to enter more than 6 digits
            fireEvent.change(newPinInput, { target: { value: "**********" } });

            // Should only contain the first 6 digits
            expect(newPinInput.value).toBe("123456");
        });

        it("should handle mixed alphanumeric input correctly", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            // Try to enter mixed input
            fireEvent.change(newPinInput, { target: { value: "a1b2c3d4e5f6g7" } });
            fireEvent.change(confirmPinInput, { target: { value: "!@#123$%^456&*()" } });

            // Should only contain numeric characters, limited to 6 digits
            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("123456");
        });

        it("should handle special characters and spaces", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");

            // Try to enter special characters and spaces
            fireEvent.change(newPinInput, { target: { value: "1 2!3@4#5$6%" } });

            // Should only contain numeric characters
            expect(newPinInput.value).toBe("123456");
        });

        it("should work correctly with both input fields", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            // Enter valid 6-digit PINs
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("123456");

            // Try to exceed limit on both fields
            fireEvent.change(newPinInput, { target: { value: "**********" } });
            fireEvent.change(confirmPinInput, { target: { value: "**********" } });

            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("987654");
        });
    });

    describe("Function Coverage Tests", () => {
        it("should test handleClose function directly", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");

            // Add some data to the form
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            expect(newPinInput.value).toBe("123456");

            // Test handleClose via close button (X button)
            const closeButton = screen.getByTestId("close-modal");
            fireEvent.click(closeButton);

            // Verify onRequestClose was called and form was reset
            expect(mockOnRequestClose).toHaveBeenCalled();
            expect(newPinInput.value).toBe("");
        });

        it("should test formik onSubmit function with token", () => {
            renderWithProvider({ token: "test-token-123" });

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Fill valid form data
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Submit the form - this tests the onSubmit function
            fireEvent.click(submitButton);

            // Verify the form submission was attempted (button should be present)
            expect(submitButton).toBeInTheDocument();
        });

        it("should test formik onSubmit function without token", () => {
            renderWithProvider(); // No token provided

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Fill valid form data
            fireEvent.change(newPinInput, { target: { value: "654321" } });
            fireEvent.change(confirmPinInput, { target: { value: "654321" } });

            // Submit the form - this tests the onSubmit function
            fireEvent.click(submitButton);

            // Verify the form submission was attempted (button should be present)
            expect(submitButton).toBeInTheDocument();
        });

        it("should test handlePinChange function with different field names", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            // Test handlePinChange for newPIN field
            fireEvent.change(newPinInput, { target: { value: "abc123def" } });
            expect(newPinInput.value).toBe("123");

            // Test handlePinChange for confirmNewPIN field
            fireEvent.change(confirmPinInput, { target: { value: "xyz789ghi" } });
            expect(confirmPinInput.value).toBe("789");

            // Test with empty values
            fireEvent.change(newPinInput, { target: { value: "" } });
            fireEvent.change(confirmPinInput, { target: { value: "" } });
            expect(newPinInput.value).toBe("");
            expect(confirmPinInput.value).toBe("");
        });

        it("should test success useEffect with form reset", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");

            // Add data to form
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            fireEvent.change(confirmPinInput, { target: { value: "123456" } });

            // Verify form has data
            expect(newPinInput.value).toBe("123456");
            expect(confirmPinInput.value).toBe("123456");

            // Trigger success state
            await act(async () => {
                store.dispatch({
                    type: "security/resetTransactionPin/fulfilled",
                    payload: { message: "PIN reset successfully" },
                });
            });

            // Verify success useEffect behavior
            await waitFor(() => {
                expect(mockOnRequestClose).toHaveBeenCalled();
                expect(newPinInput.value).toBe("");
                expect(confirmPinInput.value).toBe("");
            });
        });

        it("should test error useEffect without onError callback", async () => {
            renderWithProvider(); // No onError callback

            const newPinInput = screen.getByTestId("input-newPIN");

            // Add data to form
            fireEvent.change(newPinInput, { target: { value: "123456" } });
            expect(newPinInput.value).toBe("123456");

            // Trigger error state
            await act(async () => {
                store.dispatch({
                    type: "security/resetTransactionPin/rejected",
                    error: { message: "Some error occurred" },
                });
            });

            // Form should not be reset since no onError callback
            expect(newPinInput.value).toBe("123456");
        });

        it("should test handlePinChange with edge cases", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");

            // Test with only special characters
            fireEvent.change(newPinInput, { target: { value: "!@#$%^&*()" } });
            expect(newPinInput.value).toBe("");

            // Test with only letters
            fireEvent.change(newPinInput, { target: { value: "abcdefghijk" } });
            expect(newPinInput.value).toBe("");

            // Test with mixed content but more than 6 digits
            fireEvent.change(newPinInput, { target: { value: "1a2b3c4d5e6f7g8h9i0j" } });
            expect(newPinInput.value).toBe("123456");

            // Test with spaces and tabs
            fireEvent.change(newPinInput, { target: { value: "1 2\t3 4\t5 6 7 8" } });
            expect(newPinInput.value).toBe("123456");
        });

        it("should test form submission with different PIN values", () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            const confirmPinInput = screen.getByTestId("input-confirmNewPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Test with different valid PIN combinations
            fireEvent.change(newPinInput, { target: { value: "000000" } });
            fireEvent.change(confirmPinInput, { target: { value: "000000" } });
            fireEvent.click(submitButton);

            // Verify form submission was attempted
            expect(submitButton).toBeInTheDocument();

            // Test with another combination
            fireEvent.change(newPinInput, { target: { value: "999999" } });
            fireEvent.change(confirmPinInput, { target: { value: "999999" } });
            fireEvent.click(submitButton);

            // Verify form submission was attempted again
            expect(submitButton).toBeInTheDocument();
        });

        it("should test component with different prop combinations", () => {
            // Test with all props
            const { rerender } = renderWithProvider({
                token: "test-token",
                onError: jest.fn(),
            });

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();

            // Test with minimal props
            rerender(
                <Provider store={store}>
                    <ResetPinModal isOpen={true} onRequestClose={mockOnRequestClose} />
                </Provider>
            );

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("should test useEffect cleanup and re-execution", async () => {
            const { rerender } = renderWithProvider();

            // Trigger success state
            await act(async () => {
                store.dispatch({
                    type: "security/resetTransactionPin/fulfilled",
                    payload: { message: "PIN reset successfully" },
                });
            });

            // Clear the success state
            await act(async () => {
                store.dispatch({
                    type: "security/clearState",
                    payload: "resetPin",
                });
            });

            // Re-render with different props to test useEffect re-execution
            rerender(
                <Provider store={store}>
                    <ResetPinModal isOpen={true} onRequestClose={mockOnRequestClose} token="new-token" />
                </Provider>
            );

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });
    });
});
