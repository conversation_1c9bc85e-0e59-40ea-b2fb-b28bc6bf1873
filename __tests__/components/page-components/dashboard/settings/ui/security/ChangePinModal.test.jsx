import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import ChangePinModal from "../../../../../../../src/components/page-components/dashboard/settings/ui/security/ChangePinModal";
import securityReducer from "../../../../../../../src/redux/slices/securitySlice";

// Mock the CustomModal component
jest.mock(
    "@/components/common/custom-modal",
    () =>
        ({ isOpen, onRequestClose, children, title }) =>
            isOpen ? (
                <div data-testid="custom-modal">
                    <h1>{title}</h1>
                    <button onClick={onRequestClose} data-testid="close-modal">
                        Close
                    </button>
                    {children}
                </div>
            ) : null
);

// Mock the Button component
jest.mock("../../../../../../../src/components/common/buttonv3", () => ({
    Button: ({ children, loading, disabled, onClick, type, variant }) => (
        <button
            data-testid={variant === "outline" ? "cancel-button" : "submit-button"}
            disabled={disabled || loading}
            onClick={onClick}
            type={type}
        >
            {loading ? "Loading..." : children}
        </button>
    ),
}));

// Mock LabelInput component
jest.mock(
    "../../../../../../../src/components/common/label-input",
    () =>
        ({ formik, name, label, type, placeholder }) => (
            <div data-testid={`label-input-${name}`}>
                <label>{label}</label>
                <input
                    name={name}
                    type={type}
                    placeholder={placeholder}
                    value={formik.values[name]}
                    onChange={formik.handleChange}
                    data-testid={`input-${name}`}
                />
                {formik.errors[name] && <span data-testid={`error-${name}`}>{formik.errors[name]}</span>}
            </div>
        )
);

// Mock feedback functions
jest.mock("../../../../../../../src/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

// Mock ResetPinModal component
jest.mock(
    "../../../../../../../src/components/page-components/dashboard/settings/ui/security/ResetPinModal",
    () =>
        ({ isOpen, onRequestClose }) =>
            isOpen ? (
                <div data-testid="reset-pin-modal">
                    <h1>Reset transaction PIN</h1>
                    <button onClick={onRequestClose} data-testid="close-reset-modal">
                        Close Reset Modal
                    </button>
                </div>
            ) : null
);

describe("ChangePinModal Component", () => {
    let store;
    const mockOnRequestClose = jest.fn();
    const baseProps = {
        isOpen: true,
        onRequestClose: mockOnRequestClose,
    };

    beforeEach(() => {
        store = configureStore({
            reducer: {
                security: securityReducer,
            },
        });
        jest.clearAllMocks();
    });

    const renderWithProvider = (props = {}) => {
        return render(
            <Provider store={store}>
                <ChangePinModal {...baseProps} {...props} />
            </Provider>
        );
    };

    describe("Component Rendering", () => {
        it("should render modal when isOpen is true", () => {
            renderWithProvider();

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
            expect(screen.getByText("Change transaction PIN")).toBeInTheDocument();
        });

        it("should not render when isOpen is false", () => {
            renderWithProvider({ isOpen: false });

            expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
        });

        it("should render all PIN input fields", () => {
            renderWithProvider();

            expect(screen.getByTestId("label-input-currentPIN")).toBeInTheDocument();
            expect(screen.getByTestId("label-input-newPIN")).toBeInTheDocument();
        });

        it("should render both buttons", () => {
            renderWithProvider();

            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();
            expect(screen.getByTestId("submit-button")).toBeInTheDocument();
            expect(screen.getByText("Cancel")).toBeInTheDocument();
            expect(screen.getByText("Change PIN")).toBeInTheDocument();
        });

        it("should render forgot pin reset link", () => {
            renderWithProvider();

            expect(screen.getByText("Forgot pin?")).toBeInTheDocument();
            expect(screen.getByText("Reset it")).toBeInTheDocument();
        });
    });

    describe("Form Validation", () => {
        it("should render form validation correctly", async () => {
            renderWithProvider();

            const submitButton = screen.getByTestId("submit-button");

            // Submit button should be enabled initially (Formik validation runs on submit)
            expect(submitButton).toBeEnabled();
            expect(submitButton).toHaveTextContent("Change PIN");
        });

        it("should validate PIN length", async () => {
            renderWithProvider();

            const newPinInput = screen.getByTestId("input-newPIN");
            fireEvent.change(newPinInput, { target: { value: "123" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText("PIN must be exactly 6 digits")).toBeInTheDocument();
            });
        });

        it("should pass validation with valid inputs", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.queryByText("Current PIN is required")).not.toBeInTheDocument();
                expect(screen.queryByText("New PIN is required")).not.toBeInTheDocument();
                expect(screen.queryByText("PIN must be exactly 6 digits")).not.toBeInTheDocument();
            });
        });
    });

    describe("PIN Change Without Token", () => {
        it("should use regular changeTransactionPin action when no token provided", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should dispatch regular changeTransactionPin action
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });
    });

    describe("PIN Change With Token", () => {
        it("should use token-based changeTransactionPinWithToken action when token provided", async () => {
            renderWithProvider({ token: "test-mfa-token" });

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should dispatch token-based changeTransactionPinWithToken action
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });

        it("should handle empty token as no token", async () => {
            renderWithProvider({ token: "" });

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should use regular action for empty token
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });

        it("should handle null token as no token", async () => {
            renderWithProvider({ token: null });

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            const submitButton = screen.getByTestId("submit-button");
            fireEvent.click(submitButton);

            // Should use regular action for null token
            await waitFor(() => {
                expect(submitButton).toBeInTheDocument();
            });
        });
    });

    describe("Loading States", () => {
        it("should show loading state during PIN change", () => {
            // Mock loading state
            store.dispatch({
                type: "security/changeTransactionPin/pending",
            });

            renderWithProvider();

            const submitButton = screen.getByTestId("submit-button");
            expect(submitButton).toBeDisabled();
            expect(submitButton.textContent).toBe("Loading...");
        });

        it("should show loading state during token-based PIN change", () => {
            // Mock loading state for token-based action
            store.dispatch({
                type: "security/changeTransactionPinWithToken/pending",
            });

            renderWithProvider({ token: "test-token" });

            const submitButton = screen.getByTestId("submit-button");
            expect(submitButton).toBeDisabled();
            expect(submitButton.textContent).toBe("Loading...");
        });
    });

    describe("Success Handling", () => {
        it("should close modal on successful PIN change", async () => {
            renderWithProvider();

            // Mock successful PIN change
            store.dispatch({
                type: "security/changeTransactionPin/fulfilled",
                payload: { message: "PIN changed successfully" },
            });

            await waitFor(() => {
                expect(mockOnRequestClose).toHaveBeenCalled();
            });
        });

        it("should close modal on successful token-based PIN change", async () => {
            renderWithProvider({ token: "test-token" });

            // Mock successful token-based PIN change
            store.dispatch({
                type: "security/changeTransactionPinWithToken/fulfilled",
                payload: { message: "PIN changed successfully" },
            });

            await waitFor(() => {
                expect(mockOnRequestClose).toHaveBeenCalled();
            });
        });
    });

    describe("Modal Interaction", () => {
        it("should call onRequestClose when close button is clicked", () => {
            renderWithProvider();

            const closeButton = screen.getByTestId("close-modal");
            fireEvent.click(closeButton);

            expect(mockOnRequestClose).toHaveBeenCalled();
        });
    });

    describe("Error Handling", () => {
        it("should handle PIN change errors gracefully", async () => {
            renderWithProvider();

            // Mock error state
            store.dispatch({
                type: "security/changeTransactionPin/rejected",
                payload: { message: "Current PIN is incorrect" },
            });

            // Form should remain open and show error feedback
            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("should handle token-based PIN change errors gracefully", async () => {
            renderWithProvider({ token: "test-token" });

            // Mock error state
            store.dispatch({
                type: "security/changeTransactionPinWithToken/rejected",
                payload: { message: "Invalid token" },
            });

            // Form should remain open and show error feedback
            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });
    });

    describe("Reset PIN Functionality", () => {
        it("should open reset pin modal when reset link is clicked", () => {
            renderWithProvider();

            const resetLink = screen.getByText("Reset it");
            fireEvent.click(resetLink);

            expect(screen.getByTestId("reset-pin-modal")).toBeInTheDocument();
            expect(screen.getByText("Reset transaction PIN")).toBeInTheDocument();
        });

        it("should close reset pin modal when close button is clicked", () => {
            renderWithProvider();

            // Open reset modal
            const resetLink = screen.getByText("Reset it");
            fireEvent.click(resetLink);

            expect(screen.getByTestId("reset-pin-modal")).toBeInTheDocument();

            // Close reset modal
            const closeResetButton = screen.getByTestId("close-reset-modal");
            fireEvent.click(closeResetButton);

            expect(screen.queryByTestId("reset-pin-modal")).not.toBeInTheDocument();
        });

        it("should pass token to reset pin modal", () => {
            renderWithProvider({ token: "test-mfa-token" });

            const resetLink = screen.getByText("Reset it");
            fireEvent.click(resetLink);

            expect(screen.getByTestId("reset-pin-modal")).toBeInTheDocument();
        });

        it("should not show reset pin modal initially", () => {
            renderWithProvider();

            expect(screen.queryByTestId("reset-pin-modal")).not.toBeInTheDocument();
        });
    });

    describe("Function Coverage Tests", () => {
        it("should test handleClose function directly", () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            // Add some data to the form
            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });
            expect(currentPinInput.value).toBe("123456");
            expect(newPinInput.value).toBe("654321");

            // Test handleClose via close button (X button)
            const closeButton = screen.getByTestId("close-modal");
            fireEvent.click(closeButton);

            // Verify onRequestClose was called and form was reset
            expect(mockOnRequestClose).toHaveBeenCalled();
            expect(currentPinInput.value).toBe("");
            expect(newPinInput.value).toBe("");
        });

        it("should test handleResetPinModalClose function", () => {
            renderWithProvider();

            // Open reset modal
            const resetLink = screen.getByText("Reset it");
            fireEvent.click(resetLink);
            expect(screen.getByTestId("reset-pin-modal")).toBeInTheDocument();

            // Test handleResetPinModalClose via close button
            const closeResetButton = screen.getByTestId("close-reset-modal");
            fireEvent.click(closeResetButton);

            // Verify reset modal is closed
            expect(screen.queryByTestId("reset-pin-modal")).not.toBeInTheDocument();
        });

        it("should test formik onSubmit function with different PIN values", () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Test with different valid PIN combinations
            fireEvent.change(currentPinInput, { target: { value: "000000" } });
            fireEvent.change(newPinInput, { target: { value: "111111" } });
            fireEvent.click(submitButton);

            // Verify form submission was attempted
            expect(submitButton).toBeInTheDocument();

            // Test with another combination
            fireEvent.change(currentPinInput, { target: { value: "999999" } });
            fireEvent.change(newPinInput, { target: { value: "888888" } });
            fireEvent.click(submitButton);

            // Verify form submission was attempted again
            expect(submitButton).toBeInTheDocument();
        });

        it("should test success useEffect with form reset", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");

            // Add data to form
            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "654321" } });

            // Verify form has data
            expect(currentPinInput.value).toBe("123456");
            expect(newPinInput.value).toBe("654321");

            // Trigger success state
            await act(async () => {
                store.dispatch({
                    type: "security/changeTransactionPin/fulfilled",
                    payload: { message: "PIN changed successfully" },
                });
            });

            // Verify success useEffect behavior
            await waitFor(() => {
                expect(mockOnRequestClose).toHaveBeenCalled();
                expect(currentPinInput.value).toBe("");
                expect(newPinInput.value).toBe("");
            });
        });

        it("should test error useEffect without onError callback", async () => {
            renderWithProvider(); // No onError callback

            const currentPinInput = screen.getByTestId("input-currentPIN");

            // Add data to form
            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            expect(currentPinInput.value).toBe("123456");

            // Trigger error state
            await act(async () => {
                store.dispatch({
                    type: "security/changeTransactionPin/rejected",
                    error: { message: "Some error occurred" },
                });
            });

            // Form should not be reset since no onError callback
            expect(currentPinInput.value).toBe("123456");
        });

        it("should test error useEffect with onError callback", async () => {
            const mockOnError = jest.fn();
            renderWithProvider({ onError: mockOnError });

            const currentPinInput = screen.getByTestId("input-currentPIN");

            // Add data to form
            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            expect(currentPinInput.value).toBe("123456");

            // Trigger error state
            await act(async () => {
                store.dispatch({
                    type: "security/changeTransactionPin/rejected",
                    error: { message: "Some error occurred" },
                });
            });

            // Verify error useEffect behavior
            await waitFor(() => {
                expect(mockOnError).toHaveBeenCalled();
                expect(currentPinInput.value).toBe("");
            });
        });

        it("should test component with different prop combinations", () => {
            // Test with all props
            const { rerender } = renderWithProvider({
                token: "test-token",
                onError: jest.fn(),
            });

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();

            // Test with minimal props
            rerender(
                <Provider store={store}>
                    <ChangePinModal isOpen={true} onRequestClose={mockOnRequestClose} />
                </Provider>
            );

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("should test useEffect cleanup and re-execution", async () => {
            const { rerender } = renderWithProvider();

            // Trigger success state
            await act(async () => {
                store.dispatch({
                    type: "security/changeTransactionPin/fulfilled",
                    payload: { message: "PIN changed successfully" },
                });
            });

            // Clear the success state
            await act(async () => {
                store.dispatch({
                    type: "security/clearState",
                    payload: "changePin",
                });
            });

            // Re-render with different props to test useEffect re-execution
            rerender(
                <Provider store={store}>
                    <ChangePinModal isOpen={true} onRequestClose={mockOnRequestClose} token="new-token" />
                </Provider>
            );

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });
    });

    describe("Input Validation Edge Cases", () => {
        it("should validate current PIN field separately", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Test with invalid current PIN
            fireEvent.change(currentPinInput, { target: { value: "12345" } });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText("PIN must be exactly 6 digits")).toBeInTheDocument();
            });
        });

        it("should validate new PIN field separately", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Set valid current PIN but invalid new PIN
            fireEvent.change(currentPinInput, { target: { value: "123456" } });
            fireEvent.change(newPinInput, { target: { value: "abc" } });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText("PIN must be exactly 6 digits")).toBeInTheDocument();
            });
        });

        it("should validate required fields", async () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Submit without filling any fields
            fireEvent.click(submitButton);

            // Wait for validation to trigger
            await waitFor(() => {
                // Check that the form is still present (validation prevents submission)
                expect(currentPinInput).toBeInTheDocument();
                expect(newPinInput).toBeInTheDocument();
                expect(submitButton).toBeInTheDocument();
            });
        });

        it("should handle form submission with edge case PIN values", () => {
            renderWithProvider();

            const currentPinInput = screen.getByTestId("input-currentPIN");
            const newPinInput = screen.getByTestId("input-newPIN");
            const submitButton = screen.getByTestId("submit-button");

            // Test with all zeros
            fireEvent.change(currentPinInput, { target: { value: "000000" } });
            fireEvent.change(newPinInput, { target: { value: "000001" } });
            fireEvent.click(submitButton);

            expect(submitButton).toBeInTheDocument();

            // Test with all nines
            fireEvent.change(currentPinInput, { target: { value: "999999" } });
            fireEvent.change(newPinInput, { target: { value: "999998" } });
            fireEvent.click(submitButton);

            expect(submitButton).toBeInTheDocument();
        });
    });
});
