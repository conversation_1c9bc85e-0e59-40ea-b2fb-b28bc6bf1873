import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import MFAConfirmationDialog from "@/components/page-components/dashboard/settings/ui/security/modals/MFAConfirmationDialog";

// Mock ReactModal
jest.mock("react-modal", () => {
    return function MockReactModal({ isOpen, children, onRequestClose }) {
        return isOpen ? (
            <div data-testid="modal-overlay" onClick={onRequestClose}>
                <div data-testid="modal-content" onClick={(e) => e.stopPropagation()}>
                    {children}
                </div>
            </div>
        ) : null;
    };
});

// Mock CloseX component
jest.mock("@/components/common/close-x", () => {
    return function MockCloseX({ onClick }) {
        return (
            <button data-testid="close-x" onClick={onClick}>
                ×
            </button>
        );
    };
});

// Mock Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: function MockButton({ children, onClick, variant, loading, disabled }) {
        return (
            <button
                data-testid={`button-${variant}`}
                onClick={onClick}
                disabled={disabled || loading}
                className={loading ? "loading" : ""}
            >
                {loading ? "Loading..." : children}
            </button>
        );
    },
}));

describe("MFAConfirmationDialog", () => {
    const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        onConfirm: jest.fn(),
        title: "Test Title",
        subtitle: "Test subtitle",
        confirmButtonText: "Confirm",
        cancelButtonText: "Cancel",
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should render the modal when isOpen is true", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        expect(screen.getByTestId("modal-content")).toBeInTheDocument();
        expect(screen.getByText("Test Title")).toBeInTheDocument();
        expect(screen.getByText("Test subtitle")).toBeInTheDocument();
        expect(screen.getByText("Confirm")).toBeInTheDocument();
        expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("should not render when isOpen is false", () => {
        render(<MFAConfirmationDialog {...defaultProps} isOpen={false} />);

        expect(screen.queryByTestId("modal-content")).not.toBeInTheDocument();
    });

    it("should call onClose when close button is clicked", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        const closeButton = screen.getByTestId("close-x");
        fireEvent.click(closeButton);

        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it("should call onClose when cancel button is clicked", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        const cancelButton = screen.getByText("Cancel");
        fireEvent.click(cancelButton);

        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it("should call onConfirm when confirm button is clicked", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        const confirmButton = screen.getByText("Confirm");
        fireEvent.click(confirmButton);

        expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
    });

    it("should show loading state on confirm button when loading is true", () => {
        render(<MFAConfirmationDialog {...defaultProps} loading={true} />);

        const confirmButton = screen.getByTestId("button-primary");
        expect(confirmButton).toHaveTextContent("Loading...");
        expect(confirmButton).toBeDisabled();
    });

    it("should disable buttons when loading is true", () => {
        render(<MFAConfirmationDialog {...defaultProps} loading={true} />);

        const confirmButton = screen.getByTestId("button-primary");
        const cancelButton = screen.getByTestId("button-outline");

        expect(confirmButton).toBeDisabled();
        expect(cancelButton).toBeDisabled();
    });

    it("should render with custom button texts", () => {
        const customProps = {
            ...defaultProps,
            confirmButtonText: "Yes, proceed",
            cancelButtonText: "No, go back",
        };

        render(<MFAConfirmationDialog {...customProps} />);

        expect(screen.getByText("Yes, proceed")).toBeInTheDocument();
        expect(screen.getByText("No, go back")).toBeInTheDocument();
    });

    it("should render with danger variant for confirm button", () => {
        render(<MFAConfirmationDialog {...defaultProps} confirmButtonVariant="danger" />);

        expect(screen.getByTestId("button-destructive")).toBeInTheDocument();
    });

    it("should render without subtitle when not provided", () => {
        const propsWithoutSubtitle = {
            ...defaultProps,
            subtitle: undefined,
        };

        render(<MFAConfirmationDialog {...propsWithoutSubtitle} />);

        expect(screen.getByText("Test Title")).toBeInTheDocument();
        expect(screen.queryByText("Test subtitle")).not.toBeInTheDocument();
    });

    it("should call onClose when overlay is clicked", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        const overlay = screen.getByTestId("modal-overlay");
        fireEvent.click(overlay);

        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it("should not call onClose when modal content is clicked", () => {
        render(<MFAConfirmationDialog {...defaultProps} />);

        const modalContent = screen.getByTestId("modal-content");
        fireEvent.click(modalContent);

        expect(defaultProps.onClose).not.toHaveBeenCalled();
    });
});
