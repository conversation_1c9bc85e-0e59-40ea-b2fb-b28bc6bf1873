import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import SettingsSMSSetupModal from "@/components/page-components/dashboard/settings/ui/security/modals/SettingsSMSSetupModal";
import { fetchPersonalInfo } from "@/redux/actions/settingsActions";
import { userAxios } from "@/api/axios";
import { sendFeedback, sendCatchFeedback } from "@/functions/feedback";

// Mock dependencies
jest.mock("@/redux/actions/settingsActions", () => ({
    fetchPersonalInfo: jest.fn(),
}));

jest.mock("@/api/axios", () => ({
    userAxios: {
        post: jest.fn(),
        patch: jest.fn(),
    },
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

jest.mock("@/functions/stringManipulations", () => ({
    maskNumber: jest.fn((number) => `***${number.slice(-2)}`),
}));

// Mock FullScreenDrawer
jest.mock("@/components/common/full-screen-drawer", () => {
    return function MockFullScreenDrawer({ isOpen, onClose, title, children, className }) {
        return isOpen ? (
            <div data-testid="fullscreen-drawer" className={className}>
                <h1>{title}</h1>
                <button onClick={onClose}>Close</button>
                {children}
            </div>
        ) : null;
    };
});

// Mock OTPContainer
jest.mock("@/components/page-components/auth/otp-container", () => {
    return function MockOTPContainer({
        value,
        setValue,
        onSubmit,
        onResend,
        loading,
        resendLoading,
        title,
        subtitle,
        error,
        buttonText,
        resendText,
    }) {
        return (
            <div data-testid="otp-container">
                <h2>{title}</h2>
                <div>{subtitle}</div>
                {error && <div data-testid="error-message">{error}</div>}
                <input data-testid="otp-input" value={value} onChange={(e) => setValue(e.target.value)} />
                <button data-testid="submit-button" onClick={onSubmit} disabled={loading}>
                    {loading ? "Loading..." : buttonText}
                </button>
                <button data-testid="resend-button" onClick={onResend} disabled={resendLoading}>
                    {resendLoading ? "Sending..." : resendText}
                </button>
            </div>
        );
    };
});

const mockStore = configureStore([thunk]);

describe("SettingsSMSSetupModal", () => {
    let store;

    const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        onSetupComplete: jest.fn(),
    };

    const defaultState = {
        user: {
            user: { email: "<EMAIL>" },
        },
        settings: {
            personalInfo: {
                data: { phoneNumber: "**********" },
                loading: false,
            },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
        store = mockStore(defaultState);
        fetchPersonalInfo.mockReturnValue({ type: "FETCH_PERSONAL_INFO" });
        userAxios.post.mockResolvedValue({ data: {} });
        userAxios.patch.mockResolvedValue({ data: {} });
    });

    it("should render the modal with correct title and high z-index", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        expect(screen.getByTestId("fullscreen-drawer")).toBeInTheDocument();
        expect(screen.getByText("Set up SMS authentication")).toBeInTheDocument();
        expect(screen.getByTestId("fullscreen-drawer")).toHaveClass("!z-[99999]");
    });

    it("should inject custom styles when modal is open", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        const styleElement = document.querySelector("style");
        expect(styleElement).toBeInTheDocument();
        expect(styleElement.textContent).toContain("z-index: 99999 !important");
    });

    it("should not inject styles when modal is closed", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} isOpen={false} />
            </Provider>
        );

        const styleElement = document.querySelector("style");
        expect(styleElement).not.toBeInTheDocument();
    });

    it("should render OTP container with correct props", async () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        expect(screen.getByTestId("otp-container")).toBeInTheDocument();
        expect(screen.getByText("Verify your phone number")).toBeInTheDocument();
        expect(screen.getByText("Complete setup")).toBeInTheDocument();

        // Wait for initial OTP send to complete, then check for resend button
        await waitFor(() => {
            expect(screen.getByText("Resend code")).toBeInTheDocument();
        });
    });

    it("should fetch personal info when modal opens and data is not available", () => {
        const stateWithoutPersonalInfo = {
            ...defaultState,
            settings: {
                personalInfo: {
                    data: null,
                    loading: false,
                },
            },
        };

        store = mockStore(stateWithoutPersonalInfo);

        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        expect(fetchPersonalInfo).toHaveBeenCalled();
    });

    it("should send OTP when component mounts with phone number", async () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager", {
                receiver: "0**********",
                receiverType: "SMS",
            });
        });

        expect(sendFeedback).toHaveBeenCalledWith("OTP sent to your phone number", "success");
    });

    it("should handle OTP input changes", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        const otpInput = screen.getByTestId("otp-input");
        fireEvent.change(otpInput, { target: { value: "123456" } });

        expect(otpInput.value).toBe("123456");
    });

    it("should verify OTP and update MFA method on submit", async () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        const otpInput = screen.getByTestId("otp-input");
        fireEvent.change(otpInput, { target: { value: "123456" } });

        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager/validate", {
                receiver: "0**********",
                otp: "123456",
            });
        });

        await waitFor(() => {
            expect(userAxios.patch).toHaveBeenCalledWith(
                "/v1/team-members?emailAddress=<EMAIL>&mfaMethod=SMS"
            );
        });

        expect(sendFeedback).toHaveBeenCalledWith("SMS 2FA setup completed successfully", "success");
        expect(defaultProps.onSetupComplete).toHaveBeenCalled();
    });

    it("should handle OTP verification error", async () => {
        // First call succeeds (initial OTP send), second call fails (verification)
        userAxios.post
            .mockResolvedValueOnce({ data: {} }) // Initial OTP send
            .mockRejectedValueOnce(new Error("Invalid OTP")); // OTP verification

        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        // Wait for initial OTP send to complete
        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager", {
                receiver: "0**********",
                receiverType: "SMS",
            });
        });

        const otpInput = screen.getByTestId("otp-input");
        fireEvent.change(otpInput, { target: { value: "123456" } });

        const submitButton = screen.getByTestId("submit-button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(screen.getByTestId("error-message")).toHaveTextContent("Invalid OTP. Please try again.");
        });

        expect(sendCatchFeedback).toHaveBeenCalled();
        expect(defaultProps.onSetupComplete).not.toHaveBeenCalled();
    });

    it("should resend OTP when resend button is clicked", async () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        // Wait for initial OTP send to complete
        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager", {
                receiver: "0**********",
                receiverType: "SMS",
            });
        });

        // Clear the initial OTP send call
        userAxios.post.mockClear();

        const resendButton = screen.getByTestId("resend-button");
        fireEvent.click(resendButton);

        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalledWith("/v1/otp-manager", {
                receiver: "0**********",
                receiverType: "SMS",
            });
        });
    });

    it("should handle resend OTP error", async () => {
        userAxios.post.mockRejectedValueOnce(new Error("Failed to send"));

        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        // Wait for initial send to complete
        await waitFor(() => {
            expect(userAxios.post).toHaveBeenCalled();
        });

        // Clear and setup for resend error
        userAxios.post.mockClear();
        userAxios.post.mockRejectedValueOnce(new Error("Failed to send"));

        const resendButton = screen.getByTestId("resend-button");
        fireEvent.click(resendButton);

        await waitFor(() => {
            expect(screen.getByTestId("error-message")).toHaveTextContent("Failed to send OTP. Please try again.");
        });

        expect(sendCatchFeedback).toHaveBeenCalled();
    });

    it("should call onClose when close button is clicked", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} />
            </Provider>
        );

        const closeButton = screen.getByText("Close");
        fireEvent.click(closeButton);

        expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it("should not render when isOpen is false", () => {
        render(
            <Provider store={store}>
                <SettingsSMSSetupModal {...defaultProps} isOpen={false} />
            </Provider>
        );

        expect(screen.queryByTestId("fullscreen-drawer")).not.toBeInTheDocument();
    });
});
