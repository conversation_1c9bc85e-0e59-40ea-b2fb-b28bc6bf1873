import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import SettingsMFASetupModal from "@/components/page-components/dashboard/settings/ui/security/modals/SettingsMFASetupModal";

// Mock the individual setup modals
jest.mock("@/components/page-components/dashboard/settings/ui/security/modals/SettingsAuthenticatorModal", () => {
    return function MockSettingsAuthenticatorModal({ isOpen, onClose, onSetupComplete }) {
        return isOpen ? (
            <div data-testid="authenticator-setup-modal">
                <button onClick={onClose}>Close Authenticator</button>
                <button onClick={onSetupComplete}>Complete Authenticator Setup</button>
            </div>
        ) : null;
    };
});

jest.mock("@/components/page-components/dashboard/settings/ui/security/modals/SettingsSMSSetupModal", () => {
    return function MockSettingsSMSSetupModal({ isOpen, onClose, onSetupComplete }) {
        return isOpen ? (
            <div data-testid="sms-setup-modal">
                <button onClick={onClose}>Close SMS</button>
                <button onClick={onSetupComplete}>Complete SMS Setup</button>
            </div>
        ) : null;
    };
});

jest.mock("@/components/page-components/dashboard/settings/ui/security/modals/SettingsSecurityQuestionsModal", () => {
    return function MockSettingsSecurityQuestionsModal({ isOpen, onClose, onSetupComplete }) {
        return isOpen ? (
            <div data-testid="security-questions-setup-modal">
                <button onClick={onClose}>Close Security Questions</button>
                <button onClick={onSetupComplete}>Complete Security Questions Setup</button>
            </div>
        ) : null;
    };
});

// Mock CustomModal
jest.mock("@/components/common/custom-modal", () => {
    return function MockCustomModal({ isOpen, onRequestClose, title, children }) {
        return isOpen ? (
            <div data-testid="custom-modal">
                <h2>{title}</h2>
                <button onClick={onRequestClose}>Close Modal</button>
                {children}
            </div>
        ) : null;
    };
});

// Mock icons
jest.mock("@/components/icons/settings", () => ({
    LockIcon1: () => <div data-testid="lock-icon">Lock</div>,
    SMSIcon: () => <div data-testid="sms-icon">SMS</div>,
    ShieldIcon: () => <div data-testid="shield-icon">Shield</div>,
}));

describe("SettingsMFASetupModal", () => {
    const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        onSetupComplete: jest.fn(),
        preSelectedMethod: null,
        isFirstTimeSetup: true,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should render method selection modal when no method is preselected", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        expect(screen.getByText("Set up two-factor authentication")).toBeInTheDocument();
        expect(
            screen.getByText("Choose a method to secure your account with two-factor authentication.")
        ).toBeInTheDocument();
    });

    it("should show different title and subtitle for switching methods", () => {
        render(<SettingsMFASetupModal {...defaultProps} isFirstTimeSetup={false} />);

        expect(screen.getByText("Switch MFA method")).toBeInTheDocument();
        expect(screen.getByText("Select a new MFA method. Your current method will be replaced.")).toBeInTheDocument();
    });

    it("should render all MFA method options", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        expect(screen.getByTestId("mfa-option-authenticator")).toBeInTheDocument();
        expect(screen.getByTestId("mfa-option-sms")).toBeInTheDocument();
        expect(screen.getByTestId("mfa-option-security_question")).toBeInTheDocument();

        expect(screen.getByText("Authenticator app")).toBeInTheDocument();
        expect(screen.getByText("SMS OTP")).toBeInTheDocument();
        expect(screen.getByText("Security question")).toBeInTheDocument();
    });

    it("should show recommended badge for authenticator method", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        const authenticatorOption = screen.getByTestId("mfa-option-authenticator");
        expect(authenticatorOption).toHaveTextContent("Recommended");
    });

    it("should render authenticator setup modal when AUTHENTICATOR is preselected", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="AUTHENTICATOR" />);

        expect(screen.getByTestId("authenticator-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should render SMS setup modal when SMS is preselected", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="SMS" />);

        expect(screen.getByTestId("sms-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should render security questions setup modal when SECURITY_QUESTION is preselected", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="SECURITY_QUESTION" />);

        expect(screen.getByTestId("security-questions-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should call onClose when modal close button is clicked", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        const closeButton = screen.getByText("Close Modal");
        fireEvent.click(closeButton);

        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it("should navigate to authenticator setup when authenticator option is clicked", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        const authenticatorOption = screen.getByTestId("mfa-option-authenticator");
        fireEvent.click(authenticatorOption);

        expect(screen.getByTestId("authenticator-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should navigate to SMS setup when SMS option is clicked", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        const smsOption = screen.getByTestId("mfa-option-sms");
        fireEvent.click(smsOption);

        expect(screen.getByTestId("sms-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should navigate to security questions setup when security question option is clicked", () => {
        render(<SettingsMFASetupModal {...defaultProps} />);

        const securityQuestionOption = screen.getByTestId("mfa-option-security_question");
        fireEvent.click(securityQuestionOption);

        expect(screen.getByTestId("security-questions-setup-modal")).toBeInTheDocument();
        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should call onSetupComplete when authenticator setup is completed", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="AUTHENTICATOR" />);

        const completeButton = screen.getByText("Complete Authenticator Setup");
        fireEvent.click(completeButton);

        expect(defaultProps.onSetupComplete).toHaveBeenCalledTimes(1);
    });

    it("should call onSetupComplete when SMS setup is completed", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="SMS" />);

        const completeButton = screen.getByText("Complete SMS Setup");
        fireEvent.click(completeButton);

        expect(defaultProps.onSetupComplete).toHaveBeenCalledTimes(1);
    });

    it("should call onSetupComplete when security questions setup is completed", () => {
        render(<SettingsMFASetupModal {...defaultProps} preSelectedMethod="SECURITY_QUESTION" />);

        const completeButton = screen.getByText("Complete Security Questions Setup");
        fireEvent.click(completeButton);

        expect(defaultProps.onSetupComplete).toHaveBeenCalledTimes(1);
    });

    it("should not render anything when isOpen is false", () => {
        render(<SettingsMFASetupModal {...defaultProps} isOpen={false} />);

        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
        expect(screen.queryByTestId("authenticator-setup-modal")).not.toBeInTheDocument();
        expect(screen.queryByTestId("sms-setup-modal")).not.toBeInTheDocument();
        expect(screen.queryByTestId("security-questions-setup-modal")).not.toBeInTheDocument();
    });
});
