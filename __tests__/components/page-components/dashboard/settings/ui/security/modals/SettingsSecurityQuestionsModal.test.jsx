import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import SettingsSecurityQuestionsModal from "@/components/page-components/dashboard/settings/ui/security/modals/SettingsSecurityQuestionsModal";
import { userAxios } from "@/api/axios";
import { sendFeedback, sendCatchFeedback } from "@/functions/feedback";

// Mock dependencies
jest.mock("@/api/axios", () => ({
    userAxios: {
        get: jest.fn(),
        post: jest.fn(),
        patch: jest.fn(),
    },
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

// Mock FullScreenDrawer
jest.mock("@/components/common/full-screen-drawer", () => {
    return function MockFullScreenDrawer({ isOpen, onClose, title, children }) {
        return isOpen ? (
            <div data-testid="fullscreen-drawer">
                <h1>{title}</h1>
                <button onClick={onClose}>Close</button>
                {children}
            </div>
        ) : null;
    };
});

// Mock CustomModal
jest.mock("@/components/common/custom-modal", () => {
    return function MockCustomModal({ isOpen, onRequestClose, title, children }) {
        return isOpen ? (
            <div data-testid="custom-modal">
                <h2>{title}</h2>
                <button onClick={onRequestClose}>Close Modal</button>
                {children}
            </div>
        ) : null;
    };
});

// Mock other components
jest.mock("@/components/common/dropdown", () => {
    return function MockDropdown({ options, value, onChange, placeholder }) {
        return (
            <select value={value} onChange={(e) => onChange(e.target.value)}>
                <option value="">{placeholder}</option>
                {options?.map((option) => (
                    <option key={option.id} value={option.id}>
                        {option.question}
                    </option>
                ))}
            </select>
        );
    };
});

jest.mock("@/components/common/label-input", () => {
    return function MockLabelInput({ label, value, onChange, ...props }) {
        return (
            <div>
                <label>{label}</label>
                <input value={value} onChange={onChange} {...props} />
            </div>
        );
    };
});

const mockStore = configureStore([thunk]);

describe("SettingsSecurityQuestionsModal", () => {
    let store;

    const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        onBack: jest.fn(),
        onSetupComplete: jest.fn(),
    };

    const defaultState = {
        user: {
            user: { email: "<EMAIL>" },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
        store = mockStore(defaultState);
        userAxios.get.mockResolvedValue({
            data: [
                { id: 1, question: "What is your pet's name?" },
                { id: 2, question: "What is your mother's maiden name?" },
                { id: 3, question: "What city were you born in?" },
            ],
        });
        userAxios.post.mockResolvedValue({ data: {} });
        userAxios.patch.mockResolvedValue({ data: {} });
    });

    it("should render the modal with correct title", () => {
        render(
            <Provider store={store}>
                <SettingsSecurityQuestionsModal {...defaultProps} />
            </Provider>
        );

        expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        expect(screen.getByText("Set up security questions")).toBeInTheDocument();
    });

    it("should fetch security questions on mount", async () => {
        render(
            <Provider store={store}>
                <SettingsSecurityQuestionsModal {...defaultProps} />
            </Provider>
        );

        await waitFor(() => {
            expect(userAxios.get).toHaveBeenCalledWith("/v1/security-questions");
        });
    });

    it("should call onClose when close button is clicked", () => {
        render(
            <Provider store={store}>
                <SettingsSecurityQuestionsModal {...defaultProps} />
            </Provider>
        );

        const closeButton = screen.getByText("Close Modal");
        fireEvent.click(closeButton);

        expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it("should not render when isOpen is false", () => {
        render(
            <Provider store={store}>
                <SettingsSecurityQuestionsModal {...defaultProps} isOpen={false} />
            </Provider>
        );

        expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
    });

    it("should handle fetch security questions error", async () => {
        userAxios.get.mockRejectedValueOnce(new Error("Failed to fetch questions"));

        render(
            <Provider store={store}>
                <SettingsSecurityQuestionsModal {...defaultProps} />
            </Provider>
        );

        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalled();
        });
    });
});
