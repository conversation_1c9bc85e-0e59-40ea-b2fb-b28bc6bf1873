"use client";

import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import * as Yup from "yup";
// import jest from "jest" // Import jest to declare the variable

// Mock Redux hooks
const mockDispatch = jest.fn();
const mockUseAppSelector = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: (selector) => mockUseAppSelector(selector),
}));

// Mock Redux actions
jest.mock("@/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(() => ({ type: "GET_TEAM_MEMBER_DETAILS" })),
}));

jest.mock("@/redux/slices/settingsMfaSlice", () => ({
    resetAllStates: jest.fn(() => ({ type: "RESET_ALL_STATES" })),
}));

jest.mock("@/redux/slices/transferMfaSlice", () => ({
    clearGetTeamMemberState: jest.fn(() => ({ type: "CLEAR_GET_TEAM_MEMBER_STATE" })),
}));

// Mock sendFeedback function
jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

// Mock the MFA Verification component
jest.mock("@/components/page-components/dashboard/settings/components/settings-mfa-verification", () => ({
    __esModule: true,
    default: ({ onClose, onVerified, isOpen, userMfaType, email, phoneNumber }) => (
        <div data-testid="mfa-verification-modal" style={{ display: isOpen ? "block" : "none" }}>
            <h3>MFA Verification</h3>
            <p>User MFA Type: {userMfaType}</p>
            <p>Email: {email}</p>
            <p>Phone: {phoneNumber}</p>
            <button onClick={() => onVerified("123456")} data-testid="mfa-verify-button">
                Verify
            </button>
            <button onClick={onClose} data-testid="mfa-close-button">
                Close
            </button>
        </div>
    ),
}));

// Mock the dependencies
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, type, variant, className, "aria-label": ariaLabel }) => (
        <button
            onClick={onClick}
            type={type}
            className={variant ? `btn-${variant} ${className || ""}` : className}
            aria-label={ariaLabel}
            data-testid={`button-${ariaLabel}`}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/settings", () => ({
    PencilIcon2: ({ className }) => <svg data-testid="pencil-icon" className={className} />,
}));

// Mock the LabelInput component to capture and execute the onInput handler
jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({ formik, name, label, required, hint, locked, type, placeholder, onInput }) => {
        // Store the onInput handler to use in tests
        if (onInput) {
            window.mockOnInput = onInput;
        }
        return (
            <div className="label-input-container" data-testid={`input-${name}`}>
                <label htmlFor={name}>
                    {label}
                    {required && "*"}
                </label>
                <input
                    id={name}
                    name={name}
                    type={type}
                    value={formik.values[name]}
                    onChange={(e) => {
                        // Call the onInput handler if it exists
                        if (onInput) {
                            onInput(e);
                        }
                        formik.handleChange(e);
                    }}
                    onBlur={(e) => formik.handleBlur(e)}
                    disabled={locked}
                    placeholder={placeholder}
                    className={formik.touched[name] && formik.errors[name] ? "error" : ""}
                    data-testid={`input-field-${name}`}
                />
                {hint && <p className="hint-text">{hint}</p>}
                {formik.touched[name] && formik.errors[name] && (
                    <div className="error-message" data-testid={`error-${name}`}>
                        {formik.errors[name]}
                    </div>
                )}
            </div>
        );
    },
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ options, name, label, size, formik, className }) => (
        <div className={`dropdown-container ${className || ""} ${size || ""}`} data-testid={`dropdown-${name}`}>
            <label htmlFor={name}>{label}</label>
            <select
                id={name}
                name={name}
                value={formik.values[name]}
                onChange={(e) => formik.handleChange(e)}
                onBlur={(e) => formik.handleBlur(e)}
                className={size}
                data-testid={`select-${name}`}
            >
                {options.map((option) => (
                    <option key={option.value} value={option.value} data-testid={`option-${option.value}`}>
                        {option.label}
                    </option>
                ))}
            </select>
            {formik.touched[name] && formik.errors[name] && (
                <div className="error-message" data-testid={`error-${name}`}>
                    {formik.errors[name]}
                </div>
            )}
        </div>
    ),
}));

jest.mock("@/components/common/date-picker", () => ({
    __esModule: true,
    default: ({ label, onChange, value, error, hint, disable }) => (
        <div data-testid="date-picker" aria-label={label || "Date picker"}>
            <label htmlFor="date-input">{label}</label>
            <input
                id="date-input"
                type="date"
                data-testid="date-picker-input"
                value={value ? value.toISOString().split("T")[0] : ""}
                onChange={(e) => {
                    const newDate = new Date(e.target.value);
                    onChange(newDate);
                }}
                disabled={disable}
                aria-invalid={!!error}
            />
            {hint && <div data-testid="date-picker-hint">{hint}</div>}
            {error && <div data-testid="date-picker-error">{error}</div>}
        </div>
    ),
}));

// Import the component after mocking its dependencies
import { EditableField } from "@/components/page-components/dashboard/settings/ui/editable-field";
import { sendFeedback } from "@/functions/feedback";

describe("EditableField", () => {
    const mockOnSave = jest.fn();
    const mockOnSaveWithToken = jest.fn();
    const initialValue = "Initial Value";
    const label = "Test Label";
    const name = "testName";
    const options = [
        { label: "Option 1", value: "option1" },
        { label: "Option 2", value: "option2" },
    ];

    // Mock team member data
    const mockTeamMember = {
        mfaStatus: true,
        preferredMfaMethod: "email",
        email: "<EMAIL>",
        phoneNumber: "1234567890",
    };

    beforeEach(() => {
        jest.clearAllMocks();
        window.mockOnInput = null;
        mockDispatch.mockClear();
        sendFeedback.mockClear();

        // Default Redux state mocks
        mockUseAppSelector.mockImplementation((selector) => {
            const state = {
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: false,
                        loading: false,
                    },
                    teamMember: mockTeamMember,
                },
            };
            return selector(state);
        });
    });

    describe("Basic functionality", () => {
        it("displays the field label and value in view mode", () => {
            render(<EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} />);

            expect(screen.getByText(label)).toBeInTheDocument();
            expect(screen.getByText(initialValue)).toBeInTheDocument();
            expect(screen.getByTestId("pencil-icon")).toBeInTheDocument();
        });

        it("displays a 'no value available' message when value is empty", () => {
            render(<EditableField label={label} name={name} value="" onSave={mockOnSave} />);

            expect(screen.getByText(`No ${label.toLowerCase()} available`)).toBeInTheDocument();
        });

        it("hides the edit button when disabled is true", () => {
            render(
                <EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} disabled={true} />
            );

            expect(screen.queryByTestId("pencil-icon")).not.toBeInTheDocument();
        });

        it("enters edit mode when edit button is clicked", () => {
            render(<EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} />);

            fireEvent.click(screen.getByTestId("pencil-icon"));
            expect(screen.getByTestId(`input-${name}`)).toBeInTheDocument();
        });
    });

    describe("MFA functionality", () => {
        it("shows MFA modal when onSaveWithToken is provided and MFA is enabled", async () => {
            // Mock successful team member fetch
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change input value
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Wait for MFA modal to appear
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            expect(mockDispatch).toHaveBeenCalled();
        });

        it("calls onSaveWithToken when MFA verification is successful", async () => {
            // Mock successful team member fetch
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode and submit
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            // Wait for MFA modal and verify
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            fireEvent.click(screen.getByTestId("mfa-verify-button"));

            await waitFor(() => {
                expect(mockOnSaveWithToken).toHaveBeenCalledWith("New Value", "123456");
            });
        });

        it("shows error feedback when MFA is not enabled", async () => {
            // Mock team member without MFA
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: { ...mockTeamMember, mfaStatus: false },
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode and submit
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            });
        });

        it("closes MFA modal when close button is clicked", async () => {
            // Mock successful team member fetch
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode and submit
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            // Wait for MFA modal
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Close modal
            fireEvent.click(screen.getByTestId("mfa-close-button"));

            // Should return to view mode
            await waitFor(() => {
                expect(screen.getByText(initialValue)).toBeInTheDocument();
                expect(screen.queryByTestId(`input-${name}`)).not.toBeInTheDocument();
            });
        });
    });

    describe("Date picker with MFA", () => {
        it("shows MFA modal when date picker save is clicked", async () => {
            // Mock successful team member fetch
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value="2022-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change date value
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: "2023-05-15" } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Wait for MFA modal to appear
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });
        });

        it("calls onSaveWithToken with date value after MFA verification", async () => {
            // Mock successful team member fetch
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value="2022-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode and change date
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: "2023-05-15" } });
            fireEvent.click(screen.getByText("Save"));

            // Wait for MFA modal and verify
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            fireEvent.click(screen.getByTestId("mfa-verify-button"));

            await waitFor(() => {
                expect(mockOnSaveWithToken).toHaveBeenCalledWith("2023-05-15", "123456");
            });
        });
    });

    describe("Dropdown functionality", () => {
        it("shows a dropdown in edit mode when isDropdown is true", () => {
            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    isDropdown={true}
                    options={options}
                />
            );

            fireEvent.click(screen.getByTestId("pencil-icon"));
            expect(screen.getByTestId(`dropdown-${name}`)).toBeInTheDocument();
        });
    });

    describe("Form validation", () => {
        it("shows validation error when required field is empty", async () => {
            render(
                <EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} required={true} />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Clear the input
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "" } });
            fireEvent.blur(input); // Trigger validation

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                expect(screen.getByText("This field is required")).toBeInTheDocument();
            });
        });

        it("applies custom validation when validation schema is provided", async () => {
            const customValidationSchema = Yup.object({
                [name]: Yup.string().min(5, "Must be at least 5 characters"),
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    validationSchema={customValidationSchema}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter invalid value (too short)
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "123" } });
            fireEvent.blur(input); // Trigger validation

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                expect(screen.getByText("Must be at least 5 characters")).toBeInTheDocument();
            });
        });
    });

    describe("Phone number validation and input handling", () => {
        const phoneLabel = "Phone Number";
        const phoneName = "phoneNumber";
        const initialPhone = "12345678901"; // 11 digits

        it("automatically removes non-digit characters during input", () => {
            render(
                <EditableField
                    label={phoneLabel}
                    name={phoneName}
                    value={initialPhone}
                    onSave={mockOnSave}
                    type="tel"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Create a mock event object
            const mockEvent = {
                target: { value: "************" },
            };

            // Call the onInput handler directly
            expect(window.mockOnInput).toBeTruthy();
            window.mockOnInput(mockEvent);

            // Check that non-digit characters were removed
            expect(mockEvent.target.value).toBe("1234567890");
        });

        it("limits input to 11 digits during typing", () => {
            render(
                <EditableField
                    label={phoneLabel}
                    name={phoneName}
                    value={initialPhone}
                    onSave={mockOnSave}
                    type="tel"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Create a mock event object
            const mockEvent = {
                target: { value: "123456789012345" }, // 15 digits
            };

            // Call the onInput handler directly
            expect(window.mockOnInput).toBeTruthy();
            window.mockOnInput(mockEvent);

            // Check that input was limited to 11 digits
            expect(mockEvent.target.value).toBe("12345678901");
        });
    });

    describe("Enhanced email validation", () => {
        const emailLabel = "Email Address";
        const emailName = "emailAddress";
        const validEmail = "<EMAIL>";

        it("validates email format using regex pattern", async () => {
            render(
                <EditableField
                    label={emailLabel}
                    name={emailName}
                    value={validEmail}
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter invalid email without domain
            const input = screen.getByTestId(`input-field-${emailName}`);
            fireEvent.change(input, { target: { value: "test@" } });
            fireEvent.blur(input); // Trigger validation

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates that email doesn't have consecutive dots", async () => {
            render(
                <EditableField
                    label={emailLabel}
                    name={emailName}
                    value={validEmail}
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter invalid email with consecutive dots
            const input = screen.getByTestId(`input-field-${emailName}`);
            fireEvent.change(input, { target: { value: "<EMAIL>" } });
            fireEvent.blur(input); // Trigger validation

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });
    });

    describe("Cancel functionality", () => {
        it("returns to view mode when cancel button is clicked", () => {
            render(<EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} />);

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Click cancel
            const cancelButton = screen.getByText("Cancel");
            fireEvent.click(cancelButton);

            // Should be back in view mode
            expect(screen.getByText(initialValue)).toBeInTheDocument();
            expect(screen.queryByTestId(`input-${name}`)).not.toBeInTheDocument();
        });

        it("resets date value when cancel is clicked in date picker mode", () => {
            const initialDate = "2022-01-01";
            render(
                <EditableField label={label} name={name} value={initialDate} onSave={mockOnSave} isDatePicker={true} />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change date value
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: "2023-05-15" } });

            // Click cancel button
            const cancelButton = screen.getByText("Cancel");
            fireEvent.click(cancelButton);

            // Should be back in view mode with original value
            expect(screen.getByText(initialDate)).toBeInTheDocument();
            expect(screen.queryByTestId("date-picker")).not.toBeInTheDocument();
        });
    });

    describe("Date picker functionality", () => {
        it("shows date picker in edit mode when isDatePicker is true", () => {
            render(
                <EditableField label={label} name={name} value="2022-01-01" onSave={mockOnSave} isDatePicker={true} />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Check that DatePicker is rendered
            expect(screen.getByTestId("date-picker")).toBeInTheDocument();
        });

        it("handles date picker hint text correctly", () => {
            const hintText = "Select your date of birth";
            render(
                <EditableField
                    label={label}
                    name={name}
                    value="2022-01-01"
                    onSave={mockOnSave}
                    isDatePicker={true}
                    hint={hintText}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Check that hint is displayed
            expect(screen.getByTestId("date-picker-hint")).toHaveTextContent(hintText);
        });
    });

    describe("Age validation for date of birth", () => {
        const getDateYearsAgo = (years) => {
            const date = new Date();
            date.setFullYear(date.getFullYear() - years);
            return date;
        };

        beforeEach(() => {
            // Mock successful team member fetch for these tests
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });
        });

        it("shows age validation error when date of birth is less than 18 years old", async () => {
            const under18Date = getDateYearsAgo(17); // 17 years ago
            const dateString = under18Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Date of birth"
                    name="date-of-birth"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that makes the user under 18
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show feedback error instead of MFA modal
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "You must be above 18 years old. Please enter a valid date of birth.",
                    "error",
                    undefined,
                    "Invalid Age"
                );
            });

            // Should NOT show MFA modal
            expect(screen.queryByTestId("mfa-verification-modal")).not.toBeInTheDocument();

            // Should NOT call onSaveWithToken
            expect(mockOnSaveWithToken).not.toHaveBeenCalled();
        });

        it("shows age validation error when date of birth is exactly 18 years old", async () => {
            const exactly18Date = getDateYearsAgo(18); // Exactly 18 years ago
            const dateString = exactly18Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Date of birth"
                    name="date-of-birth"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that makes the user exactly 18
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show feedback error (exactly 18 is not above 18)
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "You must be above 18 years old. Please enter a valid date of birth.",
                    "error",
                    undefined,
                    "Invalid Age"
                );
            });

            // Should NOT show MFA modal
            expect(screen.queryByTestId("mfa-verification-modal")).not.toBeInTheDocument();
        });

        it("allows saving when date of birth is more than 18 years old", async () => {
            const over18Date = getDateYearsAgo(25); // 25 years ago
            const dateString = over18Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Date of birth"
                    name="date-of-birth"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that makes the user over 18
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show MFA modal (not age validation error)
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Should NOT show age validation error
            expect(sendFeedback).not.toHaveBeenCalledWith(
                "You must be above 18 years old. Please enter a valid date of birth.",
                "error",
                undefined,
                "Invalid Age"
            );
        });

        it("shows age validation error when date of birth is in the future", async () => {
            const futureDate = new Date();
            futureDate.setFullYear(futureDate.getFullYear() + 1); // Next year
            const dateString = futureDate.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Date of birth"
                    name="date-of-birth"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a future date
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show feedback error
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "You must be above 18 years old. Please enter a valid date of birth.",
                    "error",
                    undefined,
                    "Invalid Age"
                );
            });

            // Should NOT show MFA modal
            expect(screen.queryByTestId("mfa-verification-modal")).not.toBeInTheDocument();
        });

        it("only validates age for date-of-birth field name", async () => {
            const under18Date = getDateYearsAgo(17); // 17 years ago
            const dateString = under18Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Some other date"
                    name="some-other-date"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that would be under 18 (but it's not a date-of-birth field)
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show MFA modal (no age validation for non-date-of-birth fields)
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Should NOT show age validation error
            expect(sendFeedback).not.toHaveBeenCalledWith(
                "You must be above 18 years old. Please enter a valid date of birth.",
                "error",
                undefined,
                "Invalid Age"
            );
        });

        it("allows saving when date of birth is exactly 19 years old (above 18)", async () => {
            const exactly19Date = getDateYearsAgo(19); // Exactly 19 years ago
            const dateString = exactly19Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Date of birth"
                    name="date-of-birth"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that makes the user exactly 19 (above 18)
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show MFA modal (not age validation error)
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Should NOT show age validation error
            expect(sendFeedback).not.toHaveBeenCalledWith(
                "You must be above 18 years old. Please enter a valid date of birth.",
                "error",
                undefined,
                "Invalid Age"
            );
        });

        it("validates age with different field name (non date-of-birth)", async () => {
            // Test that age validation only applies to "date-of-birth" field name
            const under18Date = getDateYearsAgo(17); // 17 years ago
            const dateString = under18Date.toISOString().split("T")[0];

            render(
                <EditableField
                    label="Event Date"
                    name="event-date"
                    value="2000-01-01"
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Select a date that would be under 18 (but it's not a date-of-birth field)
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: dateString } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show MFA modal (no age validation for non-date-of-birth fields)
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Should NOT show age validation error
            expect(sendFeedback).not.toHaveBeenCalledWith(
                "You must be above 18 years old. Please enter a valid date of birth.",
                "error",
                undefined,
                "Invalid Age"
            );
        });
    });

    describe("Edge cases and additional branch coverage", () => {
        beforeEach(() => {
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: mockTeamMember,
                    },
                };
                return selector(state);
            });
        });

        it("handles submit without onSaveWithToken prop", async () => {
            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    // No onSaveWithToken prop
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change input value
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show 2FA required error
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            });
        });

        it("handles date save without onSaveWithToken prop", async () => {
            render(
                <EditableField
                    label="Test Date"
                    name="test-date"
                    value="2022-01-01"
                    onSave={mockOnSave}
                    isDatePicker={true}
                    // No onSaveWithToken prop
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change date
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: "2023-05-15" } });

            // Click save button
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            // Should show 2FA required error
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            });
        });

        it("handles phone input with non-digit characters", () => {
            render(
                <EditableField
                    label="Phone Number"
                    name="phoneNumber"
                    value="1234567890"
                    onSave={mockOnSave}
                    type="tel"
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Simulate typing non-digit characters in the phone input
            const input = screen.getByTestId("input-field-phoneNumber");
            fireEvent.change(input, { target: { value: "************123" } });

            // Check that the input value is processed correctly by the phone handler
            // Note: The handler is called during the onChange event and should limit to 11 digits
            expect(input.value).toBe("12345678901");
        });

        it("handles phone input exceeding 11 digits", () => {
            render(
                <EditableField
                    label="Phone Number"
                    name="phoneNumber"
                    value="1234567890"
                    onSave={mockOnSave}
                    type="tel"
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Create a mock event object with more than 11 digits
            const mockEvent = {
                target: { value: "123456789012345" }, // 15 digits
            };

            // Call the onInput handler directly
            expect(window.mockOnInput).toBeTruthy();
            window.mockOnInput(mockEvent);

            // Check that input was limited to 11 digits
            expect(mockEvent.target.value).toBe("12345678901");
        });

        it("renders with custom emptyValueText when value is empty", () => {
            const customEmptyText = "No information provided";
            render(
                <EditableField
                    label={label}
                    name={name}
                    value=""
                    onSave={mockOnSave}
                    emptyValueText={customEmptyText}
                />
            );

            expect(screen.getByText(customEmptyText)).toBeInTheDocument();
        });

        it("renders with custom className", () => {
            const customClass = "custom-class";
            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    isDropdown={true}
                    options={options}
                    className={customClass}
                />
            );

            // Enter edit mode to see the dropdown with custom className
            fireEvent.click(screen.getByTestId("pencil-icon"));
            expect(screen.getByTestId(`dropdown-${name}`)).toHaveClass(customClass);
        });

        it("handles locked input field", () => {
            render(<EditableField label={label} name={name} value={initialValue} onSave={mockOnSave} locked={true} />);

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Input should be disabled when locked
            const input = screen.getByTestId(`input-field-${name}`);
            expect(input).toBeDisabled();
        });

        it("handles size prop for dropdown", () => {
            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    isDropdown={true}
                    options={options}
                    size="md"
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));
            expect(screen.getByTestId(`dropdown-${name}`)).toHaveClass("md");
        });

        it("handles pending value being null in handleMfaVerified", async () => {
            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Submit to open MFA modal
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            // Wait for MFA modal
            await waitFor(() => {
                expect(screen.getByTestId("mfa-verification-modal")).toBeInTheDocument();
            });

            // Manually call handleMfaVerified with no pending value
            // This simulates the edge case where pendingValue is somehow null
            fireEvent.click(screen.getByTestId("mfa-verify-button"));

            await waitFor(() => {
                expect(mockOnSaveWithToken).toHaveBeenCalled();
            });
        });

        it("handles date change with valid date", () => {
            render(
                <EditableField
                    label="Test Date"
                    name="test-date"
                    value="2022-01-01"
                    onSave={mockOnSave}
                    isDatePicker={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Change to a valid date
            const dateInput = screen.getByTestId("date-picker-input");
            fireEvent.change(dateInput, { target: { value: "2023-06-15" } });

            // The date input should have the new value
            expect(dateInput.value).toBe("2023-06-15");
        });

        it("handles team member without MFA enabled during check", async () => {
            // Mock team member without MFA
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: { ...mockTeamMember, mfaStatus: false },
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode and submit
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            // Should show MFA not enabled error
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            });
        });

        it("handles team member being null during MFA check", async () => {
            // Mock null team member
            mockUseAppSelector.mockImplementation((selector) => {
                const state = {
                    transferMfaSlice: {
                        getTeamMemberDetails: {
                            success: true,
                            loading: false,
                        },
                        teamMember: null,
                    },
                };
                return selector(state);
            });

            render(
                <EditableField
                    label={label}
                    name={name}
                    value={initialValue}
                    onSave={mockOnSave}
                    onSaveWithToken={mockOnSaveWithToken}
                />
            );

            // Enter edit mode and submit
            fireEvent.click(screen.getByTestId("pencil-icon"));
            const input = screen.getByTestId(`input-field-${name}`);
            fireEvent.change(input, { target: { value: "New Value" } });
            fireEvent.click(screen.getByText("Save"));

            // Should show MFA required error
            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith(
                    "Two-factor authentication (2FA) is required to edit this field. Please set up 2FA in your security settings first.",
                    "error",
                    undefined,
                    "2FA Required"
                );
            });
        });

        it("updates local date value when prop value changes", () => {
            const { rerender } = render(
                <EditableField
                    label="Test Date"
                    name="test-date"
                    value="2022-01-01"
                    onSave={mockOnSave}
                    isDatePicker={true}
                />
            );

            // Check initial value
            expect(screen.getByText("2022-01-01")).toBeInTheDocument();

            // Update prop value
            rerender(
                <EditableField
                    label="Test Date"
                    name="test-date"
                    value="2023-06-15"
                    onSave={mockOnSave}
                    isDatePicker={true}
                />
            );

            // Should show updated value
            expect(screen.getByText("2023-06-15")).toBeInTheDocument();
        });

        it("handles non-tel and non-email input types with basic validation", async () => {
            render(
                <EditableField
                    label="Text Field"
                    name="textField"
                    value="initial"
                    onSave={mockOnSave}
                    type="text"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Clear the input to trigger required validation
            const input = screen.getByTestId("input-field-textField");
            fireEvent.change(input, { target: { value: "" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                expect(screen.getByText("This field is required")).toBeInTheDocument();
            });
        });

        it("validates email with domain that is too short", async () => {
            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email with short domain
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "test@ab" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email with local part that is too long", async () => {
            const longLocalPart = "a".repeat(65); // 65 characters (max is 64)

            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email with long local part
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: `${longLocalPart}@example.com` } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email that is too long overall", async () => {
            const veryLongEmail = "a".repeat(250) + "@example.com"; // Over 254 characters

            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter very long email
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: veryLongEmail } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email without domain dot", async () => {
            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email without domain dot
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "test@example" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email with short TLD", async () => {
            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email with short TLD
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "test@example.c" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email with multiple @ symbols", async () => {
            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email with multiple @ symbols
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "test@@example.com" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("validates email with no local part", async () => {
            render(
                <EditableField
                    label="Email"
                    name="email"
                    value="<EMAIL>"
                    onSave={mockOnSave}
                    type="email"
                    required={true}
                />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Enter email with no local part
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "@example.com" } });
            fireEvent.blur(input);

            // Submit form
            const saveButton = screen.getByText("Save");
            fireEvent.click(saveButton);

            await waitFor(() => {
                const errorElements = screen.getAllByText(/Please enter a valid email address/i);
                expect(errorElements.length).toBeGreaterThan(0);
            });
        });

        it("skips email validation when value is empty and not required", async () => {
            render(
                <EditableField label="Email" name="email" value="" onSave={mockOnSave} type="email" required={false} />
            );

            // Enter edit mode
            fireEvent.click(screen.getByTestId("pencil-icon"));

            // Leave email empty
            const input = screen.getByTestId("input-field-email");
            fireEvent.change(input, { target: { value: "" } });
            fireEvent.blur(input);

            // Should not show email validation error for empty value when not required
            expect(screen.queryByText(/Please enter a valid email address/i)).not.toBeInTheDocument();
        });
    });
});
