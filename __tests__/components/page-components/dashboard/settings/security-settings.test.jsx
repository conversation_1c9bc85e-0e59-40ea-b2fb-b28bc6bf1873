/**
 * Comprehensive test suite for SecuritySettings component
 * Designed to achieve 100% coverage across all functions, branches, and lines
 */

import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { SecuritySettings } from "../../../../../src/components/page-components/dashboard/settings/security-settings";

// Mock all child components
jest.mock(
    "../../../../../src/components/page-components/dashboard/settings/ui/security/LoginTransactionsSection",
    () => {
        return function MockLoginTransactionsSection({ onChangePassword, onChangePin }) {
            return (
                <div data-testid="login-transactions-section">
                    <button data-testid="change-pin-button" onClick={onChangePin}>
                        Change PIN
                    </button>
                    <button data-testid="change-password-button" onClick={onChangePassword}>
                        Change Password
                    </button>
                </div>
            );
        };
    }
);

jest.mock("../../../../../src/components/page-components/dashboard/settings/ui/security/ChangePasswordModal", () => {
    return function MockChangePasswordModal({ isOpen, onRequestClose, token, onError }) {
        if (!isOpen) return null;
        return (
            <div data-testid="change-password-modal">
                <span data-testid="password-modal-token">{token || "no-token"}</span>
                <button data-testid="close-password-modal" onClick={onRequestClose}>
                    Close
                </button>
                {onError && (
                    <button data-testid="trigger-password-error" onClick={onError}>
                        Error
                    </button>
                )}
            </div>
        );
    };
});

jest.mock("../../../../../src/components/page-components/dashboard/settings/ui/security/ChangePinModal", () => {
    return function MockChangePinModal({ isOpen, onRequestClose, token, onError }) {
        if (!isOpen) return null;
        return (
            <div data-testid="change-pin-modal">
                <span data-testid="pin-modal-token">{token || "no-token"}</span>
                <button data-testid="close-pin-modal" onClick={onRequestClose}>
                    Close
                </button>
                {onError && (
                    <button data-testid="trigger-pin-error" onClick={onError}>
                        Error
                    </button>
                )}
            </div>
        );
    };
});

jest.mock("../../../../../src/components/page-components/dashboard/settings/ui/security/ManageDevicesModal", () => {
    return function MockManageDevicesModal({ isOpen, onRequestClose }) {
        if (!isOpen) return null;
        return (
            <div data-testid="manage-devices-modal">
                <button data-testid="close-manage-devices-modal" onClick={onRequestClose}>
                    Close
                </button>
            </div>
        );
    };
});

jest.mock("../../../../../src/components/page-components/dashboard/settings/ui/security/TwoFaSection", () => {
    return function MockTwoFaSection() {
        return <div data-testid="two-fa-section">Two FA Section</div>;
    };
});

jest.mock(
    "../../../../../src/components/page-components/dashboard/settings/components/settings-mfa-verification",
    () => {
        return function MockSettingsMfaVerification({ userMfaType, onClose, isOpen, onVerified, email, phoneNumber }) {
            if (!isOpen) return null;
            return (
                <div data-testid="settings-mfa-verification">
                    <div data-testid="mfa-user-type">{userMfaType}</div>
                    <div data-testid="mfa-email">{email}</div>
                    <div data-testid="mfa-phone">{phoneNumber}</div>
                    <button data-testid="verify-mfa-button" onClick={() => onVerified("test-mfa-token")}>
                        Verify
                    </button>
                    <button data-testid="close-mfa-button" onClick={onClose}>
                        Close
                    </button>
                </div>
            );
        };
    }
);

// Mock Redux actions
import { fetchPersonalInfo } from "../../../../../src/redux/actions/settingsActions";
import { getTeamMemberDetails } from "../../../../../src/redux/actions/transferMfaActions";
import { resetAllStates } from "../../../../../src/redux/slices/settingsMfaSlice";

jest.mock("../../../../../src/redux/actions/settingsActions", () => ({
    fetchPersonalInfo: jest.fn(),
}));

jest.mock("../../../../../src/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(),
}));

jest.mock("../../../../../src/redux/slices/settingsMfaSlice", () => ({
    resetAllStates: jest.fn(),
}));

// Mock store factory
const createMockStore = (overrides = {}) => {
    const defaultState = {
        transferMfaSlice: {
            getTeamMemberDetails: { loading: false, success: false, error: null },
            teamMember: null,
        },
        security: {
            verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
            verifyMFA: { error: null, open: false, loading: false, success: false },
            changePin: { error: null, loading: false, success: false },
            resetPin: { error: null, loading: false, success: false },
        },
        settings: {
            personalInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
            businessInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
            businessLogo: {
                uploadLoading: false,
                uploadError: null,
                deleteLoading: false,
                deleteError: null,
                fetchLoading: false,
                fetchError: null,
            },
            changePassword: { loading: false, success: false, error: null },
        },
        ...overrides,
    };

    return configureStore({
        reducer: {
            transferMfaSlice: (state = defaultState.transferMfaSlice) => state,
            security: (state = defaultState.security) => state,
            settings: (state = defaultState.settings) => state,
        },
        preloadedState: defaultState,
    });
};

describe("SecuritySettings Component - Complete Coverage", () => {
    let mockDispatch;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
        mockDispatch = jest.fn();

        fetchPersonalInfo.mockReturnValue({ type: "FETCH_PERSONAL_INFO" });
        getTeamMemberDetails.mockReturnValue({ type: "GET_TEAM_MEMBER_DETAILS" });
        resetAllStates.mockReturnValue({ type: "RESET_ALL_STATES" });
    });

    afterEach(() => {
        jest.runOnlyPendingTimers();
        jest.useRealTimers();
    });

    const renderWithStore = (storeState = {}) => {
        const store = createMockStore(storeState);
        store.dispatch = mockDispatch;
        return {
            ...render(
                <Provider store={store}>
                    <SecuritySettings />
                </Provider>
            ),
            store,
        };
    };

    describe("Component Initialization", () => {
        it("should render and dispatch fetchPersonalInfo on mount", () => {
            renderWithStore();

            expect(screen.getByTestId("security-tab")).toBeInTheDocument();
            expect(screen.getByText("Security & Authentication")).toBeInTheDocument();
            expect(mockDispatch).toHaveBeenCalledWith({ type: "FETCH_PERSONAL_INFO" });
        });

        it("should not show modals initially", () => {
            renderWithStore();

            expect(screen.queryByTestId("change-pin-modal")).not.toBeInTheDocument();
            expect(screen.queryByTestId("change-password-modal")).not.toBeInTheDocument();
            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();
        });
    });

    describe("PIN Change Flow - Complete Coverage", () => {
        it("should handle PIN change click and dispatch getTeamMemberDetails", () => {
            renderWithStore();

            fireEvent.click(screen.getByTestId("change-pin-button"));

            expect(mockDispatch).toHaveBeenCalledWith({ type: "GET_TEAM_MEMBER_DETAILS" });
        });

        it("should show MFA verification when team member has MFA enabled", () => {
            // Start with initial store
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Now update store with team member data to trigger useEffect
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // The useEffect should trigger and show MFA modal
            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();
            expect(screen.getByTestId("mfa-user-type")).toHaveTextContent("SECURITY_QUESTION");
            expect(screen.getByTestId("mfa-email")).toHaveTextContent("<EMAIL>");
            expect(screen.getByTestId("mfa-phone")).toHaveTextContent("+**********");
        });

        it("should open PIN modal directly when no MFA", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data (no MFA) to trigger useEffect
            const storeWithoutMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: false,
                    },
                },
            });
            storeWithoutMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Should directly open PIN modal without MFA
            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();
            expect(screen.getByTestId("pin-modal-token")).toHaveTextContent("no-token");
        });

        it("should handle MFA verification and open PIN modal", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // MFA modal should be visible, now verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();
            expect(screen.getByTestId("pin-modal-token")).toHaveTextContent("test-mfa-token");
        });

        it("should close PIN modal and reset state", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open PIN modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();

            // Close PIN modal
            fireEvent.click(screen.getByTestId("close-pin-modal"));

            expect(screen.queryByTestId("change-pin-modal")).not.toBeInTheDocument();
            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should handle PIN error and trigger cleanup", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open PIN modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();

            // Trigger PIN error
            fireEvent.click(screen.getByTestId("trigger-pin-error"));

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });

            // Fast-forward timer to test cleanup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            expect(screen.queryByTestId("change-pin-modal")).not.toBeInTheDocument();
        });
    });

    describe("Password Change Flow - Complete Coverage", () => {
        it("should handle password change click and dispatch getTeamMemberDetails", () => {
            renderWithStore();

            fireEvent.click(screen.getByTestId("change-password-button"));

            expect(mockDispatch).toHaveBeenCalledWith({ type: "GET_TEAM_MEMBER_DETAILS" });
        });

        it("should show MFA verification when team member has MFA enabled", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SMS",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // The useEffect should trigger and show MFA modal
            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();
            expect(screen.getByTestId("mfa-user-type")).toHaveTextContent("SMS");
        });

        it("should open password modal directly when no MFA", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data (no MFA) to trigger useEffect
            const storeWithoutMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "AUTHENTICATOR",
                        mfaStatus: false,
                    },
                },
            });
            storeWithoutMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Should directly open password modal without MFA
            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();
            expect(screen.getByTestId("password-modal-token")).toHaveTextContent("no-token");
        });

        it("should handle MFA verification and open password modal", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "AUTHENTICATOR",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // MFA modal should be visible, now verify MFA
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();
            expect(screen.getByTestId("password-modal-token")).toHaveTextContent("test-mfa-token");
        });

        it("should close password modal and reset state", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "AUTHENTICATOR",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open password modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();

            // Close password modal
            fireEvent.click(screen.getByTestId("close-password-modal"));

            expect(screen.queryByTestId("change-password-modal")).not.toBeInTheDocument();
            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should handle password error and trigger cleanup", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "AUTHENTICATOR",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open password modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();

            // Trigger password error
            fireEvent.click(screen.getByTestId("trigger-password-error"));

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });

            // Fast-forward timer to test cleanup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            expect(screen.queryByTestId("change-password-modal")).not.toBeInTheDocument();
        });
    });

    describe("MFA Verification Modal", () => {
        it("should close MFA verification modal", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();

            fireEvent.click(screen.getByTestId("close-mfa-button"));

            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();
        });

        it("should handle MFA verification close function coverage", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();

            // Test the handleMfaVerificationClose function
            fireEvent.click(screen.getByTestId("close-mfa-button"));

            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();

            // Test that we can trigger the flow again after closing
            fireEvent.click(screen.getByTestId("change-pin-button"));
            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();
        });
    });

    describe("useEffect Hooks - Complete Coverage", () => {
        it("should handle PIN change success", () => {
            const { rerender } = renderWithStore();

            const storeWithPinSuccess = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: null, loading: false, success: true },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithPinSuccess.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPinSuccess}>
                    <SecuritySettings />
                </Provider>
            );

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should handle PIN change error with timer cleanup", () => {
            const { rerender } = renderWithStore();

            const storeWithPinError = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: "PIN change failed", loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithPinError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPinError}>
                    <SecuritySettings />
                </Provider>
            );

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });

            // Fast-forward timer to test cleanup
            act(() => {
                jest.advanceTimersByTime(1000);
            });
        });

        it("should handle password change success", () => {
            const { rerender } = renderWithStore();

            const storeWithPasswordSuccess = createMockStore({
                settings: {
                    personalInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessLogo: {
                        uploadLoading: false,
                        uploadError: null,
                        deleteLoading: false,
                        deleteError: null,
                        fetchLoading: false,
                        fetchError: null,
                    },
                    changePassword: { loading: false, success: true, error: null },
                },
            });
            storeWithPasswordSuccess.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPasswordSuccess}>
                    <SecuritySettings />
                </Provider>
            );

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should handle password change error with timer cleanup", () => {
            const { rerender } = renderWithStore();

            const storeWithPasswordError = createMockStore({
                settings: {
                    personalInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessLogo: {
                        uploadLoading: false,
                        uploadError: null,
                        deleteLoading: false,
                        deleteError: null,
                        fetchLoading: false,
                        fetchError: null,
                    },
                    changePassword: { loading: false, success: false, error: "Password change failed" },
                },
            });
            storeWithPasswordError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPasswordError}>
                    <SecuritySettings />
                </Provider>
            );

            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });

            // Fast-forward timer to test cleanup
            act(() => {
                jest.advanceTimersByTime(1000);
            });
        });
    });

    describe("useCallback Functions - Complete Coverage", () => {
        it("should test handlePinError useCallback with timer cleanup", () => {
            const { rerender, unmount } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open PIN modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            // Trigger error to start timer
            fireEvent.click(screen.getByTestId("trigger-pin-error"));

            // Unmount component to test cleanup function
            unmount();

            // Fast-forward timer
            act(() => {
                jest.advanceTimersByTime(1000);
            });
        });

        it("should test handlePasswordError useCallback with timer cleanup", () => {
            const { rerender, unmount } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Verify MFA to open password modal
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            // Trigger error to start timer
            fireEvent.click(screen.getByTestId("trigger-password-error"));

            // Unmount component to test cleanup function
            unmount();

            // Fast-forward timer
            act(() => {
                jest.advanceTimersByTime(1000);
            });
        });

        it("should test handleMfaVerified with different action types", () => {
            const { rerender } = renderWithStore();

            // Test PIN action type first
            fireEvent.click(screen.getByTestId("change-pin-button"));

            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();

            fireEvent.click(screen.getByTestId("close-pin-modal"));

            // Test password action type
            fireEvent.click(screen.getByTestId("change-password-button"));
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();
        });
    });

    describe("Edge Cases and Error Scenarios", () => {
        it("should handle team member without MFA status", () => {
            const { rerender } = renderWithStore();

            fireEvent.click(screen.getByTestId("change-pin-button"));

            const storeWithoutMfaStatus = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        // mfaStatus is undefined/null
                    },
                },
            });
            storeWithoutMfaStatus.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutMfaStatus}>
                    <SecuritySettings />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("change-pin-button"));

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();
        });

        it("should handle null team member", () => {
            const { rerender } = renderWithStore();

            fireEvent.click(screen.getByTestId("change-pin-button"));

            const storeWithNullTeamMember = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: null,
                },
            });
            storeWithNullTeamMember.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithNullTeamMember}>
                    <SecuritySettings />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Should not show MFA modal or PIN modal
            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();
            expect(screen.queryByTestId("change-pin-modal")).not.toBeInTheDocument();
        });

        it("should handle rapid state changes and cleanup", () => {
            const { rerender } = renderWithStore();

            // Rapid PIN error state changes
            const storeWithPinError1 = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: "Error 1", loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithPinError1.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPinError1}>
                    <SecuritySettings />
                </Provider>
            );

            const storeWithPinError2 = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: "Error 2", loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithPinError2.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPinError2}>
                    <SecuritySettings />
                </Provider>
            );

            // Fast-forward all timers
            act(() => {
                jest.advanceTimersByTime(2000);
            });
        });

        it("should handle component unmount during timer operations", () => {
            const { rerender, unmount } = renderWithStore();

            // Set up error state to trigger timer
            const storeWithError = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: "PIN error", loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
                settings: {
                    personalInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessLogo: {
                        uploadLoading: false,
                        uploadError: null,
                        deleteLoading: false,
                        deleteError: null,
                        fetchLoading: false,
                        fetchError: null,
                    },
                    changePassword: { loading: false, success: false, error: "Password error" },
                },
            });
            storeWithError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithError}>
                    <SecuritySettings />
                </Provider>
            );

            // Unmount component while timers are active
            unmount();

            // Fast-forward timers to test cleanup
            act(() => {
                jest.runAllTimers();
            });
        });
    });

    describe("Function Coverage Enhancement - Target Specific Functions", () => {
        it("should test handleMfaVerificationClose function directly", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data to trigger MFA modal
            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();

            // Test handleMfaVerificationClose function (lines 117-126)
            fireEvent.click(screen.getByTestId("close-mfa-button"));

            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();

            // Verify the function reset all states properly
            fireEvent.click(screen.getByTestId("change-pin-button"));
            expect(screen.getByTestId("settings-mfa-verification")).toBeInTheDocument();
        });

        it("should test handleChangePasswordModalClose function directly", () => {
            const { rerender } = renderWithStore();

            // Click password change to set the flow flag
            fireEvent.click(screen.getByTestId("change-password-button"));

            // Update store with team member data (no MFA) to directly open password modal
            const storeWithoutMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: false,
                    },
                },
            });
            storeWithoutMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutMfa}>
                    <SecuritySettings />
                </Provider>
            );

            expect(screen.getByTestId("change-password-modal")).toBeInTheDocument();

            // Test handleChangePasswordModalClose function (lines 179-195)
            fireEvent.click(screen.getByTestId("close-password-modal"));

            expect(screen.queryByTestId("change-password-modal")).not.toBeInTheDocument();
            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should test handleChangePinModalClose function directly", () => {
            const { rerender } = renderWithStore();

            // Click PIN change to set the flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Update store with team member data (no MFA) to directly open PIN modal
            const storeWithoutMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: false,
                    },
                },
            });
            storeWithoutMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutMfa}>
                    <SecuritySettings />
                </Provider>
            );

            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();

            // Test handleChangePinModalClose function (lines 198-214)
            fireEvent.click(screen.getByTestId("close-pin-modal"));

            expect(screen.queryByTestId("change-pin-modal")).not.toBeInTheDocument();
            expect(mockDispatch).toHaveBeenCalledWith({ type: "RESET_ALL_STATES" });
        });

        it("should test timer cleanup functions in useEffect hooks", () => {
            const { rerender, unmount } = renderWithStore();

            // Test PIN error useEffect timer cleanup (lines 156-175)
            const storeWithPinError = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: "PIN error", loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithPinError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPinError}>
                    <SecuritySettings />
                </Provider>
            );

            // Change to no error to trigger cleanup
            const storeWithoutError = createMockStore({
                security: {
                    verifyPin: { error: null, open: false, loading: false, success: false, pin: "" },
                    verifyMFA: { error: null, open: false, loading: false, success: false },
                    changePin: { error: null, loading: false, success: false },
                    resetPin: { error: null, loading: false, success: false },
                },
            });
            storeWithoutError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithoutError}>
                    <SecuritySettings />
                </Provider>
            );

            // Test password error useEffect timer cleanup
            const storeWithPasswordError = createMockStore({
                settings: {
                    personalInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessInfo: { data: null, loading: false, error: null, updateLoading: false, updateError: null },
                    businessLogo: {
                        uploadLoading: false,
                        uploadError: null,
                        deleteLoading: false,
                        deleteError: null,
                        fetchLoading: false,
                        fetchError: null,
                    },
                    changePassword: { loading: false, success: false, error: "Password error" },
                },
            });
            storeWithPasswordError.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithPasswordError}>
                    <SecuritySettings />
                </Provider>
            );

            // Unmount to trigger all cleanup functions
            unmount();

            // Fast-forward all timers
            act(() => {
                jest.runAllTimers();
            });
        });

        it("should test all useCallback dependency changes", () => {
            const { rerender } = renderWithStore();

            // Create different dispatch functions to test useCallback dependencies
            const mockDispatch1 = jest.fn();
            mockDispatch1.mockReturnValue(Promise.resolve());

            const store1 = createMockStore({});
            store1.dispatch = mockDispatch1;

            rerender(
                <Provider store={store1}>
                    <SecuritySettings />
                </Provider>
            );

            // Test handleChangePin with first dispatch
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Change dispatch function to trigger useCallback dependency
            const mockDispatch2 = jest.fn();
            mockDispatch2.mockReturnValue(Promise.resolve());

            const store2 = createMockStore({});
            store2.dispatch = mockDispatch2;

            rerender(
                <Provider store={store2}>
                    <SecuritySettings />
                </Provider>
            );

            // Test handleChangePassword with second dispatch
            fireEvent.click(screen.getByTestId("change-password-button"));

            expect(mockDispatch1).toHaveBeenCalled();
            expect(mockDispatch2).toHaveBeenCalled();
        });
    });

    describe("Complete Function Coverage - Edge Cases", () => {
        it("should test handleMfaVerified with null action type", () => {
            const { rerender } = renderWithStore();

            // Start with PIN flow
            fireEvent.click(screen.getByTestId("change-pin-button"));

            const storeWithMfa = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithMfa.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithMfa}>
                    <SecuritySettings />
                </Provider>
            );

            // Close MFA modal to reset action type
            fireEvent.click(screen.getByTestId("close-mfa-button"));

            // Now trigger MFA verification with null action type
            // This should test the null case in handleMfaVerified
            fireEvent.click(screen.getByTestId("change-pin-button"));
            fireEvent.click(screen.getByTestId("verify-mfa-button"));

            // Should still open PIN modal even with action type reset
            expect(screen.getByTestId("change-pin-modal")).toBeInTheDocument();
        });

        it("should test all conditional branches in useEffect", () => {
            const { rerender } = renderWithStore();

            // Test the useEffect with teamMemberSuccess && teamMember conditions
            // First test with teamMember but no success
            const storeWithTeamMemberNoSuccess = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: false, error: null },
                    teamMember: {
                        email: "<EMAIL>",
                        phoneNumber: "+**********",
                        preferredMfaMethod: "SECURITY_QUESTION",
                        mfaStatus: true,
                    },
                },
            });
            storeWithTeamMemberNoSuccess.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithTeamMemberNoSuccess}>
                    <SecuritySettings />
                </Provider>
            );

            // Click PIN change to set flow flag
            fireEvent.click(screen.getByTestId("change-pin-button"));

            // Should not show MFA modal because success is false
            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();

            // Test with success but no teamMember
            const storeWithSuccessNoTeamMember = createMockStore({
                transferMfaSlice: {
                    getTeamMemberDetails: { loading: false, success: true, error: null },
                    teamMember: null,
                },
            });
            storeWithSuccessNoTeamMember.dispatch = mockDispatch;

            rerender(
                <Provider store={storeWithSuccessNoTeamMember}>
                    <SecuritySettings />
                </Provider>
            );

            // Should not show MFA modal because teamMember is null
            expect(screen.queryByTestId("settings-mfa-verification")).not.toBeInTheDocument();
        });
    });
});
