import {
    getActivityLogFiltersFromUrlParams,
    updateUrlParamsFromActivityLogFilters,
    mapTempFiltersToApiFilters,
    mapApiFiltersToTempFilters,
} from "@/components/page-components/dashboard/teams/activity-logs/utils/urlParamUtils";

// Mock ReadonlyURLSearchParams
class MockReadonlyURLSearchParams {
    constructor(params) {
        this.params = new URLSearchParams(params);
    }

    toString() {
        return this.params.toString();
    }

    get(key) {
        return this.params.get(key);
    }

    has(key) {
        return this.params.has(key);
    }
}

describe("urlParamUtils", () => {
    describe("getActivityLogFiltersFromUrlParams", () => {
        test("extracts all parameters correctly", () => {
            const searchParams = new MockReadonlyURLSearchParams(
                "search=test&startDate=2024-01-01&endDate=2024-01-31&roleId=1&actionTypes=UserManagement&page=2&size=20&sortBy=action&sortDirection=ASC"
            );

            const result = getActivityLogFiltersFromUrlParams(searchParams);

            expect(result).toEqual({
                search: "test",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                roleId: 1,
                actionTypes: "UserManagement",
                page: 2,
                size: 20,
                sortBy: "action",
                sortDirection: "ASC",
            });
        });

        test("handles missing parameters with defaults", () => {
            const searchParams = new MockReadonlyURLSearchParams("");

            const result = getActivityLogFiltersFromUrlParams(searchParams);

            expect(result).toEqual({
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            });
        });

        test("handles partial parameters", () => {
            const searchParams = new MockReadonlyURLSearchParams("search=partial&roleId=2");

            const result = getActivityLogFiltersFromUrlParams(searchParams);

            expect(result).toEqual({
                search: "partial",
                startDate: "",
                endDate: "",
                roleId: 2,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            });
        });

        test("handles invalid numeric values", () => {
            const searchParams = new MockReadonlyURLSearchParams("roleId=invalid&page=notNumber&size=abc");

            const result = getActivityLogFiltersFromUrlParams(searchParams);

            expect(result.roleId).toBeNaN();
            expect(result.page).toBeNaN();
            expect(result.size).toBeNaN();
        });
    });

    describe("updateUrlParamsFromActivityLogFilters", () => {
        test("updates all parameters correctly", () => {
            const searchParams = new MockReadonlyURLSearchParams("");
            const filters = {
                search: "test search",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                roleId: 1,
                actionTypes: "UserManagement",
                page: 2,
                size: 20,
                sortBy: "action",
                sortDirection: "ASC",
            };

            const result = updateUrlParamsFromActivityLogFilters(searchParams, filters);

            expect(result.get("search")).toBe("test search");
            expect(result.get("startDate")).toBe("2024-01-01");
            expect(result.get("endDate")).toBe("2024-01-31");
            expect(result.get("roleId")).toBe("1");
            expect(result.get("actionTypes")).toBe("UserManagement");
            expect(result.get("page")).toBe("2");
            expect(result.get("size")).toBe("20");
            expect(result.get("sortBy")).toBe("action");
            expect(result.get("sortDirection")).toBe("ASC");
        });

        test("clears existing parameters before setting new ones", () => {
            const searchParams = new MockReadonlyURLSearchParams("search=old&page=1&otherParam=keep");
            const filters = {
                search: "new search",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = updateUrlParamsFromActivityLogFilters(searchParams, filters);

            expect(result.get("search")).toBe("new search");
            expect(result.get("page")).toBe("0");
            expect(result.get("otherParam")).toBe("keep"); // Non-activity log params should be preserved
        });

        test("handles null and undefined values", () => {
            const searchParams = new MockReadonlyURLSearchParams("");
            const filters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = updateUrlParamsFromActivityLogFilters(searchParams, filters);

            expect(result.has("search")).toBe(false);
            expect(result.has("startDate")).toBe(false);
            expect(result.has("endDate")).toBe(false);
            expect(result.has("roleId")).toBe(false);
            expect(result.has("actionTypes")).toBe(false);
            expect(result.get("page")).toBe("0");
            expect(result.get("size")).toBe("10");
        });

        test("handles roleId of 0", () => {
            const searchParams = new MockReadonlyURLSearchParams("");
            const filters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 0,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = updateUrlParamsFromActivityLogFilters(searchParams, filters);

            expect(result.get("roleId")).toBe("0");
        });
    });

    describe("mapTempFiltersToApiFilters", () => {
        test("maps basic temp filters to API filters", () => {
            const tempFilters = {
                search: "test",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                actionType: "UserManagement",
                source: "",
                dateFilterType: "custom",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters);

            expect(result).toEqual({
                search: "test",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                roleId: null,
                actionTypes: "UserManagement",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            });
        });

        test("maps Super Admin source correctly", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "Super Admin",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters);

            expect(result.roleId).toBe(0);
        });

        test("maps role name to roleId using roleMap", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "Admin",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const roleMap = {
                1: "Admin",
                2: "User",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters, roleMap);

            expect(result.roleId).toBe(1);
        });

        test("handles unknown role name", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "Unknown Role",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const roleMap = {
                1: "Admin",
                2: "User",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters, roleMap);

            expect(result.roleId).toBeNull();
        });

        test("handles empty roleMap", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "Admin",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters, {});

            expect(result.roleId).toBeNull();
        });
    });

    describe("mapApiFiltersToTempFilters", () => {
        test("maps basic API filters to temp filters", () => {
            const apiFilters = {
                search: "test",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                roleId: null,
                actionTypes: "UserManagement",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters);

            expect(result).toEqual({
                search: "test",
                startDate: "2024-01-01",
                endDate: "2024-01-31",
                actionType: "UserManagement",
                source: "",
                dateFilterType: "",
            });
        });

        test("maps Super Admin roleId correctly", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 0,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters);

            expect(result.source).toBe("Super Admin");
        });

        test("maps roleId to role name using roleMap", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 1,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const roleMap = {
                1: "Admin",
                2: "User",
            };

            const result = mapApiFiltersToTempFilters(apiFilters, roleMap);

            expect(result.source).toBe("Admin");
        });

        test("handles unknown roleId", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 999,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const roleMap = {
                1: "Admin",
                2: "User",
            };

            const result = mapApiFiltersToTempFilters(apiFilters, roleMap);

            expect(result.source).toBe("");
        });

        test("handles null roleId", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters);

            expect(result.source).toBe("");
        });

        test("handles undefined roleId", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: undefined,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters);

            expect(result.source).toBe("");
        });

        test("handles empty roleMap object", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 1,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters, {});

            expect(result.source).toBe("");
        });

        test("handles non-object roleMap", () => {
            const apiFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 1,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapApiFiltersToTempFilters(apiFilters, null);

            expect(result.source).toBe("");
        });
    });

    describe("mapTempFiltersToApiFilters additional branch coverage", () => {
        test("handles non-object roleMap", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "Admin",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters, null);

            expect(result.roleId).toBeNull();
        });

        test("handles roleMap with no matching entries", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "NonExistentRole",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const roleMap = {
                1: "Admin",
                2: "User",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters, roleMap);

            expect(result.roleId).toBeNull();
        });

        test("handles empty source string", () => {
            const tempFilters = {
                search: "",
                startDate: "",
                endDate: "",
                actionType: "",
                source: "",
                dateFilterType: "",
            };

            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const result = mapTempFiltersToApiFilters(tempFilters, currentFilters);

            expect(result.roleId).toBeNull();
        });
    });

    describe("updateUrlParamsFromActivityLogFilters additional branch coverage", () => {
        test("handles undefined values for optional parameters", () => {
            const searchParams = new MockReadonlyURLSearchParams("");
            const filters = {
                search: undefined,
                startDate: undefined,
                endDate: undefined,
                roleId: undefined,
                actionTypes: undefined,
                page: undefined,
                size: undefined,
                sortBy: undefined,
                sortDirection: undefined,
            };

            const result = updateUrlParamsFromActivityLogFilters(searchParams, filters);

            // Should use defaults for undefined values
            expect(result.get("page")).toBe("0");
            expect(result.get("size")).toBe("10");
            expect(result.get("sortBy")).toBe("timestamp");
            expect(result.get("sortDirection")).toBe("DESC");
        });
    });
});
