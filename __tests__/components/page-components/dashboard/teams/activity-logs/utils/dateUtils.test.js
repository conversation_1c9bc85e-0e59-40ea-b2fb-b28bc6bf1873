import {
    getDateRangeFromOption,
    getDateFilterLabel,
} from "@/components/page-components/dashboard/teams/activity-logs/utils/dateUtils";

describe("dateUtils", () => {
    // Mock current date for consistent testing
    const mockDate = new Date("2024-11-08T10:00:00Z");

    beforeAll(() => {
        jest.useFakeTimers();
        jest.setSystemTime(mockDate);
    });

    afterAll(() => {
        jest.useRealTimers();
    });

    describe("getDateRangeFromOption", () => {
        test("returns correct range for 'Today'", () => {
            const result = getDateRangeFromOption("Today");

            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-11-08");
            expect(result.endDate).toBe("2024-11-08");
        });

        test("returns correct range for 'Yesterday'", () => {
            const result = getDateRangeFromOption("Yesterday");

            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-11-07");
            expect(result.endDate).toBe("2024-11-07");
        });

        test("returns correct range for 'Last 7 days'", () => {
            const result = getDateRangeFromOption("Last 7 days");

            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-11-01");
            expect(result.endDate).toBe("2024-11-08");
        });

        test("returns correct range for 'Last 30 days'", () => {
            const result = getDateRangeFromOption("Last 30 days");

            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-10-09");
            expect(result.endDate).toBe("2024-11-08");
        });

        test("returns null for 'Custom date'", () => {
            const result = getDateRangeFromOption("Custom date");
            expect(result).toBeNull();
        });

        test("returns null for unknown option", () => {
            const result = getDateRangeFromOption("Unknown option");
            expect(result).toBeNull();
        });
    });

    describe("getDateFilterLabel", () => {
        test("returns 'Custom date' when both dates are provided", () => {
            const result = getDateFilterLabel("2024-01-01", "2024-01-31");
            expect(result).toBe("Custom date");
        });

        test("returns 'Today' for today's date range", () => {
            const result = getDateFilterLabel("2024-11-08", "2024-11-08");
            expect(result).toBe("Today");
        });

        test("returns 'Yesterday' for yesterday's date range", () => {
            const result = getDateFilterLabel("2024-11-07", "2024-11-07");
            expect(result).toBe("Yesterday");
        });

        test("returns 'Last 7 days' for 7-day range", () => {
            const result = getDateFilterLabel("2024-11-01", "2024-11-08");
            expect(result).toBe("Last 7 days");
        });

        test("returns 'Last 30 days' for 30-day range", () => {
            const result = getDateFilterLabel("2024-10-09", "2024-11-08");
            expect(result).toBe("Last 30 days");
        });

        test("returns 'Custom date' for other date ranges", () => {
            const result = getDateFilterLabel("2024-01-01", "2024-01-15");
            expect(result).toBe("Custom date");
        });

        test("returns 'Custom date' when only start date is provided", () => {
            const result = getDateFilterLabel("2024-01-01", null);
            expect(result).toBe("Custom date");
        });

        test("returns 'Custom date' when only end date is provided", () => {
            const result = getDateFilterLabel(null, "2024-01-31");
            expect(result).toBe("Custom date");
        });

        test("returns 'Custom date' when no dates are provided", () => {
            const result = getDateFilterLabel(null, null);
            expect(result).toBe("Custom date");
        });
    });

    describe("edge cases", () => {
        test("handles leap year correctly", () => {
            jest.setSystemTime(new Date("2024-02-29T10:00:00Z")); // Leap year

            const result = getDateRangeFromOption("Today");
            expect(result.startDate).toBe("2024-02-29");
            expect(result.endDate).toBe("2024-02-29");
        });

        test("handles year boundary correctly", () => {
            jest.setSystemTime(new Date("2024-01-01T10:00:00Z"));

            const result = getDateRangeFromOption("Yesterday");
            expect(result.startDate).toBe("2023-12-31");
            expect(result.endDate).toBe("2023-12-31");
        });

        test("handles month boundary correctly", () => {
            jest.setSystemTime(new Date("2024-03-01T10:00:00Z"));

            const result = getDateRangeFromOption("Yesterday");
            expect(result.startDate).toBe("2024-02-29"); // Leap year
            expect(result.endDate).toBe("2024-02-29");
        });
    });

    describe("getDateFilterLabel with real date calculations", () => {
        beforeEach(() => {
            // Use a consistent date for these tests
            jest.setSystemTime(new Date("2024-11-08T10:00:00Z"));
        });

        test("correctly identifies Today with real date calculation", () => {
            const today = new Date().toISOString().split("T")[0];
            const result = getDateFilterLabel(today, today);
            expect(result).toBe("Today");
        });

        test("correctly identifies Yesterday with real date calculation", () => {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split("T")[0];

            const result = getDateFilterLabel(yesterdayStr, yesterdayStr);
            expect(result).toBe("Yesterday");
        });

        test("correctly identifies Last 7 days with real date calculation", () => {
            const today = new Date().toISOString().split("T")[0];
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const sevenDaysAgoStr = sevenDaysAgo.toISOString().split("T")[0];

            const result = getDateFilterLabel(sevenDaysAgoStr, today);
            expect(result).toBe("Last 7 days");
        });

        test("correctly identifies Last 30 days with real date calculation", () => {
            const today = new Date().toISOString().split("T")[0];
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split("T")[0];

            const result = getDateFilterLabel(thirtyDaysAgoStr, today);
            expect(result).toBe("Last 30 days");
        });

        test("returns Custom date for non-matching ranges", () => {
            const result = getDateFilterLabel("2024-01-01", "2024-01-15");
            expect(result).toBe("Custom date");
        });

        test("returns Custom date when startDate is missing", () => {
            const result = getDateFilterLabel(undefined, "2024-01-15");
            expect(result).toBe("Custom date");
        });

        test("returns Custom date when endDate is missing", () => {
            const result = getDateFilterLabel("2024-01-01", undefined);
            expect(result).toBe("Custom date");
        });

        test("returns Custom date when both dates are missing", () => {
            const result = getDateFilterLabel(undefined, undefined);
            expect(result).toBe("Custom date");
        });
    });

    describe("getDateRangeFromOption with all cases", () => {
        beforeEach(() => {
            jest.setSystemTime(new Date("2024-11-08T10:00:00Z"));
        });

        test("handles Yesterday case correctly", () => {
            const result = getDateRangeFromOption("Yesterday");
            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-11-07");
            expect(result.endDate).toBe("2024-11-07");
        });

        test("handles Last 7 days case correctly", () => {
            const result = getDateRangeFromOption("Last 7 days");
            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-11-01");
            expect(result.endDate).toBe("2024-11-08");
        });

        test("handles Last 30 days case correctly", () => {
            const result = getDateRangeFromOption("Last 30 days");
            expect(result).toBeDefined();
            expect(result.startDate).toBe("2024-10-09");
            expect(result.endDate).toBe("2024-11-08");
        });

        test("handles default case (unknown option)", () => {
            const result = getDateRangeFromOption("Some unknown option");
            expect(result).toBeNull();
        });

        test("handles null option", () => {
            const result = getDateRangeFromOption(null);
            expect(result).toBeNull();
        });

        test("handles undefined option", () => {
            const result = getDateRangeFromOption(undefined);
            expect(result).toBeNull();
        });

        test("handles empty string option", () => {
            const result = getDateRangeFromOption("");
            expect(result).toBeNull();
        });
    });

    describe("getDateFilterLabel additional branch coverage", () => {
        beforeEach(() => {
            jest.setSystemTime(new Date("2024-11-08T10:00:00Z"));
        });

        test("handles null startDate and endDate", () => {
            const result = getDateFilterLabel(null, null);
            expect(result).toBe("Custom date");
        });

        test("handles startDate without endDate", () => {
            const result = getDateFilterLabel("2024-01-01", null);
            expect(result).toBe("Custom date");
        });

        test("handles endDate without startDate", () => {
            const result = getDateFilterLabel(null, "2024-01-01");
            expect(result).toBe("Custom date");
        });

        test("handles empty string dates", () => {
            const result = getDateFilterLabel("", "");
            expect(result).toBe("Custom date");
        });

        test("handles mixed null and empty string", () => {
            const result = getDateFilterLabel(null, "");
            expect(result).toBe("Custom date");
        });

        test("handles mixed empty string and null", () => {
            const result = getDateFilterLabel("", null);
            expect(result).toBe("Custom date");
        });

        test("handles invalid date strings", () => {
            const result = getDateFilterLabel("invalid-date", "another-invalid-date");
            expect(result).toBe("Custom date");
        });

        test("handles partial date matches", () => {
            // Test a date range that doesn't match any predefined options
            const result = getDateFilterLabel("2024-01-01", "2024-01-02");
            expect(result).toBe("Custom date");
        });

        test("handles same start and end date that's not today or yesterday", () => {
            const result = getDateFilterLabel("2024-01-01", "2024-01-01");
            expect(result).toBe("Custom date");
        });

        test("handles date range that's close but not exact to predefined ranges", () => {
            const today = new Date().toISOString().split("T")[0];
            const almostSevenDaysAgo = new Date();
            almostSevenDaysAgo.setDate(almostSevenDaysAgo.getDate() - 6); // 6 days instead of 7
            const almostSevenDaysAgoStr = almostSevenDaysAgo.toISOString().split("T")[0];

            const result = getDateFilterLabel(almostSevenDaysAgoStr, today);
            expect(result).toBe("Custom date");
        });

        test("handles date range that's close but not exact to 30 days", () => {
            const today = new Date().toISOString().split("T")[0];
            const almostThirtyDaysAgo = new Date();
            almostThirtyDaysAgo.setDate(almostThirtyDaysAgo.getDate() - 29); // 29 days instead of 30
            const almostThirtyDaysAgoStr = almostThirtyDaysAgo.toISOString().split("T")[0];

            const result = getDateFilterLabel(almostThirtyDaysAgoStr, today);
            expect(result).toBe("Custom date");
        });
    });
});
