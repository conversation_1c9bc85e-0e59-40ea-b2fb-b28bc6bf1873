/* eslint-disable @typescript-eslint/no-require-imports */
import { ExportActivityLog } from "@/components/page-components/dashboard/teams/activity-logs/modals/export-log-side-drawer.tsx";
import { configureStore } from "@reduxjs/toolkit";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import * as ReactRedux from "react-redux";

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));
// Mock the Redux actions
jest.mock("@/redux/actions/activityLogsActions", () => ({
    exportActivityLogs: jest.fn(() => ({ type: "EXPORT_ACTIVITY_LOGS" })),
}));

// Mock child components
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, children }) => (isOpen ? <div data-testid="side-drawer">{children}</div> : null),
}));

jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ label, id, checked, onCheckedChange }) => (
        <div data-testid={`checkbox-${id}`} onClick={onCheckedChange}>
            {label} {checked ? "(checked)" : "(unchecked)"}
        </div>
    ),
}));

jest.mock("@/components/common/radio-group", () => ({
    RadioGroup: ({ children, value, onValueChange }) => (
        <div data-testid="radio-group" onClick={() => onValueChange && onValueChange(value)}>
            {children}
        </div>
    ),
    RadioGroupItem: ({ value, id }) => (
        <input
            type="radio"
            data-testid={`radio-${id}`}
            id={id}
            value={value}
            defaultChecked={value === "LAST_3_MONTHS"} // Default to "3" (Last 3 months)
        />
    ),
}));

jest.mock("@/components/common/label", () => ({
    Label: ({ children, htmlFor }) => <label htmlFor={htmlFor}>{children}</label>,
}));

jest.mock("@/components/icons/transaction", () => ({
    ExportIcon: () => <div data-testid="export-icon" />,
}));

jest.mock("@/components/common/close-x", () => ({
    __esModule: true,
    default: ({ onClick }) => (
        <button data-testid="close-button" onClick={onClick}>
            X
        </button>
    ),
}));

jest.mock("@/components/common/button", () => ({
    Button: ({ children, onClick, variant, loading }) => (
        <button data-testid={`button-${variant || "primary"}`} onClick={onClick}>
            {loading ? "Loading..." : children}
        </button>
    ),
}));

jest.mock("@/components/common/date-picker", () => ({
    __esModule: true,
    default: ({ label, value, onChange }) => (
        <div>
            <label>{label}</label>
            <input
                type="date"
                value={value.toISOString().split("T")[0]}
                onChange={(e) => onChange(new Date(e.target.value))}
            />
        </div>
    ),
}));

// Create mock store
const createMockStore = (initialState = {}) => {
    const defaultState = {
        activityLogs: {
            exportActivityLogs: {
                data: null,
                loading: false,
                error: null,
                success: false,
            },
        },
    };

    const mergedState = { ...defaultState, ...initialState };

    return configureStore({
        reducer: {
            activityLogs: (state = mergedState.activityLogs) => state,
        },
    });
};

const { exportActivityLogs } = require("@/redux/actions/activityLogsActions");

describe("ExportActivityLog", () => {
    let mockStore;
    const mockData = [{ id: 1, name: "Test" }];
    const mockColumns = [{ field: "id", headerName: "ID" }];
    const mockAccounts = [{ accountNumber: "123456", accountName: "Test Account" }];

    beforeEach(() => {
        mockStore = createMockStore();
        jest.clearAllMocks();
    });

    const renderWithProvider = (component, store = mockStore) => {
        return render(<ReactRedux.Provider store={store}>{component}</ReactRedux.Provider>);
    };

    it("renders the export button correctly", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        expect(screen.getByTestId("button")).toHaveTextContent("Export");
        expect(screen.getByTestId("export-icon")).toBeInTheDocument();
    });

    it("opens the drawer when export button is clicked", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Drawer should not be visible initially
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();

        // Click export button
        fireEvent.click(screen.getByTestId("button"));

        // Drawer should now be visible
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.getByText("Export activity log")).toBeInTheDocument();
    });

    it("closes the drawer when close button is clicked", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();

        // Click close button
        fireEvent.click(screen.getByTestId("close-button"));

        // Drawer should be closed
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    it("closes the drawer when cancel button is clicked", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // Click cancel button
        fireEvent.click(screen.getByText("Cancel"));

        // Drawer should be closed
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    it("changes export type when a different format is selected", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // CSV should be checked by default
        expect(screen.getByTestId("checkbox-csv")).toHaveTextContent("(checked)");

        // Click PDF checkbox
        fireEvent.click(screen.getByTestId("checkbox-pdf"));

        // PDF should now be checked
        expect(screen.getByTestId("checkbox-pdf")).toHaveTextContent("(checked)");
        expect(screen.getByTestId("checkbox-csv")).toHaveTextContent("(unchecked)");
    });

    it("calls export function when export button is clicked", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // Click export button
        fireEvent.click(screen.getAllByText("Export")[1]);

        // Redux action should be called
        expect(exportActivityLogs).toHaveBeenCalledTimes(1);
        expect(exportActivityLogs).toHaveBeenCalledWith(
            expect.objectContaining({
                format: "csv",
                startDate: expect.any(String),
                endDate: expect.any(String),
            })
        );
    });

    it("renders all radio options for date range", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // Check that all radio options are rendered
        expect(screen.getByTestId("radio-group")).toBeInTheDocument();
        expect(screen.getByTestId("radio-r1")).toBeInTheDocument();
        expect(screen.getByTestId("radio-r2")).toBeInTheDocument();
        expect(screen.getByTestId("radio-r3")).toBeInTheDocument();
        expect(screen.getByTestId("radio-r5")).toBeInTheDocument();
    });

    it("handles custom date range selection", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // Select custom option
        fireEvent.click(screen.getByTestId("radio-r5"));

        // The custom radio button should be selected
        expect(screen.getByTestId("radio-r5")).toBeInTheDocument();
    });

    it("handles export with loading state", () => {
        const mockStoreWithLoading = createMockStore({
            activityLogs: {
                exportActivityLogs: {
                    data: null,
                    loading: true,
                    error: null,
                    success: false,
                },
            },
        });

        renderWithProvider(
            <ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />,
            mockStoreWithLoading
        );

        // Open the drawer
        fireEvent.click(screen.getByTestId("button"));

        // The export button should show loading state
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    it("calls onExportSuccess callback when export is successful", async () => {
        const mockOnExportSuccess = jest.fn();
        const mockStoreWithSuccess = createMockStore({
            activityLogs: {
                exportActivityLogs: {
                    data: { success: true, message: "Export successful" },
                    loading: false,
                    error: null,
                    success: true,
                },
            },
        });

        renderWithProvider(<ExportActivityLog onExportSuccess={mockOnExportSuccess} />, mockStoreWithSuccess);

        // Wait for the useEffect to run and call the callback
        await waitFor(() => {
            expect(mockOnExportSuccess).toHaveBeenCalledTimes(1);
        });
    });

    it("resets form to defaults when export is successful", () => {
        const mockStoreWithSuccess = createMockStore({
            activityLogs: {
                exportActivityLogs: {
                    data: { success: true, message: "Export successful" },
                    loading: false,
                    error: null,
                    success: true,
                },
            },
        });

        renderWithProvider(<ExportActivityLog />, mockStoreWithSuccess);

        // Open the drawer to check form state
        fireEvent.click(screen.getByTestId("button"));

        // Check that form is reset to defaults
        expect(screen.getByTestId("radio-r2")).toBeChecked(); // "Last 3 months" radio button
        expect(screen.getByTestId("checkbox-csv")).toHaveTextContent("(checked)");
        expect(screen.getByTestId("checkbox-pdf")).toHaveTextContent("(unchecked)");
    });

    it("allows switching back from PDF to CSV", () => {
        renderWithProvider(<ExportActivityLog />);

        fireEvent.click(screen.getByTestId("button"));
        fireEvent.click(screen.getByTestId("checkbox-pdf")); // Select PDF
        fireEvent.click(screen.getByTestId("checkbox-csv")); // Switch back to CSV

        expect(screen.getByTestId("checkbox-csv")).toHaveTextContent("(checked)");
        expect(screen.getByTestId("checkbox-pdf")).toHaveTextContent("(unchecked)");
    });

    it("does not crash when onExportSuccess is not provided", async () => {
        const storeWithSuccess = createMockStore({
            activityLogs: {
                exportActivityLogs: {
                    data: { success: true },
                    loading: false,
                    error: null,
                    success: true,
                },
            },
        });

        renderWithProvider(<ExportActivityLog />, storeWithSuccess);

        await waitFor(() => {
            // no-op, just ensuring no crash or error is thrown
            expect(true).toBeTruthy();
        });
    });

    it("calls sendCatchFeedback on export error", () => {
        renderWithProvider(<ExportActivityLog data={mockData} columns={mockColumns} accounts={mockAccounts} />);

        fireEvent.click(screen.getByTestId("button"));
        fireEvent.click(screen.getAllByText("Export")[1]);

        // expect(sendCatchFeedback).toHaveBeenCalled();
    });
});
