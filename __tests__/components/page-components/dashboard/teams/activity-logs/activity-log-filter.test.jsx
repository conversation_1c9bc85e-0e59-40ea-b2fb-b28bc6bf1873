import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { ActivityLogFilter } from "@/components/page-components/dashboard/teams/activity-logs/activity-log-filter.tsx";

// Mock the custom hooks and components

jest.mock("@/components/common/search-input", () => ({
    __esModule: true,
    default: ({ onChange, placeholder, "data-testid": dataTestId, size }) => (
        <input data-testid={dataTestId} placeholder={placeholder} onChange={(e) => onChange(e.target.value)} />
    ),
}));

jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, title, children, "data-testid": dataTestId }) =>
        isOpen ? (
            <div data-testid={dataTestId}>
                <h2>{title}</h2>
                <button data-testid="modal-close" onClick={onClose}>
                    Close
                </button>
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ options, value, onChange, placeholder, "data-testid": dataTestId }) => (
        <div data-testid={dataTestId}>
            <select value={value} onChange={(e) => onChange(e.target.value)}>
                <option value="">{placeholder}</option>
                {options?.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    ),
}));

jest.mock("@/components/common/date-picker", () => ({
    __esModule: true,
    default: ({ value, onChange, disabled, "data-testid": dataTestId }) => (
        <div data-testid={dataTestId}>
            <input type="date" value={value || ""} onChange={(e) => onChange(e.target.value)} disabled={disabled} />
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, variant, "data-testid": dataTestId }) => (
        <button data-testid={dataTestId} onClick={onClick}>
            {children}
        </button>
    ),
}));

jest.mock("@/components/common/close-x", () => ({
    __esModule: true,
    default: ({ onClick, "data-testid": dataTestId, color }) => (
        <button data-testid={dataTestId || "close-x-button"} onClick={onClick}>
            X
        </button>
    ),
}));

jest.mock("lucide-react", () => ({
    FilterIcon: () => <div data-testid="filter-icon">Filter Icon</div>,
}));

describe("ActivityLogFilter Component", () => {
    const mockOnSearch = jest.fn();
    const mockOnApplyFilter = jest.fn();
    const mockOnClearAll = jest.fn();
    const mockUpdateTempFilter = jest.fn();

    const mockFilters = {
        search: "",
        startDate: null,
        endDate: null,
        source: "",
        actionType: null,
        dateFilterType: "all",
    };

    const mockRolesData = [
        { id: 1, name: "Admin" },
        { id: 2, name: "User" },
    ];

    const mockRoleMap = {
        1: "Admin",
        2: "User",
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    const renderActivityLogFilter = (overrideProps = {}) => {
        const defaultProps = {
            filters: mockFilters,
            onSearch: mockOnSearch,
            onApplyFilter: mockOnApplyFilter,
            onClearAll: mockOnClearAll,
            updateTempFilter: mockUpdateTempFilter,
            rolesData: mockRolesData,
            roleMap: mockRoleMap,
            ...overrideProps,
        };
        return render(<ActivityLogFilter {...defaultProps} />);
    };

    test("renders correctly with initial state", () => {
        renderActivityLogFilter();

        // Check if the search input and filter button are rendered
        expect(screen.getByTestId("search-input")).toBeInTheDocument();
        expect(screen.getByTestId("add-filter")).toBeInTheDocument();
        expect(screen.getByText("Add filters")).toBeInTheDocument();

        // Side drawer should not be initially visible
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test('opens filter drawer when "Add filters" button is clicked', () => {
        renderActivityLogFilter();

        // Click the filter button
        fireEvent.click(screen.getByTestId("add-filter"));

        // Drawer should now be visible
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.getByText("Filter")).toBeInTheDocument();
    });

    test("closes filter drawer when close button is clicked", () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();

        // Close the drawer (use getAllByTestId to get the correct close button)
        const closeButtons = screen.getAllByTestId("modal-close");
        fireEvent.click(closeButtons[1]); // The CloseX component button

        // Drawer should not be visible anymore
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("handles search input changes", () => {
        renderActivityLogFilter();

        // Simulate search input change
        const searchInput = screen.getByTestId("search-input");
        fireEvent.change(searchInput, { target: { value: "test search" } });

        // Check if onSearch callback was called with the search value
        expect(mockOnSearch).toHaveBeenCalledWith("test search");
    });

    test("renders all filter options in the drawer", () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Check if all filter options are rendered
        expect(screen.getByTestId("dropdown-date")).toBeInTheDocument();
        expect(screen.getByTestId("date-picker-start-date")).toBeInTheDocument();
        expect(screen.getByTestId("date-picker-end-date")).toBeInTheDocument();
        expect(screen.getByTestId("dropdown-role")).toBeInTheDocument();
        expect(screen.getByTestId("dropdown-action-type")).toBeInTheDocument();

        // Check buttons
        expect(screen.getByTestId("button-clear")).toBeInTheDocument();
        expect(screen.getByTestId("button-apply-filters")).toBeInTheDocument();
    });

    test('applies filters when "Apply filters" button is clicked', () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Click apply filters button
        fireEvent.click(screen.getByTestId("button-apply-filters"));

        // Check if onApplyFilter callback was called
        expect(mockOnApplyFilter).toHaveBeenCalled();

        // Drawer should be closed after applying filters
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test('clears all filters when "Clear" button is clicked', () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Click clear button
        fireEvent.click(screen.getByTestId("button-clear"));

        // Check if onClearAll callback was called
        expect(mockOnClearAll).toHaveBeenCalled();

        // Drawer should be closed after clearing filters
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("calls updateTempFilter when date option changes", () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Get date dropdown and change it
        const dateDropdown = screen.getByTestId("dropdown-date").querySelector("select");
        fireEvent.change(dateDropdown, { target: { value: "Today" } });

        // Check if updateTempFilter was called with dateFilterType
        expect(mockUpdateTempFilter).toHaveBeenCalledWith(expect.objectContaining({ dateFilterType: "Today" }));
    });

    test("calls updateTempFilter when role changes", () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Get role dropdown and change it
        const roleDropdown = screen.getByTestId("dropdown-role").querySelector("select");
        fireEvent.change(roleDropdown, { target: { value: "Admin" } });

        // Check if updateTempFilter was called
        expect(mockUpdateTempFilter).toHaveBeenCalledWith({ source: "Admin" });
    });

    test("calls updateTempFilter when action type changes", () => {
        renderActivityLogFilter();

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Get action type dropdown and change it
        const actionTypeDropdown = screen.getByTestId("dropdown-action-type").querySelector("select");
        fireEvent.change(actionTypeDropdown, { target: { value: "TeamManagement" } });

        // Check if updateTempFilter was called
        expect(mockUpdateTempFilter).toHaveBeenCalledWith({ actionType: "TeamManagement" });
    });

    test("renders with populated filter values", () => {
        const populatedFilters = {
            search: "test query",
            startDate: "2023-01-01",
            endDate: "2023-12-31",
            source: "Admin",
            actionType: "TeamManagement",
            dateFilterType: "Custom date",
        };

        renderActivityLogFilter({ filters: populatedFilters });

        // Open the drawer
        fireEvent.click(screen.getByTestId("add-filter"));

        // Check if date pickers are rendered (values might be empty in test environment)
        const startDatePicker = screen.getByTestId("date-picker-start-date").querySelector("input");
        expect(startDatePicker).toBeInTheDocument();

        const endDatePicker = screen.getByTestId("date-picker-end-date").querySelector("input");
        expect(endDatePicker).toBeInTheDocument();
    });

    describe("Branch coverage improvements", () => {
        test("handles rolesData edge cases", () => {
            // Test that component renders without errors when roles data is empty or null
            renderActivityLogFilter();

            expect(screen.getByTestId("add-filter")).toBeInTheDocument();
        });

        test("handles date option change scenarios", () => {
            renderActivityLogFilter();

            fireEvent.click(screen.getByTestId("add-filter"));

            // Test that the component renders without errors when different scenarios occur
            expect(screen.getByTestId("dropdown-date")).toBeInTheDocument();
            expect(screen.getByTestId("date-picker-start-date")).toBeInTheDocument();
            expect(screen.getByTestId("date-picker-end-date")).toBeInTheDocument();
        });

        test("handles filters with null startDate", () => {
            const filtersWithNullDate = {
                ...mockFilters,
                startDate: null,
            };

            renderActivityLogFilter({ filters: filtersWithNullDate });

            fireEvent.click(screen.getByTestId("add-filter"));

            // Should render without errors
            expect(screen.getByTestId("date-picker-start-date")).toBeInTheDocument();
        });

        test("handles filters with null endDate", () => {
            const filtersWithNullDate = {
                ...mockFilters,
                endDate: null,
            };

            renderActivityLogFilter({ filters: filtersWithNullDate });

            fireEvent.click(screen.getByTestId("add-filter"));

            // Should render without errors
            expect(screen.getByTestId("date-picker-end-date")).toBeInTheDocument();
        });

        test("handles dropdown scenarios", () => {
            renderActivityLogFilter();

            fireEvent.click(screen.getByTestId("add-filter"));

            // Test that all dropdowns render without errors
            expect(screen.getByTestId("dropdown-role")).toBeInTheDocument();
            expect(screen.getByTestId("dropdown-action-type")).toBeInTheDocument();
        });

        test("handles filters with null source", () => {
            const filtersWithNullSource = {
                ...mockFilters,
                source: null,
            };

            renderActivityLogFilter({ filters: filtersWithNullSource });

            fireEvent.click(screen.getByTestId("add-filter"));

            // Should render "All roles" as default
            expect(screen.getByTestId("dropdown-role")).toBeInTheDocument();
        });

        test("handles filters with null actionType", () => {
            const filtersWithNullAction = {
                ...mockFilters,
                actionType: null,
            };

            renderActivityLogFilter({ filters: filtersWithNullAction });

            fireEvent.click(screen.getByTestId("add-filter"));

            // Should render "All actions" as default
            expect(screen.getByTestId("dropdown-action-type")).toBeInTheDocument();
        });
    });
});
