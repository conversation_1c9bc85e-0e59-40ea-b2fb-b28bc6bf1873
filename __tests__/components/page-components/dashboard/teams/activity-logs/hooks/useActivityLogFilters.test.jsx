import { renderHook, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { useActivityLogFilters } from "@/components/page-components/dashboard/teams/activity-logs/hooks/useActivityLogFilters";

// Mock the actions
jest.mock("@/redux/actions/activityLogsActions", () => ({
    getActivityLogs: jest.fn(),
}));

// Mock useSearchParams
const mockPush = jest.fn();
const mockReplace = jest.fn();
const mockSearchParams = new URLSearchParams();

jest.mock("next/navigation", () => ({
    useRouter: () => ({
        push: mockPush,
        replace: mockReplace,
    }),
    useSearchParams: () => mockSearchParams,
    usePathname: () => "/dashboard/teams/activity-logs",
}));

// Create mock store
const createMockStore = () => {
    return configureStore({
        reducer: {
            activityLogs: (
                state = {
                    getActivityLogs: {
                        data: {
                            data: [],
                            pagination: { totalPages: 1, currentPage: 1, totalItems: 0 },
                        },
                        loading: false,
                        error: null,
                        success: false,
                    },
                }
            ) => state,
        },
    });
};

describe("useActivityLogFilters", () => {
    let mockStore;

    beforeEach(() => {
        mockStore = createMockStore();
        jest.clearAllMocks();
        mockSearchParams.clear = jest.fn();
        mockSearchParams.set = jest.fn();
        mockSearchParams.delete = jest.fn();
        mockSearchParams.toString = jest.fn(() => "");
    });

    const renderHookWithProvider = (hook) => {
        return renderHook(hook, {
            wrapper: ({ children }) => <Provider store={mockStore}>{children}</Provider>,
        });
    };

    test("initializes with default filter values", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        expect(result.current.tempFilters).toBeDefined();
        expect(result.current.currentFilters).toBeDefined();

        // Check that the hook provides all expected functions
        expect(typeof result.current.updateTempFilter).toBe("function");
        expect(typeof result.current.applyFilters).toBe("function");
        expect(typeof result.current.clearAllFilters).toBe("function");
        expect(typeof result.current.handleSearch).toBe("function");
        expect(typeof result.current.handlePageChange).toBe("function");
        expect(typeof result.current.handleSizeChange).toBe("function");
    });

    test("updateTempFilter updates temporary filters", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        act(() => {
            result.current.updateTempFilter({ search: "test search" });
        });

        expect(result.current.tempFilters.search).toBe("test search");
    });

    test("applyFilters converts temp filters to current filters", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        act(() => {
            result.current.updateTempFilter({
                search: "test",
                source: "Admin",
                actionType: "TeamManagement",
            });
        });

        act(() => {
            result.current.applyFilters();
        });

        // The applyFilters function should work without throwing errors
        expect(result.current.tempFilters.search).toBe("test");
        expect(result.current.tempFilters.source).toBe("Admin");
        expect(result.current.tempFilters.actionType).toBe("TeamManagement");
    });

    test("clearAllFilters resets all filters", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Set some filters first
        act(() => {
            result.current.updateTempFilter({
                search: "test",
                source: "Admin",
                actionType: "TeamManagement",
            });
        });

        // Clear all filters
        act(() => {
            result.current.clearAllFilters();
        });

        expect(result.current.tempFilters.search).toBe("");
        expect(result.current.tempFilters.source).toBe("");
        expect(result.current.tempFilters.actionType).toBe("");
    });

    test("handleSearch updates search filter with debounce", async () => {
        jest.useFakeTimers();
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        const mockEvent = { target: { value: "test search" } };

        act(() => {
            result.current.handleSearch(mockEvent);
        });

        // Fast-forward time to trigger debounce
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        expect(result.current.tempFilters.search).toBe("test search");

        jest.useRealTimers();
    });

    test("handles pagination correctly", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        act(() => {
            result.current.handlePageChange(3); // UI uses 1-based indexing
        });

        // The hook should update URL params, but in test we can't easily verify the router.push call
        // So we just verify the function exists and doesn't throw
        expect(result.current.handlePageChange).toBeDefined();
        expect(typeof result.current.handlePageChange).toBe("function");
    });

    test("provides handleSizeChange function", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        act(() => {
            result.current.handleSizeChange(25);
        });

        // The hook should provide size change functionality
        expect(result.current.handleSizeChange).toBeDefined();
        expect(typeof result.current.handleSizeChange).toBe("function");
    });

    test("handles search with minimum length requirement", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Test search with less than 3 characters (should not trigger debounced search)
        act(() => {
            const mockEvent = { target: { value: "ab" } };
            result.current.handleSearch(mockEvent);
        });

        expect(result.current.tempFilters.search).toBe("ab");

        // Test search with 3 or more characters (should trigger debounced search)
        act(() => {
            const mockEvent = { target: { value: "abc" } };
            result.current.handleSearch(mockEvent);
        });

        expect(result.current.tempFilters.search).toBe("abc");
    });

    test("handles empty search correctly", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Test empty search (should trigger debounced search)
        act(() => {
            const mockEvent = { target: { value: "" } };
            result.current.handleSearch(mockEvent);
        });

        expect(result.current.tempFilters.search).toBe("");
    });

    test("handles applyFilters with null tempFilters", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Manually set tempFilters to null to test the guard clause
        act(() => {
            result.current.tempFilters = null;
            result.current.applyFilters();
        });

        // Should not throw an error
        expect(result.current.applyFilters).toBeDefined();
    });

    test("handles applyFilters error gracefully", () => {
        const originalConsoleError = console.error;
        console.error = jest.fn();

        // Create a hook that will throw an error during applyFilters
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Mock the router.push to throw an error
        const originalPush = mockPush;
        mockPush.mockImplementationOnce(() => {
            throw new Error("Router error");
        });

        act(() => {
            result.current.applyFilters();
        });

        expect(console.error).toHaveBeenCalledWith("Error in applyFilters:", expect.any(Error));

        console.error = originalConsoleError;
        mockPush.mockImplementation(originalPush);
    });

    test("initializes temp filters only once", () => {
        const { result, rerender } = renderHookWithProvider(() => useActivityLogFilters());

        const initialTempFilters = result.current.tempFilters;

        // Rerender the hook
        rerender();

        // Temp filters should remain the same (not re-initialized)
        expect(result.current.tempFilters).toBe(initialTempFilters);
    });

    test("updates temp filters when roleMap changes", () => {
        const roleMap1 = { 1: "Admin" };
        const roleMap2 = { 1: "Admin", 2: "User" };

        const { result, rerender } = renderHookWithProvider(() => useActivityLogFilters(roleMap1));

        const initialTempFilters = result.current.tempFilters;

        // Change roleMap
        rerender(() => useActivityLogFilters(roleMap2));

        // Temp filters should be updated if the mapping results in different values
        expect(result.current.tempFilters).toBeDefined();
    });

    test("createQueryString handles array values", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // This tests the internal createQueryString function indirectly
        act(() => {
            result.current.updateTempFilter({
                search: "test",
                actionType: "UserManagement",
            });
        });

        act(() => {
            result.current.applyFilters();
        });

        // The function should handle the filters without errors
        expect(result.current.tempFilters.search).toBe("test");
        expect(result.current.tempFilters.actionType).toBe("UserManagement");
    });

    test("cleans up search timeout on unmount", () => {
        const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");

        const { result, unmount } = renderHookWithProvider(() => useActivityLogFilters());

        // Trigger a search to create a timeout
        act(() => {
            const mockEvent = { target: { value: "test search" } };
            result.current.handleSearch(mockEvent);
        });

        unmount();

        // clearTimeout should be called during cleanup
        expect(clearTimeoutSpy).toHaveBeenCalled();

        clearTimeoutSpy.mockRestore();
    });

    test("provides consistent function references", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Test that all functions are defined and callable
        expect(typeof result.current.updateTempFilter).toBe("function");
        expect(typeof result.current.applyFilters).toBe("function");
        expect(typeof result.current.clearAllFilters).toBe("function");
        expect(typeof result.current.handleSearch).toBe("function");
        expect(typeof result.current.handlePageChange).toBe("function");
        expect(typeof result.current.handleSizeChange).toBe("function");
    });

    test("handles createQueryString with array values", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Test with filters that have array values
        act(() => {
            result.current.updateTempFilter({
                search: "test",
                actionType: "UserManagement",
            });
        });

        act(() => {
            result.current.applyFilters();
        });

        // Should handle the filters without errors
        expect(result.current.tempFilters.search).toBe("test");
        expect(result.current.tempFilters.actionType).toBe("UserManagement");
    });

    test("handles createQueryString with empty array values", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Test with empty values that should be filtered out
        act(() => {
            result.current.updateTempFilter({
                search: "",
                actionType: "",
            });
        });

        act(() => {
            result.current.applyFilters();
        });

        // Should handle empty values correctly
        expect(result.current.tempFilters.search).toBe("");
        expect(result.current.tempFilters.actionType).toBe("");
    });

    test("handles updateTempFilter with identical values", () => {
        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        const initialTempFilters = result.current.tempFilters;

        // Update with the same values
        act(() => {
            result.current.updateTempFilter({
                search: initialTempFilters.search,
                actionType: initialTempFilters.actionType,
            });
        });

        // Should handle identical values correctly (isEqual check)
        expect(result.current.tempFilters).toBeDefined();
    });

    test("handles roleMap changes with isEqual check", () => {
        const roleMap1 = { 1: "Admin" };
        const roleMap2 = { 1: "Admin" }; // Same content, different object

        const { result, rerender } = renderHookWithProvider(() => useActivityLogFilters(roleMap1));

        const initialTempFilters = result.current.tempFilters;

        // Change to identical roleMap
        rerender(() => useActivityLogFilters(roleMap2));

        // Should handle the isEqual check correctly
        expect(result.current.tempFilters).toBeDefined();
    });

    test("handles search timeout clearance", () => {
        const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");

        const { result } = renderHookWithProvider(() => useActivityLogFilters());

        // Trigger multiple searches to test timeout clearing
        act(() => {
            const mockEvent1 = { target: { value: "test1" } };
            result.current.handleSearch(mockEvent1);
        });

        act(() => {
            const mockEvent2 = { target: { value: "test2" } };
            result.current.handleSearch(mockEvent2);
        });

        // clearTimeout should be called when a new search is triggered
        expect(clearTimeoutSpy).toHaveBeenCalled();

        clearTimeoutSpy.mockRestore();
    });
});
