import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { CurrentFilterItems } from "@/components/page-components/dashboard/teams/activity-logs/current-filter-items";

// Mock the Badge component
jest.mock("@/components/common/badge", () => {
    return function Badge({ text, color, size }) {
        return (
            <div data-testid="filter-badge" data-color={color} data-size={size}>
                {text}
            </div>
        );
    };
});

// Mock the Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, variant, size, leftIcon }) => (
        <button data-testid="clear-all-button" onClick={onClick} data-variant={variant} data-size={size}>
            {leftIcon}
            {children}
        </button>
    ),
}));

// Mock lucide-react
jest.mock("lucide-react", () => ({
    X: ({ size }) => (
        <span data-testid="x-icon" data-size={size}>
            X
        </span>
    ),
}));

describe("CurrentFilterItems", () => {
    const mockOnClearAll = jest.fn();
    const mockRoleMap = {
        1: "Admin",
        2: "User",
        3: "Manager",
    };

    const defaultProps = {
        onClearAll: mockOnClearAll,
        roleMap: mockRoleMap,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("when no filters are active", () => {
        test("renders nothing when all filters are empty", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
                sortBy: "timestamp",
                sortDirection: "DESC",
            };

            const { container } = render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(container.firstChild).toBeNull();
        });

        test("renders nothing when filters contain only pagination and sorting", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 2,
                size: 20,
                sortBy: "action",
                sortDirection: "ASC",
            };

            const { container } = render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(container.firstChild).toBeNull();
        });
    });

    describe("when filters are active", () => {
        test("renders search filter badge", () => {
            const currentFilters = {
                search: "test search",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText('Search: "test search"')).toBeInTheDocument();
            expect(screen.getByTestId("clear-all-button")).toBeInTheDocument();
        });

        test("renders date range filter badge with both dates", () => {
            const currentFilters = {
                search: "",
                startDate: "2023-01-01",
                endDate: "2023-01-31",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("01 Jan 2023 - 31 Jan 2023")).toBeInTheDocument();
        });

        test("renders date filter badge with only start date", () => {
            const currentFilters = {
                search: "",
                startDate: "2023-01-01",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("From 01 Jan 2023")).toBeInTheDocument();
        });

        test("renders date filter badge with only end date", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "2023-01-31",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("Until 31 Jan 2023")).toBeInTheDocument();
        });

        test("renders Super Admin role filter", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 0,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("Role: Super Admin")).toBeInTheDocument();
        });

        test("renders role filter with role name from roleMap", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 1,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("Role: Admin")).toBeInTheDocument();
        });

        test("renders role filter with roleId when role name not found", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 999,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("Role: 999")).toBeInTheDocument();
        });

        test("renders role filter with roleId when no roleMap provided", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: 1,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} onClearAll={mockOnClearAll} />);

            expect(screen.getByText("Role: 1")).toBeInTheDocument();
        });

        test("renders action type filter badge", () => {
            const currentFilters = {
                search: "",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "LOGIN",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText("Action: LOGIN")).toBeInTheDocument();
        });

        test("renders multiple filter badges", () => {
            const currentFilters = {
                search: "test",
                startDate: "2023-01-01",
                endDate: "2023-01-31",
                roleId: 1,
                actionTypes: "LOGIN",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            expect(screen.getByText('Search: "test"')).toBeInTheDocument();
            expect(screen.getByText("01 Jan 2023 - 31 Jan 2023")).toBeInTheDocument();
            expect(screen.getByText("Role: Admin")).toBeInTheDocument();
            expect(screen.getByText("Action: LOGIN")).toBeInTheDocument();
            expect(screen.getByTestId("clear-all-button")).toBeInTheDocument();
        });
    });

    describe("clear all functionality", () => {
        test("calls onClearAll when clear all button is clicked", () => {
            const currentFilters = {
                search: "test",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            const clearButton = screen.getByTestId("clear-all-button");
            fireEvent.click(clearButton);

            expect(mockOnClearAll).toHaveBeenCalledTimes(1);
        });

        test("clear all button has correct styling props", () => {
            const currentFilters = {
                search: "test",
                startDate: "",
                endDate: "",
                roleId: null,
                actionTypes: "",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            const clearButton = screen.getByTestId("clear-all-button");
            expect(clearButton).toHaveAttribute("data-variant", "text-destructive");
            expect(clearButton).toHaveAttribute("data-size", "sm");
            expect(screen.getByTestId("x-icon")).toBeInTheDocument();
        });
    });

    describe("badge styling", () => {
        test("all badges have correct styling props", () => {
            const currentFilters = {
                search: "test",
                startDate: "2023-01-01",
                endDate: "2023-01-31",
                roleId: 1,
                actionTypes: "LOGIN",
                page: 0,
                size: 10,
            };

            render(<CurrentFilterItems currentFilters={currentFilters} {...defaultProps} />);

            const badges = screen.getAllByTestId("filter-badge");
            badges.forEach((badge) => {
                expect(badge).toHaveAttribute("data-color", "neutral");
                expect(badge).toHaveAttribute("data-size", "xl");
            });
        });
    });
});
