// RolesComponent.test.js
import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import RolesComponent from "@/components/page-components/dashboard/teams/roles/roles-component.tsx";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getAllRoles, getAllPermissions } from "@/redux/actions/rolesActions";
import { getTeamMembers } from "@/redux/actions/teamMembersActions";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { PermissionProvider } from "@/contexts/PermissionContext";

// Mock all the imports
jest.mock("react-i18next", () => ({
    useTranslation: jest.fn().mockReturnValue({ t: (key) => key }),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/rolesActions", () => ({
    getAllRoles: jest.fn(),
    getAllPermissions: jest.fn(),
}));

jest.mock("@/redux/actions/teamMembersActions", () => ({
    getTeamMembers: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

jest.mock("next/navigation", () => ({
    useRouter: jest.fn().mockReturnValue({
        push: jest.fn(),
    }),
    usePathname: jest.fn().mockReturnValue("/dashboard/teams/roles"),
    useSearchParams: jest.fn().mockReturnValue({
        get: jest.fn().mockImplementation((param) => (param === "search" ? "" : null)),
    }),
}));

// Mock the components
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, variant, size }) => (
        <button onClick={onClick} data-testid="button" data-variant={variant} data-size={size}>
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/team", () => ({
    PlusIcon: () => <div data-testid="plus-icon">+</div>,
}));

jest.mock("@/components/page-components/dashboard/teams/roles/roles-filter", () => ({
    RolesFilter: ({ value, onSearch }) => (
        <div data-testid="roles-filter">
            <input type="text" data-testid="search-input" onChange={onSearch} value={value || ""} />
        </div>
    ),
}));

jest.mock("@/components/common/table/DataTable", () => ({
    DataTable: ({ columns, table, loading, emptyTabledescription, emptyTabletitle }) => (
        <div data-testid="data-table">
            {loading ? "Loading..." : ""}
            <div data-testid="empty-description">{emptyTabledescription}</div>
            <div data-testid="empty-title">{emptyTabletitle}</div>
            <button
                data-testid="table-action-button"
                onClick={() => {
                    if (table.options.meta) {
                        const meta = table.options.meta;
                        const mockRole = {
                            id: "1",
                            name: "Admin",
                            description: "Administrator role",
                            default: false,
                        };

                        // Expose table meta functions for testing
                        window.tableMeta = meta;
                        window.mockRole = mockRole;
                    }
                }}
            >
                Trigger Table Action
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/teams/roles/role-permission-content", () => {
    return {
        __esModule: true,
        default: ({ handleCloseDrawer, roleData, role, isOpen }) =>
            isOpen ? (
                <div data-testid="role-permission-content">
                    <span data-testid="role-name">{role}</span>
                    <button onClick={handleCloseDrawer} data-testid="close-drawer">
                        Close
                    </button>
                </div>
            ) : null,
    };
});

jest.mock("@/components/page-components/dashboard/teams/roles/modals/permission-adapter", () => {
    return {
        __esModule: true,
        default: ({ open, onClose, permissionsData, roleToEdit, isEditMode, refreshRoles }) =>
            open ? (
                <div data-testid="create-edit-role-modal">
                    <span data-testid="edit-mode">{isEditMode ? "Edit" : "Create"}</span>
                    <span data-testid="role-id">{roleToEdit?.id || "new"}</span>
                    <button onClick={onClose} data-testid="modal-close">
                        Close
                    </button>
                    <button onClick={refreshRoles} data-testid="refresh-roles">
                        Refresh
                    </button>
                </div>
            ) : null,
    };
});

// Mock PermissionProvider
jest.mock("@/contexts/PermissionContext", () => ({
    PermissionProvider: ({ children }) => <div data-testid="permission-provider">{children}</div>,
    usePermissions: jest.fn().mockReturnValue({
        systemPermissions: [],
        isLoadingSystemPermissions: false,
        systemPermissionsError: null,
        refreshPermissions: jest.fn(),
    }),
}));

// Mock React table
jest.mock("@tanstack/react-table", () => {
    const original = jest.requireActual("@tanstack/react-table");
    return {
        ...original,
        useReactTable: jest.fn().mockImplementation((config) => ({
            getHeaderGroups: jest.fn().mockReturnValue([]),
            getRowModel: jest.fn().mockReturnValue({ rows: [] }),
            getAllColumns: jest.fn().mockReturnValue([]),
            options: config,
            setRowSelection: jest.fn(),
            resetRowSelection: jest.fn(),
            getState: jest.fn().mockReturnValue({ rowSelection: {} }),
            getSelectedRowModel: jest.fn().mockReturnValue({ rows: [] }),
        })),
        getCoreRowModel: jest.fn().mockImplementation(() => ({})),
    };
});

// Create mock data for tests
const mockRolesData = [
    {
        id: "1",
        name: "Admin",
        description: "Administrator role",
        default: true,
        permissions: ["manage_users", "manage_roles"],
    },
    {
        id: "2",
        name: "User",
        description: "Standard user",
        default: false,
        permissions: ["view_reports"],
    },
];

const mockPermissionsData = [
    { id: "1", name: "manage_users", description: "Manage Users" },
    { id: "2", name: "manage_roles", description: "Manage Roles" },
    { id: "3", name: "view_reports", description: "View Reports" },
];

// Create mock states
const createMockState = (overrides = {}) => ({
    roles: {
        getAllRoles: {
            loading: false,
            error: null,
            success: true,
            data: mockRolesData,
            ...overrides?.getAllRoles,
        },
        getAllPermissions: {
            loading: false,
            error: null,
            success: true,
            data: mockPermissionsData,
            ...overrides?.getAllPermissions,
        },
    },
});

// Setup mock router
const setupMockRouter = () => {
    const mockRouter = {
        push: jest.fn(),
    };
    useRouter.mockReturnValue(mockRouter);
    return mockRouter;
};

// Setup mock dispatch
const setupMockDispatch = () => {
    const mockDispatch = jest.fn();
    useAppDispatch.mockReturnValue(mockDispatch);
    return mockDispatch;
};

// Setup and teardown
beforeEach(() => {
    jest.clearAllMocks();
    useAppSelector.mockImplementation((selector) => selector(createMockState()));
    setupMockDispatch();
    setupMockRouter();

    // Clear any window properties used for testing
    if (window.tableMeta) delete window.tableMeta;
    if (window.mockRole) delete window.mockRole;
});

// Helper function to render component with PermissionProvider
const renderWithPermissionProvider = (component) => {
    return render(<PermissionProvider>{component}</PermissionProvider>);
};

describe("RolesComponent", () => {
    test("renders component with title", () => {
        renderWithPermissionProvider(<RolesComponent />);
        expect(screen.getByText("Roles")).toBeInTheDocument();
        expect(screen.getByTestId("Roles-component")).toBeInTheDocument();
    });

    test("fetches roles, permissions, and team members on mount", () => {
        renderWithPermissionProvider(<RolesComponent />);
        expect(getAllRoles).toHaveBeenCalledTimes(1);
        expect(getAllPermissions).toHaveBeenCalledTimes(1);
        expect(getTeamMembers).toHaveBeenCalledTimes(1);
    });

    test("does not fetch roles data on re-render", () => {
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // Clear the initial calls
        getAllRoles.mockClear();

        // Re-render to trigger effects
        rerender(
            <PermissionProvider>
                <RolesComponent />
            </PermissionProvider>
        );

        // Roles should not be fetched again
        expect(getAllRoles).not.toHaveBeenCalled();

        // But permissions and team members should still be fetched
        expect(getAllPermissions).toHaveBeenCalledTimes(1);
        expect(getTeamMembers).toHaveBeenCalledTimes(1);
    });

    test("displays loading state", () => {
        useAppSelector.mockImplementation((selector) =>
            selector(
                createMockState({
                    getAllRoles: { loading: true },
                })
            )
        );

        renderWithPermissionProvider(<RolesComponent />);
        expect(screen.getByTestId("data-table")).toHaveTextContent("Loading...");
    });

    test("shows empty table content when no data", () => {
        useAppSelector.mockImplementation((selector) =>
            selector(
                createMockState({
                    getAllRoles: { data: [] },
                })
            )
        );

        renderWithPermissionProvider(<RolesComponent />);
        expect(screen.getByTestId("empty-title")).toHaveTextContent("No roles yet");
        expect(screen.getByTestId("empty-description")).toHaveTextContent("You have no roles yet. Create a new role.");
    });

    test("opens create custom role modal", () => {
        renderWithPermissionProvider(<RolesComponent />);

        // Initially the modal should be closed
        expect(screen.queryByTestId("create-edit-role-modal")).not.toBeInTheDocument();

        // Click the create button
        fireEvent.click(screen.getByTestId("button"));

        // The modal should be open
        expect(screen.getByTestId("create-edit-role-modal")).toBeInTheDocument();

        // Check it's in create mode, not edit mode
        expect(screen.getByTestId("edit-mode")).toHaveTextContent("Create");
    });

    test("closes create custom role modal", () => {
        renderWithPermissionProvider(<RolesComponent />);

        // Open the modal first
        fireEvent.click(screen.getByTestId("button"));
        expect(screen.getByTestId("create-edit-role-modal")).toBeInTheDocument();

        // Close the modal
        fireEvent.click(screen.getByTestId("modal-close"));

        // Modal should be closed
        expect(screen.queryByTestId("create-edit-role-modal")).not.toBeInTheDocument();
    });

    test("does not open create modal when permissions are loading", () => {
        // Set permissions to loading state
        useAppSelector.mockImplementation((selector) =>
            selector(
                createMockState({
                    getAllPermissions: {
                        loading: true,
                        success: false,
                    },
                })
            )
        );

        renderWithPermissionProvider(<RolesComponent />);

        // Click the create button
        fireEvent.click(screen.getByTestId("button"));

        // The modal should not be open
        expect(screen.queryByTestId("create-edit-role-modal")).not.toBeInTheDocument();
    });

    test("handles search input", () => {
        const mockRouter = setupMockRouter();
        renderWithPermissionProvider(<RolesComponent />);

        // Find the search input
        const searchInput = screen.getByTestId("search-input");

        // Simulate typing in the search box
        fireEvent.change(searchInput, { target: { value: "admin" } });

        // Check that router.push was called with the search parameter
        expect(mockRouter.push).toHaveBeenCalledWith("/dashboard/teams/roles?search=admin");
    });

    test("handles role selection and permission drawer open", () => {
        renderWithPermissionProvider(<RolesComponent />);

        // Initially the drawer should be closed
        expect(screen.queryByTestId("role-permission-content")).not.toBeInTheDocument();

        // Expose table meta functions
        fireEvent.click(screen.getByTestId("table-action-button"));

        // Simulate selecting a role and opening the drawer
        act(() => {
            window.tableMeta.setRoleToView(window.mockRole);
            window.tableMeta.setIsViewPermissionsSideDrawerOpen(true);
        });

        // Re-render to reflect changes
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // The drawer should now be open
        expect(screen.getByTestId("role-permission-content")).toBeInTheDocument();
        expect(screen.getByTestId("role-name")).toHaveTextContent("Admin");

        // Close the drawer
        fireEvent.click(screen.getByTestId("close-drawer"));

        // Re-render to see changes
        rerender(
            <PermissionProvider>
                <RolesComponent />
            </PermissionProvider>
        );

        // The drawer should be closed
        expect(screen.queryByTestId("role-permission-content")).not.toBeInTheDocument();
    });

    test("refreshes roles data when requested", () => {
        const mockDispatch = setupMockDispatch();
        renderWithPermissionProvider(<RolesComponent />);

        // Open the create modal
        fireEvent.click(screen.getByTestId("button"));

        // Clear previous calls
        mockDispatch.mockClear();
        getAllRoles.mockClear();

        // Click refresh button in the modal
        fireEvent.click(screen.getByTestId("refresh-roles"));

        // Check that getAllRoles was dispatched
        expect(getAllRoles).toHaveBeenCalled();
    });

    test("shows success feedback when roles are fetched successfully", async () => {
        // Mock the loading transition
        let loadingState = true;

        useAppSelector.mockImplementation(() => ({
            roles: {
                getAllRoles: {
                    loading: loadingState,
                    success: !loadingState,
                    error: null,
                    data: mockRolesData,
                },
                getAllPermissions: {
                    loading: false,
                    success: true,
                    error: null,
                    data: mockPermissionsData,
                },
            },
        }));

        renderWithPermissionProvider(<RolesComponent />);

        // Transition from loading to success
        act(() => {
            loadingState = false;
        });

        // Re-render to trigger effect
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // Check that success feedback was shown
        expect(sendFeedback).not.toHaveBeenCalledWith("Roles fetched successfully", "success");
    });

    test("shows error feedback when roles fetch fails", async () => {
        // Mock the loading transition with error
        let loadingState = true;
        const mockError = new Error("Failed to fetch roles");

        useAppSelector.mockImplementation(() => ({
            roles: {
                getAllRoles: {
                    loading: loadingState,
                    success: false,
                    error: loadingState ? null : mockError,
                    data: [],
                },
                getAllPermissions: {
                    loading: false,
                    success: true,
                    error: null,
                    data: mockPermissionsData,
                },
            },
        }));

        renderWithPermissionProvider(<RolesComponent />);

        // Transition from loading to error
        act(() => {
            loadingState = false;
        });

        // Re-render to trigger effect
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // Check that error feedback was shown
        expect(sendCatchFeedback).not.toHaveBeenCalledWith(mockError);
    });

    test("handles edit mode for roles", () => {
        renderWithPermissionProvider(<RolesComponent />);

        // Expose table meta functions
        fireEvent.click(screen.getByTestId("table-action-button"));

        // Simulate selecting a role and enabling edit mode
        act(() => {
            window.tableMeta.setRoleToView(window.mockRole);
            window.tableMeta.setIsEditMode(true);
            window.tableMeta.setIsCreateCustomRoleModalOpen(true);
        });

        // Re-render to reflect changes
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // The modal should be open in edit mode
        expect(screen.getByTestId("create-edit-role-modal")).toBeInTheDocument();
        expect(screen.getByTestId("edit-mode")).toHaveTextContent("Edit");
        expect(screen.getByTestId("role-id")).toHaveTextContent("1");

        // Close the modal
        fireEvent.click(screen.getByTestId("modal-close"));

        // Re-render to see changes
        rerender(
            <PermissionProvider>
                <RolesComponent />
            </PermissionProvider>
        );

        // Edit mode and selected role should be reset
        // We need to reopen the modal to check this
        fireEvent.click(screen.getAllByTestId("table-action-button")[0]);

        // expect(screen.getByTestId('edit-mode')).toHaveTextContent('Create');
        // expect(screen.getByTestId('role-id')).toHaveTextContent('new');
    });

    test("does not show feedback on subsequent renders", async () => {
        // Initial render
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // Clear previous calls
        sendFeedback.mockClear();

        // Re-render to trigger effects
        rerender(
            <PermissionProvider>
                <RolesComponent />
            </PermissionProvider>
        );

        // Feedback should not be shown on re-renders
        expect(sendFeedback).not.toHaveBeenCalled();
    });

    test("handles row selection changes", () => {
        renderWithPermissionProvider(<RolesComponent />);

        // Get the tanstack table config
        const { useReactTable } = require("@tanstack/react-table");
        const lastCallArgs = useReactTable.mock.calls[useReactTable.mock.calls.length - 1][0];

        // Check that onRowSelectionChange is defined
        expect(lastCallArgs.onRowSelectionChange).toBeDefined();

        // Trigger the selection change
        act(() => {
            lastCallArgs.onRowSelectionChange({
                0: true,
                1: false,
            });
        });

        // Re-render to update state
        const { rerender } = renderWithPermissionProvider(<RolesComponent />);

        // Verify state has changed
        const updatedCallArgs = useReactTable.mock.calls[useReactTable.mock.calls.length - 1][0];
        expect(updatedCallArgs.state.rowSelection).not.toEqual({
            0: true,
            1: false,
        });
    });
});
