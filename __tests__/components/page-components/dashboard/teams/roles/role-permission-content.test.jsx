import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import thunk from "redux-thunk";
import "@testing-library/jest-dom";
import { PermissionProvider } from "@/contexts/PermissionContext";
import RolePermissionContent from "@/components/page-components/dashboard/teams/roles/role-permission-content.tsx";

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => cb());

// Mock the entire module to avoid importing the actual component
// which would try to import the real Redux store
jest.mock("@/redux/index", () => ({
    store: {
        getState: jest.fn(() => ({
            roles: {
                getAllPermissions: {
                    loading: false,
                    data: null,
                },
            },
        })),
    },
}));

// Now we can safely import the component

// Mock the dependencies
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, children }) => (isOpen ? <div data-testid="side-drawer">{children}</div> : null),
}));

jest.mock("@/components/icons/team", () => ({
    RoleInfo: () => <svg data-testid="role-info-icon" />,
    RolePermission: () => <svg data-testid="role-permission-icon" />,
}));

// Mock the redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
    useAppSelector: jest.fn((selector) => selector(mockState)),
}));

// Mock the roles actions
jest.mock("@/redux/actions/rolesActions", () => ({
    getAllPermissions: jest.fn(() => ({ type: "GET_ALL_PERMISSIONS" })),
    getAllRoles: { pending: "roles/getAllRoles/pending" },
}));

// Mock PermissionProvider
jest.mock("@/contexts/PermissionContext", () => ({
    PermissionProvider: ({ children }) => <div data-testid="permission-provider">{children}</div>,
    usePermissions: jest.fn().mockReturnValue({
        systemPermissions: [],
        isLoadingSystemPermissions: false,
        systemPermissionsError: null,
        refreshPermissions: jest.fn(),
    }),
}));

// Setup mock store
const middlewares = [thunk];
const mockStore = configureStore(middlewares);

// Mock state for the store
const mockState = {
    roles: {
        getAllPermissions: {
            data: [
                { id: 1, name: "Create Users", description: "Can create users", appModule: "User Management" },
                { id: 2, name: "Delete Users", description: "Can delete users", appModule: "User Management" },
                { id: 3, name: "View Accounts", description: "Can view accounts", appModule: "Accounts" },
            ],
            success: true,
            loading: false,
            error: null,
        },
    },
};

describe("RolePermissionContent Component", () => {
    let mockHandleCloseDrawer;
    let mockRoleData;
    let store;

    beforeEach(() => {
        mockHandleCloseDrawer = jest.fn();

        mockRoleData = {
            id: 1,
            name: "Admin",
            permissions: [1, 2], // Changed to match the expected format in the component
        };

        // Create a fresh store for each test
        store = mockStore(mockState);
    });

    // Helper function to render with Redux Provider
    const renderWithProvider = (ui) => {
        return render(
            <Provider store={store}>
                <PermissionProvider>{ui}</PermissionProvider>
            </Provider>
        );
    };

    test("renders the role from the 'role' prop when roleData is not provided", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} role="Manager" />
        );

        expect(screen.getByText("Manager")).toBeInTheDocument();
    });

    test("renders correctly when isOpen is true", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.getByTestId("role-permission-content")).toBeInTheDocument();
    });

    test("does not render when isOpen is false", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={false} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("displays the correct role title", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        expect(screen.getByText("Admin")).toBeInTheDocument();
    });

    test("closes the drawer when the close button is clicked", () => {
        // Create a new mock function for this test
        const handleCloseDrawer = jest.fn();

        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={handleCloseDrawer} roleData={mockRoleData} />
        );

        // Find the close button and click it
        const closeButton = screen.getByRole("button", { name: /close/i });
        fireEvent.click(closeButton);

        // Since we mocked requestAnimationFrame to immediately execute the callback,
        // handleCloseDrawer should have been called
        expect(handleCloseDrawer).toHaveBeenCalledTimes(1);
    });

    test("renders all permission sections", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        expect(screen.getByText("Role permissions")).toBeInTheDocument();
        // Check for the module name instead of individual permissions
        expect(screen.getByText("User Management")).toBeInTheDocument();
    });

    test("renders permission icons", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        // Only the role-permission-icon is used in the current implementation
        expect(screen.getByTestId("role-permission-icon")).toBeInTheDocument();
    });

    test("renders permission items", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        // Check for the individual permissions
        expect(screen.getByText("Create Users")).toBeInTheDocument();
        expect(screen.getByText("Delete Users")).toBeInTheDocument();
        // We can also check for the View Accounts permission
        expect(screen.queryByText("View Accounts")).not.toBeInTheDocument(); // This one isn't rendered because it's not in the role's permissions
    });

    test("renders role description", () => {
        renderWithProvider(
            <RolePermissionContent isOpen={true} handleCloseDrawer={mockHandleCloseDrawer} roleData={mockRoleData} />
        );

        // Check for the role description instead of the invitation message
        expect(
            screen.getByText("This role can manage team members, approve payments, and modify settings")
        ).toBeInTheDocument();
    });
});
