/* eslint-disable @typescript-eslint/no-require-imports */
import AddRecipientModal from "@/components/page-components/dashboard/send-money/local/multiple-payment-form/modals/add-recipient-modal";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { fetchRecipientDetails, getLocalRecipientAction } from "@/redux/actions/recipients/local-recipient";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { recipientActions } from "@/redux/slices/recipientsSlice";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { useFormik } from "formik";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// Mock the required modules
jest.mock("@/redux/actions/recipients/local-recipient", () => ({
    addLocalRecipient: jest.fn(),
    fetchRecipientDetails: jest.fn(),
    getLocalRecipientAction: jest.fn(),
}));

jest.mock("formik", () => ({
    useFormik: jest.fn(),
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ name, label, options, formik, value, isLoading }) => (
        <div>
            <select
                aria-label={label}
                name={name}
                data-testid={`dropdown-${name}`}
                value={value?.value || ""}
                onChange={(e) => formik.setFieldValue && formik.setFieldValue(name, e.target.value)}
                disabled={isLoading}
            >
                {options?.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
            {isLoading && <div data-testid="dropdown-loading">Loading...</div>}
        </div>
    ),
}));

jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({ name, label, placeholder, disabled, formik, className }) => (
        <div className={className}>
            <label htmlFor={name}>{label}</label>
            <input
                id={name}
                name={name}
                data-testid={`input-${name}`}
                placeholder={placeholder}
                disabled={disabled}
                value={formik.values[name] || ""}
                onChange={(e) => formik.setFieldValue && formik.setFieldValue(name, e.target.value)}
            />
            {formik.errors[name] && formik.touched[name] && (
                <div data-testid={`error-${name}`}>{formik.errors[name]}</div>
            )}
        </div>
    ),
}));

jest.mock("@/components/common/checkbox-label", () => ({
    __esModule: true,
    CheckboxLabel: ({ checked, onChange, label }) => (
        <label>
            <input
                type="checkbox"
                checked={checked}
                onChange={(e) => onChange(e.target.checked)}
                data-testid="save-recipient-checkbox"
            />
            {label}
        </label>
    ),
}));

jest.mock("@/components/common/loading-indicator", () => ({
    __esModule: true,
    default: ({ size }) => (
        <div data-testid="loading-indicator" style={{ width: size, height: size }}>
            Loading...
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, loading, variant, type }) => (
        <button
            onClick={onClick}
            disabled={disabled || loading}
            data-testid={`button-${children.toLowerCase().replace(/\s+/g, "-")}`}
            data-variant={variant}
            data-loading={loading}
            type={type}
        >
            {loading ? "Loading..." : children}
        </button>
    ),
}));

jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, children }) => (
        <div data-testid="side-drawer" data-open={isOpen}>
            {isOpen && children}
        </div>
    ),
}));

jest.mock("@/components/common/close-x", () => ({
    __esModule: true,
    default: ({ onClick, color }) => (
        <button onClick={onClick} data-testid="modal-close" style={{ color }}>
            ×
        </button>
    ),
}));

jest.mock("../../../../../../src/components/page-components/dashboard/send-money/icons.tsx", () => ({
    VerifiedIcon: () => <div data-testid="verified-icon">✓</div>,
}));

jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
    useAppDispatch: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

jest.mock("@/redux/slices/recipientsSlice", () => ({
    recipientActions: {
        clearLocalRecipientSuccess: jest.fn(),
        clearState: jest.fn(),
    },
}));

const mockStore = configureStore([]);

describe("AddRecipientModal", () => {
    let closeModalMock;
    let dispatchMock;
    let formikMock;

    const defaultState = {
        recipient: {
            addLocalRecipient: {
                error: null,
                loading: false,
                success: false,
            },
            getLocalBanks: {
                banks: [
                    { bankName: "First Bank", bankCode: "001" },
                    { bankName: "GT Bank", bankCode: "002" },
                ],
                loading: false,
            },
            getRecipientDetails: {
                details: null,
                loading: false,
                error: null,
            },
        },
    };

    beforeEach(() => {
        closeModalMock = jest.fn();
        dispatchMock = jest.fn().mockImplementation((action) => {
            if (typeof action === "function") {
                return action(dispatchMock);
            }
            return Promise.resolve(action);
        });

        formikMock = {
            values: {
                accountNumber: "",
                accountName: "",
                bank: "",
                currencyCode: "NGN",
            },
            touched: {},
            errors: {},
            handleSubmit: jest.fn((e) => e?.preventDefault && e.preventDefault()),
            submitForm: jest.fn(),
            resetForm: jest.fn(),
            setFieldValue: jest.fn(),
        };

        useAppDispatch.mockReturnValue(dispatchMock);
        useFormik.mockReturnValue(formikMock);
        useAppSelector.mockImplementation((selector) => selector(defaultState));

        // Reset all mocks
        jest.clearAllMocks();
    });

    describe("Rendering Tests", () => {
        test("renders correctly when open is true", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            expect(screen.getByText("Add a new recipient")).toBeInTheDocument();
            expect(screen.getByTestId("input-accountNumber")).toBeInTheDocument();
            expect(screen.getByTestId("dropdown-bank")).toBeInTheDocument();
            expect(screen.getByTestId("input-accountName")).toBeInTheDocument();
            expect(screen.getByText("Save recipient for future payments")).toBeInTheDocument();
            expect(screen.getByTestId("button-cancel")).toBeInTheDocument();
            expect(screen.getByTestId("button-add-recipient")).toBeInTheDocument();
        });

        test("does not render when open is false", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={false} onClose={closeModalMock} />
                </Provider>
            );

            const drawer = screen.getByTestId("side-drawer");
            expect(drawer).toHaveAttribute("data-open", "false");
        });

        test("renders loading state for banks dropdown", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getLocalBanks: {
                            banks: [],
                            loading: true,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            expect(screen.getByTestId("dropdown-loading")).toBeInTheDocument();
        });

        test("renders account name input with correct placeholder when not retrieving", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            const accountNameInput = screen.getByTestId("input-accountName");
            expect(accountNameInput).toHaveAttribute(
                "placeholder",
                "Complete all fields above to fetch recipient name"
            );
            expect(accountNameInput).toBeDisabled();
        });

        test("renders loading indicator when retrieving account details", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getRecipientDetails: {
                            details: null,
                            loading: true,
                            error: null,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
            expect(screen.getByTestId("input-accountName")).toHaveAttribute("placeholder", "Verifying account...");
        });
    });

    describe("User Interaction Tests", () => {
        test("closes modal when close button is clicked", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("modal-close"));
            expect(closeModalMock).toHaveBeenCalled();
            expect(formikMock.resetForm).toHaveBeenCalled();
        });

        test("closes modal when Cancel button is clicked", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("button-cancel"));
            expect(closeModalMock).toHaveBeenCalled();
        });

        test("triggers form submission when Add recipient button is clicked", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("button-add-recipient"));
            expect(formikMock.submitForm).toHaveBeenCalled();
        });

        test("checkbox for saving recipient toggles correctly", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            const checkbox = screen.getByTestId("save-recipient-checkbox");
            expect(checkbox).not.toBeChecked();

            fireEvent.click(checkbox);
            expect(checkbox).toBeChecked();

            fireEvent.click(checkbox);
            expect(checkbox).not.toBeChecked();
        });
    });

    describe("Form Validation and Submission", () => {
        test("disables buttons when recipient is loading", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        addLocalRecipient: {
                            loading: true,
                            success: false,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            expect(screen.getByTestId("button-cancel")).toBeDisabled();
            expect(screen.getByTestId("button-add-recipient")).toHaveAttribute("data-loading", "true");
        });
    });

    describe("Account Details Fetching", () => {
        test("fetches account details when bank and account number are filled", async () => {
            formikMock.values.bank = "001";
            formikMock.values.accountNumber = "**********";

            const mockFetchRecipientDetails = jest.fn();
            fetchRecipientDetails.mockReturnValue(mockFetchRecipientDetails);

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(fetchRecipientDetails).toHaveBeenCalledWith({
                    channelCode: "1",
                    destinationInstitutionCode: "001",
                    accountNumber: "**********",
                });
            });
        });

        test("does not fetch details when account number is less than 10 digits", async () => {
            formikMock.values.bank = "001";
            formikMock.values.accountNumber = "*********"; // 9 digits

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(fetchRecipientDetails).not.toHaveBeenCalled();
            });
        });

        test("does not fetch details when bank is not selected", async () => {
            formikMock.values.bank = "";
            formikMock.values.accountNumber = "**********";

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(fetchRecipientDetails).not.toHaveBeenCalled();
            });
        });

        test("handles fetch account details error", async () => {
            formikMock.values.bank = "001";
            formikMock.values.accountNumber = "**********";

            fetchRecipientDetails.mockRejectedValue(new Error("Network error"));

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(expect.any(Error));
            });
        });
    });

    describe("Success and Error Handling", () => {
        test("displays success feedback and closes modal on successful submission", async () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        addLocalRecipient: {
                            loading: false,
                            success: true,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Recipient added", "success");
                expect(recipientActions.clearLocalRecipientSuccess).toHaveBeenCalled();
                expect(getLocalRecipientAction).toHaveBeenCalledWith({
                    params: {
                        pageNo: 0,
                        pageSize: 10,
                    },
                });
                expect(recipientActions.clearState).toHaveBeenCalledWith("getRecipientDetails");
                expect(closeModalMock).toHaveBeenCalled();
                expect(formikMock.resetForm).toHaveBeenCalled();
            });
        });

        test("sets account name when account details are fetched successfully", async () => {
            // Start with no details
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getRecipientDetails: {
                            details: { accountName: "John Doe" },
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            // Set up form values to trigger internal retrieval
            formikMock.values.bank = "001";
            formikMock.values.accountNumber = "**********";

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(formikMock.setFieldValue).toHaveBeenCalledWith("accountName", "John Doe");
            });
        });

        test("shows error when account verification fails", async () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getRecipientDetails: {
                            details: null,
                            loading: false,
                            error: "Account not found",
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith("Account not found", expect.any(Function));
                expect(formikMock.setFieldValue).toHaveBeenCalledWith("accountName", "");
            });
        });

        test("shows error when account details exist but no account name", async () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getRecipientDetails: {
                            details: { accountName: null },
                            loading: false,
                            error: null,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(
                    "Could not retrieve account details",
                    expect.any(Function)
                );
                expect(formikMock.setFieldValue).toHaveBeenCalledWith("accountName", "");
            });
        });
    });

    describe("Edge Cases and Error Boundaries", () => {
        test("handles empty banks array", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getLocalBanks: {
                            banks: [],
                            loading: false,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            const dropdown = screen.getByTestId("dropdown-bank");
            expect(dropdown).toBeInTheDocument();
            // Should not crash when banks is empty
        });

        test("handles undefined banks", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    ...defaultState,
                    recipient: {
                        ...defaultState.recipient,
                        getLocalBanks: {
                            banks: undefined,
                            loading: false,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            // Should not crash when banks is undefined
            expect(screen.getByTestId("dropdown-bank")).toBeInTheDocument();
        });

        test("handles missing state properties gracefully", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    recipient: {
                        // Missing getLocalBanks property
                        addLocalRecipient: {
                            loading: false,
                            success: false,
                        },
                    },
                })
            );

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            // Should not crash when state properties are missing
            expect(screen.getByText("Add a new recipient")).toBeInTheDocument();
        });
    });

    describe("Bank Selection and Display", () => {
        test("displays correct bank name in dropdown", () => {
            formikMock.values.bank = "001";

            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            const dropdown = screen.getByTestId("dropdown-bank");
            expect(dropdown).toHaveValue("001");
        });

        test("handles bank selection change", () => {
            render(
                <Provider store={mockStore({})}>
                    <AddRecipientModal open={true} onClose={closeModalMock} />
                </Provider>
            );

            const dropdown = screen.getByTestId("dropdown-bank");
            fireEvent.change(dropdown, { target: { value: "002" } });

            expect(formikMock.setFieldValue).toHaveBeenCalledWith("bank", "002");
        });
    });
});
