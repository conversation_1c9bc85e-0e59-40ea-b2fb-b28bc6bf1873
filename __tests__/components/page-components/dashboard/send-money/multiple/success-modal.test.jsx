/* eslint-disable quotes */
/* eslint-disable @typescript-eslint/no-require-imports */
import { PATH_PROTECTED } from "@/routes/path";
import { fireEvent, render, screen } from "@testing-library/react";
import { Formik } from "formik";
import MultipleSuccessModal from "../../../../../../src/components/page-components/dashboard/send-money/local/multiple-payment-form/modals/success-modal.tsx";
import { formatNumberToNaira } from "../../../../../../src/functions/stringManipulations";

// Mocks
jest.mock("@/functions/date", () => ({
    formatDateForSentence: jest.fn(() => "January 1, 2025"),
}));

jest.mock("../../../../../../src/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount) => `₦${(amount ?? 0).toFixed(2)}`),
}));

jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, children }) =>
        isOpen ? (
            <div data-testid="mock-full-screen-drawer">
                <button onClick={onClose}>Close</button>
                {children}
            </div>
        ) : null,
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ onClick, children, variant }) => (
        <button data-variant={variant} onClick={onClick}>
            {children}
        </button>
    ),
}));

jest.mock("@/components/common/loading-indicator", () => ({
    __esModule: true,
    default: () => <div data-testid="loading-indicator">Loading...</div>,
}));

jest.mock("../../../../../../src/components/page-components/dashboard/send-money/icons", () => ({
    DocumentIcon: () => <svg data-testid="document-icon" />,
}));

jest.mock("@/hooks/useTransactionFees", () => ({
    useTransactionFees: jest.fn(() => ({ fees: 5000, loading: false })),
}));

jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn((selector) =>
        selector({
            sendMoney: {
                sendMultipleTransfer: {
                    reference: "MP-24011501",
                },
                getTransferApprovers: {
                    data: [],
                },
            },
        })
    ),
}));

describe("MultipleSuccessModal", () => {
    const mockOnClose = jest.fn();
    const formikMock = {
        values: {
            amount: 10000,
        },
    };
    const selectedRecipients = [{ id: 1 }, { id: 2 }];

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("renders correctly when open", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.getByTestId("success-modal")).toBeInTheDocument();
        expect(screen.getByText("Payment successful")).toBeInTheDocument();
        expect(screen.getByText("Your payment has been processed successfully.")).toBeInTheDocument();
    });

    test("does not render when `open` is false", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={false}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.queryByTestId("mock-full-screen-drawer")).not.toBeInTheDocument();
    });

    test("displays formatted amount correctly", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(formatNumberToNaira).toHaveBeenCalledWith(10000, 2);
        expect(screen.getByText("₦10000.00")).toBeInTheDocument();
    });

    test("displays ₦0.00 when amount is falsy", () => {
        const formikMockWithFalsyAmount = {
            values: {
                amount: undefined,
            },
        };

        render(
            <Formik initialValues={formikMockWithFalsyAmount.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMockWithFalsyAmount}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(formatNumberToNaira).toHaveBeenCalledWith(0, 2);
        expect(screen.getByText("₦0.00")).toBeInTheDocument();
    });

    test("displays fees correctly", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.getByText(`(+ ₦10000.00 fees)`)).toBeInTheDocument();
    });

    test("renders loading indicator when fees are loading", () => {
        const useTransactionFees = require("@/hooks/useTransactionFees").useTransactionFees;
        useTransactionFees.mockImplementationOnce(() => ({ fees: 0, loading: true }));

        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    test("displays correct number of recipients", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.getByText("2")).toBeInTheDocument();
    });

    test("renders 0 recipients when selectedRecipients is empty", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal open={true} onClose={mockOnClose} formik={formikMock} selectedRecipients={[]} />
            </Formik>
        );

        expect(screen.getByText("0")).toBeInTheDocument();
    });

    test("displays reference correctly from Redux state", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        expect(screen.getByText("MP-24011501")).toBeInTheDocument();
    });

    test("calls onClose when 'Done' button is clicked", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        fireEvent.click(screen.getByText("Done"));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    test("renders view payment link correctly when does not require approval", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        const viewPaymentLink = screen.getByText("View payment");
        expect(viewPaymentLink).toBeInTheDocument();
        expect(viewPaymentLink.closest("a")).toHaveAttribute("href", PATH_PROTECTED.payments.outgoing.root);
    });

    test("renders view payment link correctly when requires approval", () => {
        const useAppSelector = require("@/redux/hooks").useAppSelector;
        useAppSelector.mockImplementationOnce((selector) =>
            selector({
                sendMoney: {
                    sendMultipleTransfer: {
                        reference: "MP-24011501",
                    },
                    getTransferApprovers: {
                        data: [{ id: 1 }],
                    },
                },
            })
        );

        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        const viewPaymentLink = screen.getByText("View payment");
        expect(viewPaymentLink).toBeInTheDocument();
    });

    test("calls onClose when 'View payment' button is clicked", () => {
        render(
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <MultipleSuccessModal
                    open={true}
                    onClose={mockOnClose}
                    formik={formikMock}
                    selectedRecipients={selectedRecipients}
                />
            </Formik>
        );

        fireEvent.click(screen.getByText("View payment"));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
});
