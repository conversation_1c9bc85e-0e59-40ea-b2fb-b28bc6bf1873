import { store } from "@/redux";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import FormDetails from "../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/form-details";
import { sendCatchFeedback } from "../../../../../src/functions/feedback";
import { recipientActions } from "../../../../../src/redux/slices/recipientsSlice";

// -----------------------------------------------------------------------
// Mock Redux hooks
// -----------------------------------------------------------------------
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(() => jest.fn()),
    useAppSelector: jest.fn(),
}));

// Mock sendCatchFeedback function
jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

// Mock Redux action
import { fetchRecipientDetails } from "@/redux/actions/recipients/local-recipient";
jest.mock("@/redux/actions/recipients/local-recipient", () => ({
    fetchRecipientDetails: jest.fn(),
}));

// Mock Redux slice actions
jest.mock("@/redux/slices/recipientsSlice", () => ({
    recipientActions: {
        clearState: jest.fn(),
    },
}));

// -----------------------------------------------------------------------
// Child components mocks
// -----------------------------------------------------------------------
jest.mock(
    "../../../../../src/components/page-components/dashboard/accounts/payments/components/account-selector",
    () => (props) => (
        <div data-testid="account-selector" onClick={() => props.onChange("New Account")}>
            AccountSelector - {props.selectedAccount}
        </div>
    )
);

jest.mock("@/components/common/amount-input", () => (props) => <div data-testid="amount-input">{props.label}</div>);

jest.mock("@/components/common/dropdown", () => (props) => <div data-testid="dropdown">{props.label}</div>);

jest.mock("@/components/common/label-input", () => (props) => (
    <div data-testid={props["data-testid"] || "label-input"}>
        {props.label} {props.placeholder ? `- ${props.placeholder}` : ""}
        {props.customLabel && <div data-testid="custom-label">{props.customLabel}</div>}
    </div>
));

jest.mock(
    "../../../../../src/components/page-components/dashboard/send-money/local/transfer-type-selector",
    () => (props) => (
        <div
            data-testid="transfer-type-selector"
            onClick={() => {
                if (props.openRecurringModal) props.openRecurringModal();
                if (props.openScheduleModal) props.openScheduleModal();
            }}
        >
            TransferTypeSelector
        </div>
    )
);

// Mocks for loading indicator and verified icon (so we can distinguish the states)
jest.mock("@/components/common/loading-indicator", () => (props) => (
    <div data-testid="loading-indicator">LoadingIndicator</div>
));

jest.mock("../../../../../src/components/page-components/dashboard/send-money/icons", () => ({
    VerifiedIcon: () => <div data-testid="verified-icon">VerifiedIcon</div>,
}));

// -----------------------------------------------------------------------
// Default mocked state values for useAppSelector
// -----------------------------------------------------------------------
const defaultRecipientDetails = {};
const defaultLocalBanks = { banks: [{ bankName: "Access Bank", bankCode: "Access bank" }], loading: false };

describe("FormDetails component", () => {
    // Dummy callback props
    const openScheduleModal = jest.fn();
    const openRecipientModal = jest.fn();
    const openRecurringModal = jest.fn();
    const setFieldValue = jest.fn();
    const dispatch = jest.fn();
    const setAccountBalance = jest.fn();
    const setSaveRecipient = jest.fn();

    // Dummy Formik values
    const initialValues = {
        transferSource: "Account A",
        accountNumber: "**********",
        bank: "Access bank",
        accountName: "",
        amount: "",
        narration: "",
        transferType: "INSTANT",
        reoccurringFrequency: "",
        reoccurringStartDate: "",
        reoccurringReminderSchedule: "",
        recurringTenureType: "",
        reoccurringEndDate: "",
        reoccurringEndOccurrences: "",
        scheduledDate: "",
    };

    const formik = {
        values: initialValues,
        setFieldValue,
    };

    // By default, simulate a state where no recipient details are available and local banks data is loaded.
    beforeEach(() => {
        jest.clearAllMocks();
        useAppDispatch.mockReturnValue(dispatch);
        useAppSelector.mockImplementation((selector) =>
            selector({
                recipient: {
                    getRecipientDetails: defaultRecipientDetails,
                    getLocalBanks: defaultLocalBanks,
                },
            })
        );
        fetchRecipientDetails.mockReturnValue({ type: "FETCH_RECIPIENT_DETAILS" });
    });

    it("renders heading 'Payment information'", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );
        expect(screen.getByTestId("heading")).toHaveTextContent("Payment information");
    });

    it("calls formik.setFieldValue when AccountSelector changes account", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );
        // Clicking on the AccountSelector should simulate a change event.
        fireEvent.click(screen.getByTestId("account-selector"));
        expect(setFieldValue).toHaveBeenCalledWith("transferSource", "New Account");
    });

    it("renders TransferTypeSelector", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );
        expect(screen.getByTestId("transfer-type-selector")).toBeInTheDocument();
    });

    it("renders AmountInput with the correct label", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );
        expect(screen.getByTestId("amount-input")).toHaveTextContent("Amount");
    });

    it("renders Dropdown with label 'Bank'", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );
        expect(screen.getByTestId("dropdown")).toHaveTextContent("Bank");
    });

    it("calls openRecipientModal when 'Choose recipient' button is clicked", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );

        // Find the custom label with "Choose recipient" button and click it
        const customLabel = screen.getByTestId("custom-label");
        const chooseRecipientButton = customLabel.querySelector("button");
        fireEvent.click(chooseRecipientButton);

        expect(openRecipientModal).toHaveBeenCalled();
    });

    it("calls openScheduleModal and openRecurringModal through TransferTypeSelector", () => {
        render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );

        fireEvent.click(screen.getByTestId("transfer-type-selector"));

        expect(openScheduleModal).toHaveBeenCalled();
        expect(openRecurringModal).toHaveBeenCalled();
    });

    describe("account verification flow", () => {
        it("dispatches fetchRecipientDetails when accountNumber is 10 characters and bank is selected", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );

            expect(dispatch).toHaveBeenCalled();
            expect(fetchRecipientDetails).toHaveBeenCalledWith({
                accountNumber: "**********",
                channelCode: "1",
                destinationInstitutionCode: "Access bank",
            });
        });

        it("doesn't dispatch fetchRecipientDetails when accountNumber is less than 10 characters", () => {
            const newFormik = { ...formik, values: { ...initialValues, accountNumber: "*********" } };
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={newFormik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );

            // fetchRecipientDetails should not be called
            expect(fetchRecipientDetails).not.toHaveBeenCalled();
        });

        it("doesn't dispatch fetchRecipientDetails when bank is not selected", () => {
            const newFormik = { ...formik, values: { ...initialValues, bank: "" } };
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={newFormik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );

            // fetchRecipientDetails should not be called
            expect(fetchRecipientDetails).not.toHaveBeenCalled();
        });
    });

    describe("account name field behavior", () => {
        it("shows LoadingIndicator when fetchingDetails is true", () => {
            // Simulate a state where recipient details are still being fetched.
            useAppSelector.mockImplementation((selector) =>
                selector({
                    recipient: {
                        getRecipientDetails: { loading: true },
                        getLocalBanks: defaultLocalBanks,
                    },
                })
            );
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );
            expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
        });
    });

    describe("integration with banks data", () => {
        it("renders banks dropdown when banks are loading", () => {
            useAppSelector.mockImplementation((selector) =>
                selector({
                    recipient: {
                        getRecipientDetails: defaultRecipientDetails,
                        getLocalBanks: { banks: [], loading: true },
                    },
                })
            );

            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );

            expect(screen.getByTestId("dropdown")).toBeInTheDocument();
        });
    });

    it("handles fetchDetailsError in useEffect and calls the necessary functions", () => {
        // First set up the component with normal state
        useAppSelector.mockImplementation((selector) =>
            selector({
                recipient: {
                    getRecipientDetails: { loading: false },
                    getLocalBanks: defaultLocalBanks,
                },
            })
        );

        const { rerender } = render(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );

        // Reset the mock calls from initial render
        setFieldValue.mockClear();
        sendCatchFeedback.mockClear();
        recipientActions.clearState.mockClear();

        // Now update the state to include an error
        useAppSelector.mockImplementation((selector) =>
            selector({
                recipient: {
                    getRecipientDetails: {
                        loading: false,
                        error: "Invalid account number",
                    },
                    getLocalBanks: defaultLocalBanks,
                },
            })
        );

        // Re-render with the new state that includes the error
        rerender(
            <Provider store={store}>
                <FormDetails
                    formik={formik}
                    openScheduleModal={openScheduleModal}
                    openRecipientModal={openRecipientModal}
                    openRecurringModal={openRecurringModal}
                    setAccountBalance={setAccountBalance}
                    saveRecipient={false}
                    setSaveRecipient={setSaveRecipient}
                    showSaveRecipient={true}
                />
            </Provider>
        );

        // Verify that error handling in the useEffect occurred
        expect(setFieldValue).toHaveBeenCalledWith("accountName", "");
        expect(sendCatchFeedback).toHaveBeenCalledWith("Invalid account number", expect.any(Function));

        // Simulate that sendCatchFeedback calls its callback
        const callback = sendCatchFeedback.mock.calls[0][1];
        callback();

        // Verify that the clearState action was called by the callback
        expect(recipientActions.clearState).toHaveBeenCalledWith("getRecipientDetails");
    });

    describe("save recipient checkbox", () => {
        it("renders save recipient checkbox when showSaveRecipient is true", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={true}
                    />
                </Provider>
            );

            expect(screen.getByText("Save recipient for future payments")).toBeInTheDocument();
        });

        it("does not render save recipient checkbox when showSaveRecipient is false", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        openScheduleModal={openScheduleModal}
                        openRecipientModal={openRecipientModal}
                        openRecurringModal={openRecurringModal}
                        setAccountBalance={setAccountBalance}
                        saveRecipient={false}
                        setSaveRecipient={setSaveRecipient}
                        showSaveRecipient={false}
                    />
                </Provider>
            );

            expect(screen.queryByText("Save recipient for future payments")).not.toBeInTheDocument();
        });
    });
});
