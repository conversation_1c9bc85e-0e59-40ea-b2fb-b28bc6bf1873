/* eslint-disable @typescript-eslint/no-require-imports */
import { render, screen } from "@testing-library/react";
import TransferTypeIndicator from "../../../../../src/components/page-components/dashboard/send-money/local/transfer-type-indicator";

// Mock dependencies
jest.mock("@/functions/date", () => ({
    formatDateForSentence: jest.fn((date) => `formatted ${date.toISOString().split("T")[0]}`),
}));

jest.mock("../../../../../src/components/page-components/dashboard/send-money/local/utils", () => ({
    getRecurringFeedbackString: jest.fn(
        ({ frequency, startDate, endDate }) => `${frequency} from ${startDate} to ${endDate}`
    ),
    getFrequencyValue: jest.fn(({ frequency, startDate, endDate, endCount }) => {
        if (endCount) return endCount;
        if (frequency === "MONTHLY") return 3;
        if (frequency === "WEEKLY") return 12;
        return 1;
    }),
    getRecurringEndStringFromOccurrences: jest.fn(
        ({ occurrences, frequency }) => `${occurrences} ${frequency.toLowerCase()}`
    ),
}));

jest.mock("pluralize", () => jest.fn((word, count) => (count === 1 ? word : `${word}s`)));

jest.mock("../../../../../src/components/page-components/dashboard/send-money/icons.tsx", () => ({
    CalendarIcon: () => <span data-testid="calendar-icon">CalendarIcon</span>,
}));

describe("TransferTypeIndicator component", () => {
    let formik;

    beforeEach(() => {
        jest.clearAllMocks();
        formik = {
            values: {
                transferType: "INSTANT",
                scheduledDate: "",
                reoccurringFrequency: "",
                reoccurringStartDate: "",
                reoccurringEndDate: "",
                reoccurringEndOccurrences: undefined,
                accountName: "",
                accountNumber: "",
                amount: "",
                bank: "",
                narration: "",
                requiresApproval: true,
                transferSource: {
                    accountNumber: "**********",
                    accountName: "Test Account",
                    currencyCode: "NGN",
                },
            },
            setFieldValue: jest.fn(),
        };
    });

    it("renders nothing when transferType is INSTANT", () => {
        render(<TransferTypeIndicator formik={formik} />);
        expect(screen.queryByTestId("scheduledDate")).not.toBeInTheDocument();
        expect(screen.queryByTestId("calendar-icon")).not.toBeInTheDocument();
        expect(screen.queryByText(/This payment will be scheduled for/)).not.toBeInTheDocument();
        expect(screen.queryByText(/payments will be created/)).not.toBeInTheDocument();
    });

    it("renders scheduled payment message when transferType is SCHEDULED and scheduledDate is set", () => {
        formik.values.transferType = "SCHEDULED";
        formik.values.scheduledDate = "2025-06-01";

        render(<TransferTypeIndicator formik={formik} />);

        expect(screen.getByTestId("scheduledDate")).toBeInTheDocument();
        expect(screen.getByTestId("calendar-icon")).toBeInTheDocument();
        expect(screen.getByText("This payment will be scheduled for formatted 2025-06-01")).toBeInTheDocument();
        expect(require("@/functions/date").formatDateForSentence).toHaveBeenCalledWith(expect.any(Date));
    });

    it("does not render scheduled message when transferType is SCHEDULED but scheduledDate is missing", () => {
        formik.values.transferType = "SCHEDULED";
        formik.values.scheduledDate = "";

        render(<TransferTypeIndicator formik={formik} />);

        expect(screen.queryByTestId("scheduledDate")).not.toBeInTheDocument();
        expect(screen.queryByTestId("calendar-icon")).not.toBeInTheDocument();
        expect(screen.queryByText(/This payment will be scheduled for/)).not.toBeInTheDocument();
    });

    it("does not render recurring message when transferType is RECURRING but required fields are missing", () => {
        formik.values.transferType = "RECURRING";

        render(<TransferTypeIndicator formik={formik} />);

        expect(screen.queryByTestId("calendar-icon")).not.toBeInTheDocument();
        expect(screen.queryByText(/payments will be created/)).not.toBeInTheDocument();
    });

    it("applies correct styling to scheduled payment message", () => {
        formik.values.transferType = "SCHEDULED";
        formik.values.scheduledDate = "2025-06-01";

        render(<TransferTypeIndicator formik={formik} />);

        const container = screen.getByTestId("scheduledDate");
        expect(container).toHaveClass("flex items-center w-full gap-3 p-4 rounded-lg bg-[#F9F0FE]");
        expect(screen.getByText(/This payment will be scheduled for/)).toHaveClass("text-[#7707B6] text-sm");
    });
});
