import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import withPermissionCheck from "@/components/hoc/withPermissionCheck";
import { usePermissions } from "@/contexts/PermissionContext";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { useRouter, usePathname } from "next/navigation";
import "@testing-library/jest-dom";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
    usePathname: jest.fn(),
}));

jest.mock("@/contexts/PermissionContext", () => ({
    usePermissions: jest.fn(),
}));

jest.mock("@/hooks/usePermissionCheck", () => ({
    usePermissionCheck: jest.fn(),
}));

// Mock console.warn to avoid noise in tests
const originalWarn = console.warn;
beforeAll(() => {
    console.warn = jest.fn();
});

afterAll(() => {
    console.warn = originalWarn;
});

jest.mock("@/routes/path", () => ({
    PATH_PROTECTED: {
        root: "/login",
    },
}));

describe("withPermissionCheck HOC", () => {
    // Mock router
    const mockPush = jest.fn();
    const mockPathname = jest.fn();

    // Mock permission hooks
    const mockCanAccessRoute = jest.fn();
    const mockPermissionExists = jest.fn();

    // Test component
    const TestComponent = () => <div data-testid="test-component">Test Content</div>;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Mock sessionStorage
        const mockSessionStorage = {
            getItem: jest.fn(),
            setItem: jest.fn(),
        };
        Object.defineProperty(window, "sessionStorage", {
            value: mockSessionStorage,
            writable: true,
        });

        // Setup default mock implementations
        useRouter.mockReturnValue({ push: mockPush });
        usePathname.mockReturnValue("/current-path");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A", "PERM_B"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        usePermissionCheck.mockReturnValue({
            canAccessRoute: mockCanAccessRoute,
            permissionExists: mockPermissionExists,
        });
    });

    test("renders the wrapped component when user has required permissions", async () => {
        mockCanAccessRoute.mockReturnValue(true);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A"],
        });

        render(<WrappedComponent />);

        // After permissions are checked, should render the component
        await waitFor(() => {
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });

        expect(mockCanAccessRoute).toHaveBeenCalledWith(["PERM_A"], false);
        expect(mockPush).not.toHaveBeenCalled();
    });

    test("redirects when user doesn't have required permissions", async () => {
        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
            redirectTo: "/dashboard",
        });

        render(<WrappedComponent />);

        // Should not render the component
        await waitFor(() => {
            expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        });

        expect(mockCanAccessRoute).toHaveBeenCalledWith(["PERM_C"], false);
        expect(mockPush).toHaveBeenCalledWith("/dashboard");
    });

    test("uses default redirect path when not specified", async () => {
        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(mockPush).toHaveBeenCalledWith("/login");
        });
    });

    test("respects requireAll parameter", async () => {
        mockCanAccessRoute.mockReturnValue(true);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A", "PERM_B"],
            requireAll: true,
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });

        expect(mockCanAccessRoute).toHaveBeenCalledWith(["PERM_A", "PERM_B"], true);
    });

    test("warns about invalid permissions in development environment", () => {
        // Mock development environment
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = "development";

        mockPermissionExists.mockImplementation((permission) => permission === "PERM_A");

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A", "INVALID_PERM"],
        });

        render(<WrappedComponent />);

        expect(console.warn).toHaveBeenCalledWith("withPermissionCheck: Invalid permissions detected:", [
            "INVALID_PERM",
        ]);

        // Restore environment
        process.env.NODE_ENV = originalEnv;
    });

    test("shows nothing while permissions are loading", () => {
        usePermissions.mockReturnValue({
            userPermissions: [],
            isLoadingPermissions: true,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: false, // This is the key - system not ready
        });

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A"],
        });

        render(<WrappedComponent />);

        expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        expect(mockPush).not.toHaveBeenCalled();
    });

    test("sets correct displayName for the wrapped component", () => {
        const NamedComponent = () => <div>Named Component</div>;
        NamedComponent.displayName = "CustomName";

        const WrappedComponent = withPermissionCheck(NamedComponent, {
            requiredPermissions: ["PERM_A"],
        });

        expect(WrappedComponent.displayName).toBe("withPermissionCheck(CustomName)");
    });

    test("uses function name when displayName is not available", () => {
        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A"],
        });

        expect(WrappedComponent.displayName).toBe("withPermissionCheck(TestComponent)");
    });

    test("falls back to 'Component' when name is not available", () => {
        const AnonymousComponent = () => <div>Anonymous</div>;
        Object.defineProperty(AnonymousComponent, "name", { value: undefined });

        const WrappedComponent = withPermissionCheck(AnonymousComponent, {
            requiredPermissions: ["PERM_A"],
        });

        expect(WrappedComponent.displayName).toBe("withPermissionCheck(Component)");
    });

    test("blocks access when using cached permissions (strict security)", () => {
        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: true, // Using cached permissions
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(true);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A"],
        });

        render(<WrappedComponent />);

        // Should not render component due to strict security
        expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        expect(mockPush).not.toHaveBeenCalled(); // No redirect, just blocked
    });

    test("blocks access when permission fetch failed (strict security)", () => {
        usePermissions.mockReturnValue({
            userPermissions: [],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: true, // Permission fetch failed
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_A"],
        });

        render(<WrappedComponent />);

        // Should not render component due to strict security
        expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        expect(mockPush).not.toHaveBeenCalled(); // No redirect, just blocked
    });

    test("redirects to previous route when available and safe", async () => {
        // Mock sessionStorage to return a previous route
        window.sessionStorage.getItem.mockReturnValue("/safe-previous-route");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"], // User doesn't have this permission
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should redirect to previous route instead of default
            expect(mockPush).toHaveBeenCalledWith("/safe-previous-route");
        });
    });

    test("uses default redirect when previous route is unsafe", async () => {
        // Mock sessionStorage to return an unsafe previous route
        window.sessionStorage.getItem.mockReturnValue("/dashboard/unsafe-route");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"], // User doesn't have this permission
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should use default redirect for unsafe previous route
            expect(mockPush).toHaveBeenCalledWith("/login");
        });
    });

    test("denies access for uncertain state (default deny branch)", async () => {
        // Simulate uncertain state: not using cached, not failed, but not hasRequiredAccess
        mockCanAccessRoute.mockReturnValue(false);
        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        });
    });

    test("renders error state when permissionFetchFailed is true", async () => {
        mockCanAccessRoute.mockReturnValue(false);
        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: true,
            isPermissionSystemReady: true,
        });

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(screen.getByText(/Security Verification Required/)).toBeInTheDocument();
        });
    });

    test("renders error state when isUsingCachedPermissions is true", async () => {
        mockCanAccessRoute.mockReturnValue(false);
        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: true,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(screen.getByText(/Security Verification Required/)).toBeInTheDocument();
        });
    });

    test("handles retry functionality in error state", async () => {
        // Save and mock window.location
        const originalLocation = window.location;
        delete window.location;
        window.location = { reload: jest.fn() };

        mockCanAccessRoute.mockReturnValue(false);
        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: true,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_C"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            expect(screen.getByText(/Security Verification Required/)).toBeInTheDocument();
        });

        // Click the retry button
        const retryButton = screen.getByText("Refresh & Verify");
        retryButton.click();

        expect(window.location.reload).toHaveBeenCalled();

        // Restore original window.location
        window.location = originalLocation;
    });

    test("redirects to dashboard when previous route is protected", async () => {
        // Mock sessionStorage to return a protected route
        window.sessionStorage.getItem.mockReturnValue("/dashboard/some-protected-route");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"],
            redirectTo: "/custom-login",
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should redirect to custom login instead of protected route
            expect(mockPush).toHaveBeenCalledWith("/custom-login");
        });
    });

    test("redirects to default when previous route is auth route", async () => {
        // Mock sessionStorage to return an auth route
        window.sessionStorage.getItem.mockReturnValue("/auth/login");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should redirect to default instead of auth route
            expect(mockPush).toHaveBeenCalledWith("/login");
        });
    });

    test("handles case when previous route is same as current pathname", async () => {
        // Mock sessionStorage to return the same route as current pathname
        window.sessionStorage.getItem.mockReturnValue("/current-path");

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should use default redirect when previous route is same as current
            expect(mockPush).toHaveBeenCalledWith("/login");
        });
    });

    test("handles case when no previous route is stored", async () => {
        // Mock sessionStorage to return null
        window.sessionStorage.getItem.mockReturnValue(null);

        usePermissions.mockReturnValue({
            userPermissions: ["PERM_A"],
            isLoadingPermissions: false,
            isUsingCachedPermissions: false,
            permissionFetchFailed: false,
            isPermissionSystemReady: true,
        });

        mockCanAccessRoute.mockReturnValue(false);

        const WrappedComponent = withPermissionCheck(TestComponent, {
            requiredPermissions: ["PERM_B"],
        });

        render(<WrappedComponent />);

        await waitFor(() => {
            // Should use default redirect when no previous route
            expect(mockPush).toHaveBeenCalledWith("/login");
        });
    });
});
