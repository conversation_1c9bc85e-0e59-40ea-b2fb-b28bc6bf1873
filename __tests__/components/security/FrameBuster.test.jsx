import React from "react";
import { render } from "@testing-library/react";
import FrameBuster from "@/components/security/FrameBuster";

// Mock console methods to avoid polluting test output
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Helper function to mock window properties
const mockWindowProperty = (property, value, shouldThrow = false) => {
    const descriptor = shouldThrow
        ? {
              get: () => {
                  throw new Error(`Access to ${property} denied`);
              },
              configurable: true,
          }
        : {
              value,
              writable: true,
              configurable: true,
          };

    Object.defineProperty(window, property, descriptor);
};

describe("FrameBuster", () => {
    let originalWindow;
    let originalDocument;

    beforeEach(() => {
        jest.clearAllMocks();

        // Store original objects
        originalWindow = { ...window };
        originalDocument = { ...document };

        // Mock console methods
        console.warn = jest.fn();
        console.error = jest.fn();
    });

    afterEach(() => {
        // Restore console methods
        console.warn = originalConsoleWarn;
        console.error = originalConsoleError;

        // Restore window properties
        Object.defineProperty(window, "self", {
            value: originalWindow.self,
            writable: true,
            configurable: true,
        });
        Object.defineProperty(window, "top", {
            value: originalWindow.top,
            writable: true,
            configurable: true,
        });
        Object.defineProperty(window, "location", {
            value: originalWindow.location,
            writable: true,
            configurable: true,
        });

        // Restore document
        if (originalDocument.body) {
            document.body.innerHTML = "";
        }
    });

    describe("Component Rendering", () => {
        it("should not render any visible content", () => {
            const { container } = render(<FrameBuster />);

            // Component should return null and not render anything
            expect(container.firstChild).toBeNull();
        });
    });

    describe("Frame Detection Logic", () => {
        it("should handle normal window environment", () => {
            // In normal Jest environment, window.self === window.top
            render(<FrameBuster />);

            // Should not trigger any warnings in normal environment
            expect(console.warn).not.toHaveBeenCalled();
        });

        it("should detect when running in a frame (window.self !== window.top)", () => {
            // Mock window.self and window.top to be different objects
            const mockSelf = { different: "self" };
            const mockTop = {
                location: { href: "" },
            };

            Object.defineProperty(window, "self", {
                value: mockSelf,
                writable: true,
                configurable: true,
            });
            Object.defineProperty(window, "top", {
                value: mockTop,
                writable: true,
                configurable: true,
            });

            render(<FrameBuster />);

            // Should detect frame and log warning
            expect(console.warn).toHaveBeenCalledWith("Clickjacking attempt detected! Breaking out of frame.");
        });

        it("should detect frame when window.top access throws error", () => {
            // Mock window.top to throw error when accessed (cross-origin scenario)
            Object.defineProperty(window, "top", {
                get: () => {
                    throw new Error("Cross-origin access denied");
                },
                configurable: true,
            });

            render(<FrameBuster />);

            // Should detect frame and log warning
            expect(console.warn).toHaveBeenCalledWith("Clickjacking attempt detected! Breaking out of frame.");
        });
    });

    describe("Event Listener Management", () => {
        it("should add and remove event listeners properly", () => {
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");
            const removeEventListenerSpy = jest.spyOn(window, "removeEventListener");

            const { unmount } = render(<FrameBuster />);

            // Should add resize event listener
            expect(addEventListenerSpy).toHaveBeenCalledWith("resize", expect.any(Function));

            // Get the handler function that was added
            const handler = addEventListenerSpy.mock.calls[0][1];

            // Unmount component to trigger cleanup
            unmount();

            // Should remove the same event listener
            expect(removeEventListenerSpy).toHaveBeenCalledWith("resize", handler);

            addEventListenerSpy.mockRestore();
            removeEventListenerSpy.mockRestore();
        });
    });

    describe("Security Features", () => {
        it("should be designed to detect iframe embedding", () => {
            // This test verifies the component exists and has the right structure
            const { container } = render(<FrameBuster />);

            // Component should not render visible content (returns null)
            expect(container.firstChild).toBeNull();

            // Should set up event listeners for security monitoring
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");
            render(<FrameBuster />);
            expect(addEventListenerSpy).toHaveBeenCalled();
            addEventListenerSpy.mockRestore();
        });

        it("should clean up event listeners on unmount", () => {
            const removeEventListenerSpy = jest.spyOn(window, "removeEventListener");

            const { unmount } = render(<FrameBuster />);
            unmount();

            // Should clean up event listeners
            expect(removeEventListenerSpy).toHaveBeenCalled();

            removeEventListenerSpy.mockRestore();
        });
    });

    describe("Frame Breaking Attempts", () => {
        it("should attempt to redirect top window when frame is detected", () => {
            // Mock window.self and window.top to be different objects
            const mockSelf = { different: "self" };
            const mockTop = {
                location: { href: "" },
            };

            Object.defineProperty(window, "self", {
                value: mockSelf,
                writable: true,
                configurable: true,
            });
            Object.defineProperty(window, "top", {
                value: mockTop,
                writable: true,
                configurable: true,
            });
            Object.defineProperty(window, "location", {
                value: { href: "https://example.com/test" },
                writable: true,
                configurable: true,
            });

            render(<FrameBuster />);

            // Should attempt to set top window location
            expect(mockTop.location.href).toBe("https://example.com/test");
        });

        it("should fall back to replacing body content when top window redirect fails", () => {
            // Mock window.self and window.top to be different objects
            const mockSelf = { different: "self" };

            Object.defineProperty(window, "self", {
                value: mockSelf,
                writable: true,
                configurable: true,
            });

            // Mock window.top.location.href to throw error when set
            Object.defineProperty(window, "top", {
                value: {
                    location: {
                        set href(value) {
                            throw new Error("Cannot access top window");
                        },
                    },
                },
                writable: true,
                configurable: true,
            });

            Object.defineProperty(window, "location", {
                value: { href: "https://example.com/test" },
                writable: true,
                configurable: true,
            });

            render(<FrameBuster />);

            // Should log error
            expect(console.error).toHaveBeenCalledWith("Unable to break out of frame:", expect.any(Error));

            // Should replace body content with security warning
            expect(document.body.innerHTML).toContain("Security Warning");
            expect(document.body.innerHTML).toContain("This page cannot be displayed in a frame for security reasons.");
            expect(document.body.innerHTML).toContain("Open Secure Page");
            expect(document.body.innerHTML).toContain("https://example.com/test");
        });
    });

    describe("Frame Change Detection", () => {
        it("should detect frame changes on resize events", () => {
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");

            render(<FrameBuster />);

            // Get the resize handler
            const resizeHandler = addEventListenerSpy.mock.calls.find((call) => call[0] === "resize")?.[1];

            expect(resizeHandler).toBeDefined();

            // Clear previous console calls
            console.warn.mockClear();

            // Mock frame detection for resize handler
            const mockSelf = { different: "self" };
            Object.defineProperty(window, "self", {
                value: mockSelf,
                writable: true,
                configurable: true,
            });
            Object.defineProperty(window, "top", {
                value: { different: "top" },
                writable: true,
                configurable: true,
            });

            // Trigger resize event
            if (resizeHandler) {
                resizeHandler();
            }

            // Should detect frame change and log warning
            expect(console.warn).toHaveBeenCalledWith("Frame change detected - potential clickjacking attempt!");

            addEventListenerSpy.mockRestore();
        });

        it("should handle resize handler when not in frame", () => {
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");

            render(<FrameBuster />);

            // Get the resize handler
            const resizeHandler = addEventListenerSpy.mock.calls.find((call) => call[0] === "resize")?.[1];

            expect(resizeHandler).toBeDefined();

            // Clear previous console calls
            console.warn.mockClear();

            // Trigger resize event (window.self === window.top by default in Jest)
            if (resizeHandler) {
                resizeHandler();
            }

            // Should not log warning when not in frame
            expect(console.warn).not.toHaveBeenCalled();

            addEventListenerSpy.mockRestore();
        });
    });

    describe("Component Functionality", () => {
        it("should execute useEffect hook on mount", () => {
            // Test that the component's useEffect runs
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");

            render(<FrameBuster />);

            // Should add event listener (proves useEffect ran)
            expect(addEventListenerSpy).toHaveBeenCalled();

            addEventListenerSpy.mockRestore();
        });

        it("should handle component lifecycle", () => {
            const addEventListenerSpy = jest.spyOn(window, "addEventListener");
            const removeEventListenerSpy = jest.spyOn(window, "removeEventListener");

            const { unmount } = render(<FrameBuster />);

            // Should add event listener on mount
            expect(addEventListenerSpy).toHaveBeenCalled();

            // Unmount to test cleanup
            unmount();

            // Should remove event listener on unmount
            expect(removeEventListenerSpy).toHaveBeenCalled();

            addEventListenerSpy.mockRestore();
            removeEventListenerSpy.mockRestore();
        });
    });
});
