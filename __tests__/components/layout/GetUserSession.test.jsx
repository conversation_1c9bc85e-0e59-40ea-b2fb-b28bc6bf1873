import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import GetUserSession from "@/components/layout/GetUserSession";
import { updateUser } from "@/redux/features/user";

// Mock the Redux hooks
const mockDispatch = jest.fn();
const mockUseAppSelector = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: (selector) => mockUseAppSelector(selector),
}));

// Mock the updateUser action
jest.mock("@/redux/features/user", () => ({
    updateUser: jest.fn(),
}));

describe("GetUserSession", () => {
    // Create a mock store for Provider
    const mockStore = configureStore({
        reducer: {
            signin: (state = { isAuthenticated: false, user: null }) => state,
        },
    });

    beforeEach(() => {
        jest.clearAllMocks();
        // Reset the mock implementation
        updateUser.mockImplementation((payload) => ({
            type: "user/updateUser",
            payload,
        }));
    });

    it("renders without crashing and returns null", () => {
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: false,
            user: null,
        });

        const { container } = render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        // Component should render nothing (return null)
        expect(container.firstChild).toBeNull();
    });

    it("dispatches updateUser when user and isAuthenticated are both present", () => {
        const mockUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
        };

        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: mockUser,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).toHaveBeenCalledWith({ user: mockUser });
        expect(mockDispatch).toHaveBeenCalledWith({
            type: "user/updateUser",
            payload: { user: mockUser },
        });
    });

    it("does not dispatch updateUser when isAuthenticated is false", () => {
        const mockUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
        };

        mockUseAppSelector.mockReturnValue({
            isAuthenticated: false,
            user: mockUser,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("does not dispatch updateUser when user is null", () => {
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: null,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("does not dispatch updateUser when user is undefined", () => {
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: undefined,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("does not dispatch updateUser when both isAuthenticated and user are false/null", () => {
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: false,
            user: null,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("handles empty user object", () => {
        const emptyUser = {};

        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: emptyUser,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).toHaveBeenCalledWith({ user: emptyUser });
        expect(mockDispatch).toHaveBeenCalledWith({
            type: "user/updateUser",
            payload: { user: emptyUser },
        });
    });

    it("re-runs effect when isAuthenticated changes", () => {
        const mockUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
        };

        // Initial render with isAuthenticated: false
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: false,
            user: mockUser,
        });

        const { rerender } = render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(mockDispatch).not.toHaveBeenCalled();

        // Re-render with isAuthenticated: true
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: mockUser,
        });

        rerender(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).toHaveBeenCalledWith({ user: mockUser });
        expect(mockDispatch).toHaveBeenCalledWith({
            type: "user/updateUser",
            payload: { user: mockUser },
        });
    });

    it("does not re-run effect when user changes but isAuthenticated stays the same", () => {
        const initialUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
        };
        const updatedUser = {
            id: 1,
            name: "John Smith",
            email: "<EMAIL>",
        };

        // Initial render
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: initialUser,
        });

        const { rerender } = render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(mockDispatch).toHaveBeenCalledTimes(1);
        expect(updateUser).toHaveBeenCalledWith({ user: initialUser });

        // Clear mocks to test if effect runs again
        jest.clearAllMocks();

        // Re-render with different user but same isAuthenticated
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true, // Same value
            user: updatedUser, // Different user
        });

        rerender(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        // Effect should not run again because isAuthenticated didn't change
        expect(mockDispatch).not.toHaveBeenCalled();
        expect(updateUser).not.toHaveBeenCalled();
    });

    it("handles complex user object with nested properties", () => {
        const complexUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
            profile: {
                avatar: "avatar.jpg",
                preferences: {
                    theme: "dark",
                    notifications: true,
                },
            },
            roles: ["user", "admin"],
        };

        mockUseAppSelector.mockReturnValue({
            isAuthenticated: true,
            user: complexUser,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(updateUser).toHaveBeenCalledWith({ user: complexUser });
        expect(mockDispatch).toHaveBeenCalledWith({
            type: "user/updateUser",
            payload: { user: complexUser },
        });
    });

    it("calls useAppSelector with correct selector function", () => {
        mockUseAppSelector.mockReturnValue({
            isAuthenticated: false,
            user: null,
        });

        render(
            <Provider store={mockStore}>
                <GetUserSession />
            </Provider>
        );

        expect(mockUseAppSelector).toHaveBeenCalledWith(expect.any(Function));

        // Test the selector function
        const selectorFunction = mockUseAppSelector.mock.calls[0][0];
        const mockState = {
            signin: {
                isAuthenticated: true,
                user: { name: "Test User" },
            },
        };

        const result = selectorFunction(mockState);
        expect(result).toEqual({
            isAuthenticated: true,
            user: { name: "Test User" },
        });
    });
});
