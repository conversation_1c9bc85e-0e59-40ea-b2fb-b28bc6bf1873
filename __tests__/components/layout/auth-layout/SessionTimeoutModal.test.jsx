import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import SessionTimeoutModal from "@/components/layout/auth-layout/SessionTimeoutModal";

// Mock the Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, loading, disabled, variant, fullWidth, ...props }) => (
        <button
            onClick={onClick}
            disabled={disabled}
            data-testid={children === "Logout" ? "logout-btn" : "continue-btn"}
            data-loading={loading}
            data-variant={variant}
            data-fullwidth={fullWidth}
            {...props}
        >
            {children}
        </button>
    ),
}));

// Mock CustomModal component
jest.mock("@/components/common/custom-modal", () => {
    return function MockCustomModal({ isOpen, onRequestClose, width, children }) {
        return isOpen ? (
            <div data-testid="custom-modal" data-width={width}>
                <button onClick={onRequestClose} data-testid="modal-close-btn">
                    Close Modal
                </button>
                {children}
            </div>
        ) : null;
    };
});

// Mock <PERSON>ide React icons
jest.mock("lucide-react", () => ({
    Clock: ({ className, ...props }) => (
        <div data-testid="clock-icon" className={className} {...props}>
            Clock Icon
        </div>
    ),
    AlertTriangle: ({ className, ...props }) => (
        <div data-testid="alert-triangle-icon" className={className} {...props}>
            Alert Triangle Icon
        </div>
    ),
}));

describe("SessionTimeoutModal", () => {
    const defaultProps = {
        isOpen: true,
        timeLeft: 30,
        onContinue: jest.fn(),
        onLogout: jest.fn(),
        loading: false,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Rendering", () => {
        it("renders correctly when open", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
            expect(screen.getByTestId("custom-modal")).toHaveAttribute("data-width", "400px");
            expect(screen.getByText("Session Timeout Warning")).toBeInTheDocument();
            expect(
                screen.getByText("Your session will expire due to inactivity. Do you want to continue?")
            ).toBeInTheDocument();
            expect(screen.getByTestId("alert-triangle-icon")).toBeInTheDocument();
            expect(screen.getByTestId("clock-icon")).toBeInTheDocument();
            expect(screen.getByTestId("logout-btn")).toBeInTheDocument();
            expect(screen.getByTestId("continue-btn")).toBeInTheDocument();
        });

        it("does not render when closed", () => {
            render(<SessionTimeoutModal {...defaultProps} isOpen={false} />);

            expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
            expect(screen.queryByText("Session Timeout Warning")).not.toBeInTheDocument();
        });

        it("renders with correct modal width", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            expect(screen.getByTestId("custom-modal")).toHaveAttribute("data-width", "400px");
        });

        it("renders icons with correct styling", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            const alertIcon = screen.getByTestId("alert-triangle-icon");
            const clockIcon = screen.getByTestId("clock-icon");

            expect(alertIcon).toHaveClass("h-8", "w-8", "text-yellow-600");
            expect(clockIcon).toHaveClass("h-5", "w-5", "mr-2");
        });
    });

    describe("Time Formatting", () => {
        it("formats time correctly for seconds only", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={45} />);

            expect(screen.getByText("00:45")).toBeInTheDocument();
        });

        it("formats time correctly for minutes and seconds", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={125} />);

            expect(screen.getByText("02:05")).toBeInTheDocument();
        });

        it("formats time correctly for single digit minutes and seconds", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={65} />);

            expect(screen.getByText("01:05")).toBeInTheDocument();
        });

        it("formats time correctly for zero seconds", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={0} />);

            expect(screen.getByText("00:00")).toBeInTheDocument();
        });

        it("formats time correctly for exactly one minute", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={60} />);

            expect(screen.getByText("01:00")).toBeInTheDocument();
        });

        it("formats time correctly for large numbers", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={3661} />);

            expect(screen.getByText("61:01")).toBeInTheDocument();
        });

        it("handles negative time values", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={-30} />);

            expect(screen.getByText("-1:-30")).toBeInTheDocument();
        });
    });

    describe("Button Interactions", () => {
        it("calls onContinue when Continue Session button is clicked", () => {
            const mockOnContinue = jest.fn();
            render(<SessionTimeoutModal {...defaultProps} onContinue={mockOnContinue} />);

            fireEvent.click(screen.getByTestId("continue-btn"));

            expect(mockOnContinue).toHaveBeenCalledTimes(1);
        });

        it("calls onLogout when Logout button is clicked", () => {
            const mockOnLogout = jest.fn();
            render(<SessionTimeoutModal {...defaultProps} onLogout={mockOnLogout} />);

            fireEvent.click(screen.getByTestId("logout-btn"));

            expect(mockOnLogout).toHaveBeenCalledTimes(1);
        });

        it("does not call handlers when buttons are disabled due to loading", () => {
            const mockOnContinue = jest.fn();
            const mockOnLogout = jest.fn();

            render(
                <SessionTimeoutModal
                    {...defaultProps}
                    onContinue={mockOnContinue}
                    onLogout={mockOnLogout}
                    loading={true}
                />
            );

            fireEvent.click(screen.getByTestId("continue-btn"));
            fireEvent.click(screen.getByTestId("logout-btn"));

            expect(mockOnContinue).not.toHaveBeenCalled();
            expect(mockOnLogout).not.toHaveBeenCalled();
        });
    });

    //   describe("Loading State", () => {
    //     // it("disables both buttons when loading is true", () => {
    //     //   render(<SessionTimeoutModal {...defaultProps} loading={true} />);

    //     //   const logoutBtn = screen.getByTestId("logout-btn");
    //     //   const continueBtn = screen.getByTestId("continue-btn");

    //     //   expect(logoutBtn).toBeDisabled();
    //     //   expect(continueBtn).toBeDisabled();
    //     //   expect(logoutBtn).toHaveAttribute("data-loading", "true");
    //     //   expect(continueBtn).toHaveAttribute("data-loading", "false"); // Only logout button shows loading
    //     // });

    //     // it("enables both buttons when loading is false", () => {
    //     //   render(<SessionTimeoutModal {...defaultProps} loading={false} />);

    //     //   const logoutBtn = screen.getByTestId("logout-btn");
    //     //   const continueBtn = screen.getByTestId("continue-btn");

    //     //   expect(logoutBtn).not.toBeDisabled();
    //     //   expect(continueBtn).not.toBeDisabled();
    //     //   expect(logoutBtn).toHaveAttribute("data-loading", "false");
    //     //   expect(continueBtn).toHaveAttribute("data-loading", "false");
    //     // });
    //   });

    describe("Button Properties", () => {
        it("renders logout button with correct variant", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            const logoutBtn = screen.getByTestId("logout-btn");

            expect(logoutBtn).toHaveAttribute("data-variant", "outline-destructive");
            expect(logoutBtn).toHaveAttribute("data-fullwidth", "true");
        });

        it("renders continue button with correct properties", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            const continueBtn = screen.getByTestId("continue-btn");

            expect(continueBtn).toHaveAttribute("data-fullwidth", "true");
            expect(continueBtn).not.toHaveAttribute("data-variant");
        });
    });

    describe("Modal Behavior", () => {
        it("prevents closing by clicking outside (onRequestClose is empty function)", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            const closeBtn = screen.getByTestId("modal-close-btn");
            fireEvent.click(closeBtn);

            // Modal should still be open since onRequestClose is an empty function
            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper heading structure", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            const heading = screen.getByRole("heading", { level: 3 });
            expect(heading).toHaveTextContent("Session Timeout Warning");
        });

        it("has descriptive text for screen readers", () => {
            render(<SessionTimeoutModal {...defaultProps} />);

            expect(
                screen.getByText("Your session will expire due to inactivity. Do you want to continue?")
            ).toBeInTheDocument();
        });

        it("displays time in a readable format", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={90} />);

            const timeDisplay = screen.getByText("01:30");
            expect(timeDisplay).toHaveClass("text-xl", "font-mono", "font-bold");
        });
    });

    describe("Edge Cases", () => {
        it("handles zero timeLeft", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={0} />);

            expect(screen.getByText("00:00")).toBeInTheDocument();
            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("handles very large timeLeft values", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={9999} />);

            expect(screen.getByText("166:39")).toBeInTheDocument();
        });

        it("handles fractional seconds (should floor the value)", () => {
            render(<SessionTimeoutModal {...defaultProps} timeLeft={30.7} />);

            expect(screen.getByText("00:30.7")).toBeInTheDocument();
        });
    });

    describe("Component State Changes", () => {
        it("updates time display when timeLeft prop changes", () => {
            const { rerender } = render(<SessionTimeoutModal {...defaultProps} timeLeft={30} />);

            expect(screen.getByText("00:30")).toBeInTheDocument();

            rerender(<SessionTimeoutModal {...defaultProps} timeLeft={15} />);

            expect(screen.getByText("00:15")).toBeInTheDocument();
            expect(screen.queryByText("00:30")).not.toBeInTheDocument();
        });

        it("updates loading state when loading prop changes", () => {
            const { rerender } = render(<SessionTimeoutModal {...defaultProps} loading={false} />);

            expect(screen.getByTestId("logout-btn")).not.toBeDisabled();
            expect(screen.getByTestId("continue-btn")).not.toBeDisabled();

            rerender(<SessionTimeoutModal {...defaultProps} loading={true} />);

            expect(screen.getByTestId("logout-btn")).toBeDisabled();
            expect(screen.getByTestId("continue-btn")).toBeDisabled();
        });

        it("shows and hides modal based on isOpen prop", () => {
            const { rerender } = render(<SessionTimeoutModal {...defaultProps} isOpen={true} />);

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();

            rerender(<SessionTimeoutModal {...defaultProps} isOpen={false} />);

            expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
        });
    });
});
