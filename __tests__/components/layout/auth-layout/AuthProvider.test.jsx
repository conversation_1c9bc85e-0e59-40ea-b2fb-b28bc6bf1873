"use client";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import AuthProvider from "@/components/layout/auth-layout/AuthProvider";
import { useTokenRefresh } from "@/hooks/useTokenRefresh";
import { useRouteGuard } from "@/hooks/useRouteGuard";
import { useSessionTimeout } from "@/hooks/useSessionTimeout";
import { useAppDispatch } from "@/redux/hooks";
import { usePathname } from "next/navigation";
import { cookies } from "@/lib/cookies";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { updateToken } from "@/redux/features/user";
import { refreshAccessToken, logTokenExpiry } from "@/lib/token-refresh";

// Mock all dependencies
jest.mock("@/hooks/useTokenRefresh", () => ({
    useTokenRefresh: jest.fn(),
}));

jest.mock("@/hooks/useRouteGuard", () => ({
    useRouteGuard: jest.fn(),
}));

jest.mock("@/hooks/useSessionTimeout", () => ({
    useSessionTimeout: jest.fn(),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
}));

jest.mock("next/navigation", () => ({
    usePathname: jest.fn(),
}));

jest.mock("@/lib/cookies", () => ({
    cookies: {
        isAuthenticated: jest.fn(),
        getToken: jest.fn(),
        getRefreshToken: jest.fn(),
    },
}));

jest.mock("@/redux/slices/auth/signinSlice", () => ({
    signinActions: {
        updateToken: jest.fn(),
    },
}));

jest.mock("@/redux/features/user", () => ({
    updateToken: jest.fn(),
}));

jest.mock("@/lib/token-refresh", () => ({
    refreshAccessToken: jest.fn(),
    logTokenExpiry: jest.fn(),
}));

// Mock SessionTimeoutModal component
jest.mock("@/components/layout/auth-layout/SessionTimeoutModal", () => {
    return function MockSessionTimeoutModal({ isOpen, timeLeft, onContinue, onLogout, loading }) {
        return isOpen ? (
            <div data-testid="session-timeout-modal">
                <span data-testid="time-left">{timeLeft}</span>
                <button data-testid="continue-btn" onClick={onContinue}>
                    Continue
                </button>
                <button data-testid="logout-btn" onClick={onLogout} disabled={loading}>
                    Logout
                </button>
            </div>
        ) : null;
    };
});

describe("AuthProvider", () => {
    // Common mocks
    const mockCheckAndRefreshToken = jest.fn();
    const mockDispatch = jest.fn();
    const mockResetInactivityTimer = jest.fn();
    const mockContinueSession = jest.fn();
    const mockHandleLogout = jest.fn();

    // Store original timers
    const originalSetInterval = global.setInterval;
    const originalClearInterval = global.clearInterval;
    const originalConsoleLog = console.log;

    // Setup for each test
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock console.log to avoid noise
        console.log = jest.fn();

        // Setup proper timer mocks
        global.setInterval = jest.fn((callback, delay) => {
            return originalSetInterval(callback, delay);
        });
        global.clearInterval = jest.fn((id) => {
            if (originalClearInterval) {
                originalClearInterval(id);
            }
        });

        // Mock useTokenRefresh
        useTokenRefresh.mockReturnValue({
            checkAndRefreshToken: mockCheckAndRefreshToken,
        });

        // Mock useRouteGuard
        useRouteGuard.mockReturnValue({});

        // Mock useAppDispatch
        useAppDispatch.mockReturnValue(mockDispatch);

        // Mock usePathname - default to non-login route
        usePathname.mockReturnValue("/dashboard");

        // Mock updateToken action creators
        signinActions.updateToken.mockReturnValue({ type: "signin/updateToken" });
        updateToken.mockReturnValue({ type: "user/updateToken" });

        // Mock refreshAccessToken
        refreshAccessToken.mockResolvedValue("new-access-token");

        // Mock logTokenExpiry
        logTokenExpiry.mockImplementation(() => {});

        // Mock document.hidden
        Object.defineProperty(document, "hidden", {
            configurable: true,
            writable: true,
            value: false,
        });

        // Mock document event methods
        const eventListeners = {};
        document.addEventListener = jest.fn((event, callback) => {
            eventListeners[event] = callback;
        });
        document.removeEventListener = jest.fn();

        // Store event listeners for testing
        document._eventListeners = eventListeners;
    });

    afterEach(() => {
        // Restore original timers and console
        global.setInterval = originalSetInterval;
        global.clearInterval = originalClearInterval;
        console.log = originalConsoleLog;

        // Clean up event listeners
        delete document._eventListeners;
    });

    it("renders children correctly", () => {
        // Mock session timeout hook for unauthenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: false,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(false);

        render(
            <AuthProvider>
                <div data-testid="child-element">Child Content</div>
            </AuthProvider>
        );

        expect(screen.getByTestId("child-element")).toBeInTheDocument();
        expect(screen.getByText("Child Content")).toBeInTheDocument();
        expect(screen.queryByTestId("session-timeout-modal")).not.toBeInTheDocument();
    });

    it("sets up token refresh interval when authenticated", async () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);
        cookies.getToken.mockReturnValue("access-token");
        cookies.getRefreshToken.mockReturnValue("refresh-token");

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Wait for async operations
        await waitFor(() => {
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        expect(global.setInterval).toHaveBeenCalledWith(expect.any(Function), 4 * 60 * 1000);
        expect(logTokenExpiry).toHaveBeenCalled();
    });

    it("cleans up interval on unmount", () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);

        const { unmount } = render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        unmount();

        expect(global.clearInterval).toHaveBeenCalled();
    });

    it("handles visibility change events when authenticated", async () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);
        cookies.getToken.mockReturnValue("access-token");
        cookies.getRefreshToken.mockReturnValue("refresh-token");

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Check if event listener was added
        expect(document.addEventListener).toHaveBeenCalledWith("visibilitychange", expect.any(Function));

        // Get the visibility change handler
        const visibilityHandler = document._eventListeners.visibilitychange;

        // Simulate document becoming visible (not hidden)
        Object.defineProperty(document, "hidden", { value: false, configurable: true });

        // Call the visibility handler
        visibilityHandler();

        // Wait for async operations
        await waitFor(() => {
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        expect(mockResetInactivityTimer).toHaveBeenCalled();

        // Simulate document becoming hidden
        Object.defineProperty(document, "hidden", { value: true, configurable: true });

        // Reset mock call count
        refreshAccessToken.mockClear();
        mockResetInactivityTimer.mockClear();

        visibilityHandler();

        // Functions should not be called again when document is hidden
        expect(refreshAccessToken).not.toHaveBeenCalled();
        expect(mockResetInactivityTimer).not.toHaveBeenCalled();
    });

    it("removes event listener on unmount", () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);

        const { unmount } = render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        unmount();

        // Check if event listener was removed
        expect(document.removeEventListener).toHaveBeenCalledWith("visibilitychange", expect.any(Function));
    });

    it("renders SessionTimeoutModal when authenticated and showWarning is true", () => {
        // Mock session timeout hook for authenticated state with warning
        useSessionTimeout.mockReturnValue({
            showWarning: true,
            timeLeft: 25,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Check if modal is rendered
        expect(screen.getByTestId("session-timeout-modal")).toBeInTheDocument();
        expect(screen.getByTestId("time-left").textContent).toBe("25");
    });

    it("doesn't render SessionTimeoutModal when not authenticated", () => {
        // Mock session timeout hook for unauthenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: true, // Even if warning is true
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: false, // Not authenticated
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(false);

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Modal should not be rendered
        expect(screen.queryByTestId("session-timeout-modal")).not.toBeInTheDocument();
    });

    it("passes correct props to SessionTimeoutModal", () => {
        // Mock session timeout hook with specific values
        useSessionTimeout.mockReturnValue({
            showWarning: true,
            timeLeft: 15,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: true, // Set loading to true
        });

        cookies.isAuthenticated.mockReturnValue(true);

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Check modal props
        expect(screen.getByTestId("time-left").textContent).toBe("15");
        expect(screen.getByTestId("logout-btn")).toBeDisabled(); // Should be disabled when loading

        // Test continue button click
        fireEvent.click(screen.getByTestId("continue-btn"));
        expect(mockContinueSession).toHaveBeenCalled();

        // Test logout button click (should not work when disabled)
        fireEvent.click(screen.getByTestId("logout-btn"));
        expect(mockHandleLogout).not.toHaveBeenCalled(); // Should not be called when disabled
    });

    it("handles authentication state changes", async () => {
        // Start with authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);
        cookies.getToken.mockReturnValue("access-token");
        cookies.getRefreshToken.mockReturnValue("refresh-token");

        const { rerender } = render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Wait for initial token refresh
        await waitFor(() => {
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        // Change to unauthenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: false,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(false);

        rerender(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Modal should not be rendered
        expect(screen.queryByTestId("session-timeout-modal")).not.toBeInTheDocument();
    });

    it("handles token refresh with no tokens", async () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);
        cookies.getToken.mockReturnValue(null); // No access token
        cookies.getRefreshToken.mockReturnValue(null); // No refresh token

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Verify that interval was set up
        expect(global.setInterval).toHaveBeenCalledWith(expect.any(Function), 4 * 60 * 1000);

        // Actions should not be dispatched without tokens
        expect(signinActions.updateToken).not.toHaveBeenCalled();
        expect(updateToken).not.toHaveBeenCalled();
    });

    it("handles token refresh interval with tokens", async () => {
        // Mock session timeout hook for authenticated state
        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);
        cookies.getToken.mockReturnValue("access-token");
        cookies.getRefreshToken.mockReturnValue("refresh-token");

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Wait for initial refresh
        await waitFor(() => {
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        // Get the interval callback
        const intervalCallback = global.setInterval.mock.calls[0][0];

        // Execute the interval callback
        await intervalCallback();

        // Wait for the interval callback to complete
        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith({ type: "signin/updateToken" });
        });

        // Check if actions were dispatched
        expect(mockDispatch).toHaveBeenCalledWith({ type: "user/updateToken" });
        expect(signinActions.updateToken).toHaveBeenCalledWith({
            accessToken: "new-access-token",
            refreshToken: "refresh-token",
        });
        expect(updateToken).toHaveBeenCalledWith({ token: "new-access-token" });
    });

    it("doesn't set up token refresh on login routes", () => {
        // Mock pathname to be a login route
        usePathname.mockReturnValue("/auth/login");

        useSessionTimeout.mockReturnValue({
            showWarning: false,
            timeLeft: 30,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Should not set up interval on login routes
        expect(global.setInterval).not.toHaveBeenCalled();
        expect(refreshAccessToken).not.toHaveBeenCalled();
    });

    it("doesn't render SessionTimeoutModal on login routes", () => {
        // Mock pathname to be a login route
        usePathname.mockReturnValue("/auth/login");

        useSessionTimeout.mockReturnValue({
            showWarning: true,
            timeLeft: 25,
            continueSession: mockContinueSession,
            handleLogout: mockHandleLogout,
            resetInactivityTimer: mockResetInactivityTimer,
            isAuthenticated: true,
            loading: false,
        });

        cookies.isAuthenticated.mockReturnValue(true);

        render(
            <AuthProvider>
                <div>Child Content</div>
            </AuthProvider>
        );

        // Modal should not be rendered on login routes
        expect(screen.queryByTestId("session-timeout-modal")).not.toBeInTheDocument();
    });
});
