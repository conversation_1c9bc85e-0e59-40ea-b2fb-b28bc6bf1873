import React from "react";
import { render, screen } from "@testing-library/react";
import ServerProtectedPage from "@/components/examples/server-protected-page";
import { checkPermissions, getUserPermissions } from "@/utils/server-permission-check";
import "@testing-library/jest-dom";

// Mock dependencies
jest.mock("@/utils/server-permission-check", () => ({
    checkPermissions: jest.fn(),
    getUserPermissions: jest.fn(),
}));

// No longer need to mock static permissions

describe("ServerProtectedPage Component", () => {
    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Setup default mock implementations
        checkPermissions.mockResolvedValue(true);
        getUserPermissions.mockResolvedValue(["view_team_members"]);
    });

    test("renders the main content when user has required permissions", async () => {
        // Render the component (need to await since it's an async component)
        const { container } = render(await ServerProtectedPage());

        // Check that the main content is rendered
        expect(screen.getByText("Server Protected Page")).toBeInTheDocument();
        expect(screen.getByText(/This page is protected at the server level/)).toBeInTheDocument();

        // Verify that permission checks were called correctly
        expect(checkPermissions).toHaveBeenCalledWith(["view_team_members"], false, "/dashboard");
        expect(getUserPermissions).toHaveBeenCalled();
    });

    test("doesn't render role management section when user doesn't have the permission", async () => {
        // User has basic permissions but not role management
        getUserPermissions.mockResolvedValue(["view_team_members"]);

        const { container } = render(await ServerProtectedPage());

        // Main content should be rendered
        expect(screen.getByText("Server Protected Page")).toBeInTheDocument();

        // Role management section should not be rendered
        expect(screen.queryByText("Role Management")).not.toBeInTheDocument();
        expect(
            screen.queryByText("This section is only visible to users who can manage roles.")
        ).not.toBeInTheDocument();
    });

    test("renders role management section when user has the permission", async () => {
        // User has both basic and role management permissions
        getUserPermissions.mockResolvedValue(["view_team_members", "create_custom_roles"]);

        const { container } = render(await ServerProtectedPage());

        // Main content should be rendered
        expect(screen.getByText("Server Protected Page")).toBeInTheDocument();

        // Role management section should be rendered
        expect(screen.getByText("Role Management")).toBeInTheDocument();
        expect(screen.getByText("This section is only visible to users who can manage roles.")).toBeInTheDocument();
    });

    test("checkPermissions is called with the correct parameters", async () => {
        const { container } = render(await ServerProtectedPage());

        expect(checkPermissions).toHaveBeenCalledWith(["view_team_members"], false, "/dashboard");
    });

    test("getUserPermissions is called to get additional permissions", async () => {
        const { container } = render(await ServerProtectedPage());

        expect(getUserPermissions).toHaveBeenCalled();
    });
});
