import { renderHook, act } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { useTokenRefresh } from "@/hooks/useTokenRefresh";
import { refreshAccessToken, validateAndRefreshToken } from "@/lib/token-refresh";
import { cookies } from "@/lib/cookies";
import { PATH_AUTH } from "@/routes/path";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
}));

jest.mock("@/lib/token-refresh", () => ({
    refreshAccessToken: jest.fn(),
    validateAndRefreshToken: jest.fn(),
}));

jest.mock("@/lib/cookies", () => ({
    cookies: {
        isAuthenticated: jest.fn(),
    },
}));

jest.mock("@/routes/path", () => ({
    PATH_AUTH: {
        login: "/auth/login",
    },
}));

describe("useTokenRefresh", () => {
    // Mock implementations
    const mockPush = jest.fn();
    const mockRouter = {
        push: mockPush,
        replace: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
        prefetch: jest.fn(),
    };

    // Console spy
    let consoleErrorSpy;

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup router mock
        useRouter.mockReturnValue(mockRouter);

        // Setup default mocks
        cookies.isAuthenticated.mockReturnValue(false);
        validateAndRefreshToken.mockResolvedValue(true);
        refreshAccessToken.mockResolvedValue("new-token");

        // Spy on console.error
        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe("Initialization", () => {
        it("should return the correct interface", () => {
            const { result } = renderHook(() => useTokenRefresh());

            expect(result.current).toEqual({
                checkAndRefreshToken: expect.any(Function),
                forceRefresh: expect.any(Function),
                isAuthenticated: expect.any(Boolean),
            });
        });

        it("should return authentication status from cookies", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useTokenRefresh());

            expect(result.current.isAuthenticated).toBe(true);
            expect(cookies.isAuthenticated).toHaveBeenCalled();
        });

        it("should return false when not authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(false);

            const { result } = renderHook(() => useTokenRefresh());

            expect(result.current.isAuthenticated).toBe(false);
        });
    });

    describe("checkAndRefreshToken", () => {
        it("should return true when token validation succeeds", async () => {
            validateAndRefreshToken.mockResolvedValue(true);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.checkAndRefreshToken();
            });

            expect(returnValue).toBe(true);
            expect(validateAndRefreshToken).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
        });

        it("should return false and redirect when token validation fails", async () => {
            validateAndRefreshToken.mockResolvedValue(false);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.checkAndRefreshToken();
            });

            expect(returnValue).toBe(false);
            expect(validateAndRefreshToken).toHaveBeenCalled();
            expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login);
        });

        it("should handle errors and redirect to login", async () => {
            const error = new Error("Token validation failed");
            validateAndRefreshToken.mockRejectedValue(error);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.checkAndRefreshToken();
            });

            expect(returnValue).toBe(false);
            expect(validateAndRefreshToken).toHaveBeenCalled();
            expect(consoleErrorSpy).toHaveBeenCalledWith("Token refresh error:", error);
            expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login);
        });

        it("should be stable across re-renders", () => {
            const { result, rerender } = renderHook(() => useTokenRefresh());

            const firstCallback = result.current.checkAndRefreshToken;

            rerender();

            const secondCallback = result.current.checkAndRefreshToken;

            expect(firstCallback).toBe(secondCallback);
        });

        it("should handle network errors gracefully", async () => {
            const networkError = new Error("Network error");
            networkError.code = "NETWORK_ERROR";
            validateAndRefreshToken.mockRejectedValue(networkError);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.checkAndRefreshToken();
            });

            expect(returnValue).toBe(false);
            expect(consoleErrorSpy).toHaveBeenCalledWith("Token refresh error:", networkError);
            expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login);
        });
    });

    describe("forceRefresh", () => {
        it("should return new token when force refresh succeeds", async () => {
            const newToken = "new-access-token";
            refreshAccessToken.mockResolvedValue(newToken);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(newToken);
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        it("should return null when force refresh returns null", async () => {
            refreshAccessToken.mockResolvedValue(null);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(null);
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
        });

        it("should handle errors and return null", async () => {
            const error = new Error("Force refresh failed");
            refreshAccessToken.mockRejectedValue(error);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(null);
            expect(refreshAccessToken).toHaveBeenCalledWith(true);
            expect(consoleErrorSpy).toHaveBeenCalledWith("Force refresh error:", error);
        });

        it("should be stable across re-renders", () => {
            const { result, rerender } = renderHook(() => useTokenRefresh());

            const firstCallback = result.current.forceRefresh;

            rerender();

            const secondCallback = result.current.forceRefresh;

            expect(firstCallback).toBe(secondCallback);
        });

        it("should handle timeout errors", async () => {
            const timeoutError = new Error("Request timeout");
            timeoutError.code = "TIMEOUT";
            refreshAccessToken.mockRejectedValue(timeoutError);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(null);
            expect(consoleErrorSpy).toHaveBeenCalledWith("Force refresh error:", timeoutError);
        });

        it("should handle server errors", async () => {
            const serverError = new Error("Internal server error");
            serverError.status = 500;
            refreshAccessToken.mockRejectedValue(serverError);

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(null);
            expect(consoleErrorSpy).toHaveBeenCalledWith("Force refresh error:", serverError);
        });
    });

    describe("Router Integration", () => {
        it("should use the router from useRouter hook", () => {
            renderHook(() => useTokenRefresh());

            expect(useRouter).toHaveBeenCalled();
        });

        it("should redirect to correct login path", async () => {
            validateAndRefreshToken.mockResolvedValue(false);

            const { result } = renderHook(() => useTokenRefresh());

            await act(async () => {
                await result.current.checkAndRefreshToken();
            });

            expect(mockPush).toHaveBeenCalledWith("/auth/login");
        });

        // it("should handle router push errors gracefully", async () => {
        //   validateAndRefreshToken.mockResolvedValue(false)
        //   mockPush.mockImplementation(() => {
        //     throw new Error("Router error")
        //   })

        //   const { result } = renderHook(() => useTokenRefresh())

        //   // Should not throw
        //   await act(async () => {
        //     await result.current.checkAndRefreshToken()
        //   })

        //   expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login)
        // })
    });

    describe("Authentication Status", () => {
        it("should reflect current authentication status", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useTokenRefresh());

            expect(result.current.isAuthenticated).toBe(true);
        });

        it("should update when authentication status changes", () => {
            cookies.isAuthenticated.mockReturnValue(false);

            const { result, rerender } = renderHook(() => useTokenRefresh());

            expect(result.current.isAuthenticated).toBe(false);

            // Change authentication status
            cookies.isAuthenticated.mockReturnValue(true);

            rerender();

            expect(result.current.isAuthenticated).toBe(true);
        });
    });

    describe("Error Handling", () => {
        // it("should handle undefined errors gracefully", async () => {
        //   validateAndRefreshToken.mockRejectedValue(undefined)

        //   const { result } = renderHook(() => useTokenRefresh())

        //   let returnValue
        //   await act(async () => {
        //     returnValue = await result.current.checkAndRefreshToken()
        //   })

        //   expect(returnValue).toBe(false)
        //   expect(consoleErrorSpy).toHaveBeenCalledWith("Token refresh error:", undefined)
        //   expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login)
        // })

        it("should handle string errors", async () => {
            refreshAccessToken.mockRejectedValue("String error");

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe(null);
            expect(consoleErrorSpy).toHaveBeenCalledWith("Force refresh error:", "String error");
        });
    });

    describe("Concurrent Operations", () => {
        it("should handle multiple concurrent checkAndRefreshToken calls", async () => {
            validateAndRefreshToken.mockImplementation(
                () => new Promise((resolve) => setTimeout(() => resolve(true), 100))
            );

            const { result } = renderHook(() => useTokenRefresh());

            let results;
            await act(async () => {
                results = await Promise.all([
                    result.current.checkAndRefreshToken(),
                    result.current.checkAndRefreshToken(),
                    result.current.checkAndRefreshToken(),
                ]);
            });

            expect(results).toEqual([true, true, true]);
            expect(validateAndRefreshToken).toHaveBeenCalledTimes(3);
        });

        it("should handle multiple concurrent forceRefresh calls", async () => {
            refreshAccessToken.mockImplementation(
                () => new Promise((resolve) => setTimeout(() => resolve("token"), 100))
            );

            const { result } = renderHook(() => useTokenRefresh());

            let results;
            await act(async () => {
                results = await Promise.all([
                    result.current.forceRefresh(),
                    result.current.forceRefresh(),
                    result.current.forceRefresh(),
                ]);
            });

            expect(results).toEqual(["token", "token", "token"]);
            expect(refreshAccessToken).toHaveBeenCalledTimes(3);
        });
    });

    describe("Memory Management", () => {
        it("should not cause memory leaks on unmount", () => {
            const { unmount } = renderHook(() => useTokenRefresh());

            // Should not throw
            expect(() => unmount()).not.toThrow();
        });

        it("should maintain callback references with useCallback", () => {
            const { result, rerender } = renderHook(() => useTokenRefresh());

            const initialCallbacks = {
                checkAndRefreshToken: result.current.checkAndRefreshToken,
                forceRefresh: result.current.forceRefresh,
            };

            // Re-render multiple times
            rerender();
            rerender();
            rerender();

            expect(result.current.checkAndRefreshToken).toBe(initialCallbacks.checkAndRefreshToken);
            expect(result.current.forceRefresh).toBe(initialCallbacks.forceRefresh);
        });
    });

    describe("Edge Cases", () => {
        // it("should handle when validateAndRefreshToken returns undefined", async () => {
        //   validateAndRefreshToken.mockResolvedValue(undefined)

        //   const { result } = renderHook(() => useTokenRefresh())

        //   let returnValue
        //   await act(async () => {
        //     returnValue = await result.current.checkAndRefreshToken()
        //   })

        //   expect(returnValue).toBe(false)
        //   expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login)
        // })

        it("should handle when refreshAccessToken returns empty string", async () => {
            refreshAccessToken.mockResolvedValue("");

            const { result } = renderHook(() => useTokenRefresh());

            let returnValue;
            await act(async () => {
                returnValue = await result.current.forceRefresh();
            });

            expect(returnValue).toBe("");
        });

        it("should handle when cookies.isAuthenticated throws", () => {
            cookies.isAuthenticated.mockImplementation(() => {
                throw new Error("Cookie error");
            });

            expect(() => {
                renderHook(() => useTokenRefresh());
            }).toThrow("Cookie error");
        });
    });
});
