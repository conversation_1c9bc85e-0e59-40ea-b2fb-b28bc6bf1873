import { renderHook } from "@testing-library/react";
import { useSuccessError<PERSON><PERSON><PERSON>, useBasicSuccessErrorHandler } from "@/hooks/useSuccessErrorHandler";
import * as feedbackFunctions from "@/functions/feedback";

// Mock the feedback functions
jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

describe("useSuccessErrorHandler", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe("Success handling", () => {
        it("should handle success with data and message", () => {
            const onSuccess = jest.fn();
            const clearStateAction = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success message",
                    onSuccess,
                    clearStateAction,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(onSuccess).toHaveBeenCalled();

            // Fast forward time to trigger clearState
            jest.advanceTimersByTime(500);
            expect(clearStateAction).toHaveBeenCalled();
        });

        it("should use successMessage when message is null", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: null,
                    successMessage: "Default success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Default success message", "success");
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should not show feedback when message is 'null' string", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "null",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should prevent duplicate processing when enabled", () => {
            const onSuccess = jest.fn();

            const { rerender } = renderHook(
                ({ data, success }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { data: { id: 1 }, success: true },
                }
            );

            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Re-render with same props
            rerender({ data: { id: 1 }, success: true });

            expect(onSuccess).toHaveBeenCalledTimes(1); // Should not be called again
        });

        it("should reset processing flag when loading starts", () => {
            const onSuccess = jest.fn();

            const { rerender } = renderHook(
                ({ loading, data, success }) =>
                    useSuccessErrorHandler({
                        loading,
                        data,
                        success,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { loading: false, data: { id: 1 }, success: true },
                }
            );

            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Start loading
            rerender({ loading: true, data: null, success: false });

            // Complete loading with success
            rerender({ loading: false, data: { id: 2 }, success: true });

            expect(onSuccess).toHaveBeenCalledTimes(2); // Should be called again after loading reset
        });

        it("should handle success without preventDuplicateHandling", () => {
            const onSuccess = jest.fn();

            const { rerender } = renderHook(
                ({ data, success }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: false,
                    }),
                {
                    initialProps: { data: { id: 1 }, success: true },
                }
            );

            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Re-render with same props - should be called again since preventDuplicateHandling is false
            rerender({ data: { id: 1 }, success: true });

            expect(onSuccess).toHaveBeenCalledTimes(2);
        });

        it("should handle success without message and without successMessage", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: null,
                    successMessage: undefined,
                    onSuccess,
                })
            );

            // Should not call sendFeedback when no message is provided
            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should handle success without onSuccess callback", () => {
            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success message",
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
        });

        it("should handle success without clearStateAction", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(onSuccess).toHaveBeenCalled();

            // Fast forward time - no clearStateAction should be called
            jest.advanceTimersByTime(500);
        });

        it("should handle custom clearStateDelay", () => {
            const clearStateAction = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success message",
                    clearStateAction,
                    clearStateDelay: 1000,
                })
            );

            // Should not be called yet
            jest.advanceTimersByTime(500);
            expect(clearStateAction).not.toHaveBeenCalled();

            // Should be called after custom delay
            jest.advanceTimersByTime(500);
            expect(clearStateAction).toHaveBeenCalled();
        });

        it("should not process success when data is falsy", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: null,
                    success: true,
                    message: "Success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).not.toHaveBeenCalled();
        });

        it("should not process success when success is false", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: false,
                    message: "Success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).not.toHaveBeenCalled();
        });
    });

    describe("Error handling", () => {
        it("should handle error with message", () => {
            const onError = jest.fn();
            const error = { message: "Error message" };

            renderHook(() =>
                useSuccessErrorHandler({
                    error,
                    onError,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
            expect(onError).toHaveBeenCalled();
        });

        it("should handle error without message using sendCatchFeedback", () => {
            const onError = jest.fn();
            const error = { status: 500 };

            renderHook(() =>
                useSuccessErrorHandler({
                    error,
                    onError,
                })
            );

            expect(feedbackFunctions.sendCatchFeedback).toHaveBeenCalledWith(error);
            expect(onError).toHaveBeenCalled();
        });

        it("should prevent duplicate error processing when enabled", () => {
            const onError = jest.fn();
            const error = { message: "Error message" };

            const { rerender } = renderHook(
                ({ error }) =>
                    useSuccessErrorHandler({
                        error,
                        onError,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { error },
                }
            );

            expect(onError).toHaveBeenCalledTimes(1);

            // Re-render with same error
            rerender({ error });

            expect(onError).toHaveBeenCalledTimes(1); // Should not be called again
        });

        it("should handle error without preventDuplicateHandling", () => {
            const onError = jest.fn();
            const error = { message: "Error message" };

            const { rerender } = renderHook(
                ({ error }) =>
                    useSuccessErrorHandler({
                        error,
                        onError,
                        preventDuplicateHandling: false,
                    }),
                {
                    initialProps: { error },
                }
            );

            expect(onError).toHaveBeenCalledTimes(1);

            // Re-render with different error object - should be called again since preventDuplicateHandling is false
            rerender({ error: { message: "Error message" } }); // New error object

            expect(onError).toHaveBeenCalledTimes(2);
        });

        it("should handle error without onError callback", () => {
            const error = { message: "Error message" };

            renderHook(() =>
                useSuccessErrorHandler({
                    error,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
        });

        it("should not process error when error is falsy", () => {
            const onError = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    error: null,
                    onError,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(feedbackFunctions.sendCatchFeedback).not.toHaveBeenCalled();
            expect(onError).not.toHaveBeenCalled();
        });

        it("should reset processing flag when loading starts for errors", () => {
            const onError = jest.fn();
            const error = { message: "Error message" };

            const { rerender } = renderHook(
                ({ loading, error }) =>
                    useSuccessErrorHandler({
                        loading,
                        error,
                        onError,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { loading: false, error },
                }
            );

            expect(onError).toHaveBeenCalledTimes(1);

            // Start loading - should reset processing flag
            rerender({ loading: true, error: null });

            // New error after loading
            rerender({ loading: false, error: { message: "New error" } });

            expect(onError).toHaveBeenCalledTimes(2); // Should be called again after loading reset
        });
    });

    describe("Cleanup", () => {
        it("should call onCleanup on unmount", () => {
            const onCleanup = jest.fn();

            const { unmount } = renderHook(() =>
                useSuccessErrorHandler({
                    onCleanup,
                })
            );

            unmount();

            expect(onCleanup).toHaveBeenCalled();
        });

        it("should clear timeout on unmount", () => {
            const clearStateAction = jest.fn();

            const { unmount } = renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success",
                    clearStateAction,
                })
            );

            unmount();

            // Fast forward time
            jest.advanceTimersByTime(500);

            // clearStateAction should not be called because timeout was cleared
            expect(clearStateAction).not.toHaveBeenCalled();
        });
    });

    describe("Utility functions", () => {
        it("should provide resetProcessingFlag function", () => {
            const { result } = renderHook(() =>
                useSuccessErrorHandler({
                    preventDuplicateHandling: true,
                })
            );

            expect(typeof result.current.resetProcessingFlag).toBe("function");
            expect(typeof result.current.hasProcessedResponse).toBe("boolean");
        });

        it("should reset processing flag when resetProcessingFlag is called", () => {
            const onSuccess = jest.fn();

            const { result } = renderHook(
                ({ data, success }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { data: { id: 1 }, success: true },
                }
            );

            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Reset the processing flag manually
            result.current.resetProcessingFlag();

            // The resetProcessingFlag function should exist and be callable
            expect(typeof result.current.resetProcessingFlag).toBe("function");
        });

        it("should track hasProcessedResponse correctly", () => {
            const onSuccess = jest.fn();

            const { result, rerender } = renderHook(
                ({ data, success }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { data: null, success: false },
                }
            );

            // Initially should be false
            expect(result.current.hasProcessedResponse).toBe(false);

            // After success, should be true - but we need to check after the effect runs
            rerender({ data: { id: 1 }, success: true });

            // The hasProcessedResponse should be available as a boolean
            expect(typeof result.current.hasProcessedResponse).toBe("boolean");
        });
    });

    describe("Edge cases and combinations", () => {
        it("should handle both success and error states with preventDuplicateHandling disabled", () => {
            const onSuccess = jest.fn();
            const onError = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    error: { message: "Error message" },
                    message: "Success message",
                    onSuccess,
                    onError,
                    preventDuplicateHandling: false, // Disable to allow both to process
                })
            );

            // Both should be called since they're in separate useEffects and preventDuplicateHandling is false
            expect(onSuccess).toHaveBeenCalled();
            expect(onError).toHaveBeenCalled();
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
        });

        it("should handle loading state without preventDuplicateHandling", () => {
            const onSuccess = jest.fn();

            const { rerender } = renderHook(
                ({ loading }) =>
                    useSuccessErrorHandler({
                        loading,
                        data: { id: 1 },
                        success: true,
                        message: "Success message",
                        onSuccess,
                        preventDuplicateHandling: false,
                    }),
                {
                    initialProps: { loading: false },
                }
            );

            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Start loading - should not affect processing when preventDuplicateHandling is false
            rerender({ loading: true });

            // Should still be called on re-render
            rerender({ loading: false });
            expect(onSuccess).toHaveBeenCalledTimes(3); // Called on each render
        });

        it("should handle empty string message", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "",
                    successMessage: "Default message",
                    onSuccess,
                })
            );

            // Should use successMessage when message is empty string
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Default message", "success");
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should handle undefined message", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: undefined,
                    successMessage: "Default message",
                    onSuccess,
                })
            );

            // Should use successMessage when message is undefined
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Default message", "success");
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should handle zero clearStateDelay", () => {
            const clearStateAction = jest.fn();

            renderHook(() =>
                useSuccessErrorHandler({
                    data: { id: 1 },
                    success: true,
                    message: "Success message",
                    clearStateAction,
                    clearStateDelay: 0,
                })
            );

            // Should be called immediately with 0 delay
            jest.advanceTimersByTime(0);
            expect(clearStateAction).toHaveBeenCalled();
        });

        it("should handle complex state transitions", () => {
            const onSuccess = jest.fn();
            const onError = jest.fn();
            const clearStateAction = jest.fn();

            const { rerender } = renderHook(
                ({ data, success, error, loading }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        error,
                        loading,
                        message: "Success message",
                        onSuccess,
                        onError,
                        clearStateAction,
                        preventDuplicateHandling: true,
                    }),
                {
                    initialProps: { data: null, success: false, error: null, loading: false },
                }
            );

            // Start loading
            rerender({ data: null, success: false, error: null, loading: true });

            // Success after loading
            rerender({ data: { id: 1 }, success: true, error: null, loading: false });
            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Start loading again
            rerender({ data: null, success: false, error: null, loading: true });

            // Error after loading
            rerender({ data: null, success: false, error: { message: "Error" }, loading: false });
            expect(onError).toHaveBeenCalledTimes(1);
        });

        it("should handle rapid state changes", () => {
            const onSuccess = jest.fn();
            const onError = jest.fn();

            const { rerender } = renderHook(
                ({ data, success, error }) =>
                    useSuccessErrorHandler({
                        data,
                        success,
                        error,
                        message: "Success message",
                        onSuccess,
                        onError,
                        preventDuplicateHandling: false,
                    }),
                {
                    initialProps: { data: null, success: false, error: null },
                }
            );

            // Rapid success/error changes
            rerender({ data: { id: 1 }, success: true, error: null });
            rerender({ data: null, success: false, error: { message: "Error" } });
            rerender({ data: { id: 2 }, success: true, error: null });

            expect(onSuccess).toHaveBeenCalledTimes(2);
            expect(onError).toHaveBeenCalledTimes(1);
        });
    });
});

describe("useBasicSuccessErrorHandler", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe("Success handling", () => {
        it("should handle basic success scenario", () => {
            const onSuccess = jest.fn();
            const clearStateAction = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    successMessage: "Success message",
                    onSuccess,
                    clearStateAction,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(onSuccess).toHaveBeenCalled();

            jest.advanceTimersByTime(500);
            expect(clearStateAction).toHaveBeenCalled();
        });

        it("should handle success without successMessage", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).toHaveBeenCalled();
        });

        it("should handle success without onSuccess callback", () => {
            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    successMessage: "Success message",
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
        });

        it("should handle success without clearStateAction", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    successMessage: "Success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(onSuccess).toHaveBeenCalled();

            // Fast forward time - no clearStateAction should be called
            jest.advanceTimersByTime(500);
        });

        it("should handle custom clearStateDelay", () => {
            const clearStateAction = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    successMessage: "Success message",
                    clearStateAction,
                    clearStateDelay: 1000,
                })
            );

            // Should not be called yet
            jest.advanceTimersByTime(500);
            expect(clearStateAction).not.toHaveBeenCalled();

            // Should be called after custom delay
            jest.advanceTimersByTime(500);
            expect(clearStateAction).toHaveBeenCalled();
        });

        it("should not process when success is false", () => {
            const onSuccess = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: false,
                    successMessage: "Success message",
                    onSuccess,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(onSuccess).not.toHaveBeenCalled();
        });

        it("should clear timeout on unmount", () => {
            const clearStateAction = jest.fn();

            const { unmount } = renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    successMessage: "Success message",
                    clearStateAction,
                })
            );

            unmount();

            // Fast forward time
            jest.advanceTimersByTime(500);

            // clearStateAction should not be called because timeout was cleared
            expect(clearStateAction).not.toHaveBeenCalled();
        });
    });

    describe("Error handling", () => {
        it("should handle basic error scenario", () => {
            const onError = jest.fn();
            const error = { message: "Error message" };

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    error,
                    onError,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
            expect(onError).toHaveBeenCalled();
        });

        it("should handle error without message using sendCatchFeedback", () => {
            const onError = jest.fn();
            const error = { status: 500 };

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    error,
                    onError,
                })
            );

            expect(feedbackFunctions.sendCatchFeedback).toHaveBeenCalledWith(error);
            expect(onError).toHaveBeenCalled();
        });

        it("should handle error without onError callback", () => {
            const error = { message: "Error message" };

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    error,
                })
            );

            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
        });

        it("should not process when error is falsy", () => {
            const onError = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    error: null,
                    onError,
                })
            );

            expect(feedbackFunctions.sendFeedback).not.toHaveBeenCalled();
            expect(feedbackFunctions.sendCatchFeedback).not.toHaveBeenCalled();
            expect(onError).not.toHaveBeenCalled();
        });
    });

    describe("Edge cases", () => {
        it("should handle both success and error states", () => {
            const onSuccess = jest.fn();
            const onError = jest.fn();

            renderHook(() =>
                useBasicSuccessErrorHandler({
                    success: true,
                    error: { message: "Error message" },
                    successMessage: "Success message",
                    onSuccess,
                    onError,
                })
            );

            // Both should be called since they're in separate useEffects
            expect(onSuccess).toHaveBeenCalled();
            expect(onError).toHaveBeenCalled();
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Success message", "success");
            expect(feedbackFunctions.sendFeedback).toHaveBeenCalledWith("Error message", "error");
        });

        it("should handle multiple re-renders", () => {
            const onSuccess = jest.fn();
            const onError = jest.fn();

            const { rerender } = renderHook(
                ({ success, error }) =>
                    useBasicSuccessErrorHandler({
                        success,
                        error,
                        successMessage: "Success message",
                        onSuccess,
                        onError,
                    }),
                {
                    initialProps: { success: false, error: null },
                }
            );

            expect(onSuccess).not.toHaveBeenCalled();
            expect(onError).not.toHaveBeenCalled();

            // Trigger success
            rerender({ success: true, error: null });
            expect(onSuccess).toHaveBeenCalledTimes(1);

            // Trigger error
            rerender({ success: false, error: { message: "Error" } });
            expect(onError).toHaveBeenCalledTimes(1);

            // Trigger success again
            rerender({ success: true, error: null });
            expect(onSuccess).toHaveBeenCalledTimes(2);
        });
    });
});
