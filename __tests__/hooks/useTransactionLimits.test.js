import { renderHook, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { useTransactionLimits } from "../../src/hooks/useTransactionLimits";
import transactionLimitsReducer from "../../src/redux/slices/transactionLimitsSlice";
import {
    updateTransactionLimit,
    updateTransactionLimitWithToken,
} from "../../src/redux/actions/settingsTransactionLimitsActions";

// Mock the actions
jest.mock("../../src/redux/actions/settingsTransactionLimitsActions", () => {
    const createMockAsyncThunk = (name) => {
        const mockFn = jest.fn(() => ({ type: name }));
        mockFn.pending = { type: `${name}/pending` };
        mockFn.fulfilled = { type: `${name}/fulfilled` };
        mockFn.rejected = { type: `${name}/rejected` };
        return mockFn;
    };

    return {
        fetchTransactionLimits: createMockAsyncThunk("transactionLimits/fetchLimits"),
        fetchTransactionLimit: createMockAsyncThunk("transactionLimits/fetchLimit"),
        updateTransactionLimit: createMockAsyncThunk("transactionLimits/updateLimit"),
        updateTransactionLimitWithToken: createMockAsyncThunk("transactionLimits/updateWithToken"),
        mapLimitTypeToApi: jest.fn((title) => {
            const mapping = {
                "FCMB Transfer": "FCMB_TRANSFER",
                "Other Bank Transfer": "OTHER_BANK_TRANSFER",
                "Bill Payment": "BILL_PAYMENT",
            };
            return mapping[title] || title;
        }),
    };
});

describe("useTransactionLimits Hook", () => {
    let store;
    let wrapper;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                transactionLimits: transactionLimitsReducer,
                corporate: () => ({ corporateId: "test-corp-id" }),
            },
        });

        wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;

        jest.clearAllMocks();
    });

    describe("Hook Initialization", () => {
        it("should return all required functions and state", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            expect(result.current).toHaveProperty("limits");
            expect(result.current).toHaveProperty("getLimits");
            expect(result.current).toHaveProperty("getLimit");
            expect(result.current).toHaveProperty("updateLimits");
            expect(result.current).toHaveProperty("isLoading");
            expect(result.current).toHaveProperty("error");
            expect(result.current).toHaveProperty("isUpdating");
            expect(result.current).toHaveProperty("updateSuccess");
            expect(result.current).toHaveProperty("updateError");
            expect(result.current).toHaveProperty("resetUpdateState");
        });

        it("should initialize with correct default state", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            expect(result.current.limits).toEqual([]);
            expect(result.current.isLoading).toBe(false);
            expect(result.current.error).toBe(null);
            expect(result.current.isUpdating).toBe(false);
            expect(result.current.updateSuccess).toBe(false);
            expect(result.current.updateError).toBe(null);
        });
    });

    describe("updateLimits function", () => {
        const mockLimits = {
            daily: 1000000,
            perTransaction: 500000,
            maximum: 5000000,
        };

        it("should use regular updateTransactionLimit when no token provided", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits);
            });

            expect(updateTransactionLimit).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
            });

            expect(updateTransactionLimitWithToken).not.toHaveBeenCalled();
        });

        it("should use updateTransactionLimitWithToken when token provided", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });
            const token = "test-mfa-token";

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, token);
            });

            expect(updateTransactionLimitWithToken).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
                token: token,
            });

            expect(updateTransactionLimit).not.toHaveBeenCalled();
        });

        it("should use regular updateTransactionLimit when token is empty string", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, "");
            });

            expect(updateTransactionLimit).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
            });

            expect(updateTransactionLimitWithToken).not.toHaveBeenCalled();
        });

        it("should use regular updateTransactionLimit when token is null", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, null);
            });

            expect(updateTransactionLimit).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
            });

            expect(updateTransactionLimitWithToken).not.toHaveBeenCalled();
        });

        it("should map different limit types correctly", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            act(() => {
                result.current.updateLimits("Other Bank Transfer", mockLimits, "test-token");
            });

            expect(updateTransactionLimitWithToken).toHaveBeenCalledWith({
                data: {
                    limitType: "OTHER_BANK_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
                token: "test-token",
            });
        });

        it("should handle bill payment limits", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            act(() => {
                result.current.updateLimits("Bill Payment", mockLimits);
            });

            expect(updateTransactionLimit).toHaveBeenCalledWith({
                data: {
                    limitType: "BILL_PAYMENT",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
            });
        });
    });

    describe("State Updates", () => {
        it("should reflect loading state correctly", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            // Mock loading state
            act(() => {
                store.dispatch({
                    type: "transactionLimits/updateWithToken/pending",
                });
            });

            expect(result.current.isUpdating).toBe(true);
        });

        it("should reflect success state correctly", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            // Mock success state
            act(() => {
                store.dispatch({
                    type: "transactionLimits/updateWithToken/fulfilled",
                    payload: {
                        limitType: "FCMB_TRANSFER",
                        daily: 1000000,
                        perTransaction: 500000,
                        maximum: 5000000,
                    },
                });
            });

            expect(result.current.updateSuccess).toBe(true);
        });

        it("should reflect error state correctly", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });

            // Mock error state
            act(() => {
                store.dispatch({
                    type: "transactionLimits/updateWithToken/rejected",
                    payload: "Update failed",
                });
            });

            expect(result.current.updateError).toBe("Update failed");
        });
    });

    describe("Edge Cases", () => {
        it("should handle undefined token", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });
            const mockLimits = {
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, undefined);
            });

            expect(updateTransactionLimit).toHaveBeenCalled();
            expect(updateTransactionLimitWithToken).not.toHaveBeenCalled();
        });

        it("should handle very long token", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });
            const longToken = "a".repeat(1000);
            const mockLimits = {
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, longToken);
            });

            expect(updateTransactionLimitWithToken).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
                token: longToken,
            });
        });

        it("should handle special characters in token", () => {
            const { result } = renderHook(() => useTransactionLimits(), { wrapper });
            const specialToken = "token+with/special=chars&more";
            const mockLimits = {
                daily: 1000000,
                perTransaction: 500000,
                maximum: 5000000,
            };

            act(() => {
                result.current.updateLimits("FCMB Transfer", mockLimits, specialToken);
            });

            expect(updateTransactionLimitWithToken).toHaveBeenCalledWith({
                data: {
                    limitType: "FCMB_TRANSFER",
                    daily: mockLimits.daily,
                    perTransaction: mockLimits.perTransaction,
                    maximum: mockLimits.maximum,
                },
                token: specialToken,
            });
        });
    });

    describe("Function Stability", () => {
        it("should maintain function references across re-renders", () => {
            const { result, rerender } = renderHook(() => useTransactionLimits(), { wrapper });

            const firstUpdateLimits = result.current.updateLimits;
            const firstGetLimits = result.current.getLimits;

            rerender();

            expect(result.current.updateLimits).toBe(firstUpdateLimits);
            expect(result.current.getLimits).toBe(firstGetLimits);
        });
    });
});
