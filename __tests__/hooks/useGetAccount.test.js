/**
 * @file useGetAccount.test.js
 * @purpose Test for useGetAccount hook functionality
 */

import { renderHook, act, waitFor } from "@testing-library/react";
import useGetAccountDetails from "@/hooks/useGetAccount";
import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";

// Mock dependencies
jest.mock("@/api/axios");
jest.mock("@/functions/feedback");

const mockAcctsAxios = acctsAxios;
const mockSendCatchFeedback = sendCatchFeedback;

describe("useGetAccountDetails Hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("API Call Functionality", () => {
        test("should make API call with correct account number", async () => {
            const mockResponseData = {
                balance: 2500.5,
                accountName: "Business Account",
                accountNumber: "**********",
            };

            mockAcctsAxios.get.mockResolvedValue({ data: mockResponseData });

            const { result } = renderHook(() => useGetAccountDetails());

            let response;
            await act(async () => {
                response = await result.current.getDetails("**********");
            });

            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/**********/details");
            expect(response).toEqual(mockResponseData);
            expect(result.current.details).toEqual(mockResponseData);
        });

        test("should handle API errors correctly", async () => {
            const mockError = new Error("Network Error");
            mockAcctsAxios.get.mockRejectedValue(mockError);

            const { result } = renderHook(() => useGetAccountDetails());

            let response;
            await act(async () => {
                response = await result.current.getDetails("**********");
            });

            expect(mockSendCatchFeedback).toHaveBeenCalledWith(mockError);
            expect(response).toBeUndefined();
            expect(result.current.details).toBeUndefined();
        });

        test("should manage loading state correctly during API calls", async () => {
            let resolvePromise;
            const apiPromise = new Promise((resolve) => {
                resolvePromise = resolve;
            });

            mockAcctsAxios.get.mockReturnValue(apiPromise);

            const { result } = renderHook(() => useGetAccountDetails());

            // Initial state
            expect(result.current.loading).toBe(false);

            // Start API call
            act(() => {
                result.current.getDetails("**********");
            });

            // Should be loading
            expect(result.current.loading).toBe(true);

            // Resolve the API call
            await act(async () => {
                resolvePromise({ data: { balance: 500 } });
                await apiPromise;
            });

            // Should no longer be loading
            expect(result.current.loading).toBe(false);
        });

        test("should set loading to false even when API call fails", async () => {
            mockAcctsAxios.get.mockRejectedValue(new Error("API Error"));

            const { result } = renderHook(() => useGetAccountDetails());

            await act(async () => {
                await result.current.getDetails("**********");
            });

            expect(result.current.loading).toBe(false);
        });
    });

    describe("State Management", () => {
        test("should initialize with correct default state", () => {
            const { result } = renderHook(() => useGetAccountDetails());

            expect(result.current.loading).toBe(false);
            expect(result.current.details).toBeUndefined();
            expect(typeof result.current.getDetails).toBe("function");
        });

        test("should update details state after successful API call", async () => {
            const mockData = {
                balance: 1500,
                accountName: "Savings Account",
                accountNumber: "**********",
            };

            mockAcctsAxios.get.mockResolvedValue({ data: mockData });

            const { result } = renderHook(() => useGetAccountDetails());

            await act(async () => {
                await result.current.getDetails("**********");
            });

            expect(result.current.details).toEqual(mockData);
        });

        test("should clear details state when API call fails", async () => {
            // First, set some data
            mockAcctsAxios.get.mockResolvedValueOnce({
                data: { balance: 100, accountName: "Test" },
            });

            const { result } = renderHook(() => useGetAccountDetails());

            await act(async () => {
                await result.current.getDetails("**********");
            });

            expect(result.current.details).toBeDefined();

            // Now make it fail
            mockAcctsAxios.get.mockRejectedValueOnce(new Error("Failed"));

            await act(async () => {
                await result.current.getDetails("**********");
            });

            expect(result.current.details).toBeUndefined();
        });
    });

    describe("Concurrent Calls Handling", () => {
        test("should handle multiple concurrent API calls correctly", async () => {
            mockAcctsAxios.get
                .mockResolvedValueOnce({ data: { balance: 100, accountNumber: "acc1" } })
                .mockResolvedValueOnce({ data: { balance: 200, accountNumber: "acc2" } })
                .mockResolvedValueOnce({ data: { balance: 300, accountNumber: "acc3" } });

            const { result } = renderHook(() => useGetAccountDetails());

            const promises = [];

            await act(async () => {
                promises.push(result.current.getDetails("acc1"));
                promises.push(result.current.getDetails("acc2"));
                promises.push(result.current.getDetails("acc3"));

                await Promise.all(promises);
            });

            expect(mockAcctsAxios.get).toHaveBeenCalledTimes(3);
            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/acc1/details");
            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/acc2/details");
            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/acc3/details");
        });

        test("should handle concurrent calls and reflect loading state correctly", async () => {
            let resolveFirst, resolveSecond;

            const firstPromise = new Promise((resolve) => {
                resolveFirst = resolve;
            });
            const secondPromise = new Promise((resolve) => {
                resolveSecond = resolve;
            });

            mockAcctsAxios.get.mockReturnValueOnce(firstPromise).mockReturnValueOnce(secondPromise);

            const { result } = renderHook(() => useGetAccountDetails());

            // Start first call
            act(() => {
                result.current.getDetails("first");
            });

            expect(result.current.loading).toBe(true);

            // Start second call while first is still pending
            act(() => {
                result.current.getDetails("second");
            });

            expect(result.current.loading).toBe(true);

            // Resolve both calls
            await act(async () => {
                resolveFirst({ data: { balance: 100 } });
                resolveSecond({ data: { balance: 200 } });
                await Promise.all([firstPromise, secondPromise]);
            });

            // Should be done loading after both calls complete
            expect(result.current.loading).toBe(false);
        });
    });

    describe("Edge Cases", () => {
        test("should handle empty account number gracefully", async () => {
            mockAcctsAxios.get.mockResolvedValue({ data: null });

            const { result } = renderHook(() => useGetAccountDetails());

            let response;
            await act(async () => {
                response = await result.current.getDetails("");
            });

            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts//details");
            expect(response).toBeNull();
        });

        test("should handle null account number gracefully", async () => {
            mockAcctsAxios.get.mockResolvedValue({ data: null });

            const { result } = renderHook(() => useGetAccountDetails());

            let response;
            await act(async () => {
                response = await result.current.getDetails(null);
            });

            expect(mockAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/null/details");
            expect(response).toBeNull();
        });

        test("should handle undefined response data", async () => {
            mockAcctsAxios.get.mockResolvedValue({ data: undefined });

            const { result } = renderHook(() => useGetAccountDetails());

            let response;
            await act(async () => {
                response = await result.current.getDetails("**********");
            });

            expect(response).toBeUndefined();
            expect(result.current.details).toBeUndefined();
        });
    });
});
