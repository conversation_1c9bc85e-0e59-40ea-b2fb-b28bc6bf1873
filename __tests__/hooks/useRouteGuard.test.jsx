import { renderHook, act } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { useTokenRefresh } from "@/hooks/useTokenRefresh";
import { PATH_AUTH } from "@/routes/path";
import { useRouteGuard } from "@/hooks/useRouteGuard";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
}));

jest.mock("@/hooks/useTokenRefresh", () => ({
    useTokenRefresh: jest.fn(),
}));

jest.mock("@/routes/path", () => ({
    PATH_AUTH: {
        login: "/auth/login",
    },
}));

describe("useRouteGuard", () => {
    // Mock implementations
    const mockPush = jest.fn();
    const mockCheckAndRefreshToken = jest.fn();

    // Mock event listeners
    const addEventListenerMock = jest.fn();
    const removeEventListenerMock = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup router mock
        useRouter.mockReturnValue({
            push: mockPush,
        });

        // Setup token refresh mock
        useTokenRefresh.mockReturnValue({
            checkAndRefreshToken: mockCheckAndRefreshToken,
        });

        // Setup window event listeners
        window.addEventListener = addEventListenerMock;
        window.removeEventListener = removeEventListenerMock;
    });

    afterEach(() => {
        // Restore original window methods after tests
        jest.restoreAllMocks();
    });

    it("should initialize correctly and return checkAndRefreshToken", () => {
        const { result } = renderHook(() => useRouteGuard());

        expect(result.current).toEqual({
            checkAndRefreshToken: mockCheckAndRefreshToken,
        });
    });

    it("should add beforeunload event listener on mount", () => {
        renderHook(() => useRouteGuard());

        expect(addEventListenerMock).toHaveBeenCalledWith("beforeunload", expect.any(Function));
    });

    it("should remove beforeunload event listener on unmount", () => {
        const { unmount } = renderHook(() => useRouteGuard());

        unmount();

        expect(removeEventListenerMock).toHaveBeenCalledWith("beforeunload", expect.any(Function));
    });

    it("should call checkAndRefreshToken when beforeunload event is triggered", () => {
        renderHook(() => useRouteGuard());

        // Get the beforeunload handler
        const beforeUnloadHandler = addEventListenerMock.mock.calls.find((call) => call[0] === "beforeunload")[1];

        // Trigger the handler
        beforeUnloadHandler();

        expect(mockCheckAndRefreshToken).toHaveBeenCalledTimes(1);
    });

    describe("Route protection behavior", () => {
        // Since we can't directly test handleRouteChange (it's inside useEffect),
        // we'll test the behavior by mocking checkAndRefreshToken responses

        it("should allow navigation for authenticated users", async () => {
            // Setup mock to return authenticated
            mockCheckAndRefreshToken.mockResolvedValue(true);

            renderHook(() => useRouteGuard());

            // Get the beforeunload handler to verify it's set up correctly
            expect(addEventListenerMock).toHaveBeenCalledWith("beforeunload", expect.any(Function));
        });

        it("should redirect to login for unauthenticated users", async () => {
            // Setup mock to return unauthenticated
            mockCheckAndRefreshToken.mockResolvedValue(false);

            renderHook(() => useRouteGuard());

            // Get the beforeunload handler to verify it's set up correctly
            expect(addEventListenerMock).toHaveBeenCalledWith("beforeunload", expect.any(Function));
        });
    });

    it("should maintain stable dependencies in useEffect", () => {
        const { rerender } = renderHook(() => useRouteGuard());

        // First render
        expect(addEventListenerMock).toHaveBeenCalledTimes(1);

        // Re-render with same dependencies
        rerender();

        // Should not add event listener again if dependencies are stable
        expect(addEventListenerMock).toHaveBeenCalledTimes(1);
    });

    it("should update event listener if router changes", () => {
        const { rerender } = renderHook(() => useRouteGuard());

        // First render
        expect(addEventListenerMock).toHaveBeenCalledTimes(1);

        // Change router mock to simulate dependency change
        const newMockPush = jest.fn();
        useRouter.mockReturnValue({
            push: newMockPush,
        });

        // Re-render
        rerender();

        // Should remove old listener and add new one
        expect(removeEventListenerMock).toHaveBeenCalledTimes(1);
        expect(addEventListenerMock).toHaveBeenCalledTimes(2);
    });

    it("should update event listener if checkAndRefreshToken changes", () => {
        const { rerender } = renderHook(() => useRouteGuard());

        // First render
        expect(addEventListenerMock).toHaveBeenCalledTimes(1);

        // Change checkAndRefreshToken mock to simulate dependency change
        const newMockCheckAndRefreshToken = jest.fn();
        useTokenRefresh.mockReturnValue({
            checkAndRefreshToken: newMockCheckAndRefreshToken,
        });

        // Re-render
        rerender();

        // Should remove old listener and add new one
        expect(removeEventListenerMock).toHaveBeenCalledTimes(1);
        expect(addEventListenerMock).toHaveBeenCalledTimes(2);
    });
});
