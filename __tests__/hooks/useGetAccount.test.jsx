import useGetAccountDetails from "@/hooks/useGetAccount";

import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { act, renderHook } from "@testing-library/react";

// Mock dependencies
jest.mock("@/api/axios");
jest.mock("@/functions/feedback");

const mockedAcctsAxios = acctsAxios;
const mockedSendCatchFeedback = sendCatchFeedback;

describe("useGetAccountDetails", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("initial state", () => {
        it("should initialize with correct default values", () => {
            const { result } = renderHook(() => useGetAccountDetails());

            expect(result.current.loading).toBe(false);
            expect(result.current.details).toBeUndefined();
            expect(typeof result.current.getDetails).toBe("function");
        });
    });

    describe("getDetails function", () => {
        const mockAccountNumber = "**********";
        const mockAccountDetails = {
            accountNumber: "**********",
            accountName: "John Doe",
            balance: 1000.5,
            accountType: "Savings",
        };

        describe("successful API call", () => {
            beforeEach(() => {
                mockedAcctsAxios.get.mockResolvedValue({
                    data: mockAccountDetails,
                });
            });

            it("should fetch account details successfully", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                let returnedData;
                await act(async () => {
                    returnedData = await result.current.getDetails(mockAccountNumber);
                });

                expect(mockedAcctsAxios.get).toHaveBeenCalledWith(`/api/v1/accounts/${mockAccountNumber}/details`);
                expect(mockedAcctsAxios.get).toHaveBeenCalledTimes(1);
                expect(result.current.details).toEqual(mockAccountDetails);
                expect(returnedData).toEqual(mockAccountDetails);
                expect(result.current.loading).toBe(false);
            });

            it("should set loading to true during API call and false after completion", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                // Mock a delayed response to test loading state
                mockedAcctsAxios.get.mockImplementation(
                    () => new Promise((resolve) => setTimeout(() => resolve({ data: mockAccountDetails }), 100))
                );

                let getDetailsPromise;
                act(() => {
                    getDetailsPromise = result.current.getDetails(mockAccountNumber);
                });

                // Check loading state is true during the call
                expect(result.current.loading).toBe(true);

                // Wait for the promise to resolve
                await act(async () => {
                    await getDetailsPromise;
                });

                // Check loading state is false after completion
                expect(result.current.loading).toBe(false);
            });

            it("should handle multiple successful calls correctly", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                const secondAccountNumber = "**********";
                const secondMockDetails = {
                    accountNumber: "**********",
                    accountName: "Jane Smith",
                    balance: 2500.75,
                    accountType: "Checking",
                };

                // First call
                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toEqual(mockAccountDetails);

                // Second call with different data
                mockedAcctsAxios.get.mockResolvedValueOnce({
                    data: secondMockDetails,
                });

                await act(async () => {
                    await result.current.getDetails(secondAccountNumber);
                });

                expect(result.current.details).toEqual(secondMockDetails);
                expect(mockedAcctsAxios.get).toHaveBeenCalledTimes(2);
            });
        });

        describe("failed API call", () => {
            const mockError = new Error("Network error");

            beforeEach(() => {
                mockedAcctsAxios.get.mockRejectedValue(mockError);
            });

            it("should handle API errors correctly", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                let returnedData;
                await act(async () => {
                    returnedData = await result.current.getDetails(mockAccountNumber);
                });

                expect(mockedAcctsAxios.get).toHaveBeenCalledWith(`/api/v1/accounts/${mockAccountNumber}/details`);
                expect(mockedSendCatchFeedback).toHaveBeenCalledWith(mockError);
                expect(mockedSendCatchFeedback).toHaveBeenCalledTimes(1);
                expect(result.current.details).toBeUndefined();
                expect(returnedData).toBeUndefined();
                expect(result.current.loading).toBe(false);
            });

            it("should set loading to false after error", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.loading).toBe(false);
            });

            it("should reset details to undefined on error", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                // First, set some details with a successful call
                mockedAcctsAxios.get.mockResolvedValueOnce({
                    data: mockAccountDetails,
                });

                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toEqual(mockAccountDetails);

                // Then make a failed call
                mockedAcctsAxios.get.mockRejectedValueOnce(mockError);

                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toBeUndefined();
            });

            it("should handle different types of errors", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                const errorTypes = [
                    new Error("Network error"),
                    { message: "API error", status: 500 },
                    "String error",
                    null,
                    undefined,
                ];

                for (const error of errorTypes) {
                    mockedAcctsAxios.get.mockRejectedValueOnce(error);

                    await act(async () => {
                        await result.current.getDetails(mockAccountNumber);
                    });

                    expect(mockedSendCatchFeedback).toHaveBeenCalledWith(error);
                    expect(result.current.details).toBeUndefined();
                    expect(result.current.loading).toBe(false);
                }

                expect(mockedSendCatchFeedback).toHaveBeenCalledTimes(errorTypes.length);
            });
        });

        describe("edge cases", () => {
            it("should handle empty account number", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                mockedAcctsAxios.get.mockResolvedValue({
                    data: mockAccountDetails,
                });

                await act(async () => {
                    await result.current.getDetails("");
                });

                expect(mockedAcctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts//details");
            });

            it("should handle null response data", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                mockedAcctsAxios.get.mockResolvedValue({
                    data: null,
                });

                let returnedData;
                await act(async () => {
                    returnedData = await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toBeNull();
                expect(returnedData).toBeNull();
            });

            it("should handle undefined response data", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                mockedAcctsAxios.get.mockResolvedValue({
                    data: undefined,
                });

                let returnedData;
                await act(async () => {
                    returnedData = await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toBeUndefined();
                expect(returnedData).toBeUndefined();
            });

            it("should handle response with no data property", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                // Mock response without data property
                mockedAcctsAxios.get.mockResolvedValue({});

                let returnedData;
                await act(async () => {
                    returnedData = await result.current.getDetails(mockAccountNumber);
                });

                expect(result.current.details).toBeUndefined();
                expect(returnedData).toBeUndefined();
            });

            it("should handle concurrent calls correctly", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                // Mock different delays for concurrent calls
                mockedAcctsAxios.get
                    .mockImplementationOnce(
                        () => new Promise((resolve) => setTimeout(() => resolve({ data: mockAccountDetails }), 200))
                    )
                    .mockImplementationOnce(
                        () =>
                            new Promise((resolve) =>
                                setTimeout(
                                    () => resolve({ data: { ...mockAccountDetails, accountNumber: "999" } }),
                                    100
                                )
                            )
                    );

                let promise1, promise2;

                await act(async () => {
                    promise1 = result.current.getDetails("123");
                    promise2 = result.current.getDetails("456");

                    await Promise.all([promise1, promise2]);
                });

                // The last resolved call should set the final state
                expect(result.current.loading).toBe(false);
                expect(mockedAcctsAxios.get).toHaveBeenCalledTimes(2);
            });

            it("should ensure finally block execution in all scenarios", async () => {
                const { result } = renderHook(() => useGetAccountDetails());

                // Test with successful call
                mockedAcctsAxios.get.mockResolvedValueOnce({ data: mockAccountDetails });

                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                // Should set loading to false via finally block
                expect(result.current.loading).toBe(false);

                // Test with error call
                const mockError = new Error("Test error");
                mockedAcctsAxios.get.mockRejectedValueOnce(mockError);

                await act(async () => {
                    await result.current.getDetails(mockAccountNumber);
                });

                // Should still set loading to false via finally block even after error
                expect(result.current.loading).toBe(false);
            });
        });
    });

    describe("state persistence", () => {
        it("should maintain state across multiple renders", async () => {
            const { result, rerender } = renderHook(() => useGetAccountDetails());

            const mockAccountDetails = {
                accountNumber: "**********",
                accountName: "John Doe",
                balance: 1000.5,
            };

            mockedAcctsAxios.get.mockResolvedValue({
                data: mockAccountDetails,
            });

            await act(async () => {
                await result.current.getDetails("**********");
            });

            expect(result.current.details).toEqual(mockAccountDetails);

            // Re-render the hook
            rerender();

            // State should persist
            expect(result.current.details).toEqual(mockAccountDetails);
            expect(result.current.loading).toBe(false);
        });
    });
});
