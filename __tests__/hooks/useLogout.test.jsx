import { renderHook, act, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { useAppDispatch } from "@/redux/hooks";
import { signOut } from "@/redux/features/user";
import { PATH_AUTH } from "@/routes/path";
import { cookies } from "@/lib/cookies";
import { clearLoginData, clearSignupData } from "@/lib/auth-cleanup";
import { clearLoginProgress } from "@/lib/login-utils";
import { clearSignupProgress } from "@/lib/session-utils";
import { userAxios } from "@/api/axios";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { handleError } from "@/lib/utils";
import { useLogout } from "@/hooks/useLogout";

// Mock all dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
}));

jest.mock("@/redux/features/user", () => ({
    signOut: jest.fn(),
}));

jest.mock("@/lib/cookies", () => ({
    cookies: {
        getToken: jest.fn(),
    },
}));

jest.mock("@/lib/auth-cleanup", () => ({
    clearLoginData: jest.fn(),
    clearSignupData: jest.fn(),
}));

jest.mock("@/lib/login-utils", () => ({
    clearLoginProgress: jest.fn(),
}));

jest.mock("@/lib/session-utils", () => ({
    clearSignupProgress: jest.fn(),
}));

jest.mock("@/api/axios", () => ({
    userAxios: {
        post: jest.fn(),
    },
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
    sendFeedback: jest.fn(),
}));

jest.mock("@/lib/utils", () => ({
    handleError: jest.fn(),
}));

jest.mock("@/routes/path", () => ({
    PATH_AUTH: {
        login: "/auth/login",
    },
}));

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
console.error = jest.fn();

describe("useLogout", () => {
    const mockDispatch = jest.fn();
    const mockPush = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup mocks
        useAppDispatch.mockReturnValue(mockDispatch);
        useRouter.mockReturnValue({ push: mockPush });
        signOut.mockReturnValue({ type: "user/signOut" });
        handleError.mockImplementation((error) => error.message || "Unknown error");

        // Setup localStorage mock
        const localStorageMock = (() => {
            let store = {};
            return {
                getItem: jest.fn((key) => store[key] || null),
                setItem: jest.fn((key, value) => {
                    store[key] = value.toString();
                }),
                removeItem: jest.fn((key) => {
                    delete store[key];
                }),
                clear: jest.fn(() => {
                    store = {};
                }),
            };
        })();

        Object.defineProperty(window, "localStorage", {
            value: localStorageMock,
        });

        // Setup document.cookie mock
        Object.defineProperty(document, "cookie", {
            writable: true,
            value: "token=abc123; refreshToken=def456; sessionId=ghi789",
        });
    });

    afterEach(() => {
        console.error = originalConsoleError;
    });

    describe("Initial State", () => {
        it("returns correct initial values", () => {
            const { result } = renderHook(() => useLogout());

            expect(result.current.loading).toBe(false);
            expect(result.current.success).toBe(false);
            expect(result.current.isError).toBe(false);
            expect(typeof result.current.logout).toBe("function");
        });
    });

    describe("Successful Logout", () => {
        it("performs successful logout with default options", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            // Check API call
            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/invalidate", {
                token: "test-token",
                reason: "Logout",
            });

            // Check cleanup functions were called
            expect(clearLoginData).toHaveBeenCalled();
            expect(clearSignupData).toHaveBeenCalled();
            expect(clearLoginProgress).toHaveBeenCalled();
            expect(clearSignupProgress).toHaveBeenCalled();

            // Check localStorage cleanup
            expect(localStorage.removeItem).toHaveBeenCalledWith("preLoginData");
            expect(localStorage.removeItem).toHaveBeenCalledWith("loginEmail");
            expect(localStorage.removeItem).toHaveBeenCalledWith("loginUsername");
            expect(localStorage.removeItem).toHaveBeenCalledWith("loginPhone");
            expect(localStorage.removeItem).toHaveBeenCalledWith("mfaMethod");
            expect(localStorage.removeItem).toHaveBeenCalledWith("deviceVerification");
            expect(localStorage.removeItem).toHaveBeenCalledWith("magicCodeData");
            expect(localStorage.removeItem).toHaveBeenCalledWith("sessionDetails");
            expect(localStorage.removeItem).toHaveBeenCalledWith("tokenDetails");

            // Check Redux action
            expect(mockDispatch).toHaveBeenCalledWith({ type: "user/signOut" });

            // Check feedback and navigation
            expect(sendFeedback).toHaveBeenCalledWith("Logged out successfully", "success");
            expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login);

            // Check final state
            expect(result.current.loading).toBe(false);
            expect(result.current.success).toBe(false); // Reset in finally
            expect(result.current.isError).toBe(false);
        });

        it("performs logout without redirect when redirect option is false", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout({ redirect: false });
            });

            expect(userAxios.post).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
            expect(sendFeedback).toHaveBeenCalledWith("Logged out successfully", "success");
        });

        it("performs logout without feedback when feedback option is false", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout({ feedback: false });
            });

            expect(userAxios.post).toHaveBeenCalled();
            expect(sendFeedback).not.toHaveBeenCalled();
            expect(mockPush).toHaveBeenCalledWith(PATH_AUTH.login);
        });

        it("performs logout with both redirect and feedback disabled", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout({ redirect: false, feedback: false });
            });

            expect(userAxios.post).toHaveBeenCalled();
            expect(sendFeedback).not.toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });

    describe("Failed Logout", () => {
        it("handles API error during logout", async () => {
            const error = new Error("Network error");
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockRejectedValueOnce(error);
            handleError.mockReturnValue("Network error");

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/invalidate", {
                token: "test-token",
                reason: "Logout",
            });

            expect(handleError).toHaveBeenCalledWith(error);
            expect(sendCatchFeedback).toHaveBeenCalledWith("Network error");

            // Check final state
            expect(result.current.loading).toBe(false);
            expect(result.current.success).toBe(false);
            expect(result.current.isError).toBe(false); // Reset in finally
        });

        it("handles logout with no token", async () => {
            cookies.getToken.mockReturnValue(null);
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            expect(userAxios.post).toHaveBeenCalledWith("/v1/authentication/invalidate", {
                token: null,
                reason: "Logout",
            });
        });
    });

    describe("Loading States", () => {
        it("sets loading state during logout process", async () => {
            cookies.getToken.mockReturnValue("test-token");

            let resolvePromise;
            const promise = new Promise((resolve) => {
                resolvePromise = resolve;
            });
            userAxios.post.mockReturnValue(promise);

            const { result } = renderHook(() => useLogout());

            // Start logout
            act(() => {
                result.current.logout();
            });

            // Check loading state
            expect(result.current.loading).toBe(true);
            expect(result.current.success).toBe(false);
            expect(result.current.isError).toBe(false);

            // Resolve the promise
            await act(async () => {
                resolvePromise({ data: { success: true } });
                await promise;
            });

            // Check final state
            expect(result.current.loading).toBe(false);
        });
    });

    describe("Data Cleanup", () => {
        // it("handles localStorage removal errors gracefully", async () => {
        //   cookies.getToken.mockReturnValue("test-token");
        //   userAxios.post.mockResolvedValueOnce({ data: { success: true } });

        //   // Mock localStorage.removeItem to throw error for specific key
        //   const originalRemoveItem = localStorage.removeItem;
        //   localStorage.removeItem.mockImplementation((key) => {
        //     if (key === "preLoginData") {
        //       throw new Error("Storage error");
        //     }
        //     return originalRemoveItem(key);
        //   });

        //   const { result } = renderHook(() => useLogout());

        //   await act(async () => {
        //     await result.current.logout();
        //   });

        //   expect(console.error).toHaveBeenCalledWith(
        //     "Error removing preLoginData from localStorage:",
        //     expect.any(Error)
        //   );

        //   // Other keys should still be removed
        //   expect(localStorage.removeItem).toHaveBeenCalledWith("loginEmail");
        //   expect(localStorage.removeItem).toHaveBeenCalledWith("loginUsername");
        // });

        it("clears all cookies correctly", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            // Mock document.cookie with multiple cookies
            Object.defineProperty(document, "cookie", {
                writable: true,
                value: "token=abc123; refreshToken=def456; sessionId=ghi789",
            });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            // Check that cookies were cleared (this is hard to test directly, but we can verify the function ran)
            expect(userAxios.post).toHaveBeenCalled();
        });

        it("handles empty cookie string", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            // Mock empty cookie string
            Object.defineProperty(document, "cookie", {
                writable: true,
                value: "",
            });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            expect(userAxios.post).toHaveBeenCalled();
        });
    });

    describe("State Management", () => {
        it("resets states properly in finally block", async () => {
            const error = new Error("Test error");
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockRejectedValueOnce(error);

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            // All states should be reset to false in finally block
            expect(result.current.loading).toBe(false);
            expect(result.current.success).toBe(false);
            expect(result.current.isError).toBe(false);
        });

        it("sets success state temporarily during successful logout", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            // Success state is set but then reset in finally
            expect(result.current.success).toBe(false);
        });

        it("sets error state temporarily during failed logout", async () => {
            const error = new Error("Test error");
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockRejectedValueOnce(error);

            const { result } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            // Error state is set but then reset in finally
            expect(result.current.isError).toBe(false);
        });
    });

    describe("Callback Dependencies", () => {
        it("maintains stable logout function reference", () => {
            const { result, rerender } = renderHook(() => useLogout());

            const firstLogout = result.current.logout;

            rerender();

            const secondLogout = result.current.logout;

            expect(firstLogout).toBe(secondLogout);
        });

        it("maintains stable clearAllUserData function reference", async () => {
            cookies.getToken.mockReturnValue("test-token");
            userAxios.post.mockResolvedValueOnce({ data: { success: true } });

            const { result, rerender } = renderHook(() => useLogout());

            await act(async () => {
                await result.current.logout();
            });

            const firstCall = clearLoginData.mock.calls.length;

            rerender();

            await act(async () => {
                await result.current.logout();
            });

            // clearLoginData should be called again, indicating the callback is stable
            expect(clearLoginData.mock.calls.length).toBe(firstCall + 1);
        });
    });
});
