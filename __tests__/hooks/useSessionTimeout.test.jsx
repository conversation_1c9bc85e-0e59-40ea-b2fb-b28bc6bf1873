import { renderHook, act, waitFor } from "@testing-library/react";
import { useSessionTimeout } from "@/hooks/useSessionTimeout";
import { cookies } from "@/lib/cookies";
import { useLogout } from "@/hooks/useLogout";

// Mock dependencies
jest.mock("@/lib/cookies", () => ({
    cookies: {
        isAuthenticated: jest.fn(),
    },
}));

jest.mock("@/hooks/useLogout", () => ({
    useLogout: jest.fn(),
}));

describe("useSessionTimeout", () => {
    // Mock implementations
    const mockLogout = jest.fn();
    const mockOnWarning = jest.fn();
    const mockOnTimeout = jest.fn();

    // Store original functions
    const originalSetTimeout = global.setTimeout;
    const originalSetInterval = global.setInterval;
    const originalClearTimeout = global.clearTimeout;
    const originalClearInterval = global.clearInterval;
    const originalAddEventListener = document.addEventListener;
    const originalRemoveEventListener = document.removeEventListener;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();

        // Setup useLogout mock
        useLogout.mockReturnValue({
            logout: mockLogout,
            loading: false,
            isError: false,
            success: false,
        });

        // Setup cookies mock
        cookies.isAuthenticated.mockReturnValue(false);

        // Mock document event listeners
        document.addEventListener = jest.fn();
        document.removeEventListener = jest.fn();
    });

    afterEach(() => {
        jest.useRealTimers();
        // Restore original functions
        document.addEventListener = originalAddEventListener;
        document.removeEventListener = originalRemoveEventListener;
    });

    describe("Initialization", () => {
        it("should initialize with default values", () => {
            const { result } = renderHook(() => useSessionTimeout());

            expect(result.current.showWarning).toBe(false);
            expect(result.current.timeLeft).toBe(30); // 30 seconds default
            expect(result.current.isWarningActive).toBe(false);
            expect(result.current.isAuthenticated).toBe(false);
            expect(result.current.loading).toBe(false);
            expect(typeof result.current.continueSession).toBe("function");
            expect(typeof result.current.handleLogout).toBe("function");
            expect(typeof result.current.resetInactivityTimer).toBe("function");
        });

        it("should initialize with custom options", () => {
            const options = {
                inactivityTimeout: 2 * 60 * 1000, // 2 minutes
                warningTimeout: 15 * 1000, // 15 seconds
                onWarning: mockOnWarning,
                onTimeout: mockOnTimeout,
            };

            const { result } = renderHook(() => useSessionTimeout(options));

            expect(result.current.timeLeft).toBe(15); // 15 seconds
        });
    });

    describe("Authentication Status", () => {
        it("should check authentication status on mount", () => {
            renderHook(() => useSessionTimeout());

            expect(cookies.isAuthenticated).toHaveBeenCalled();
        });

        it("should set up periodic authentication check", () => {
            renderHook(() => useSessionTimeout());

            // Check that setInterval was called for auth checking
            expect(jest.getTimerCount()).toBeGreaterThan(0);
        });

        it("should update authentication status when cookies change", async () => {
            cookies.isAuthenticated.mockReturnValue(false);

            const { result } = renderHook(() => useSessionTimeout());

            expect(result.current.isAuthenticated).toBe(false);

            // Simulate auth status change
            cookies.isAuthenticated.mockReturnValue(true);

            await act(async () => {
                jest.advanceTimersByTime(1000);
            });

            expect(result.current.isAuthenticated).toBe(true);
        });

        it("should clear timers when user becomes unauthenticated", async () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // User becomes unauthenticated
            cookies.isAuthenticated.mockReturnValue(false);

            await act(async () => {
                jest.advanceTimersByTime(1000);
            });

            expect(result.current.isAuthenticated).toBe(false);
            expect(result.current.showWarning).toBe(false);
            expect(result.current.isWarningActive).toBe(false);
        });
    });

    describe("Event Listeners", () => {
        it("should add event listeners for user activity when authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            const events = ["mousedown", "mousemove", "keypress", "scroll", "touchstart", "click"];

            events.forEach((event) => {
                expect(document.addEventListener).toHaveBeenCalledWith(event, expect.any(Function), true);
            });
        });

        it("should remove event listeners on unmount", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { unmount } = renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            unmount();

            const events = ["mousedown", "mousemove", "keypress", "scroll", "touchstart", "click"];

            events.forEach((event) => {
                expect(document.removeEventListener).toHaveBeenCalledWith(event, expect.any(Function), true);
            });
        });

        it("should reset inactivity timer on user activity when authenticated and warning not active", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Get the event handler
            const mousedownCall = document.addEventListener.mock.calls.find((call) => call[0] === "mousedown");

            if (mousedownCall) {
                const mousedownHandler = mousedownCall[1];

                // Simulate user activity
                act(() => {
                    mousedownHandler();
                });

                // Timer should be reset (new timer created)
                expect(jest.getTimerCount()).toBeGreaterThan(0);
            }
        });

        it("should not reset timer on user activity when warning is active", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for effects and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000); // Trigger warning
            });

            const mousedownCall = document.addEventListener.mock.calls.find((call) => call[0] === "mousedown");

            if (mousedownCall) {
                const mousedownHandler = mousedownCall[1];
                const timerCountBefore = jest.getTimerCount();

                // Simulate user activity during warning
                act(() => {
                    mousedownHandler();
                });

                // Timer count should not increase (no new timer created)
                expect(jest.getTimerCount()).toBe(timerCountBefore);
            }
        });

        it("should not reset timer on user activity when not authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(false);

            renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            const mousedownCall = document.addEventListener.mock.calls.find((call) => call[0] === "mousedown");

            if (mousedownCall) {
                const mousedownHandler = mousedownCall[1];
                const timerCountBefore = jest.getTimerCount();

                // Simulate user activity when not authenticated
                act(() => {
                    mousedownHandler();
                });

                // Timer count should not increase
                expect(jest.getTimerCount()).toBe(timerCountBefore);
            }
        });
    });

    describe("Inactivity Timer", () => {
        it("should start inactivity timer when authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Should have timers running
            expect(jest.getTimerCount()).toBeGreaterThan(0);
        });

        it("should not start timer when not authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(false);

            renderHook(() => useSessionTimeout());

            // Wait for effects to run
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Should only have auth check interval
            expect(jest.getTimerCount()).toBe(1);
        });

        it("should reset inactivity timer on resetInactivityTimer call", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            const initialTimerCount = jest.getTimerCount();

            act(() => {
                result.current.resetInactivityTimer();
            });

            // Should have same or more timers (timer was reset)
            expect(jest.getTimerCount()).toBeGreaterThanOrEqual(initialTimerCount);
        });
    });

    describe("Warning Countdown", () => {
        it("should start warning countdown after inactivity timeout", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Fast forward to trigger inactivity timeout
            act(() => {
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            expect(result.current.showWarning).toBe(true);
            expect(result.current.isWarningActive).toBe(true);
            expect(result.current.timeLeft).toBe(29); // Default warning timeout
        });

        it("should call onWarning callback when warning starts", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            renderHook(() =>
                useSessionTimeout({
                    onWarning: mockOnWarning,
                })
            );

            // Wait for initial setup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Fast forward to trigger warning
            act(() => {
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            expect(mockOnWarning).toHaveBeenCalled();
        });

        // it("should countdown warning timer", () => {
        //   cookies.isAuthenticated.mockReturnValue(true);

        //   const { result } = renderHook(() => useSessionTimeout());

        //   // Wait for initial setup and trigger warning
        //   act(() => {
        //     jest.advanceTimersByTime(1000);
        //     jest.advanceTimersByTime(5 * 60 * 1000);
        //   });

        //   expect(result.current.timeLeft).toBe(29);

        //   // Advance countdown
        //   act(() => {
        //     jest.advanceTimersByTime(1000);
        //   });

        //   expect(result.current.timeLeft).toBe(29);
        // });

        it("should logout when countdown reaches zero", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            renderHook(() =>
                useSessionTimeout({
                    onTimeout: mockOnTimeout,
                })
            );

            // Wait for initial setup and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            // Let countdown reach zero
            act(() => {
                jest.advanceTimersByTime(30 * 1000);
            });

            expect(mockLogout).toHaveBeenCalled();
            expect(mockOnTimeout).toHaveBeenCalled();
        });

        it("should have backup timeout for logout", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            renderHook(() => useSessionTimeout());

            // Wait for initial setup and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            // Should have multiple timers (countdown + backup)
            expect(jest.getTimerCount()).toBeGreaterThan(1);
        });
    });

    describe("Continue Session", () => {
        it("should reset warning state and restart inactivity timer", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            expect(result.current.showWarning).toBe(true);
            expect(result.current.isWarningActive).toBe(true);

            // Continue session
            act(() => {
                result.current.continueSession();
            });

            expect(result.current.showWarning).toBe(false);
            expect(result.current.isWarningActive).toBe(false);
            expect(result.current.timeLeft).toBe(30); // Reset to default
        });

        it("should not restart timer if not authenticated", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            // User becomes unauthenticated
            cookies.isAuthenticated.mockReturnValue(false);

            act(() => {
                jest.advanceTimersByTime(1000);
            });

            const timerCountBefore = jest.getTimerCount();

            // Continue session
            act(() => {
                result.current.continueSession();
            });

            // Should not add new timers
            expect(jest.getTimerCount()).toBeLessThanOrEqual(timerCountBefore);
        });
    });

    describe("Handle Logout", () => {
        it("should call logout function", () => {
            const { result } = renderHook(() => useSessionTimeout());

            act(() => {
                result.current.handleLogout();
            });

            expect(mockLogout).toHaveBeenCalled();
        });

        it("should call onTimeout callback", () => {
            const { result } = renderHook(() =>
                useSessionTimeout({
                    onTimeout: mockOnTimeout,
                })
            );

            act(() => {
                result.current.handleLogout();
            });

            expect(mockOnTimeout).toHaveBeenCalled();
        });

        it("should clear timers and reset state when logout has error or success", () => {
            useLogout.mockReturnValue({
                logout: mockLogout,
                loading: false,
                isError: true,
                success: false,
            });

            const { result } = renderHook(() => useSessionTimeout());

            act(() => {
                result.current.handleLogout();
            });

            // State should be reset
            expect(result.current.showWarning).toBe(false);
            expect(result.current.isWarningActive).toBe(false);
        });
    });

    describe("Timer Cleanup", () => {
        it("should clear all timers on clearAllTimers call", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup and create some timers
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000); // Trigger warning
            });

            const timerCountBefore = jest.getTimerCount();

            // Reset timer (which calls clearAllTimers internally)
            act(() => {
                result.current.resetInactivityTimer();
            });

            // Timers should be managed (cleared and recreated)
            expect(jest.getTimerCount()).toBeGreaterThan(0);
        });

        it("should clear timers on unmount", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { unmount } = renderHook(() => useSessionTimeout());

            // Wait for initial setup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            unmount();

            // All timers should be cleared
            expect(jest.getTimerCount()).toBe(0);
        });

        it("should clear auth check interval on unmount", () => {
            const { unmount } = renderHook(() => useSessionTimeout());

            unmount();

            // All timers should be cleared
            expect(jest.getTimerCount()).toBe(0);
        });
    });

    describe("Edge Cases", () => {
        it("should handle callback updates", () => {
            const { rerender } = renderHook(({ onWarning, onTimeout }) => useSessionTimeout({ onWarning, onTimeout }), {
                initialProps: { onWarning: mockOnWarning, onTimeout: mockOnTimeout },
            });

            const newOnWarning = jest.fn();
            const newOnTimeout = jest.fn();

            rerender({ onWarning: newOnWarning, onTimeout: newOnTimeout });

            // Should not crash
            expect(true).toBe(true);
        });

        it("should not reset timer when warning is active", () => {
            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout());

            // Wait for initial setup and trigger warning
            act(() => {
                jest.advanceTimersByTime(1000);
                jest.advanceTimersByTime(5 * 60 * 1000);
            });

            const timerCountBefore = jest.getTimerCount();

            // Try to reset timer while warning is active
            act(() => {
                result.current.resetInactivityTimer();
            });

            // Should not create new inactivity timer
            expect(jest.getTimerCount()).toBe(timerCountBefore);
        });

        it("should handle custom timeout values", () => {
            const customOptions = {
                inactivityTimeout: 2 * 60 * 1000, // 2 minutes
                warningTimeout: 15 * 1000, // 15 seconds
            };

            cookies.isAuthenticated.mockReturnValue(true);

            const { result } = renderHook(() => useSessionTimeout(customOptions));

            expect(result.current.timeLeft).toBe(15);

            // Wait for initial setup
            act(() => {
                jest.advanceTimersByTime(1000);
            });

            // Should have timers running
            expect(jest.getTimerCount()).toBeGreaterThan(0);
        });
    });

    describe("Loading State", () => {
        it("should return loading state from useLogout", () => {
            useLogout.mockReturnValue({
                logout: mockLogout,
                loading: true,
                isError: false,
                success: false,
            });

            const { result } = renderHook(() => useSessionTimeout());

            expect(result.current.loading).toBe(true);
        });
    });
});
