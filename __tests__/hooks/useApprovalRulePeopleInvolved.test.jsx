/**
 * Purpose: Test suite for useApprovalRulePeopleInvolved custom hook.
 *
 * Functionality: Comprehensive tests to verify the hook's deduplication logic, caching behavior,
 * error handling, and proper integration with Redux. Tests ensure that multiple components with
 * identical parameters only trigger one API call, cache entries expire correctly, and error
 * states are handled appropriately without fallback data.
 *
 * Dependencies: React Testing Library, Jest, Redux mock store, and custom hook testing utilities.
 *
 * Usage: Run via Jest to verify the hook behaves correctly in various scenarios including
 * multiple component mounts, cache expiration, error conditions, and cleanup operations.
 */

import { renderHook, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { useApprovalRulePeopleInvolved, cacheUtils } from "@/hooks/useApprovalRulePeopleInvolved";
import { getTransferApprovers } from "@/redux/actions/sendMoneyActions";

// Mock Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

// Mock Redux actions
jest.mock("@/redux/actions/sendMoneyActions", () => ({
    getTransferApprovers: jest.fn(),
}));

const mockStore = configureStore([]);

describe("useApprovalRulePeopleInvolved", () => {
    let store;
    let mockDispatch;
    let mockUseAppSelector;
    let mockUseAppDispatch;

    beforeEach(() => {
        // Clear cache before each test
        cacheUtils.clearCache();

        // Create mock store
        store = mockStore({
            sendMoney: {
                getTransferApprovers: {
                    data: null,
                    loading: false,
                    error: null,
                },
            },
        });

        // Setup mocks
        mockDispatch = jest.fn();
        mockUseAppSelector = jest.fn();
        mockUseAppDispatch = jest.fn();

        // Mock the Redux hooks
        const { useAppDispatch, useAppSelector } = require("@/redux/hooks");
        useAppDispatch.mockReturnValue(mockDispatch);
        useAppSelector.mockImplementation(mockUseAppSelector);

        // Mock getTransferApprovers action
        getTransferApprovers.mockReturnValue({
            type: "sendMoney/getTransferApprovers/pending",
            payload: { amount: 1000, type: "TRANSFER" },
        });

        // Default selector return
        mockUseAppSelector.mockReturnValue({
            data: null,
            loading: false,
            error: null,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        cacheUtils.clearCache();
    });

    const renderHookWithProvider = (params) => {
        return renderHook(() => useApprovalRulePeopleInvolved(params), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });
    };

    describe("Basic functionality", () => {
        it("should make API call when enabled with valid parameters", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({
                    type: "sendMoney/getTransferApprovers/pending",
                    payload: { amount: 1000, type: "TRANSFER" },
                });
            });
        });

        it("should not make API call when disabled", () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: false,
            };

            renderHookWithProvider(params);

            expect(mockDispatch).not.toHaveBeenCalled();
        });

        it("should not make API call when amount is missing", () => {
            const params = {
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            renderHookWithProvider(params);

            expect(mockDispatch).not.toHaveBeenCalled();
        });
    });

    describe("Deduplication logic", () => {
        it("should make only one API call for multiple hooks with identical parameters", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            // Render multiple hooks with same parameters
            renderHookWithProvider(params);
            renderHookWithProvider(params);
            renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(1);
            });
        });

        it("should make separate API calls for hooks with different parameters", async () => {
            const params1 = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const params2 = {
                amount: 2000,
                type: "TRANSFER",
                reference: "TXN456",
                enabled: true,
            };

            renderHookWithProvider(params1);
            renderHookWithProvider(params2);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(2);
            });
        });

        it("should track cache entries correctly", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            expect(cacheUtils.getCacheSize()).toBe(0);

            renderHookWithProvider(params);

            await waitFor(() => {
                expect(cacheUtils.getCacheSize()).toBe(1);
            });

            const cacheKey = "approval_1000_TRANSFER_TXN123_";
            const cacheEntry = cacheUtils.getCacheEntry(cacheKey);
            expect(cacheEntry).toBeDefined();
            expect(cacheEntry?.key).toBe(cacheKey);
        });
    });

    describe("Cache management", () => {
        it("should use cached data within cache duration", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            // First render
            const { unmount: unmount1 } = renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(1);
            });

            unmount1();

            // Second render with same parameters (should use cache)
            renderHookWithProvider(params);

            // Should not make another API call
            expect(mockDispatch).toHaveBeenCalledTimes(1);
        });

        it("should provide refetch functionality", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const { result } = renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(1);
            });

            // Call refetch
            result.current.refetch();

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(2);
            });
        });
    });

    describe("Error handling", () => {
        it("should handle error state correctly", () => {
            mockUseAppSelector.mockReturnValue({
                data: null,
                loading: false,
                error: "API Error",
            });

            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const { result } = renderHookWithProvider(params);

            expect(result.current.error).toBe("API Error");
            expect(result.current.data).toBeNull();
            expect(result.current.loading).toBe(false);
        });

        it("should handle loading state correctly", () => {
            mockUseAppSelector.mockReturnValue({
                data: null,
                loading: true,
                error: null,
            });

            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const { result } = renderHookWithProvider(params);

            expect(result.current.loading).toBe(true);
            expect(result.current.data).toBeNull();
            expect(result.current.error).toBeNull();
        });
    });

    describe("Data handling", () => {
        it("should return data when API call succeeds", () => {
            const mockData = [
                { id: 1, name: "John Doe", role: "Approver", approvalLevel: 1 },
                { id: 2, name: "Jane Smith", role: "Manager", approvalLevel: 2 },
            ];

            mockUseAppSelector.mockReturnValue({
                data: mockData,
                loading: false,
                error: null,
            });

            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const { result } = renderHookWithProvider(params);

            expect(result.current.data).toEqual(mockData);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBeNull();
        });
    });

    describe("Parameter variations", () => {
        it("should handle requestId parameter correctly", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                requestId: "REQ123",
                enabled: true,
            };

            renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({
                    type: "sendMoney/getTransferApprovers/pending",
                    payload: { amount: 1000, type: "TRANSFER" },
                });
            });

            const cacheKey = "approval_1000_TRANSFER__REQ123";
            const cacheEntry = cacheUtils.getCacheEntry(cacheKey);
            expect(cacheEntry).toBeDefined();
        });

        it("should handle both reference and requestId parameters", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                requestId: "REQ123",
                enabled: true,
            };

            renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({
                    type: "sendMoney/getTransferApprovers/pending",
                    payload: { amount: 1000, type: "TRANSFER" },
                });
            });

            const cacheKey = "approval_1000_TRANSFER_TXN123_REQ123";
            const cacheEntry = cacheUtils.getCacheEntry(cacheKey);
            expect(cacheEntry).toBeDefined();
        });
    });

    describe("Cleanup", () => {
        it("should handle component unmount correctly", async () => {
            const params = {
                amount: 1000,
                type: "TRANSFER",
                reference: "TXN123",
                enabled: true,
            };

            const { unmount } = renderHookWithProvider(params);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledTimes(1);
            });

            // Unmount should not cause errors
            expect(() => unmount()).not.toThrow();
        });
    });
});
