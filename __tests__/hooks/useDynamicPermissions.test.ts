import { renderHook } from "@testing-library/react";
import {
    useDynamicPermissions,
    usePermissionModule,
    usePermissionExists,
    useAllPermissionsList,
} from "@/hooks/useDynamicPermissions";

jest.mock("@/services/permissionsService", () => {
    const state = {
        isLoaded: true,
        isLoading: false,
        error: null,
        allPermissions: { MOD: { VIEW: "VIEW" } },
        permissionsList: [{ id: 1, name: "PERM" }],
        lastFetched: Date.now(),
    };
    return {
        __esModule: true,
        default: {
            initialize: jest.fn(),
            getState: jest.fn(() => state),
            subscribe: jest.fn((cb) => {
                cb(state);
                return () => {};
            }),
            refresh: jest.fn(),
            getModulePermissions: jest.fn(() => ({ VIEW: "VIEW" })),
            hasPermission: jest.fn(() => true),
            getAllPermissionsList: jest.fn(() => [{ id: 1, name: "PERM" }]),
        },
    };
});

describe("useDynamicPermissions hook", () => {
    it("returns permissions and state", () => {
        const { result } = renderHook(() => useDynamicPermissions());
        expect(result.current.permissions).toBeDefined();
        expect(typeof result.current.isLoading).toBe("boolean");
        expect(typeof result.current.isLoaded).toBe("boolean");
        expect(["string", "object"].includes(typeof result.current.error)).toBe(true);
        expect(typeof result.current.refresh).toBe("function");
    });
});

describe("usePermissionModule hook", () => {
    it("returns module permissions", () => {
        const { result } = renderHook(() => usePermissionModule("MOD"));
        expect(result.current.permissions).toBeDefined();
    });
});

describe("usePermissionExists hook", () => {
    it("returns existence of permission", () => {
        const { result } = renderHook(() => usePermissionExists("PERM"));
        expect(typeof result.current.exists).toBe("boolean");
    });
});

describe("useAllPermissionsList hook", () => {
    it("returns all permissions as a list", () => {
        const { result } = renderHook(() => useAllPermissionsList());
        expect(Array.isArray(result.current.permissions)).toBe(true);
    });
});
