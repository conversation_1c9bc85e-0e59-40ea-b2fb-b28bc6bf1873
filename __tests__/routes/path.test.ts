import { PATH_AUTH, PATH_DASHBOARD, PATH_PROTECTED } from "@/routes/path";

describe("PATH_AUTH", () => {
    it("should be defined", () => {
        // Verify PATH_AUTH is defined
        expect(PATH_AUTH).toBeDefined();
    });

    it("should be an object", () => {
        // Verify PATH_AUTH is an object
        expect(typeof PATH_AUTH).toBe("object");
    });

    it("should have a root property", () => {
        // Verify PATH_AUTH has a root property
        expect(PATH_AUTH.root).toBeDefined();
    });

    it("should have a login property", () => {
        // Verify PATH_AUTH has a login property
        expect(PATH_AUTH.login).toBeDefined();
    });

    it("should have a signup property", () => {
        // Verify PATH_AUTH has a signup property
        expect(PATH_AUTH.signup).toBeDefined();
    });

    it("should have the correct login path", () => {
        // Verify PATH_AUTH.login has the correct value
        expect(PATH_AUTH.login).toBe("/auth/login");
    });

    it("should have the correct signup path", () => {
        // Verify PATH_AUTH.signup has the correct value
        expect(PATH_AUTH.signup).toBe("/auth/sign-up");
    });

    it("should have the correct twoFaVerification path", () => {
        // Verify PATH_AUTH.twoFaVerification has the correct value
        expect(PATH_AUTH.twoFaVerification).toBe("/auth/login/two-fa-verification");
    });

    it("should have the correct verifyDevice path", () => {
        // Verify PATH_AUTH.verifyDevice has the correct value
        expect(PATH_AUTH.verifyDevice).toBe("/auth/login/verify-device");
    });

    it("should have the correct trustDevice path", () => {
        // Verify PATH_AUTH.trustDevice has the correct value
        expect(PATH_AUTH.trustDevice).toBe("/auth/login/trust-device");
    });
});

describe("PATH_DASHBOARD", () => {
    it("should be defined", () => {
        // Verify PATH_DASHBOARD is defined
        expect(PATH_DASHBOARD).toBeDefined();
    });

    it("should be an object", () => {
        // Verify PATH_DASHBOARD is an object
        expect(typeof PATH_DASHBOARD).toBe("object");
    });

    it("should have a root property", () => {
        // Verify PATH_DASHBOARD has a root property
        expect(PATH_DASHBOARD.root).toBeDefined();
    });

    it("should have a transactions property", () => {
        // Verify PATH_DASHBOARD has a transactions property
        expect(PATH_DASHBOARD.transactions).toBeDefined();
    });

    it("should have a profile property", () => {
        // Verify PATH_DASHBOARD has a profile property
        expect(PATH_DASHBOARD.profile).toBeDefined();
    });

    it("should have a settings property", () => {
        // Verify PATH_DASHBOARD has a settings property
        expect(PATH_DASHBOARD.settings).toBeDefined();
    });

    it("should have the correct root path", () => {
        // Verify PATH_DASHBOARD.root has the correct value
        expect(PATH_DASHBOARD.root).toBe("/dashboard");
    });
});

describe("PATH_PROTECTED", () => {
    it("should be defined", () => {
        // Verify PATH_PROTECTED is defined
        expect(PATH_PROTECTED).toBeDefined();
    });

    it("should be an object", () => {
        // Verify PATH_PROTECTED is an object
        expect(typeof PATH_PROTECTED).toBe("object");
    });

    it("should have a root property", () => {
        // Verify PATH_PROTECTED has a root property
        expect(PATH_PROTECTED.root).toBeDefined();
    });

    it("should have an accounts property", () => {
        // Verify PATH_PROTECTED has an accounts property
        expect(PATH_PROTECTED.accounts).toBeDefined();
    });

    it("should have a cards property", () => {
        // Verify PATH_PROTECTED has a cards property
        expect(PATH_PROTECTED.cards).toBeDefined();
    });

    it("should have the correct root path", () => {
        // Verify PATH_PROTECTED.root has the correct value
        expect(PATH_PROTECTED.root).toBe("/dashboard");
    });

    it("should have dynamic routes for bill payments", () => {
        // Verify PATH_PROTECTED.billPayments.single.root is a function
        expect(typeof PATH_PROTECTED.billPayments.single.root).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.single.root("electricity")).toBe(
            "/payments/bill-payments/electricity/single"
        );
    });

    it("should have dynamic routes for bulk bill payments", () => {
        // Verify PATH_PROTECTED.billPayments.bulk.root is a function
        expect(typeof PATH_PROTECTED.billPayments.bulk.root).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.bulk.root("airtime")).toBe("/payments/bill-payments/airtime/bulk");
    });

    it("should have dynamic routes for single bill payment info", () => {
        // Verify PATH_PROTECTED.billPayments.single.paymentInfo is a function
        expect(typeof PATH_PROTECTED.billPayments.single.paymentInfo).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.single.paymentInfo("electricity")).toBe(
            "/payments/bill-payments/electricity/single/payment-info"
        );
    });

    it("should have dynamic routes for single bill payment review", () => {
        // Verify PATH_PROTECTED.billPayments.single.review is a function
        expect(typeof PATH_PROTECTED.billPayments.single.review).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.single.review("electricity")).toBe(
            "/payments/bill-payments/electricity/single/review"
        );
    });

    it("should have dynamic routes for bulk bill payment verify", () => {
        // Verify PATH_PROTECTED.billPayments.bulk.verify is a function
        expect(typeof PATH_PROTECTED.billPayments.bulk.verify).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.bulk.verify("airtime")).toBe("/payments/bill-payments/airtime/bulk/verify");
    });

    it("should have dynamic routes for bulk bill payment info", () => {
        // Verify PATH_PROTECTED.billPayments.bulk.paymentInfo is a function
        expect(typeof PATH_PROTECTED.billPayments.bulk.paymentInfo).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.bulk.paymentInfo("airtime")).toBe(
            "/payments/bill-payments/airtime/bulk/payment-info"
        );
    });

    it("should have dynamic routes for bulk bill payment review", () => {
        // Verify PATH_PROTECTED.billPayments.bulk.review is a function
        expect(typeof PATH_PROTECTED.billPayments.bulk.review).toBe("function");

        // Verify the function returns the correct path
        expect(PATH_PROTECTED.billPayments.bulk.review("airtime")).toBe("/payments/bill-payments/airtime/bulk/review");
    });
});
