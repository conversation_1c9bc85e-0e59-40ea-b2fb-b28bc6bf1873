import {
    transformToBackwardCompatibleFormat,
    createEmptyPermissionsStructure,
    MODULE_MAPPING,
} from "@/utils/permissionTransforms";

describe("permissionsTransform utility", () => {
    describe("createEmptyPermissionsStructure", () => {
        it("should create empty permissions structure", () => {
            const result = createEmptyPermissionsStructure();

            expect(result).toEqual({
                OVERVIEW_PERMISSIONS: {},
                SETTINGS_PERMISSIONS: {},
                OUTGOING_PAYMENTS_PERMISSIONS: {},
                SEND_MONEY_PERMISSIONS: {},
                ACCOUNTS_PERMISSIONS: {},
                TRANSACTIONS_PERMISSIONS: {},
                BILL_PAYMENTS_PERMISSIONS: {},
                BENEFICIARIES_PERMISSIONS: {},
                TEAM_MANAGEMENT_PERMISSIONS: {},
                SUPPORT_PERMISSIONS: {},
                ONBOARDING_PERMISSIONS: {},
                ALL_PERMISSIONS: {},
            });
        });
    });

    describe("MODULE_MAPPING", () => {
        it("should contain all expected mappings", () => {
            expect(MODULE_MAPPING).toEqual({
                OVERVIEW_PERMISSIONS: "OVERVIEW_PERMISSIONS",
                DASHBOARD_PERMISSIONS: "OVERVIEW_PERMISSIONS",
                SETTINGS_PERMISSIONS: "SETTINGS_PERMISSIONS",
                USER_MANAGEMENT_PERMISSIONS: "SETTINGS_PERMISSIONS",
                OUTGOING_PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
                PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
                SEND_MONEY_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
                TRANSFER_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
                ACCOUNTS_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
                ACCOUNT_MANAGEMENT_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
                TRANSACTIONS_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
                TRANSACTION_MANAGEMENT_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
                BILL_PAYMENTS_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
                BILL_MANAGEMENT_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
                BENEFICIARIES_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
                BENEFICIARY_MANAGEMENT_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
                TEAM_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
                ROLE_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
                SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
                CUSTOMER_SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
                ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
                USER_ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
            });
        });
    });

    describe("transformToBackwardCompatibleFormat", () => {
        it("should transform empty permissions correctly", () => {
            const result = transformToBackwardCompatibleFormat({});

            expect(result).toEqual({
                OVERVIEW_PERMISSIONS: {},
                SETTINGS_PERMISSIONS: {},
                OUTGOING_PAYMENTS_PERMISSIONS: {},
                SEND_MONEY_PERMISSIONS: {},
                ACCOUNTS_PERMISSIONS: {},
                TRANSACTIONS_PERMISSIONS: {},
                BILL_PAYMENTS_PERMISSIONS: {},
                BENEFICIARIES_PERMISSIONS: {},
                TEAM_MANAGEMENT_PERMISSIONS: {},
                SUPPORT_PERMISSIONS: {},
                ONBOARDING_PERMISSIONS: {},
                ALL_PERMISSIONS: {},
            });
        });

        it("should transform permissions with direct module mapping", () => {
            const allPermissions = {
                OVERVIEW_PERMISSIONS: { VIEW: "VIEW" },
                SETTINGS_PERMISSIONS: { EDIT: "EDIT" },
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.OVERVIEW_PERMISSIONS).toEqual({ VIEW: "VIEW" });
            expect(result.SETTINGS_PERMISSIONS).toEqual({ EDIT: "EDIT" });
            expect(result.ALL_PERMISSIONS).toEqual({ VIEW: "VIEW", EDIT: "EDIT" });
        });

        it("should map aliased modules correctly", () => {
            const allPermissions = {
                DASHBOARD_PERMISSIONS: { VIEW: "VIEW" },
                USER_MANAGEMENT_PERMISSIONS: { EDIT: "EDIT" },
                PAYMENTS_PERMISSIONS: { PAY: "PAY" },
                TRANSFER_PERMISSIONS: { SEND: "SEND" },
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.OVERVIEW_PERMISSIONS).toEqual({ VIEW: "VIEW" });
            expect(result.SETTINGS_PERMISSIONS).toEqual({ EDIT: "EDIT" });
            expect(result.OUTGOING_PAYMENTS_PERMISSIONS).toEqual({ PAY: "PAY" });
            expect(result.SEND_MONEY_PERMISSIONS).toEqual({ SEND: "SEND" });
            expect(result.ALL_PERMISSIONS).toEqual({
                VIEW: "VIEW",
                EDIT: "EDIT",
                PAY: "PAY",
                SEND: "SEND",
            });
        });

        it("should handle unknown modules by adding to ALL_PERMISSIONS", () => {
            const allPermissions = {
                UNKNOWN_MODULE: { UNKNOWN: "UNKNOWN" },
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.ALL_PERMISSIONS).toEqual({ UNKNOWN: "UNKNOWN" });
        });

        it("should handle non-object permissions gracefully", () => {
            const allPermissions: Record<string, unknown> = {
                OVERVIEW_PERMISSIONS: null,
                SETTINGS_PERMISSIONS: "string",
                ACCOUNTS_PERMISSIONS: 123,
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.OVERVIEW_PERMISSIONS).toEqual({});
            expect(result.SETTINGS_PERMISSIONS).toEqual({});
            expect(result.ACCOUNTS_PERMISSIONS).toEqual({});
            expect(result.ALL_PERMISSIONS).toEqual({});
        });

        it("should merge permissions from multiple modules correctly", () => {
            const allPermissions = {
                OVERVIEW_PERMISSIONS: { VIEW: "VIEW" },
                DASHBOARD_PERMISSIONS: { DASHBOARD: "DASHBOARD" },
                SETTINGS_PERMISSIONS: { EDIT: "EDIT" },
                USER_MANAGEMENT_PERMISSIONS: { MANAGE: "MANAGE" },
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.OVERVIEW_PERMISSIONS).toEqual({
                VIEW: "VIEW",
                DASHBOARD: "DASHBOARD",
            });
            expect(result.SETTINGS_PERMISSIONS).toEqual({
                EDIT: "EDIT",
                MANAGE: "MANAGE",
            });
            expect(result.ALL_PERMISSIONS).toEqual({
                VIEW: "VIEW",
                DASHBOARD: "DASHBOARD",
                EDIT: "EDIT",
                MANAGE: "MANAGE",
            });
        });

        it("should handle all module mappings", () => {
            const allPermissions = {
                OVERVIEW_PERMISSIONS: { OVERVIEW: "OVERVIEW" },
                DASHBOARD_PERMISSIONS: { DASHBOARD: "DASHBOARD" },
                SETTINGS_PERMISSIONS: { SETTINGS: "SETTINGS" },
                USER_MANAGEMENT_PERMISSIONS: { USER_MGMT: "USER_MGMT" },
                OUTGOING_PAYMENTS_PERMISSIONS: { OUTGOING: "OUTGOING" },
                PAYMENTS_PERMISSIONS: { PAYMENTS: "PAYMENTS" },
                SEND_MONEY_PERMISSIONS: { SEND: "SEND" },
                TRANSFER_PERMISSIONS: { TRANSFER: "TRANSFER" },
                ACCOUNTS_PERMISSIONS: { ACCOUNTS: "ACCOUNTS" },
                ACCOUNT_MANAGEMENT_PERMISSIONS: { ACCOUNT_MGMT: "ACCOUNT_MGMT" },
                TRANSACTIONS_PERMISSIONS: { TRANSACTIONS: "TRANSACTIONS" },
                TRANSACTION_MANAGEMENT_PERMISSIONS: { TRANSACTION_MGMT: "TRANSACTION_MGMT" },
                BILL_PAYMENTS_PERMISSIONS: { BILL_PAYMENTS: "BILL_PAYMENTS" },
                BILL_MANAGEMENT_PERMISSIONS: { BILL_MGMT: "BILL_MGMT" },
                BENEFICIARIES_PERMISSIONS: { BENEFICIARIES: "BENEFICIARIES" },
                BENEFICIARY_MANAGEMENT_PERMISSIONS: { BENEFICIARY_MGMT: "BENEFICIARY_MGMT" },
                TEAM_MANAGEMENT_PERMISSIONS: { TEAM_MGMT: "TEAM_MGMT" },
                ROLE_MANAGEMENT_PERMISSIONS: { ROLE_MGMT: "ROLE_MGMT" },
                SUPPORT_PERMISSIONS: { SUPPORT: "SUPPORT" },
                CUSTOMER_SUPPORT_PERMISSIONS: { CUSTOMER_SUPPORT: "CUSTOMER_SUPPORT" },
                ONBOARDING_PERMISSIONS: { ONBOARDING: "ONBOARDING" },
                USER_ONBOARDING_PERMISSIONS: { USER_ONBOARDING: "USER_ONBOARDING" },
            };

            const result = transformToBackwardCompatibleFormat(allPermissions);

            expect(result.OVERVIEW_PERMISSIONS).toEqual({
                OVERVIEW: "OVERVIEW",
                DASHBOARD: "DASHBOARD",
            });
            expect(result.SETTINGS_PERMISSIONS).toEqual({
                SETTINGS: "SETTINGS",
                USER_MGMT: "USER_MGMT",
            });
            expect(result.OUTGOING_PAYMENTS_PERMISSIONS).toEqual({
                OUTGOING: "OUTGOING",
                PAYMENTS: "PAYMENTS",
            });
            expect(result.SEND_MONEY_PERMISSIONS).toEqual({
                SEND: "SEND",
                TRANSFER: "TRANSFER",
            });
            expect(result.ACCOUNTS_PERMISSIONS).toEqual({
                ACCOUNTS: "ACCOUNTS",
                ACCOUNT_MGMT: "ACCOUNT_MGMT",
            });
            expect(result.TRANSACTIONS_PERMISSIONS).toEqual({
                TRANSACTIONS: "TRANSACTIONS",
                TRANSACTION_MGMT: "TRANSACTION_MGMT",
            });
            expect(result.BILL_PAYMENTS_PERMISSIONS).toEqual({
                BILL_PAYMENTS: "BILL_PAYMENTS",
                BILL_MGMT: "BILL_MGMT",
            });
            expect(result.BENEFICIARIES_PERMISSIONS).toEqual({
                BENEFICIARIES: "BENEFICIARIES",
                BENEFICIARY_MGMT: "BENEFICIARY_MGMT",
            });
            expect(result.TEAM_MANAGEMENT_PERMISSIONS).toEqual({
                TEAM_MGMT: "TEAM_MGMT",
                ROLE_MGMT: "ROLE_MGMT",
            });
            expect(result.SUPPORT_PERMISSIONS).toEqual({
                SUPPORT: "SUPPORT",
                CUSTOMER_SUPPORT: "CUSTOMER_SUPPORT",
            });
            expect(result.ONBOARDING_PERMISSIONS).toEqual({
                ONBOARDING: "ONBOARDING",
                USER_ONBOARDING: "USER_ONBOARDING",
            });

            // Check ALL_PERMISSIONS contains everything
            expect(Object.keys(result.ALL_PERMISSIONS)).toHaveLength(22);
        });
    });
});
