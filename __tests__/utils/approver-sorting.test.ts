import { sortApproversByStatus, sortApproversByLevel } from "@/utils/approver-sorting";

// Mock data for testing
const mockApprovers = [
    {
        id: 1,
        userId: 101,
        name: "<PERSON>",
        role: "Manager",
        approvalLevel: 3,
        status: null, // Pending
        date: null,
    },
    {
        id: 2,
        userId: 102,
        name: "<PERSON>",
        role: "Director",
        approvalLevel: 1,
        status: "APPROVE" as const,
        date: "23 Nov, 2024 3:00:00 PM",
    },
    {
        id: 3,
        userId: 103,
        name: "<PERSON>",
        role: "Senior Manager",
        approvalLevel: 2,
        status: "DECLIN<PERSON>" as const,
        date: "23 Nov, 2024 4:00:00 PM",
    },
    {
        id: 4,
        userId: 104,
        name: "<PERSON>",
        role: "Manager",
        approvalLevel: 4,
        status: "APPROVE" as const,
        date: "23 Nov, 2024 5:00:00 PM",
    },
    {
        id: 5,
        userId: 105,
        name: "<PERSON>",
        role: "Executive",
        approvalLevel: 1,
        status: "<PERSON><PERSON>IN<PERSON>" as const,
        date: "23 Nov, 2024 6:00:00 PM",
    },
    {
        id: 6,
        userId: 106,
        name: "<PERSON>",
        role: "Director",
        approvalLevel: 2,
        status: null, // Pending
        date: null,
    },
];

describe("Approver Sorting Utilities", () => {
    describe("sortApproversByStatus", () => {
        it("sorts approvers with approved first, declined second, pending last", () => {
            const sorted = sortApproversByStatus(mockApprovers);

            // Check the status order
            const statuses = sorted.map((approver) => approver.status);

            // Count occurrences
            const approvedCount = statuses.filter((status) => status === "APPROVE").length;
            const declinedCount = statuses.filter((status) => status === "DECLINE").length;
            const pendingCount = statuses.filter((status) => status === null).length;

            expect(approvedCount).toBe(2);
            expect(declinedCount).toBe(2);
            expect(pendingCount).toBe(2);

            // Check that approved comes before declined, and declined comes before pending
            const firstApprovedIndex = statuses.findIndex((status) => status === "APPROVE");
            const firstDeclinedIndex = statuses.findIndex((status) => status === "DECLINE");
            const firstPendingIndex = statuses.findIndex((status) => status === null);

            expect(firstApprovedIndex).toBeLessThan(firstDeclinedIndex);
            expect(firstDeclinedIndex).toBeLessThan(firstPendingIndex);
        });

        it("sorts by approval level within the same status group", () => {
            const sorted = sortApproversByStatus(mockApprovers);

            // Find approved approvers and check their order
            const approvedApprovers = sorted.filter((approver) => approver.status === "APPROVE");
            expect(approvedApprovers).toHaveLength(2);
            expect(approvedApprovers[0].approvalLevel).toBeLessThanOrEqual(approvedApprovers[1].approvalLevel);

            // Find declined approvers and check their order
            const declinedApprovers = sorted.filter((approver) => approver.status === "DECLINE");
            expect(declinedApprovers).toHaveLength(2);
            expect(declinedApprovers[0].approvalLevel).toBeLessThanOrEqual(declinedApprovers[1].approvalLevel);

            // Find pending approvers and check their order
            const pendingApprovers = sorted.filter((approver) => approver.status === null);
            expect(pendingApprovers).toHaveLength(2);
            expect(pendingApprovers[0].approvalLevel).toBeLessThanOrEqual(pendingApprovers[1].approvalLevel);
        });

        it("returns the correct specific order for mock data", () => {
            const sorted = sortApproversByStatus(mockApprovers);

            // Expected order: Approved (level 1, 4), Declined (level 1, 2), Pending (level 2, 3)
            expect(sorted[0].name).toBe("Jane Smith"); // APPROVE, level 1
            expect(sorted[1].name).toBe("Alice Brown"); // APPROVE, level 4
            expect(sorted[2].name).toBe("Charlie Davis"); // DECLINE, level 1
            expect(sorted[3].name).toBe("Bob Wilson"); // DECLINE, level 2
            expect(sorted[4].name).toBe("Diana Prince"); // null, level 2
            expect(sorted[5].name).toBe("John Doe"); // null, level 3
        });

        it("does not mutate the original array", () => {
            const originalLength = mockApprovers.length;
            const originalFirstItem = mockApprovers[0];

            sortApproversByStatus(mockApprovers);

            expect(mockApprovers).toHaveLength(originalLength);
            expect(mockApprovers[0]).toBe(originalFirstItem);
        });

        it("handles empty array", () => {
            const result = sortApproversByStatus([]);
            expect(result).toEqual([]);
        });

        it("handles single approver", () => {
            const singleApprover = [mockApprovers[0]];
            const result = sortApproversByStatus(singleApprover);
            expect(result).toEqual(singleApprover);
        });

        it("handles all approvers with same status", () => {
            const allApproved = [
                { ...mockApprovers[0], status: "APPROVE" as const },
                { ...mockApprovers[1], status: "APPROVE" as const },
                { ...mockApprovers[2], status: "APPROVE" as const },
            ];

            const result = sortApproversByStatus(allApproved);

            // Should be sorted by approval level
            expect(result[0].approvalLevel).toBeLessThanOrEqual(result[1].approvalLevel);
            expect(result[1].approvalLevel).toBeLessThanOrEqual(result[2].approvalLevel);
        });
    });

    describe("sortApproversByLevel", () => {
        it("sorts approvers by approval level in ascending order", () => {
            const sorted = sortApproversByLevel(mockApprovers);

            for (let i = 0; i < sorted.length - 1; i++) {
                expect(sorted[i].approvalLevel).toBeLessThanOrEqual(sorted[i + 1].approvalLevel);
            }
        });

        it("does not mutate the original array", () => {
            const originalLength = mockApprovers.length;
            const originalFirstItem = mockApprovers[0];

            sortApproversByLevel(mockApprovers);

            expect(mockApprovers).toHaveLength(originalLength);
            expect(mockApprovers[0]).toBe(originalFirstItem);
        });

        it("handles empty array", () => {
            const result = sortApproversByLevel([]);
            expect(result).toEqual([]);
        });

        it("handles approvers with same approval level", () => {
            const sameLevel = [
                { ...mockApprovers[0], approvalLevel: 2 },
                { ...mockApprovers[1], approvalLevel: 2 },
                { ...mockApprovers[2], approvalLevel: 1 },
            ];

            const result = sortApproversByLevel(sameLevel);

            expect(result[0].approvalLevel).toBe(1);
            expect(result[1].approvalLevel).toBe(2);
            expect(result[2].approvalLevel).toBe(2);
        });
    });
});
