import {
    checkPermissions,
    getUserPermissions,
    getAllSystemPermissions,
    validatePermissions,
} from "@/utils/server-permission-check";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { redirect } from "next/navigation";

// Mock dependencies
jest.mock("next/headers", () => ({
    cookies: jest.fn(),
}));

jest.mock("jwt-decode", () => ({
    jwtDecode: jest.fn(),
}));

jest.mock("next/navigation", () => ({
    redirect: jest.fn(),
}));

// Mock global fetch
global.fetch = jest.fn();

describe("server-permission-check", () => {
    // Mock cookie store
    const mockCookieStore = {
        get: jest.fn(),
    };

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Setup default mock implementations
        cookies.mockReturnValue(mockCookieStore);
        mockCookieStore.get.mockReturnValue({ value: "mock-token" });
        jwtDecode.mockReturnValue({ roleid: 1 });

        // Mock successful API responses
        fetch.mockImplementation((url) => {
            if (url.includes("/v1/role/1")) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ permissions: [1, 2] }),
                });
            } else if (url.includes("/v1/permissions")) {
                return Promise.resolve({
                    ok: true,
                    json: () =>
                        Promise.resolve([
                            { id: 1, name: "PERM_A" },
                            { id: 2, name: "PERM_B" },
                            { id: 3, name: "PERM_C" },
                        ]),
                });
            }
            return Promise.reject(new Error("Unexpected URL"));
        });

        // Mock environment variables
        process.env.NEXT_PUBLIC_USER_API_URL = "https://api.example.com";
    });

    describe("checkPermissions", () => {
        test("returns true when user has required permission (requireAll=false)", async () => {
            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(true);
            expect(redirect).not.toHaveBeenCalled();
            expect(mockCookieStore.get).toHaveBeenCalledWith("auth_token");
            expect(jwtDecode).toHaveBeenCalledWith("mock-token");
            expect(fetch).toHaveBeenCalledWith(
                "https://api.example.com/v1/role/1",
                expect.objectContaining({
                    headers: { Authorization: "Bearer mock-token" },
                })
            );
        });

        test("returns true when user has all required permissions (requireAll=true)", async () => {
            const result = await checkPermissions(["PERM_A", "PERM_B"], true, "/dashboard");

            expect(result).toBe(true);
            expect(redirect).not.toHaveBeenCalled();
        });

        test("redirects when user doesn't have any required permission (requireAll=false)", async () => {
            const result = await checkPermissions(["PERM_D"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("redirects when user doesn't have all required permissions (requireAll=true)", async () => {
            const result = await checkPermissions(["PERM_A", "PERM_C"], true, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("redirects when token is missing", async () => {
            mockCookieStore.get.mockReturnValue(null);

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalled();
        });

        test("redirects when roleId is missing", async () => {
            jwtDecode.mockReturnValue({});

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("redirects when role API call fails", async () => {
            fetch.mockImplementationOnce(() => Promise.resolve({ ok: false }));

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("redirects when permissions API call fails", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: [1, 2] }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({ ok: false });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("handles API errors gracefully", async () => {
            fetch.mockRejectedValue(new Error("API Error"));

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("handles role data without permissions", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: null }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve([]),
                    });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });

        test("handles role data with empty permissions array", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: [] }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve([]),
                    });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const result = await checkPermissions(["PERM_A"], false, "/dashboard");

            expect(result).toBe(false);
            expect(redirect).toHaveBeenCalledWith("/dashboard");
        });
    });

    describe("getUserPermissions", () => {
        test("returns user permissions when token is valid", async () => {
            const permissions = await getUserPermissions();

            expect(permissions).toEqual(["PERM_A", "PERM_B"]);
            expect(mockCookieStore.get).toHaveBeenCalledWith("auth_token");
            expect(jwtDecode).toHaveBeenCalledWith("mock-token");
        });

        test("returns empty array when token is missing", async () => {
            mockCookieStore.get.mockReturnValue(null);

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("returns empty array when roleId is missing", async () => {
            jwtDecode.mockReturnValue({});

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("returns empty array when role API call fails", async () => {
            fetch.mockImplementationOnce(() => Promise.resolve({ ok: false }));

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("returns empty array when permissions API call fails", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: [1, 2] }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({ ok: false });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("handles API errors gracefully", async () => {
            fetch.mockRejectedValue(new Error("API Error"));

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("handles role data without permissions", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: null }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve([]),
                    });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("handles role data with empty permissions array", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: [] }),
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve([]),
                    });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const permissions = await getUserPermissions();

            expect(permissions).toEqual([]);
        });

        test("filters out null permissions when mapping fails", async () => {
            fetch.mockImplementation((url) => {
                if (url.includes("/v1/role/1")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ permissions: [1, 999] }), // 999 doesn't exist
                    });
                } else if (url.includes("/v1/permissions")) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve([{ id: 1, name: "PERM_A" }]),
                    });
                }
                return Promise.reject(new Error("Unexpected URL"));
            });

            const permissions = await getUserPermissions();

            expect(permissions).toEqual(["PERM_A"]); // Only PERM_A should be returned
        });
    });

    describe("getAllSystemPermissions", () => {
        test("returns all system permissions when token is valid", async () => {
            const permissions = await getAllSystemPermissions();

            expect(permissions).toEqual([
                { id: 1, name: "PERM_A" },
                { id: 2, name: "PERM_B" },
                { id: 3, name: "PERM_C" },
            ]);
            expect(mockCookieStore.get).toHaveBeenCalledWith("auth_token");
        });

        test("returns empty array when token is missing", async () => {
            mockCookieStore.get.mockReturnValue(null);

            const permissions = await getAllSystemPermissions();

            expect(permissions).toEqual([]);
        });

        test("returns empty array when API call fails", async () => {
            fetch.mockImplementationOnce(() => Promise.resolve({ ok: false }));

            const permissions = await getAllSystemPermissions();

            expect(permissions).toEqual([]);
        });

        test("handles API errors gracefully", async () => {
            fetch.mockRejectedValue(new Error("API Error"));

            const permissions = await getAllSystemPermissions();

            expect(permissions).toEqual([]);
        });
    });

    describe("validatePermissions", () => {
        test("returns valid result when all permissions exist", async () => {
            const result = await validatePermissions(["PERM_A", "PERM_B"]);

            expect(result).toEqual({
                valid: true,
                missing: [],
                existing: ["PERM_A", "PERM_B"],
            });
        });

        test("returns invalid result when some permissions are missing", async () => {
            const result = await validatePermissions(["PERM_A", "PERM_D", "PERM_E"]);

            expect(result).toEqual({
                valid: false,
                missing: ["PERM_D", "PERM_E"],
                existing: ["PERM_A"],
            });
        });

        test("returns invalid result when all permissions are missing", async () => {
            const result = await validatePermissions(["PERM_D", "PERM_E"]);

            expect(result).toEqual({
                valid: false,
                missing: ["PERM_D", "PERM_E"],
                existing: [],
            });
        });

        test("handles API errors gracefully", async () => {
            fetch.mockRejectedValue(new Error("API Error"));

            const result = await validatePermissions(["PERM_A", "PERM_B"]);

            expect(result).toEqual({
                valid: false,
                missing: ["PERM_A", "PERM_B"],
                existing: [],
            });
        });

        test("handles empty permissions array", async () => {
            const result = await validatePermissions([]);

            expect(result).toEqual({
                valid: true,
                missing: [],
                existing: [],
            });
        });
    });
});
