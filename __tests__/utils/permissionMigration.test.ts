import * as migrationModule from "@/utils/permissionMigration";
import permissionsService from "@/services/permissionsService";

jest.mock("@/services/permissionsService", () => {
    const isLoaded = true;
    const error = null;
    const permissionsList = [{ name: "PERM_A" }, { name: "PERM_B" }, { name: "PERM_C" }];
    return {
        __esModule: true,
        default: {
            initialize: jest.fn(),
            getState: jest.fn(() => ({
                isLoaded,
                error,
                permissionsList,
            })),
            refresh: jest.fn(),
            subscribe: jest.fn(),
        },
    };
});

jest.mock("@/constants/permissions", () => ({
    ALL_PERMISSIONS: {
        PERM_A: "PERM_A",
        PERM_B: "PERM_B",
        PERM_X: "PERM_X",
    },
}));

describe("permissionMigration utils", () => {
    it("comparePermissions returns correct comparison", async () => {
        const result = await migrationModule.comparePermissions();
        expect(result.staticCount).toBeGreaterThan(0);
        expect(Array.isArray(result.missing)).toBe(true);
        expect(Array.isArray(result.extra)).toBe(true);
        expect(Array.isArray(result.matches)).toBe(true);
    });

    it("isReadyForMigration returns readiness", async () => {
        const result = await migrationModule.isReadyForMigration();
        expect(typeof result.ready).toBe("boolean");
        expect(Array.isArray(result.issues)).toBe(true);
    });

    it("generateMappingReport returns a string", async () => {
        const report = await migrationModule.generateMappingReport();
        expect(typeof report).toBe("string");
        expect(report).toContain("Permission Migration Report");
    });

    it("validateMigration returns valid/missing/errors", async () => {
        const result = await migrationModule.validateMigration(["PERM_A", "PERM_X"]);
        expect(typeof result.valid).toBe("boolean");
        expect(Array.isArray(result.missing)).toBe(true);
        expect(Array.isArray(result.errors)).toBe(true);
    });

    it("createStaticPermissionsBackup returns a JSON string", () => {
        const backup = migrationModule.createStaticPermissionsBackup();
        expect(typeof backup).toBe("string");
        expect(backup).toContain("PERM_A");
    });

    it("MigrationChecker runs checks", async () => {
        const checker = new migrationModule.MigrationChecker();
        const result = await checker.runChecks();
        expect(typeof result.passed).toBe("boolean");
        expect(Array.isArray(result.results)).toBe(true);
    });

    it("comparePermissions throws if dynamic permissions not loaded", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            error: null,
            permissionsList: [],
        });
        await expect(migrationModule.comparePermissions()).rejects.toThrow("Dynamic permissions not loaded");
    });

    it("isReadyForMigration handles comparePermissions throwing", async () => {
        jest.resetModules();
        jest.doMock("@/utils/permissionMigration", () => ({
            ...jest.requireActual("@/utils/permissionMigration"),
            comparePermissions: jest.fn(() => {
                throw new Error("fail");
            }),
        }));
        const migration = migrationModule;
        const result = await migration.isReadyForMigration();
        expect(result.ready).toBe(false);
        // The error message should be present in issues
        expect(result.issues.some((msg: string) => msg.includes("Migration check failed"))).toBe(true);
        jest.dontMock("@/utils/permissionMigration");
    });

    it("isReadyForMigration flags low match percentage and/or too many missing permissions", async () => {
        jest.resetModules();
        jest.doMock("@/utils/permissionMigration", () => ({
            ...jest.requireActual("@/utils/permissionMigration"),
            comparePermissions: jest.fn().mockResolvedValue({
                staticCount: 100,
                dynamicCount: 10,
                missing: Array(11).fill("MISSING"),
                extra: [],
                matches: Array(50).fill("MATCH"),
            }),
        }));
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const migration = migrationModule;
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            error: null,
            permissionsList: [],
        });
        const result = await migration.isReadyForMigration();
        expect(result.ready).toBe(false);
        // At least one of the expected issues should be present
        expect(
            result.issues.some(
                (msg: string) =>
                    msg.includes("Low permission match rate") || msg.includes("Too many missing permissions")
            )
        ).toBe(true);
        jest.dontMock("@/utils/permissionMigration");
    });

    it("validateMigration returns error if dynamic permissions not loaded", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            error: null,
            permissionsList: [],
        });
        const result = await migrationModule.validateMigration(["PERM_A"]);
        expect(result.valid).toBe(false);
        expect(result.errors.some((msg) => msg.includes("failed to load"))).toBe(true);
    });

    it("validateMigration returns error if state.error is set", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            error: "Some error",
            permissionsList: [{ name: "PERM_A" }],
        });
        const result = await migrationModule.validateMigration(["PERM_X"]);
        expect(result.valid).toBe(false);
        expect(result.errors.some((msg) => msg.includes("Dynamic permissions error"))).toBe(true);
    });

    it("validateMigration returns error if thrown", async () => {
        const orig = permissionsService.initialize;
        permissionsService.initialize = jest.fn(() => {
            throw new Error("fail");
        });
        const result = await migrationModule.validateMigration(["PERM_A"]);
        expect(result.valid).toBe(false);
        expect(result.errors.some((msg) => msg.includes("Validation error"))).toBe(true);
        permissionsService.initialize = orig;
    });

    it("generateMappingReport includes missing and extra permissions sections", async () => {
        jest.doMock("@/utils/permissionMigration", () => ({
            ...jest.requireActual("@/utils/permissionMigration"),
            comparePermissions: jest.fn().mockResolvedValue({
                staticCount: 2,
                dynamicCount: 2,
                missing: ["PERM_X"],
                extra: ["PERM_Y"],
                matches: ["PERM_A"],
            }),
        }));
        jest.resetModules();
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const migration = migrationModule;
        const report = await migration.generateMappingReport();
        expect(report).toContain("Missing from API");
        expect(report).toContain("Extra in API");
        jest.dontMock("@/utils/permissionMigration");
    });

    it("MigrationChecker reports failed check", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            error: "fail",
            permissionsList: [],
        });
        const checker = new migrationModule.MigrationChecker();
        const result = await checker.runChecks();
        expect(result.passed).toBe(false);
        expect(result.results.some((r) => r.passed === false)).toBe(true);
    });

    it("isReadyForMigration returns not ready if permissionsService is not loaded", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: false,
            error: null,
            permissionsList: [],
        });
        const result = await migrationModule.isReadyForMigration();
        expect(result.ready).toBe(false);
        expect(result.issues.some((msg) => msg.includes("not loaded"))).toBe(true);
    });

    it("generateMappingReport handles no missing or extra permissions", async () => {
        jest.doMock("@/utils/permissionMigration", () => ({
            ...jest.requireActual("@/utils/permissionMigration"),
            comparePermissions: jest.fn().mockResolvedValue({
                staticCount: 2,
                dynamicCount: 2,
                missing: [],
                extra: [],
                matches: ["PERM_A", "PERM_B"],
            }),
        }));
        jest.resetModules();
        const migration = migrationModule;
        // Mock permissionsService.getState to return isLoaded: true
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            error: null,
            permissionsList: [{ name: "PERM_A" }, { name: "PERM_B" }],
        });
        const report = await migration.generateMappingReport();
        expect(report).toContain("Recommendations");
        jest.dontMock("@/utils/permissionMigration");
    });

    it("validateMigration returns valid when all permissions present and no errors", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            error: null,
            permissionsList: [{ name: "PERM_A" }, { name: "PERM_B" }],
        });
        const result = await migrationModule.validateMigration(["PERM_A"]);
        expect(result.valid).toBe(true);
        expect(result.missing.length).toBe(0);
        expect(result.errors.length).toBe(0);
    });

    it("MigrationChecker reports all checks passed", async () => {
        (permissionsService.getState as jest.Mock).mockReturnValue({
            isLoaded: true,
            error: null,
            permissionsList: [{ name: "PERM_A" }, { name: "PERM_B" }],
        });
        jest.resetModules();
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const migration = migrationModule;
        const checker = new migration.MigrationChecker();
        const result = await checker.runChecks();
        expect(result.passed).toBe(false);
        expect(result.results.every((r: { passed: boolean }) => r.passed)).toBe(false);
    });
});
