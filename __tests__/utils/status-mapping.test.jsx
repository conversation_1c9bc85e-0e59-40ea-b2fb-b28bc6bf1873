/**
 * Purpose: Unit tests for the status mapping utility to ensure consistent status handling.
 *
 * Functionality: This test file validates the status mapping utility functions that provide
 * consistent status text and color mapping across different transaction types. It tests
 * the main getStatusMapping function, status classification utilities, and legacy compatibility
 * functions. The tests ensure proper handling of edge cases like null/undefined values,
 * case insensitivity, and various status formats (underscore, camelCase, etc.).
 *
 * Dependencies: Requires the status mapping utility from '@/utils/status-mapping' and
 * Jest testing framework for test execution and assertions.
 *
 * Usage: Run with Jest test runner to validate status mapping functionality and maintain
 * 85%+ test coverage as per project guidelines. Tests cover all major functions and edge cases.
 */

import {
    getStatusMapping,
    getStatusColorLegacy,
    isSuccessfulStatus,
    isPendingStatus,
    isAwaitingApprovalStatus,
    isFailedStatus,
    isRejectedApprovalStatus,
    isProcessingStatus,
    getAllStatusValues,
    STATUS_VALUES,
} from "@/utils/status-mapping";

describe("Status Mapping Utility", () => {
    describe("getStatusMapping", () => {
        test("should map successful status correctly", () => {
            const result = getStatusMapping("Successful");
            expect(result).toEqual({
                text: "Successful",
                color: "success",
                rawStatus: "Successful",
            });
        });

        test("should map failed status correctly", () => {
            const result = getStatusMapping("Failed");
            expect(result).toEqual({
                text: "Failed",
                color: "error",
                rawStatus: "Failed",
            });
        });

        test("should map pending status correctly", () => {
            const result = getStatusMapping("Pending");
            expect(result).toEqual({
                text: "Pending",
                color: "warning",
                rawStatus: "Pending",
            });
        });

        test("should map awaiting approval status correctly", () => {
            const result = getStatusMapping("Awaiting Approval");
            expect(result).toEqual({
                text: "Awaiting Approval",
                color: "warning",
                rawStatus: "Awaiting Approval",
            });
        });

        test("should map processing status correctly", () => {
            const result = getStatusMapping("Processing");
            expect(result).toEqual({
                text: "Processing",
                color: "brand",
                rawStatus: "Processing",
            });
        });

        test("should map rejected approval status correctly", () => {
            const result = getStatusMapping("Rejected Approval");
            expect(result).toEqual({
                text: "Rejected Approval",
                color: "error",
                rawStatus: "Rejected Approval",
            });
        });

        test("should handle null status", () => {
            const result = getStatusMapping(null);
            expect(result).toEqual({
                text: "Unknown",
                color: "neutral",
                rawStatus: "Unknown",
            });
        });

        test("should handle undefined status", () => {
            const result = getStatusMapping(undefined);
            expect(result).toEqual({
                text: "Unknown",
                color: "neutral",
                rawStatus: "Unknown",
            });
        });

        test("should handle case insensitive status", () => {
            const result = getStatusMapping("SUCCESSFUL");
            expect(result).toEqual({
                text: "Successful",
                color: "success",
                rawStatus: "Successful",
            });
        });

        test("should handle underscore variations", () => {
            const result = getStatusMapping("awaiting_approval");
            expect(result).toEqual({
                text: "Awaiting Approval",
                color: "warning",
                rawStatus: "Awaiting Approval",
            });
        });

        test("should handle unknown status with default fallback", () => {
            const result = getStatusMapping("Unknown Status");
            expect(result).toEqual({
                text: "Unknown Status",
                color: "neutral",
                rawStatus: "Unknown Status",
            });
        });
    });

    describe("status classification utilities", () => {
        test("isSuccessfulStatus should work correctly", () => {
            expect(isSuccessfulStatus("Successful")).toBe(true);
            expect(isSuccessfulStatus("successful")).toBe(true);
            expect(isSuccessfulStatus("Pending")).toBe(false);
            expect(isSuccessfulStatus("Failed")).toBe(false);
        });

        test("isPendingStatus should work correctly", () => {
            expect(isPendingStatus("Pending")).toBe(true);
            expect(isPendingStatus("pending")).toBe(true);
            expect(isPendingStatus("Awaiting Approval")).toBe(false);
            expect(isPendingStatus("Successful")).toBe(false);
            expect(isPendingStatus("Failed")).toBe(false);
        });

        test("isAwaitingApprovalStatus should work correctly", () => {
            expect(isAwaitingApprovalStatus("Awaiting Approval")).toBe(true);
            expect(isAwaitingApprovalStatus("awaiting_approval")).toBe(true);
            expect(isAwaitingApprovalStatus("Pending")).toBe(false);
            expect(isAwaitingApprovalStatus("Successful")).toBe(false);
            expect(isAwaitingApprovalStatus("Failed")).toBe(false);
        });

        test("isFailedStatus should work correctly", () => {
            expect(isFailedStatus("Failed")).toBe(true);
            expect(isFailedStatus("failed")).toBe(true);
            expect(isFailedStatus("Rejected Approval")).toBe(false);
            expect(isFailedStatus("Successful")).toBe(false);
            expect(isFailedStatus("Pending")).toBe(false);
        });

        test("isRejectedApprovalStatus should work correctly", () => {
            expect(isRejectedApprovalStatus("Rejected Approval")).toBe(true);
            expect(isRejectedApprovalStatus("rejected_approval")).toBe(true);
            expect(isRejectedApprovalStatus("Failed")).toBe(false);
            expect(isRejectedApprovalStatus("Successful")).toBe(false);
            expect(isRejectedApprovalStatus("Pending")).toBe(false);
        });

        test("isProcessingStatus should work correctly", () => {
            expect(isProcessingStatus("Processing")).toBe(true);
            expect(isProcessingStatus("processing")).toBe(true);
            expect(isProcessingStatus("Pending")).toBe(false);
            expect(isProcessingStatus("Successful")).toBe(false);
        });

        test("should handle null and undefined values", () => {
            expect(isSuccessfulStatus(null)).toBe(false);
            expect(isPendingStatus(undefined)).toBe(false);
            expect(isAwaitingApprovalStatus("")).toBe(false);
            expect(isFailedStatus(null)).toBe(false);
            expect(isRejectedApprovalStatus(undefined)).toBe(false);
            expect(isProcessingStatus("")).toBe(false);
        });
    });

    describe("getAllStatusValues", () => {
        test("should return all status values", () => {
            const allValues = getAllStatusValues();
            expect(Array.isArray(allValues)).toBe(true);
            expect(allValues.length).toBeGreaterThan(0);
            expect(allValues).toContain("Successful");
            expect(allValues).toContain("Failed");
            expect(allValues).toContain("Pending");
            expect(allValues).toContain("Awaiting Approval");
        });

        test("should return unique values", () => {
            const allValues = getAllStatusValues();
            const uniqueValues = [...new Set(allValues)];
            expect(allValues.length).toBe(uniqueValues.length);
        });
    });

    describe("getStatusColorLegacy", () => {
        test("should return correct colors for legacy compatibility", () => {
            expect(getStatusColorLegacy("Successful")).toBe("success");
            expect(getStatusColorLegacy("Failed")).toBe("error");
            expect(getStatusColorLegacy("Pending")).toBe("warning");
            expect(getStatusColorLegacy("Awaiting Approval")).toBe("warning");
        });

        test("should handle case insensitive input", () => {
            expect(getStatusColorLegacy("SUCCESSFUL")).toBe("success");
            expect(getStatusColorLegacy("failed")).toBe("error");
            expect(getStatusColorLegacy("PENDING")).toBe("warning");
        });

        test("should return default color for unknown status", () => {
            expect(getStatusColorLegacy("Unknown")).toBe("neutral");
            expect(getStatusColorLegacy(null)).toBe("neutral");
            expect(getStatusColorLegacy(undefined)).toBe("neutral");
        });
    });

    describe("STATUS_VALUES constant", () => {
        test("should contain expected status values", () => {
            expect(STATUS_VALUES).toBeDefined();
            expect(typeof STATUS_VALUES).toBe("object");
            expect(STATUS_VALUES.SUCCESSFUL).toBe("Successful");
            expect(STATUS_VALUES.FAILED).toBe("Failed");
            expect(STATUS_VALUES.PENDING).toBe("Pending");
            expect(STATUS_VALUES.AWAITING_APPROVAL).toBe("Awaiting Approval");
            expect(STATUS_VALUES.PROCESSING).toBe("Processing");
            expect(STATUS_VALUES.REJECTED_APPROVAL).toBe("Rejected Approval");
            expect(STATUS_VALUES.UNKNOWN).toBe("Unknown");
        });
    });
});
