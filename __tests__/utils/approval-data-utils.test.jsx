import {
    sortApproversByLevel,
    categorizeApproversByStatus,
    calculateApprovalProgress,
    validateApprovalData,
    formatApprovalProgress,
    isApprovalComplete,
    getNextPendingApprover,
} from "@/utils/approval-data-utils";

describe("Approval Data Utils", () => {
    // Mock approval data for testing
    const mockApprovers = [
        {
            id: 178,
            userId: 117,
            name: "<PERSON><PERSON>",
            role: "BigR<PERSON>",
            approvalLevel: 1,
            status: "APPROVE",
            date: "23 Nov, 2024 3:00:00 PM",
        },
        {
            id: 179,
            userId: 110,
            name: "<PERSON>jee<PERSON> withaccount",
            role: "Super Admin",
            approvalLevel: 2,
            status: "DECLINE",
            date: "23 Nov, 2024 3:00:00 PM",
        },
        {
            id: 177,
            userId: 128,
            name: "<PERSON> Petrol",
            role: "BigRole",
            approvalLevel: 3,
            status: null,
            date: null,
        },
    ];

    describe("sortApproversByLevel", () => {
        it("should sort approvers by approval level in ascending order", () => {
            const unsortedApprovers = [
                { ...mockApprovers[2], approvalLevel: 3 },
                { ...mockApprovers[0], approvalLevel: 1 },
                { ...mockApprovers[1], approvalLevel: 2 },
            ];

            const result = sortApproversByLevel(unsortedApprovers);

            expect(result[0].approvalLevel).toBe(1);
            expect(result[1].approvalLevel).toBe(2);
            expect(result[2].approvalLevel).toBe(3);
        });

        it("should handle empty array", () => {
            const result = sortApproversByLevel([]);
            expect(result).toEqual([]);
        });

        it("should handle single approver", () => {
            const singleApprover = [mockApprovers[0]];
            const result = sortApproversByLevel(singleApprover);
            expect(result).toEqual(singleApprover);
        });

        it("should handle duplicate approval levels", () => {
            const duplicateApprovers = [
                { ...mockApprovers[0], approvalLevel: 1, name: "First" },
                { ...mockApprovers[1], approvalLevel: 1, name: "Second" },
            ];

            const result = sortApproversByLevel(duplicateApprovers);
            expect(result).toHaveLength(2);
            expect(result[0].approvalLevel).toBe(1);
            expect(result[1].approvalLevel).toBe(1);
        });

        it("should not mutate original array", () => {
            const original = [...mockApprovers];
            sortApproversByLevel(mockApprovers);
            expect(mockApprovers).toEqual(original);
        });

        it("should handle approvers with undefined/null approval levels", () => {
            const approversWithUndefinedLevels = [
                { ...mockApprovers[0], approvalLevel: undefined },
                { ...mockApprovers[1], approvalLevel: 1 },
                { ...mockApprovers[2], approvalLevel: null },
            ];

            const result = sortApproversByLevel(approversWithUndefinedLevels);

            // Approver with level 1 should be first, undefined/null should be last
            expect(result[0].approvalLevel).toBe(1);
            expect([undefined, null]).toContain(result[1].approvalLevel);
            expect([undefined, null]).toContain(result[2].approvalLevel);
        });

        it("should handle non-array input gracefully", () => {
            const result = sortApproversByLevel(null);
            expect(result).toEqual([]);
        });
    });

    describe("categorizeApproversByStatus", () => {
        it("should categorize approvers by status correctly", () => {
            const result = categorizeApproversByStatus(mockApprovers);

            expect(result.approved).toHaveLength(1);
            expect(result.approved[0].status).toBe("APPROVE");
            expect(result.declined).toHaveLength(1);
            expect(result.declined[0].status).toBe("DECLINE");
            expect(result.pending).toHaveLength(1);
            expect(result.pending[0].status).toBeNull();
        });

        it("should handle empty array", () => {
            const result = categorizeApproversByStatus([]);
            expect(result.approved).toEqual([]);
            expect(result.declined).toEqual([]);
            expect(result.pending).toEqual([]);
        });

        it("should handle only approved approvers", () => {
            const approvedOnly = [
                { ...mockApprovers[0], status: "APPROVE" },
                { ...mockApprovers[1], status: "APPROVE" },
            ];

            const result = categorizeApproversByStatus(approvedOnly);
            expect(result.approved).toHaveLength(2);
            expect(result.declined).toHaveLength(0);
            expect(result.pending).toHaveLength(0);
        });

        it("should handle only declined approvers", () => {
            const declinedOnly = [
                { ...mockApprovers[0], status: "DECLINE" },
                { ...mockApprovers[1], status: "DECLINE" },
            ];

            const result = categorizeApproversByStatus(declinedOnly);
            expect(result.approved).toHaveLength(0);
            expect(result.declined).toHaveLength(2);
            expect(result.pending).toHaveLength(0);
        });

        it("should handle only pending approvers", () => {
            const pendingOnly = [
                { ...mockApprovers[0], status: null },
                { ...mockApprovers[1], status: null },
            ];

            const result = categorizeApproversByStatus(pendingOnly);
            expect(result.approved).toHaveLength(0);
            expect(result.declined).toHaveLength(0);
            expect(result.pending).toHaveLength(2);
        });

        it("should handle undefined status as pending", () => {
            const undefinedStatus = [{ ...mockApprovers[0], status: undefined }];

            const result = categorizeApproversByStatus(undefinedStatus);
            expect(result.pending).toHaveLength(1);
        });

        it("should handle non-array input gracefully", () => {
            const result = categorizeApproversByStatus(null);
            expect(result.approved).toEqual([]);
            expect(result.declined).toEqual([]);
            expect(result.pending).toEqual([]);
        });

        it("should handle non-array input as undefined", () => {
            const result = categorizeApproversByStatus(undefined);
            expect(result.approved).toEqual([]);
            expect(result.declined).toEqual([]);
            expect(result.pending).toEqual([]);
        });
    });

    describe("calculateApprovalProgress", () => {
        it("should calculate approval progress correctly", () => {
            const result = calculateApprovalProgress(mockApprovers);

            expect(result.approved_count).toBe(1);
            expect(result.total_count).toBe(3);
            expect(result.declined_count).toBe(1);
            expect(result.pending_count).toBe(1);
        });

        it("should handle empty array", () => {
            const result = calculateApprovalProgress([]);
            expect(result.approved_count).toBe(0);
            expect(result.total_count).toBe(0);
            expect(result.declined_count).toBe(0);
            expect(result.pending_count).toBe(0);
        });

        it("should handle all approved", () => {
            const allApproved = [
                { ...mockApprovers[0], status: "APPROVE" },
                { ...mockApprovers[1], status: "APPROVE" },
                { ...mockApprovers[2], status: "APPROVE" },
            ];

            const result = calculateApprovalProgress(allApproved);
            expect(result.approved_count).toBe(3);
            expect(result.total_count).toBe(3);
            expect(result.declined_count).toBe(0);
            expect(result.pending_count).toBe(0);
        });

        it("should handle all declined", () => {
            const allDeclined = [
                { ...mockApprovers[0], status: "DECLINE" },
                { ...mockApprovers[1], status: "DECLINE" },
                { ...mockApprovers[2], status: "DECLINE" },
            ];

            const result = calculateApprovalProgress(allDeclined);
            expect(result.approved_count).toBe(0);
            expect(result.total_count).toBe(3);
            expect(result.declined_count).toBe(3);
            expect(result.pending_count).toBe(0);
        });

        it("should handle all pending", () => {
            const allPending = [
                { ...mockApprovers[0], status: null },
                { ...mockApprovers[1], status: null },
                { ...mockApprovers[2], status: null },
            ];

            const result = calculateApprovalProgress(allPending);
            expect(result.approved_count).toBe(0);
            expect(result.total_count).toBe(3);
            expect(result.declined_count).toBe(0);
            expect(result.pending_count).toBe(3);
        });

        it("should include percentage calculation", () => {
            const result = calculateApprovalProgress(mockApprovers);
            expect(result.approval_percentage).toBe(33.33);
        });

        it("should handle zero total for percentage", () => {
            const result = calculateApprovalProgress([]);
            expect(result.approval_percentage).toBe(0);
        });
    });

    describe("validateApprovalData", () => {
        it("should validate clean data successfully", () => {
            const result = validateApprovalData(mockApprovers);
            expect(result.isValid).toBe(true);
            expect(result.errors).toEqual([]);
            expect(result.warnings).toEqual([]);
        });

        it("should detect missing required fields", () => {
            const invalidData = [
                { id: 1, name: "Test" }, // missing userId, role, approvalLevel, status
                { ...mockApprovers[0] },
            ];

            const result = validateApprovalData(invalidData);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Missing required fields in approver at index 0");
        });

        it("should detect duplicate approval levels", () => {
            const duplicateData = [
                { ...mockApprovers[0], approvalLevel: 1 },
                { ...mockApprovers[1], approvalLevel: 1 },
            ];

            const result = validateApprovalData(duplicateData);
            expect(result.warnings).toContain("Duplicate approval level found: 1");
        });

        it("should detect invalid status values", () => {
            const invalidStatus = [{ ...mockApprovers[0], status: "INVALID_STATUS" }];

            const result = validateApprovalData(invalidStatus);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Invalid status "INVALID_STATUS" at index 0');
        });

        it("should handle empty array", () => {
            const result = validateApprovalData([]);
            expect(result.isValid).toBe(true);
            expect(result.errors).toEqual([]);
            expect(result.warnings).toEqual([]);
        });

        it("should handle null input", () => {
            const result = validateApprovalData(null);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Invalid input: expected array");
        });

        it("should handle undefined input", () => {
            const result = validateApprovalData(undefined);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Invalid input: expected array");
        });

        it("should handle non-array input", () => {
            const result = validateApprovalData("not an array");
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Invalid input: expected array");
        });

        it("should validate approval level ranges", () => {
            const invalidLevel = [
                { ...mockApprovers[0], approvalLevel: -1 },
                { ...mockApprovers[1], approvalLevel: 0 },
            ];

            const result = validateApprovalData(invalidLevel);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Invalid approval level -1 at index 0");
            expect(result.errors).toContain("Invalid approval level 0 at index 1");
        });

        it("should detect missing dates for approved/declined statuses", () => {
            const missingDates = [
                { ...mockApprovers[0], status: "APPROVE", date: null },
                { ...mockApprovers[1], status: "DECLINE", date: undefined },
            ];

            const result = validateApprovalData(missingDates);
            expect(result.warnings).toContain("Missing date for approved/declined status at index 0");
            expect(result.warnings).toContain("Missing date for approved/declined status at index 1");
        });
    });

    describe("Edge Cases and Error Handling", () => {
        it("should handle malformed approval objects", () => {
            const malformedData = [null, undefined, "string", 123, mockApprovers[0]];

            const result = validateApprovalData(malformedData);
            expect(result.isValid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);
        });

        it("should handle very large datasets efficiently", () => {
            const largeDataset = Array.from({ length: 1000 }, (_, index) => ({
                ...mockApprovers[0],
                id: index + 1,
                userId: index + 100,
                approvalLevel: Math.floor(index / 100) + 1,
            }));

            const startTime = performance.now();
            const result = sortApproversByLevel(largeDataset);
            const endTime = performance.now();

            expect(result).toHaveLength(1000);
            expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
        });

        it("should handle special characters in names and roles", () => {
            const specialChars = [
                { ...mockApprovers[0], name: "José María O'Connor", role: "VP & CFO" },
                { ...mockApprovers[1], name: "李小明", role: "总经理" },
                { ...mockApprovers[2], name: "François Müller", role: "Responsable financier" },
            ];

            const result = validateApprovalData(specialChars);
            expect(result.isValid).toBe(true);
        });
    });

    describe("formatApprovalProgress", () => {
        it("should format approval progress correctly", () => {
            const progress = {
                approved_count: 2,
                total_count: 3,
                declined_count: 0,
                pending_count: 1,
                approval_percentage: 66.67,
            };

            const result = formatApprovalProgress(progress);
            expect(result).toBe("2 of 3 approvals granted");
        });

        it("should handle zero approvals", () => {
            const progress = {
                approved_count: 0,
                total_count: 3,
                declined_count: 1,
                pending_count: 2,
                approval_percentage: 0,
            };

            const result = formatApprovalProgress(progress);
            expect(result).toBe("0 of 3 approvals granted");
        });

        it("should handle all approvals granted", () => {
            const progress = {
                approved_count: 3,
                total_count: 3,
                declined_count: 0,
                pending_count: 0,
                approval_percentage: 100,
            };

            const result = formatApprovalProgress(progress);
            expect(result).toBe("3 of 3 approvals granted");
        });

        it("should handle single approval", () => {
            const progress = {
                approved_count: 1,
                total_count: 1,
                declined_count: 0,
                pending_count: 0,
                approval_percentage: 100,
            };

            const result = formatApprovalProgress(progress);
            expect(result).toBe("1 of 1 approvals granted");
        });
    });

    describe("isApprovalComplete", () => {
        it("should return true when all approvals are granted", () => {
            const allApproved = [
                { ...mockApprovers[0], status: "APPROVE" },
                { ...mockApprovers[1], status: "APPROVE" },
                { ...mockApprovers[2], status: "APPROVE" },
            ];

            const result = isApprovalComplete(allApproved);
            expect(result).toBe(true);
        });

        it("should return false when there are pending approvals", () => {
            const result = isApprovalComplete(mockApprovers);
            expect(result).toBe(false);
        });

        it("should return false when there are no approved approvals", () => {
            const allPending = [
                { ...mockApprovers[0], status: null },
                { ...mockApprovers[1], status: null },
                { ...mockApprovers[2], status: null },
            ];

            const result = isApprovalComplete(allPending);
            expect(result).toBe(false);
        });

        it("should return false when all approvals are declined", () => {
            const allDeclined = [
                { ...mockApprovers[0], status: "DECLINE" },
                { ...mockApprovers[1], status: "DECLINE" },
                { ...mockApprovers[2], status: "DECLINE" },
            ];

            const result = isApprovalComplete(allDeclined);
            expect(result).toBe(false);
        });

        it("should return true when some are approved and none are pending", () => {
            const someApprovedNonePending = [
                { ...mockApprovers[0], status: "APPROVE" },
                { ...mockApprovers[1], status: "DECLINE" },
            ];

            const result = isApprovalComplete(someApprovedNonePending);
            expect(result).toBe(true);
        });

        it("should handle empty array", () => {
            const result = isApprovalComplete([]);
            expect(result).toBe(false);
        });

        it("should handle non-array input gracefully", () => {
            const result = isApprovalComplete(null);
            expect(result).toBe(false);
        });
    });

    describe("getNextPendingApprover", () => {
        it("should return the first pending approver by approval level", () => {
            const unsortedApprovers = [
                { ...mockApprovers[2], approvalLevel: 3, status: null },
                { ...mockApprovers[0], approvalLevel: 1, status: "APPROVE" },
                { ...mockApprovers[1], approvalLevel: 2, status: null },
            ];

            const result = getNextPendingApprover(unsortedApprovers);
            expect(result).toEqual(
                expect.objectContaining({
                    approvalLevel: 2,
                    status: null,
                })
            );
        });

        it("should return null when no pending approvers exist", () => {
            const allApproved = [
                { ...mockApprovers[0], status: "APPROVE" },
                { ...mockApprovers[1], status: "APPROVE" },
                { ...mockApprovers[2], status: "DECLINE" },
            ];

            const result = getNextPendingApprover(allApproved);
            expect(result).toBeNull();
        });

        it("should return the lowest level pending approver", () => {
            const multipleApprovers = [
                { ...mockApprovers[0], approvalLevel: 1, status: null },
                { ...mockApprovers[1], approvalLevel: 2, status: null },
                { ...mockApprovers[2], approvalLevel: 3, status: null },
            ];

            const result = getNextPendingApprover(multipleApprovers);
            expect(result).toEqual(
                expect.objectContaining({
                    approvalLevel: 1,
                    status: null,
                })
            );
        });

        it("should handle empty array", () => {
            const result = getNextPendingApprover([]);
            expect(result).toBeNull();
        });

        it("should handle single pending approver", () => {
            const singlePending = [{ ...mockApprovers[0], status: null }];

            const result = getNextPendingApprover(singlePending);
            expect(result).toEqual(
                expect.objectContaining({
                    status: null,
                })
            );
        });

        it("should handle mixed approval levels with gaps", () => {
            const gappedLevels = [
                { ...mockApprovers[0], approvalLevel: 5, status: null },
                { ...mockApprovers[1], approvalLevel: 1, status: "APPROVE" },
                { ...mockApprovers[2], approvalLevel: 3, status: null },
            ];

            const result = getNextPendingApprover(gappedLevels);
            expect(result).toEqual(
                expect.objectContaining({
                    approvalLevel: 3,
                    status: null,
                })
            );
        });

        it("should handle non-array input gracefully", () => {
            const result = getNextPendingApprover(null);
            expect(result).toBeNull();
        });
    });
});
