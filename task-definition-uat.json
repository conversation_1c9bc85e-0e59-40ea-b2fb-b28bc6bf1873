{"family": "cib-client-frontend-uat", "containerDefinitions": [{"name": "cib-client-frontend", "image": "406816179930.dkr.ecr.eu-west-1.amazonaws.com/cib-client-frontend-uat", "cpu": 1024, "memory": 2048, "portMappings": [{"name": "cib-client-frontend-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cib-client-frontend-uat", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::406816179930:role/ECSTaskExecutionRole", "executionRoleArn": "arn:aws:iam::406816179930:role/ECSTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}